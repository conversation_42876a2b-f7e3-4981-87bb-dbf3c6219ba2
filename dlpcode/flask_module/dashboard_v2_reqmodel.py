from pydantic import BaseModel, Field, validator, root_validator, conint
from typing import Dict, Optional, List, Literal
from uuid import UUID

class WidgetDetails(BaseModel):
    starttime: int = Field(0, description="Start time as epoch timestamp")
    endtime: Optional[int] = Field(None, description="End time as epoch timestamp")
    scanpolicy: Optional[UUID] = Field(None, description="Scan Policy UUID")

class Dashboardv2Model(BaseModel):
    widgetnames: Optional[
        Dict[
            Literal[
                "totalscannedfiles",
                "sensitivefileowners",
                "filestoscan",
                "dailyfilescansensitiveresults",
                "dailyfilescanscannedresults",
                "sensitivefileslabeled",
                "sensitivefilesdistribution",
                "compliancefiles",
                "compliancefilesshowmore",
                "aicategories",
            ],
            WidgetDetails
        ]
    ] = Field(
        None, description="Dictionary of widget names with their respective configurations requested to poplulate the dashboard"
    )
