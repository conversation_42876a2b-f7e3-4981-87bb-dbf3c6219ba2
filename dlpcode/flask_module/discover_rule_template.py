import traceback
from flask import Blueprint, request
from pydantic import ValidationError
from util.common_log import get_logger
from util.config import configs
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes

logger = get_logger("api")
discover_rule_template = Blueprint("discover_rule_template", __name__)


@discover_rule_template.route('/list', methods=['GET'])
def handle_discover_rule_template_list_get():
    from flask_module.discover_rule_template_reqmodel import DiscoverRuleTemplateListQueryReqModel
    from service import predefined_rule_template_service

    try:
        # get all conditions
        conditions = request.args.to_dict()
        logger.info(f"conditions: {conditions}")
        try:
            reqmodel = DiscoverRuleTemplateListQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        logger.info(f"reqmodel: {reqmodel}")
        results = predefined_rule_template_service.get_rule_template_list_by_conditions(reqmodel.dict(exclude_none=True), logger)
        if results is None:
            return error_response(Ecodes.NOT_FOUND, 404, 'Rule templates not found')
        return success_response(results, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@discover_rule_template.route('/rule', methods=['GET'])
def handle_discover_rule_template_get():
    from flask_module.discover_rule_template_reqmodel import DiscoverRuleTemplateQueryReqModel
    from service import predefined_rule_template_service

    try:
        # get all conditions
        conditions = request.args.to_dict()
        logger.info(f"conditions: {conditions}")
        try:
            reqmodel = DiscoverRuleTemplateQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        logger.info(f"reqmodel: {reqmodel}")
        results = predefined_rule_template_service.get_rule_template_by_id(reqmodel.id, logger)
        if results is None:
            return error_response(Ecodes.NOT_FOUND, 404, 'Rule template not found')
        return success_response(results, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)
    
@discover_rule_template.route('/rule_summary', methods=['GET'])
def handle_discover_rule_template_summary_get():
    from flask_module.discover_rule_template_reqmodel import DiscoverRuleSummaryQueryReqModel
    from service import predefined_rule_template_service

    try:
        # get all conditions
        conditions = request.args.to_dict()
        logger.info(f"conditions: {conditions}")
        try:
            reqmodel = DiscoverRuleSummaryQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        logger.info(f"reqmodel: {reqmodel}")
        results = predefined_rule_template_service.get_rule_template_summary_by_ids(reqmodel.id, logger)
        if results is None:
            return error_response(Ecodes.NOT_FOUND, 404, 'Rule template not found')
        return success_response(results, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)