import re
from flask import request, jsonify, abort
from flask_module.session import check
from util.err_codes import error_response
import util.err_codes as Ecodes
from system import dlp_file


ROLE_RESOURCE_PERMISSIONS = dlp_file.file_to_json("/dlpcode/data/role_permission.json")


def register_hooks(app):
    def check_valid_cookie():
        sess = check(request)
        if not sess:
            ret = re.search(r'(=?).(js|css|html|json|xml|ico|png|jpeg|gif|swf|otf|eot|ttf|woff|woff2)', request.path)
            # print(f'{request.path} => {ret}')
            if not ret:
                return error_response(Ecodes.UNAUTHORIZED_ERROR, 401, "401 Unauthorized")
        else:
            method = request.method
            path = request.path
            ret = check_permission(sess, path, method)
            if ret < 0:
                return error_response(Ecodes.FORBIDDEN_ERROR, 403,
                                      "You do not have permission to perform this operation.")
        return ""

    @app.before_request
    def before_request_func():
        if '/' == request.path:
            return
        # if request.path.startswith( '/api/v1/ftnt_device') or request.path.startswith('/api/v1/api_key'):
        #     return
        ignore_paths = ['user.checkLogin', 'user.login', 'user.logout', 'index', 'serve_statics', 'fabric']
        if request.endpoint not in ignore_paths:
            ret = check_valid_cookie()
            if ret:
                return ret
            
    @app.after_request
    def after_request_func(response):
  
        origin = request.headers.get('Origin')
        if origin:
            response.headers['Access-Control-Allow-Origin'] = origin

        response.headers['Access-Control-Allow-Credentials'] = 'true'
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['Content-Security-Policy'] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data:; "
            "connect-src 'self'; "
            "object-src 'none'; "
            "base-uri 'self'; "
            "frame-ancestors 'self';"
        )


        return response

    @app.errorhandler(401)
    def unauthorized_error(error):
        response = jsonify({"ErrorCode": -1, "ErrorMessage": error.description})
        response.status_code = 401
        return response
    
        


def check_permission(sess, path, method):
    roles = sess.get('roles', [])
    if not roles:
        return -1

    if 'admin' in roles:
        return 0  # Admin role has all permissions

    for role in roles:
        permissions = ROLE_RESOURCE_PERMISSIONS.get(role, {})
        allowed_methods = permissions.get(path, [])
        if method in allowed_methods:
            return 0  # Allowed if method is found in any role's permissions

    return -1  # Forbidden if method is not allowed in any role's permissions

def validate_condition_relation_string(s):
    pattern = r'(?<!\S)\( | \)(?!\S)| (?<!\S)AND(?!\S) | (?<!\S)OR(?!\S) |(?<!\S)NOT(?!\S)|(?<!\S)c\d+(?!\S)'
    
    matches = re.findall(pattern, s)
    open_parentheses = s.count('(')
    close_parentheses = s.count(')')
    and_count = s.count('AND')
    or_count = s.count('OR')
    not_count = s.count('NOT')
    condition_count = len(re.findall(r'\bc\d+\b', s))
    
    if open_parentheses != close_parentheses:
        return False, "the number of opening and closing parentheses are not equal"
    
    if len(matches) != (open_parentheses + close_parentheses + and_count + or_count + not_count + condition_count):
        return False, "the spaces around '(', ')', 'AND', 'OR', 'NOT', or conditions are incorrect"
    
    # Check logical expression validity
    try:
        eval(s.replace('AND', 'and').replace('OR', 'or').replace('NOT', 'not').replace('c', ''))
    except Exception as e:
        return False, f"Invalid logical expression"
    
    return True, None

def preload_modules():
    """
    Preload modules to reduce latency on the first access.
    """
    try:
        from flask_module import collector_reqmodel
    except Exception as e:
        print(f"Failed to preload module: {e}")
    
    
def start_preloading():
    import threading
    preload_thread = threading.Thread(target=preload_modules, daemon=True)
    preload_thread.start()