import json
import traceback
from flask import Blueprint, request
from pydantic import ValidationError
from util.common_log import get_logger
from util.config import configs
from util.err_codes import error_response, success_response
from util.err_codes import ModErrCode
from flask_module import session
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from util.enum_ import ProtectionConst, CredentialType
from storage.service.profiles import get_storage_profile_by_id


logger = get_logger("api")
collector = Blueprint("scans", __name__)


def suspend_list_task( ids, need_resume: bool = True, reason=""):
    from service.task_queue_service import TaskQueueService
    from domain_model.task_queue import SuspendingEvent

    tqs = TaskQueueService()
    for id in ids :
        tqs.add_priority_event(SuspendingEvent(scan_policy_id=id, wait_for_result=1, resume_job=need_resume, fail_reason=reason))

    return


@collector.route('/', methods=['DELETE'])
def delete_scan_policy( ):
    from domain_model.scan_policy import delete_scan_policies, get_scan_policy
    from flask_module.collector_reqmodel import ScanPolicyDeleteReqModel
    from service import scan_policy_service
    from service.incidents_data_scanning import get_ds_incidents_by_conditions, update_ds_incident_scan_status

    try:
        # delete an existing scan policy
        args = request.args.to_dict(flat=False)
        logger.info(f"Deleting scan policy by args {args}")

        try:
            reqmodel = ScanPolicyDeleteReqModel(**args)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02000002, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        name_list = []
        not_found = []
        for scan_policy_id in reqmodel.id:
            scan_policy = get_scan_policy(id=scan_policy_id)
            if not scan_policy:
                not_found.append(scan_policy_id)
            else:
                name_list.append(scan_policy.name)
        if not_found:
            return error_response(ModErrCode.ErrCode02000003, 404, extend_message={not_found})

        for scan_policy_id in reqmodel.id:
            ref_count, ref_list = scan_policy_service.check_ref_by_id(scan_policy_id)

        if ref_count > 0:
            return error_response(ModErrCode.ErrCode02000004, 400, referred_by_dlp_rules=ref_list)

        # Check if scan policy is referenced by any devices
        for scan_policy_id in reqmodel.id:
            device_ref = scan_policy_service.get_h2_scan_ids_check_ref(scan_policy_id, logger)
            if device_ref["using"]:
                return error_response(ModErrCode.ErrCode02000005, 400, referred_by_devices=device_ref["devices"])

        # remodel.id is a list
        suspend_list_task(reqmodel.id, False, reason="Delete Scan")

        row_count = 0
        for scan_policy_id in reqmodel.id:
            scan_policy_service.clear_db_by_id(scan_policy_id,logger)
            row_count = row_count + delete_scan_policies(id=scan_policy_id)

        if row_count == 0:
            return error_response(ModErrCode.ErrCode02000006, 404)

        incidents, _ = get_ds_incidents_by_conditions(conditions={"sid": reqmodel.id}, logger=logger)
        update_ds_incident_scan_status([i["id"] for i in incidents], value=False, logger=logger)

        name_list_str = ", ".join(name_list)
        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Delete scan policy {name_list_str}",
            desc="Delete scan policy",
            type=LogType.SCANS.value,
            action=LogAction.DELETE.value,
        )
        return success_response({'message': 'Scan deleted'})
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000007, 500)

def check_target_path(storage_type, scan_init_info, protection_profiles, task_uuid):
    from service.protection_profile_service import get_protection_profiles
    from huey_worker.protection_action_task import get_connector_from_scan_policy, get_connector_from_protection_profile

    try:
        quarantine_uuids = protection_profiles.get('quarantine_profiles', [])
        copy_uuids = protection_profiles.get('copy_profiles', [])

        for copy_uuid in copy_uuids:
            copy_profiles, _ = get_protection_profiles({'id': copy_uuid})
            if copy_profiles is None or len(copy_profiles) < 1:
                return 404, ModErrCode.ErrCode02050001, copy_uuid

            copy_profile = copy_profiles[0]
            profile = copy_profile['profile']
            path = profile['path']
            credential_type = profile['credential_type']
            if credential_type == CredentialType.USE_SAME_CREDENTIAL_WITH_SCAN_POLICY:
                connector = get_connector_from_scan_policy(storage_type, scan_init_info)
                if not connector.check_folder_exist(path, task_uuid):
                    return 404, ModErrCode.ErrCode02050002, path
                if not connector.check_write_access(path, task_uuid):
                    return 500, ModErrCode.ErrCode02050031, path
            else:
                connector = get_connector_from_protection_profile(copy_profile)

            new_folder = f"{ProtectionConst.SENSITIVE_FILES_FOLDER_PREFIX.value}{str(task_uuid).split('-')[0]}"
            new_folder_path = f"{path}/{new_folder}"
            if connector.check_folder_exist(new_folder_path, task_uuid):
                logger.info(f"Folder {new_folder_path} is exist, no need to create")
                continue
            if connector.create_folder(path, new_folder, task_uuid) is None:
                return 500, ModErrCode.ErrCode02050003, f"{path}/{new_folder}"

        for quarantine_uuid in quarantine_uuids:
            quarantine_profiles, _ = get_protection_profiles({'id': quarantine_uuid})
            if quarantine_profiles is None or len(quarantine_profiles) < 1:
                return 404, ModErrCode.ErrCode02050004, quarantine_uuid

            quarantine_profile = quarantine_profiles[0]
            profile = quarantine_profile['profile']
            path = profile['path']
            credential_type = profile['credential_type']
            if credential_type == CredentialType.USE_SAME_CREDENTIAL_WITH_SCAN_POLICY:
                connector = get_connector_from_scan_policy(storage_type, scan_init_info)
                if not connector.check_folder_exist(path, task_uuid):
                    return 404, ModErrCode.ErrCode02050005, path
                if not connector.check_write_access(path, task_uuid):
                    return 500, ModErrCode.ErrCode02050032, path
            else:
                connector = get_connector_from_protection_profile(quarantine_profile)

            new_folder = f"{ProtectionConst.SENSITIVE_FILES_FOLDER_PREFIX.value}{str(task_uuid).split('-')[0]}"
            new_folder_path = f"{path}/{new_folder}"
            if connector.check_folder_exist(new_folder_path, task_uuid):
                logger.info(f"Folder {new_folder_path} is exist, no need to create")
                continue
            if connector.create_folder(path, new_folder, task_uuid) is None:
                return 500, ModErrCode.ErrCode02050006, f"{path}/{new_folder}"

        return 200, None, ""
    except Exception as e:
        logger.exception(traceback.format_exc())
        return 500, ModErrCode.ErrCode02050007, None

@collector.route('/', methods=['GET', 'POST', 'PUT'])
def handle_scan_policy():
    from flask_module.collector_reqmodel import (
        ScanPolicyCreateReqModel,
        ScanPolicyUpdateReqModel,
        ScanPolicyQueryReqModel,
    )
    from service import scan_policy_service
    from util.enum_ import TaskStatus
    from psycopg2.errors import UniqueViolation
    from system.dlp_license import is_licensed
    from domain_model.scan_policy import delete_scan_policies

    if request.method == 'POST':
        try:
            # check if the license is valid
            if not is_licensed():
                max_scan_policy_creation = configs.get("no_license_limitation",{}).get("max_scan_policy_creation", 1)
                if len(scan_policy_service.get_all_scan_policies()) >= max_scan_policy_creation:
                    return error_response(ModErrCode.ErrCode02000007, 400, extend_message=max_scan_policy_creation)

            # create a new scann policy
            data = request.get_json()

            # set the default value for status
            # Newly created scan policy should be in IDLING status
            data["status"] = TaskStatus.IDLING.value

            try:
                reqmodel = ScanPolicyCreateReqModel(**data)
            except ValidationError as e:
                return error_response(ModErrCode.ErrCode02000008, 400, custom_message=e)
            logger.info(f"reqmodel: {reqmodel}")

            max_scan_policy = configs["max_scan_policy"]
            result = scan_policy_service.check_max_scan_policy(max_scan_policy)
            if result < 0:
                return error_response(ModErrCode.ErrCode02000009, 400, extend_message=max_scan_policy)

            payload = reqmodel.dict(exclude_none=True)
            storage_id = payload['scan_init_info']['storage_id']
            storage = get_storage_profile_by_id(storage_id)
            if storage == None:
                return error_response(ModErrCode.ErrCode02000073, 500)

            scan_policy = scan_policy_service.create_scan_policy(payload,max_scan_policy,logger)
            if scan_policy == None:
                logger.error(f"Create scan policy failed!")
                return error_response(ModErrCode.ErrCode02000010, 400, extend_message='Create scan policy failed')

            status_code, err_code, err_msg = check_target_path(scan_policy.storage_type, scan_policy.scan_init_info, payload.get("protection_profiles", {}), scan_policy.id)
            if status_code != 200:
                delete_scan_policies(id=scan_policy.id)
                return error_response(err_code, status_code, extend_message=err_msg)

            scan_policy_service.clear_db_by_id(scan_policy.id,logger)
            record_event_log(
                user=session.get_user(request),
                level=LogLevel.INFO.value,
                message=f"Create scan policy {scan_policy.name}",
                desc="Create scan policy",
                type=LogType.SCANS.value,
                action=LogAction.CREATE.value,
            )
            return success_response(scan_policy.to_dict(), 201)
        except UniqueViolation:
            return error_response(ModErrCode.ErrCode02000011, 409)
        except:
            logger.exception(traceback.format_exc())
            return error_response(ModErrCode.ErrCode02000012, 500)

    elif request.method == 'GET':
        try:
            # read an existing dlp_policy
            conditions = request.args.to_dict()

            try:
                reqmodel = ScanPolicyQueryReqModel(**conditions)
            except ValidationError as e:
                return error_response(ModErrCode.ErrCode02000013, 400, custom_message=e)
            logger.info(f"reqmodel: {reqmodel}")

            total_count, results = scan_policy_service.get_scan_policy_by_conditions(reqmodel.dict(exclude_none=True), logger)
            if results is None:
                return error_response(ModErrCode.ErrCode02000014, 500)

            for scan_policy in results:
                data = scan_policy_service.gather_scan_info(str(scan_policy["id"]))
                scan_policy["last_scan_result"] = data["last_scan_result"]
                scan_policy["target"] = data["target"]
                scan_policy["scan_init_info"] = data["scan_init_info"]
                if data["status_name"]:
                    scan_policy["status"] = data["status"]
                    scan_policy["status_name"] = data["status_name"]
                    scan_policy["progress"] = data["progress"]
                else:
                    scan_policy["status_name"] = "UNKNOWN"
                    scan_policy["progress"] = 0

                _, ref_list = scan_policy_service.check_ref_by_id(scan_policy["id"])
                scan_policy["referred_by_dlp_rules"] = ref_list

            return success_response({
                "list": results,
                "total": total_count,
                "page": reqmodel.page,
                "per_page": reqmodel.per_page,
            })
        except:
            logger.exception(traceback.format_exc())
            return error_response(ModErrCode.ErrCode02000015, 500)

    elif request.method == 'PUT':
        try:
            data = request.get_json()
            policy_id = data.get('id')
            if not policy_id:
                return error_response(ModErrCode.ErrCode02000016, 400)

            spolicy = scan_policy_service.read_scan_policy(policy_id)
            if spolicy == None :
                return error_response(ModErrCode.ErrCode02000017, 400)
            storage_type = spolicy.to_dict()["storage_type"]
            data["storage_type"] = storage_type
            logger.info(f"Update scan_policy: {data}")

            try:
                reqmodel = ScanPolicyUpdateReqModel(**data)
            except ValidationError as e:
                return error_response(ModErrCode.ErrCode02000018, 400, e)

            payload = reqmodel.dict(exclude_none=False)
            storage_id = payload['scan_init_info']['storage_id']
            storage = get_storage_profile_by_id(storage_id)
            if storage == None:
                return error_response(ModErrCode.ErrCode02000074, 500)

            status_code, err_code, err_msg = check_target_path(payload["storage_type"], payload["scan_init_info"], payload.get("protection_profiles", {}), policy_id)
            if status_code != 200:
                return error_response(err_code, status_code, extend_message=err_msg)

            updated_policy = scan_policy_service.update_scan_policy(policy_id, payload)
            if updated_policy is None:
                return error_response(ModErrCode.ErrCode02000019, 404)

            #try to stop the policy
            suspend_list_task([policy_id],reason="Edit Scan Policy")

            record_event_log(
                user=session.get_user(request),
                level=LogLevel.INFO.value,
                message=f"Edit scan policy {updated_policy.name}",
                desc="Edit scan policy",
                type=LogType.SCANS.value,
                action=LogAction.EDIT.value,
            )
            return success_response(updated_policy.to_dict())
        except:
            logger.exception(traceback.format_exc())
            return error_response(ModErrCode.ErrCode02000020, 500)


@collector.route('/custom_datatype_checkref', methods=['GET'])
def custom_datatype_check_ref():
    from flask_module.collector_reqmodel import ScanPolicyCustomDatatypeCheckRefReqModel
    from service import custom_datatype_service

    try:
        conditions = request.args.to_dict()
        try:
            reqmodel = ScanPolicyCustomDatatypeCheckRefReqModel(**conditions)
        except ValidationError as e:
                return error_response(ModErrCode.ErrCode02000021, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        custom_data_id = request.args.get('id', type=str)
        ref_data = custom_datatype_service.get_custom_datatype_check_ref(custom_data_id, logger)
        if not ref_data:
            return success_response([])
        return success_response(ref_data)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000022, 500)


@collector.route('/discover_policy_checkref', methods=['GET'])
def discover_policy_check_ref():
    from flask_module.collector_reqmodel import ScanPolicyDiscoverPolicyCheckRefReqModel
    from service import scan_policy_service

    try:
        conditions = request.args.to_dict()
        try:
            reqmodel = ScanPolicyDiscoverPolicyCheckRefReqModel(**conditions)
        except ValidationError as e:
                return error_response(ModErrCode.ErrCode02000023, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        policy_id = request.args.get('id', type=str)
        ref_data = scan_policy_service.get_discover_policy_check_ref(policy_id, logger)
        if not ref_data:
            return success_response([])
        return success_response(ref_data)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000024, 500)


@collector.route('/history', methods=['GET'])
def handle_scan_history():
    from flask_module.collector_reqmodel import ScanPolicyQueryHistoryReqModel
    from service import scan_history_service

    try:
        conditions = request.args.to_dict()
        try:
            reqmodel = ScanPolicyQueryHistoryReqModel(**conditions)
        except ValidationError as e:
                return error_response(ModErrCode.ErrCode02000025, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        results = scan_history_service.get_scan_policy_history(reqmodel.dict(exclude_none=True), logger)
        return success_response([history.to_dict() for history in results])
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000026, 500)


@collector.route('/list_folders', methods=['GET'])
def handle_list_folders():
    from connector.aws_connector import AWSConnector
    from connector.sharepoint_connector import SharePointConnector
    from connector.sharepoint_token_connector import SharePointTokenConnector
    from connector.smb_connector import SMBConnector
    from connector.google_connector import GoogleConnector
    from flask_module.collector_reqmodel import ListFoldersQueryReqModel
    from util.enum_ import StorageType
    from storage.service.profiles import get_storage_profile_by_id

    try:
        # list the folders for the storage
        folder_list = []
        try:
            args = request.args.to_dict()
            logger.info(f"List folders  for storage{args}")
            reqmodel = ListFoldersQueryReqModel(**args)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02000027, 400, custom_message=e)

        stype = reqmodel.storage_type
        storage = get_storage_profile_by_id(reqmodel.storage_id)

        init_params = storage['auth_info']
        init_params["storage_type"] = stype
        init_params["storage_id"] = reqmodel.storage_id

        if stype == StorageType.SHAREPOINT_OL:
            init_params['allow_ntlm'] = False
        elif stype == StorageType.SHAREPOINT_OP:
            init_params['allow_ntlm'] = True

        connector_class_map = {
            1: AWSConnector,
            2: SharePointTokenConnector,
            3: SharePointConnector,
            4: SMBConnector,
            6: GoogleConnector
        }
        connector_class = connector_class_map.get(stype)
        if stype == StorageType.SHAREPOINT_OL and init_params.get("usecredentials", True):
            connector_class = SharePointConnector
        connector = connector_class(init_params)

        folder_list = connector.get_folders()
        if folder_list is None:
            return error_response(ModErrCode.ErrCode02000028, 404)

        page = reqmodel.page
        per_page = reqmodel.per_page
        total = len(folder_list)
        if page is None and per_page is None:
            return success_response({
                "list": folder_list
            })
        slice_from = per_page * (page - 1)
        slice_to = per_page * page
        folder_list = folder_list[slice_from:slice_to]
        return success_response({
            "list": folder_list,
            "total": total,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
        })
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000029, 500)

@collector.route('/list_folders_view', methods=['GET'])
def handle_list_folders_view():
    from domain_model.scan_policy import get_scan_policy
    from connector.aws_connector import AWSConnector
    from connector.sharepoint_connector import SharePointConnector
    from connector.sharepoint_token_connector import SharePointTokenConnector
    from connector.smb_connector import SMBConnector
    from connector.google_connector import GoogleConnector
    from flask_module.collector_reqmodel import ListFoldersViewQueryReqModel
    from util.enum_ import StorageType
    from storage.service.profiles import get_storage_profile_by_id

    try:
        # list the folders for the storage
        folder_list = []
        try:
            args = request.args.to_dict()
            logger.info(f"List folders  for scan id{args}")
            reqmodel = ListFoldersViewQueryReqModel(**args)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02000030, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        scan_policy = get_scan_policy(id=reqmodel.scan_policy_id)
        if scan_policy is None:
            return error_response(ModErrCode.ErrCode02000031, 404)

        stype = scan_policy.storage_type
        storage_id = scan_policy.scan_init_info['storage_id']
        storage = get_storage_profile_by_id(storage_id)

        init_params = storage['auth_info']
        init_params["storage_type"] = stype
        init_params["storage_id"] = storage_id

        # secrets are encrypted in the db
        init_params["pwd_encrypted"] = 1
        init_params["pwd_encrypted2"] = 1

        if stype == StorageType.SHAREPOINT_OL:
            init_params['allow_ntlm'] = False
        elif stype == StorageType.SHAREPOINT_OP:
            init_params['allow_ntlm'] = True

        connector_class_map = {
            1: AWSConnector,
            2: SharePointTokenConnector,
            3: SharePointConnector,
            4: SMBConnector,
            6: GoogleConnector,
        }
        connector_class = connector_class_map.get(stype)
        if stype == StorageType.SHAREPOINT_OL and init_params.get("usecredentials", True):
            connector_class = SharePointConnector
        connector = connector_class(init_params, pwd_encrypted = init_params["pwd_encrypted"], pwd_encrypted2 = init_params["pwd_encrypted2"])

        folder_list = connector.get_folders()
        if folder_list is None:
            return error_response(ModErrCode.ErrCode02000032, 404)

        page = reqmodel.page
        per_page = reqmodel.per_page
        total = len(folder_list)
        if page is None and per_page is None:
            return success_response({
                "list": folder_list
            })
        slice_from = per_page * (page - 1)
        slice_to = per_page * page
        folder_list = folder_list[slice_from:slice_to]
        return success_response({
            "list": folder_list,
            "total": total,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
        })
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000033, 500)

@collector.route('/aws_regions', methods=['GET'])
def list_supported_regions():
    from connector.aws_connector import AWSConnector

    try:
        result = {
            "Errcode": 0,
            "Data": AWSConnector.get_supported_regions(),
        }
        return success_response(result)
    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000036, 500)


@collector.route('/resume', methods=['POST'])
def resume_a_scan():
    from domain_model.scan_policy import get_scan_policy, get_scan_policies
    from domain_model.task_queue import ResumeScanEvent
    from flask_module.collector_reqmodel import OnlyIDReqModel
    from service.task_queue_service import TaskQueueService
    from system import dlp_license
    from util.enum_ import TaskStatus
    try:
        conditions = request.json
        reqmodel = OnlyIDReqModel(**conditions)
    except ValidationError as e:
        return error_response(ModErrCode.ErrCode02000037, 400, custom_message=e)

    try:
        scan_policy = get_scan_policy(id=reqmodel.id)
        if scan_policy is None:
            return error_response(ModErrCode.ErrCode02000038, 404)

        if not dlp_license.is_licensed():
            bucket = []
            policies = get_scan_policies()
            for policy in policies:
                if policy.status != TaskStatus.SUSPENDING:
                    bucket.append(str(policy.id))

            if len(bucket) >= 1:
                if reqmodel.id not in bucket:
                    return error_response(ModErrCode.ErrCode02000039, 500)

        tqs = TaskQueueService()
        _, event_result = tqs.add_priority_event(ResumeScanEvent(scan_policy_id=str(scan_policy.id), wait_for_result=1))
        if not event_result or event_result["val"] == 0:
            message = event_result["message"] if event_result else None
            return error_response(ModErrCode.ErrCode02000040, 500, custom_message=message)

        result = {
            "Errcode": 0,
            "Data": "Scan policy resumed",
        }

        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Resume scan policy {scan_policy.name}",
            desc="Resume scan policy",
            type=LogType.SCANS.value,
            action=LogAction.START.value,
        )

        return success_response(result)
    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000041, 500)


@collector.route('/suspend', methods=['POST'])
def suspend_a_scan():
    from domain_model.scan_policy import get_scan_policy
    from domain_model.task_queue import SuspendingEvent
    from flask_module.collector_reqmodel import OnlyIDReqModel
    from service.task_queue_service import TaskQueueService
    from util.enum_ import TaskStatus

    try:
        conditions = request.json
        reqmodel = OnlyIDReqModel(**conditions)
    except ValidationError as e:
        return error_response(ModErrCode.ErrCode02000042, 400, custom_message=e)

    try:
        scan_policy = get_scan_policy(id=reqmodel.id)
        if scan_policy is None:
            return error_response(ModErrCode.ErrCode02000043, 404)

        if scan_policy.status == TaskStatus.SUSPENDING:
            result = {
                "Errcode": 0,
                "Data": "Scan policy already suspended",
            }
            return success_response(result)

        tqs = TaskQueueService()
        tqs.add_priority_event(SuspendingEvent(scan_policy_id=str(scan_policy.id), wait_for_result=1, fail_reason="Canceled By User"))
        result = {
            "Errcode": 0,
            "Data": "Scan policy suspended",
        }

        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Suspend scan policy {scan_policy.name}",
            desc="Suspend scan policy",
            type=LogType.SCANS.value,
            action=LogAction.STOP.value,
        )
        return success_response(result)
    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000044, 500)


@collector.route('/full_scan', methods=['POST'])
def trigger_a_full_scan():
    from domain_model.scan_policy import get_scan_policy, get_scan_policies
    from domain_model.task_queue import TriggerFullScanEvent
    from flask_module.collector_reqmodel import OnlyIDReqModel
    from service.task_queue_service import TaskQueueService
    from util.enum_ import TaskStatus
    from system import dlp_license

    try:
        conditions = request.json
        reqmodel = OnlyIDReqModel(**conditions)
    except ValidationError as e:
        return error_response(ModErrCode.ErrCode02000045, 400, custom_message=e)

    try:
        scan_policy = get_scan_policy(id=reqmodel.id)
        if scan_policy is None:
            return error_response(ModErrCode.ErrCode02000046, 404)

        if not dlp_license.is_licensed():
            bucket = []
            policies = get_scan_policies()
            for policy in policies:
                if policy.status != TaskStatus.SUSPENDING:
                    bucket.append(str(policy.id))

            if len(bucket) >= 1:
                if reqmodel.id not in bucket:
                    return error_response(ModErrCode.ErrCode02000047, 500)

        tqs = TaskQueueService()
        tqs.add_priority_event(TriggerFullScanEvent(scan_policy_id=str(scan_policy.id), wait_for_result=1))
        result = {
            "Errcode": 0,
            "Data": "The full scan is triggered",
        }

        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Start full scan with Scan Policy {scan_policy.name}",
            desc="Start full scan",
            type=LogType.SCANS.value,
            action=LogAction.START.value,
        )

        return success_response(result)
    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000048, 500)

@collector.route('/mimetypes', methods=['GET'])
def handle_get_mimetypes():
    try:
        from util.mime_type_mapping import get_mime_type_info
        return success_response(get_mime_type_info())
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000049, 500)

FILETYPE_FILE_PATH='/dlpcode/file_type_with_description.json'
@collector.route('/filetypes', methods=['GET'])
def handle_get_filetypes():
    try:
        with open(FILETYPE_FILE_PATH, 'r') as f:
            data = json.load(f)

        return success_response(data)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000050, 500)


@collector.route('/status', methods=['POST'])
def get_scan_status():
    from domain_model.scan_policy import get_scan_policy
    from flask_module.collector_reqmodel import OnlyIDReqModel
    from util.enum_ import TaskStatus
    from service.task_management_service import TaskManagementService

    try:
        conditions = request.json
        reqmodel = OnlyIDReqModel(**conditions)
    except ValidationError as e:
        return error_response(ModErrCode.ErrCode02000051, 400, custom_message=e)

    try:
        scan_policy = get_scan_policy(id=reqmodel.id)
        if scan_policy is None:
            return error_response(ModErrCode.ErrCode02000052, 404)

        status = TaskStatus(scan_policy.status)
        data = {
            "status": status.name,
            "progress": 0,
        }

        if status == TaskStatus.SCANNING:
            tms = TaskManagementService(str(scan_policy.id))
            session_key = tms.session_tracker.get_session_key()
            total = tms.analyze_worker_tracker.get_total_count(session_key)
            progress = tms.analyze_worker_tracker.get_current_progress(session_key)
            ignored = tms.analyze_worker_tracker.get_ignored_count(session_key)
            if -1 in [total, progress, ignored]:
                data["progress"] = 0
            else:
                data["progress"] = int((progress + ignored) / total * 100)

        result = {
            "Errcode": 0,
            "Data": data,
        }
        return success_response(result)
    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000053, 500)


@collector.route('/sensitive_data', methods=['GET'])
def handle_scan_sensitive_data():
    from flask_module.collector_reqmodel import ScanSensitiveDataQueryReqModel
    from service import scan_policy_service
    from system.dlp_license import is_licensed
    from storage.service.identity import get_storage_identity_summary

    try:
        conditions = request.args.to_dict(flat=False)
        try:
            reqmodel = ScanSensitiveDataQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02000054, 400, custom_message=e.errors())
        logger.info(f"reqmodel: {reqmodel}")

        response_data = {}
        if not is_licensed():
            max_sensitive_data = configs.get("no_license_limitation",{}).get("max_sensitive_data", 1000)
            _, real_count = scan_policy_service.get_sensitive_data_by_conditions(reqmodel.dict(exclude_none=True), -1, logger)
            response_data["real_count"] = real_count
        else:
            max_sensitive_data = -1
        results, total_count = scan_policy_service.get_sensitive_data_by_conditions(reqmodel.dict(exclude_none=True), max_sensitive_data, logger)

        identities = set()
        storages = set()
        for r in results:
            storages.add(r['storage_id'])
            identities.update(r.get('collaborators', []))
            identities.update(r.get('owner', []))
        identity_list = get_storage_identity_summary(identities, storages)

        response_data.update({
            "list": results,
            "identity_list": identity_list,
            "total": total_count,
            "page": 1 if reqmodel.page is None else reqmodel.page,
            "per_page": 10 if reqmodel.per_page is None else reqmodel.per_page,
        })
        return success_response(response_data)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000055, 500)


@collector.route('/all', methods=['GET'])
def get_all_scan_policy_list():
    from domain_model.scan_policy import get_scan_policies
    from service.scan_policy_service import gather_scan_info

    try:
        result = []
        for scan_policy in get_scan_policies():
            info = gather_scan_info(str(scan_policy.id))
            result.append({
                "id": str(scan_policy.id),
                "name": scan_policy.name,
                "target": info["target"],
                "storage_type": scan_policy.storage_type,
                "description": scan_policy.description,
                "quarantine": bool(scan_policy.protection_profiles.get("quarantine_profiles", [])),
                "copy": bool(scan_policy.protection_profiles.get("copy_profiles", []))
            })
        return success_response(result)
    except Exception as e:
        logger.error(e)
        return error_response(ModErrCode.ErrCode02000056, 500)


@collector.route('/sensitive_data_details', methods=['GET'])
def handle_scan_sensitive_data_details():
    from flask_module.collector_reqmodel import ScanSensitiveDataDetailsQueryReqModel
    from service import scan_policy_service

    try:
        conditions = request.args.to_dict()
        try:
            reqmodel = ScanSensitiveDataDetailsQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02000057, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        results = scan_policy_service.get_sensitive_data_details(reqmodel.dict(exclude_none=True), logger)
        return success_response(results)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000058, 500)


@collector.route('/sensitive_data_details', methods=['PUT'])
def handle_scan_sensitive_data_details_update():
    from flask_module.collector_reqmodel import ScanSensitiveDataDetailsUpdateReqModel
    from service import scan_policy_service

    try:
        data = request.get_json()
        try:
            reqmodel = ScanSensitiveDataDetailsUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02000059, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        results, file_name = scan_policy_service.update_sensitive_data_details(reqmodel.dict(exclude_none=True))
        if results:
            user = session.get_user(request)
            record_event_log(user=user, level=LogLevel.INFO.value, message=f"Edit labels for file {file_name}",
            desc='Edit file labels', type=LogType.LABELS.value, action=LogAction.EDIT.value)
            return success_response({"message": "Successfully updated file labels"})
        else:
            error_response(ModErrCode.ErrCode02000060, 500)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000061, 500)


@collector.route('/sensitive_data_summary', methods=['GET'])
def handle_scan_sensitive_data_summary():
    from flask_module.collector_reqmodel import ScanSensitiveDataSummaryQueryReqModel
    from service import scan_policy_service

    try:
        conditions = request.args.to_dict()
        try:
            reqmodel = ScanSensitiveDataSummaryQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02000062, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        results = scan_policy_service.get_sensitive_data_summary(reqmodel.dict(exclude_none=True), logger)
        return success_response(results)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000063, 500)


@collector.route('/sensitive_folder_details', methods=['GET'])
def handle_sensitive_folder_details_get():
    from flask_module.collector_reqmodel import ScanSensitiveFolderDetailsQueryReqModel
    from service import scan_policy_service

    try:
        conditions = request.args.to_dict()
        try:
            reqmodel = ScanSensitiveFolderDetailsQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02000064, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        results = scan_policy_service.get_sensitive_folder_details(reqmodel.dict(exclude_none=True))
        return success_response(results)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000065, 500)


@collector.route('/sensitive_folder_details', methods=['PUT'])
def handle_sensitive_folder_details_update():
    from flask_module.collector_reqmodel import ScanSensitiveFolderDetailsUpdateReqModel
    from service import scan_policy_service

    try:
        data = request.get_json()
        try:
            reqmodel = ScanSensitiveFolderDetailsUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02000066, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        results = scan_policy_service.update_sensitive_folder_details(reqmodel.dict(exclude_none=True))
        if results:
            user = session.get_user(request)
            record_event_log(user=user, level=LogLevel.INFO.value, message=f"Edit labels for folder {reqmodel.dict(exclude_none=True).get('folder_path', '')}",
            desc='Edit folder labels', type=LogType.LABELS.value, action=LogAction.EDIT.value)
            return success_response({"message": "Successfully updated folder labels"})
        else:
            error_response(ModErrCode.ErrCode02000067, 500)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000068, 500)


@collector.route('/h2_discover_policy_checkref', methods=['GET'])
def handle_h2_discover_policy_checkref():
    from flask_module.collector_reqmodel import H2DiscoverPolicyCheckRefReqModel
    from service import scan_policy_service

    try:
        conditions = request.args.to_dict()
        try:
            reqmodel = H2DiscoverPolicyCheckRefReqModel(**conditions)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02000069, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        result = scan_policy_service.get_h2_discover_policy_check_ref(reqmodel.id)
        return success_response(result)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000070, 500)


@collector.route('/h2_scan_ids_checkref', methods=['GET'])
def handle_h2_scan_ids_checkref():
    from flask_module.collector_reqmodel import H2ScanIdsCheckRefReqModel
    from service import scan_policy_service

    try:
        conditions = request.args.to_dict()
        try:
            reqmodel = H2ScanIdsCheckRefReqModel(**conditions)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02000071, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        result = scan_policy_service.get_h2_scan_ids_check_ref(reqmodel.id)
        return success_response(result)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02000072, 500)
