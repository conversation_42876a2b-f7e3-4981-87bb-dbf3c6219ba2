from util.common_log import get_logger
from pydantic import BaseModel, validator
from pydantic import Field, constr, conint, confloat, SecretStr

logger = get_logger("api")

class FabricCfg(BaseModel):
    status: str = Field(..., description="Status")
    upstream_ip: str = Field(..., alias="upstreamIp", description="Upstream IP")
    mgmt_ip: str = Field(..., alias="mgmtIp", description="Management IP")
    mgmt_port: int = Field(..., alias="mgmtPort", description="Management port")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
