import uuid
from typing import List, Optional

from pydantic import BaseModel, Field, validator, constr, conint, root_validator

from util.common_log import get_logger

logger = get_logger("api")


class ApiKeyAttributesModel(BaseModel):
    """
    Model for API key attributes stored in JSONB field.
    This can be extended based on specific requirements.
    """
    description: Optional[str] = Field(None, description="Optional description for the API key")

    class Config:
        extra = "forbid"


class ApiKeyCreateReqModel(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="API key name (unique)")
    status: bool = Field(False, description="API key status (enabled/disabled)")
    type: int = Field(1, description="Type of API key")
    attributes: Optional[ApiKeyAttributesModel] = Field(None, description="Additional attributes for the API key")

    class Config:
        extra = "forbid"

    @validator('name')
    def name_validator(cls, v):
        if not v.strip():
            raise ValueError("Name cannot be empty or just whitespace")
        return v


class ApiKeyQueryReqModel(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="API key name (fuzzy match)")
    status: Optional[bool] = Field(None, description="API key status")
    type: Optional[int] = Field(None, description="API key type")
    sort_field: str = Field('updated_at', description="Sort field")
    sort_method: str = Field('desc', description="Sort order")
    page: conint(ge=1) = Field(1, description="Page number")
    per_page: conint(ge=1, le=100) = Field(10, description="Items per page")
    key: Optional[str] = Field(None, description="API key name (fuzzy match)")

    class Config:
        extra = "forbid"

    @validator('sort_field')
    def validate_sort_field(cls, v):
        allowed_fields = ['name', 'status', 'created_at', 'updated_at']
        if v not in allowed_fields:
            raise ValueError(f"Sort field must be one of {allowed_fields}")
        return v

    @validator('sort_method')
    def validate_sort_method(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError("Sort method must be 'asc' or 'desc'")
        return v


class ApiKeyUpdateReqModel(BaseModel):
    id: uuid.UUID = Field(..., description="UUID of API key to update")
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="API key name (unique)")
    status: Optional[bool] = Field(None, description="API key status (enabled/disabled)")
    attributes: Optional[ApiKeyAttributesModel] = Field(None, description="Additional attributes for the API key")

    class Config:
        extra = "forbid"

    @root_validator(pre=True)
    def check_at_least_one_field(cls, values):
        update_fields = ['name', 'status', 'attributes']
        if not any(values.get(field) is not None for field in update_fields):
            raise ValueError("At least one field must be provided for update")
        return values

    @validator('name')
    def name_validator(cls, v):
        if v is not None and not v.strip():
            raise ValueError("Name cannot be empty or just whitespace")
        return v


class ApiKeyDeleteReqModel(BaseModel):
    id: List[constr(min_length=36, max_length=36)] = Field(..., description="List of API key UUIDs to delete")

    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError:
            raise ValueError(f"Invalid UUID: {v}")
        return v
