from flask import Blueprint, jsonify, request
from util.common_log import get_logger
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from pydantic import ValidationError
from service.predefined_datatype_service import GLOBAL_CONFIG
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from flask_module import session
from system.system_upload import save_upload_file
import os
from domain_model.edm_template import (
    create_edm_template, 
    update_edm_template,
    get_edm_templates,
    get_edm_template,
    delete_edm_templates
)
from domain_model.edm_rule import get_edm_rules
from service.edm_service import get_reference_cnt
from flask_module.source_file_info import SOURCE_FILE_SERVICE
from psycopg2.errors import UniqueViolation
from util.enum_ import EDMTemplateStatus

logger = get_logger("api")
edm_template = Blueprint("edm_template", __name__)

@edm_template.route('/', methods=['POST'])
def handle_edm_template_create():
    try:
        from flask_module.edm_template_reqmodel import CreateEDMTemplateReqModel
        data = request.get_json()
        req_model = CreateEDMTemplateReqModel(**data)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)

    edm_dict = req_model.dict(exclude_none=True)

    try:
        edm_template = {
            "name": req_model.name,
            "description": req_model.description,
            "source_file_id": req_model.source_file_id,
            "data_field": edm_dict["data_field"],
            "attributes_ext": {
                "status": EDMTemplateStatus.IN_PROGRESS.value,
                "err_msg": None
            }
        }
        template =  create_edm_template(payload=edm_template)
        if template:
            SOURCE_FILE_SERVICE.generate_hash_source_file(
                new_source_file_id=str(req_model.source_file_id), 
                columns =  edm_dict["data_field"],
                template_id=str(template.id)
            )
            record_event_log(
                user=session.get_user(request),
                level=LogLevel.INFO.value,
                message=f"Create EDM index {template.name}",
                desc="Create new EDM index",
                action=LogAction.CREATE.value,
                type=LogType.EDM.value
            )
            return success_response({"ErrorCode": 0, "Data":  template.to_dict()})
        else:
            return error_response(Ecodes.INTERNAL_ERROR, 400, "create EDM template failed")
        
    except UniqueViolation:
        return error_response(Ecodes.DUPLICATE_RESOURCE, 409, "A duplicate EDM Index Name already exists.")
    except Exception as e:
        logger.error("error here")
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)

@edm_template.route('/', methods=['PUT'])
def handle_edm_template_update():
    try:
        from flask_module.edm_template_reqmodel import UpdateEDMTemplateReqModel
        data = request.get_json()
        req_model = UpdateEDMTemplateReqModel(**data)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    edm_dict = req_model.dict(exclude_none=True)
    try:
        old_template = get_edm_template(id = req_model.id)
        if not old_template:
            return error_response(Ecodes.NOT_FOUND, 404, f"EDM template {req_model.id} does not exist")
        rules, _ = get_edm_rules(conditions={"edm_template_id": str(old_template.Get_id())})
        _, _, scan_cnt = get_reference_cnt(edm_ids=[str(rule.id) for rule in rules])
        if scan_cnt > 0:
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, f"Editing is not allowed when the EDM dataset is being referenced by scan policy.")
        if old_template.attributes_ext["status"] == EDMTemplateStatus.IN_PROGRESS.value:
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, f"The EDM hash task is still in progress. The current EDM dataset is unavailable.")
        
        edm_template = {
            "name": req_model.name,
            "description": req_model.description,
            "source_file_id": req_model.source_file_id,
            "data_field": edm_dict["data_field"]
        }
        if req_model.source_file_id and str(old_template.source_file_id) != req_model.source_file_id:
            edm_template["attributes_ext"] = {
                "status": EDMTemplateStatus.IN_PROGRESS.value,
                "err_msg": None
            }
            SOURCE_FILE_SERVICE.generate_hash_source_file(
                new_source_file_id=str(req_model.source_file_id),
                old_source_file_id=str(old_template.source_file_id), 
                columns = edm_dict["data_field"],
                template_id=str(old_template.id)
            )
        template =  update_edm_template(template_id=req_model.id, updates=edm_template)
        if template:
            record_event_log(
                user=session.get_user(request),
                level=LogLevel.INFO.value,
                message=f"Update EDM index {template.name}",
                desc="Update EDM index",
                action=LogAction.UPGRADE.value,
                type=LogType.EDM.value
            )
            return success_response({"ErrorCode": 0, "Data": "EDM template updated"})
        else:
            return error_response(Ecodes.INTERNAL_ERROR, 400, "update EDM template failed")

    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)

@edm_template.route("/", methods=['DELETE'])
def handle_edm_template_delete():
    try:
        from flask_module.edm_template_reqmodel import DeleteEDMTemplateReqModel
        args = request.args.to_dict()
        req_model = DeleteEDMTemplateReqModel(**args)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        template = get_edm_template(id=req_model.id)
        if not template:
            return error_response(Ecodes.NOT_FOUND, 404, "EDM template not found")
        
        rules, _ = get_edm_rules(conditions={"edm_template_id": req_model.id})
        discover_policy_cnt, dlp_policy_cnt, scan_cnt = get_reference_cnt(edm_ids=[str(rule.id) for rule in rules])
        if discover_policy_cnt > 0 or dlp_policy_cnt > 0 or scan_cnt > 0:
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, f"Deleting is not allowed when the EDM dataset is being referenced.")
        
        if template.attributes_ext["status"] == EDMTemplateStatus.IN_PROGRESS.value:
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, f"The EDM hash task is still in progress. The current EDM dataset is unavailable.")

        res = delete_edm_templates(id=req_model.id)
        if not res:
            raise Exception("Delete IDM template failed")
        
        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Delete EDM index",
            desc="Delete EDM index",
            action=LogAction.DELETE.value,
            type=LogType.EDM.value
        )
        return success_response({"ErrorCode": 0, "Data": "EDM template deleted"})
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)

@edm_template.route("/", methods=['GET'])
def handle_edm_template_query():
    from flask_module.edm_template_reqmodel import QueryEDMTemplateReqModel
    from domain_model.source_file import get_source_files
    try:
        args = request.args.to_dict()
        reqmodel = QueryEDMTemplateReqModel(**args)
    except ValidationError as e:
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    logger.info(f"reqmodel: {reqmodel}")

    try:
        templates, total = get_edm_templates(
            conditions=reqmodel.dict(exclude_none=True),
            sort_field=reqmodel.sort_field,
            sort_method=reqmodel.sort_method,
            page=reqmodel.page,
            per_page=reqmodel.per_page
        )
        ret_templates = []
        for template in templates:
            rules, rule_cnt = get_edm_rules(conditions={"edm_template_id": str(template.Get_id())})
            discover_policy_cnt, dlp_policy_cnt, scan_cnt = get_reference_cnt(edm_ids=[str(rule.id) for rule in rules])
            ret_template = template.to_dict()
            ret_template["edm_rule_cnt"] = rule_cnt
            source_files = get_source_files(id=template.source_file_id)
            if not source_files:
                ret_template["source_file_name"] = "Unknown"
            else:
                source_file = source_files[0]
                ret_template["source_file_name"] = source_file.name
            ret_template["discover_policy_cnt"] = discover_policy_cnt
            ret_template["dlp_policy_cnt"] = dlp_policy_cnt
            ret_template["scan_cnt"] = scan_cnt
            ret_templates.append(ret_template)
        return success_response({
            "list":ret_templates,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
            "total":total
        })
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)

    
@edm_template.route("/test", methods=['GET'])
def handle_edm_template_test():
    from service.edm_service import is_value_in_templates
    from flask_module.edm_template_reqmodel import TestEDMTemplateReqModel
    try:
        args = request.args.to_dict()
        req_model = TestEDMTemplateReqModel(**args)
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        exist, msg = is_value_in_templates(template_id=req_model.id, value=req_model.value, data_field=req_model.field_name)
        ret = {
            "Exist": exist,
            "Message": msg
        }
        return success_response({"ErrorCode": 0, "Data": ret})
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)
