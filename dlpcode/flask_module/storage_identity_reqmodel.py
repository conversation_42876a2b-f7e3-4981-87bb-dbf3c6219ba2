from pydantic import BaseModel, Field
from typing import Literal, Optional
from uuid import UUID
from util.enum_ import UserType, IdentityType

class GetStorageIdentityReqModel(BaseModel):
    sid: Optional[UUID] = Field(None, description="Storage UUID")
    name: Optional[str] = Field(None, description="Identity display name")
    email: Optional[str] = Field(None, description="Identity email")
    type: Optional[UserType] = Field(None, description="internal: 1, external: 2")
    identity_type: Optional[IdentityType] = Field(None, description="Identity type. user: 1, group: 2, site group: 3")
    sensitive_file: Optional[bool] = Field(None, description="Identities with sensitive files")
    sensitive_file_access: Optional[bool] = Field(None, description="Identities with sensitive file access")

    sort_field: str = Field('name', max_length=32, description="Sort field name")
    sort_method: Literal['desc', 'asc'] = Field('asc', description="Sort method")
    page: int = Field(1, ge=1, description="Page number")
    per_page: int = Field(10, ge=0, le=1024, description="Page size")

    class Config:
        extra = 'forbid'

class GetAllStorageIdentityReqModel(BaseModel):
    sid: Optional[UUID] = Field(None, description="Storage UUID")

    class Config:
        extra = 'forbid'

class GetStorageIdentityDetailReqModel(BaseModel):
    sid: UUID = Field(..., description="storage UUID")
    identifier: str = Field(..., description="identity identifier")

    class Config:
        extra = 'forbid'
