from flask import Blueprint, jsonify, request
from util.common_log import get_logger
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from pydantic import ValidationError
from service.predefined_datatype_service import GLOBAL_CONFIG
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from flask_module import session
from system.system_upload import save_upload_file, get_upload_file_info
import os
from domain_model.source_file import (
    create_source_file, 
    get_source_files,
    delete_source_files,
    update_source_file
)
from service.source_file_service import SourceFileService
from service.edm_service import get_edm_template, get_reference_cnt
from domain_model.idm_template import get_idm_template
from domain_model.edm_template import get_edm_template
from domain_model.edm_rule import get_edm_rules

logger = get_logger("api")
source_file = Blueprint("source_file", __name__)
SOURCE_FILE_SERVICE = SourceFileService(GLOBAL_CONFIG.get("source_file", {}))

@source_file.route('/', methods=['GET'])
def handle_source_file_get():
    try:
        from flask_module.source_file_reqmodel import GetSourceFileReqModel
        args = request.args.to_dict()
        req_model = GetSourceFileReqModel(**args)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        source_files = get_source_files(id=req_model.id)
        if not source_file:
            return success_response({"ErrorCode": 0, "Data": {}})
        return success_response({"ErrorCode": 0, "Data": [source_file.to_dict() for source_file in source_files]})
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)

@source_file.route('/', methods=['POST'])
def handle_source_file_create():
    try:
        from flask_module.source_file_reqmodel import CreateSourceFileReqModel
        args = request.args.to_dict()
        req_model = CreateSourceFileReqModel(**args)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        file_size, start_bytes = get_upload_file_info(request=request)
        validate, err_msg = SOURCE_FILE_SERVICE.check_file_size(file_size, req_model.template_type)
        if not validate:
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, err_msg)
        
        if start_bytes == 0:
            source_file = {
                "name": req_model.name,
                "template_type": req_model.template_type,
                "file_type": req_model.file_type
            }
            source_file_id = create_source_file(payload=source_file)
            if not source_file_id:
                raise ValueError("create source file failed")
        else:
            if not req_model.source_file_id:
                return error_response(Ecodes.VALIDATION_ERROR, 400, f"Required source_file_id")
            source_file_id = req_model.source_file_id
        
        upload = request.files.get('file')
        dir_path = SOURCE_FILE_SERVICE.get_dir(template_type=req_model.template_type)
        file_path = os.path.join(dir_path, f"{source_file_id}_{os.path.basename(upload.filename)}")
        errno, errmsg = save_upload_file(request=request, upload=upload, pathfile=file_path, upload_path=dir_path)
        if errno != 0:
            raise RuntimeError(errmsg)
        if errmsg.startswith("Chunk saved"):
            return success_response({"ErrorCode": 0, "Data": "Chunk saved", "Cookies":{"source_file_id": source_file_id}})
        
        updates = {"original_file_path": file_path}
        if req_model.template_type == "edm":
            try:
                file_info = SourceFileService.get_file_info(file_path=file_path, file_type=req_model.file_type)
            except Exception as e:
                SOURCE_FILE_SERVICE.clear_source_file(file_path=file_path, source_file_id=source_file_id)
                return error_response(Ecodes.VALIDATION_ERROR, 400, f"Get file info failed: {e}")
            data_info, row, column = file_info

            if req_model.template_id:
                rules, _ = get_edm_rules(conditions={"edm_template_id": req_model.template_id})
                _, _, scan_cnt = get_reference_cnt(edm_ids=[str(rule.id) for rule in rules])
                if scan_cnt > 0:
                    SOURCE_FILE_SERVICE.clear_source_file(file_path=file_path, source_file_id=source_file_id)
                    return error_response(Ecodes.FORBIDDEN_ERROR, 403, "Editing is not allowed when the EDM dataset is being referenced by scan policy.")
                edm_template = get_edm_template(id=req_model.template_id)
                if not SourceFileService.check_compatibility(file_info=data_info, data_fields=edm_template.data_field.keys()):
                    SOURCE_FILE_SERVICE.clear_source_file(file_path=file_path, source_file_id=source_file_id)
                    return error_response(Ecodes.FORBIDDEN_ERROR, 403, f"The source file header must include {edm_template.data_field.keys()}")
            validate, err_msg = SOURCE_FILE_SERVICE.check_row_and_column(file_path=file_path, row=row, column=column)
            if not validate:
                SOURCE_FILE_SERVICE.clear_source_file(file_path=file_path, source_file_id=source_file_id)
                return error_response(Ecodes.FORBIDDEN_ERROR, 403, err_msg)
            updates["data_info"] = data_info
        validate, err_msg = SOURCE_FILE_SERVICE.check_file_num_limit(file_path=file_path, template_type=req_model.template_type)
        if not validate:
            SOURCE_FILE_SERVICE.clear_source_file(file_path=file_path, source_file_id=source_file_id)
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, err_msg)

        #create source file
        update_source_file(id=source_file_id, updates=updates)
        return success_response({"ErrorCode": 0, "Data": source_file_id})
    except Exception as e:
        SOURCE_FILE_SERVICE.clear_source_file(file_path=file_path, source_file_id=source_file_id)
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)
    
@source_file.route("/", methods=['DELETE'])
def handle_source_file_delete():
    try:
        from flask_module.source_file_reqmodel import DeleteSourceFileReqModel
        args = request.args.to_dict()
        req_model = DeleteSourceFileReqModel(**args)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        if get_edm_template(source_file_id=req_model.id) or get_idm_template(source_file_id=req_model.id):
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, f"Deletion is not allowed when the source file is being referenced.")
        res = delete_source_files(id=req_model.id)
        if not res:
            raise Exception("Delete source file failed")
        return success_response({"ErrorCode": 0, "Data": "Source file deleted"})
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)


