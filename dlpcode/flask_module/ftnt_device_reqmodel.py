import uuid
from enum import Enum
from typing import List, Optional, Dict, Any, Literal, Union
from pydantic import BaseModel, Field, validator, constr, conint, root_validator, StrictStr
import ipaddress

class PrecisionLevel(str, Enum):
    LOW = "Low"
    MEDIUM = "Medium"
    HIGH = "High"

class IPListValidator:
    @classmethod
    def validate_iplist(cls, iplist: List[str]):
        if not isinstance(iplist, list):
            raise ValueError("iplist must be an array of IP addresses")
        if not iplist:
            raise ValueError("iplist must not be empty")

        for ip in iplist:
            if not isinstance(ip, str):
                raise ValueError(f"Invalid IP address format: {ip}")
            try:
                parse_ip_list(ip)
            except ValueError:
                raise ValueError(f"Invalid IP address: {ip}")
        return iplist


class FOSDeviceInfo(BaseModel, IPListValidator):
    iplist: List[str] = Field(..., description="List of IP addresses")
    enable_client_validation: bool = Field(..., description="Whether to enable client validation")
    cert_id: Optional[str] = Field(None, description="Certificate ID when client validation is enabled")
    file_type_categories: List[str] = Field(..., description="List of file type categories")
    min_file_size_in_bytes: int = Field(..., description="Minimum file size in bytes")
    max_file_size_in_bytes: int = Field(..., description="Maximum file size in bytes")
    scan_ids: List[str] = Field(..., description="List of scan IDs")

    class Config:
        extra = "forbid"

    @validator('iplist')
    def validate_ips(cls, v):
        return cls.validate_iplist(v)

    @validator('cert_id')
    def validate_cert_id_format(cls, v):
        if v is not None:
            try:
                uuid.UUID(v)
            except ValueError:
                raise ValueError(f"Invalid cert_id format: {v}")
        return v

    @root_validator(skip_on_failure=True)
    def validate_cert_id_relationship(cls, values):
        cert_id = values.get('cert_id')
        enable_client_validation = values.get('enable_client_validation', False)

        if cert_id is not None and not enable_client_validation:
            raise ValueError("When cert_id is provided, enable_client_validation must be true")
        if enable_client_validation and cert_id is None:
            raise ValueError("When enable_client_validation is true, cert_id is required")
        return values

    @validator('min_file_size_in_bytes', 'max_file_size_in_bytes')
    def validate_file_sizes(cls, v):
        if not isinstance(v, int):
            raise ValueError(f"File size must be an integer")
        return v

    @root_validator(skip_on_failure=True)
    def validate_file_size_range(cls, values):
        min_size = values.get('min_file_size_in_bytes')
        max_size = values.get('max_file_size_in_bytes')
        if min_size is not None and max_size is not None:
            if min_size > max_size:
                raise ValueError("min_file_size_in_bytes cannot be greater than max_file_size_in_bytes")
        return values

    @validator('scan_ids')
    def validate_scan_ids(cls, v):
        if not isinstance(v, list):
            raise ValueError("scan_ids must be an array of strings")
        if not v:
            raise ValueError("scan_ids cannot be empty")
        for scan_id in v:
            if not isinstance(scan_id, str):
                raise ValueError("scan_ids must be an array of strings")
            try:
                uuid.UUID(scan_id)
            except ValueError:
                raise ValueError(f"Invalid scan_id format: {scan_id}")
        return v
    #validate file_category, must be a list of strings
    @validator('file_type_categories')
    def validate_file_type_categories(cls, v):
        if not isinstance(v, list):
            raise ValueError("file_type_categories must be an array of strings")
        if not v:
            raise ValueError("file_type_categories cannot be empty")
        for file_category in v:
            if not isinstance(file_category, str):
                raise ValueError("file_type_categories must be an array of strings")
        return v



class FCTDeviceInfo(BaseModel, IPListValidator):
    iplist: List[str] = Field(..., description="List of IP addresses")
    enable_client_validation: bool = Field(..., description="Whether to enable client validation")

    class Config:
        extra = "forbid"

    @validator('iplist')
    def validate_ips(cls, v):
        return cls.validate_iplist(v)


class EMSDeviceInfo(BaseModel, IPListValidator):
    iplist: List[str] = Field(..., description="List of IP addresses")
    enable_api_key: bool = Field(..., description="Whether to enable API key")
    file_type_categories: List[str] = Field(..., description="List of file type categories")
    min_file_size_in_bytes: int = Field(..., description="Minimum file size in bytes")
    max_file_size_in_bytes: int = Field(..., description="Maximum file size in bytes")
    scan_ids: List[str] = Field(..., description="List of scan IDs")
    enable_upload_file_scan: bool = Field(..., description="Whether to enable upload file scan")
    enable_document_classification: Optional[bool] = Field(None, description="Whether to enable document classification")
    document_classification_precision_level: Optional[PrecisionLevel] = Field(None, description="Document classification precision level")
    discovery_policy_ids: Optional[List[str]] = Field(None, description="List of discovery policy IDs")
    #data_type_detection_precision_level: Optional[StrictStr] = Field(None, description="Data type detection precision level")
    data_type_detection_precision_level: Optional[PrecisionLevel] = Field(None, description="Data type detection precision level")  # 只允许 Low, Medium, High

    class Config:
        extra = "forbid"

    @validator('iplist')
    def validate_ips(cls, v):
        return cls.validate_iplist(v)

    @validator('file_type_categories')
    def validate_file_type_categories(cls, v):
        if not isinstance(v, list):
            raise ValueError("file_type_categories must be an array of strings")
        if not v:
            raise ValueError("file_type_categories cannot be empty")
        for file_category in v:
            if not isinstance(file_category, str):
                raise ValueError("file_type_categories must be an array of strings")
        return v

    @validator('min_file_size_in_bytes', 'max_file_size_in_bytes')
    def validate_file_sizes(cls, v):
        if not isinstance(v, int):
            raise ValueError(f"File size must be an integer")
        return v

    @root_validator(skip_on_failure=True)
    def validate_file_size_range(cls, values):
        min_size = values.get('min_file_size_in_bytes')
        max_size = values.get('max_file_size_in_bytes')
        if min_size is not None and max_size is not None:
            if min_size > max_size:
                raise ValueError("min_file_size_in_bytes cannot be greater than max_file_size_in_bytes")
        return values

    @validator('scan_ids')
    def validate_scan_ids(cls, v):
        if not isinstance(v, list):
            raise ValueError("scan_ids must be an array of strings")
        if not v:
            raise ValueError("scan_ids cannot be empty")
        for scan_id in v:
            if not isinstance(scan_id, str):
                raise ValueError("scan_ids must be an array of strings")
            try:
                uuid.UUID(scan_id)
            except ValueError:
                raise ValueError(f"Invalid scan_id format: {scan_id}")
        return v

    @root_validator(skip_on_failure=True)
    def validate_upload_scan_fields(cls, values):
        enable_upload = values.get('enable_upload_file_scan')
        if enable_upload is None:
            # When enable_upload is None, related fields should not be provided
            doc_classification = values.get('enable_document_classification')
            precision_level = values.get('document_classification_precision_level')
            discovery_ids = values.get('discovery_policy_ids')
            data_type_precision_level = values.get('data_type_detection_precision_level')

            if doc_classification is not None:
                raise ValueError("enable_document_classification should not be provided when enable_upload_file_scan is not set")
            if precision_level is not None:
                raise ValueError("document_classification_precision_level should not be provided when enable_upload_file_scan is not set")
            if discovery_ids is not None:
                raise ValueError("discovery_policy_ids should not be provided when enable_upload_file_scan is not set")
            if data_type_precision_level is not None:
                raise ValueError("data_type_detection_precision_level should not be provided when enable_upload_file_scan is not set")
            return values

        if enable_upload is True:
            # Check if required fields are provided when enable_upload_file_scan is being set to True
            doc_classification = values.get('enable_document_classification')
            precision_level = values.get('document_classification_precision_level')
            discovery_ids = values.get('discovery_policy_ids')
            data_type_precision_level = values.get('data_type_detection_precision_level')

            if doc_classification is None:
                raise ValueError("When enable_upload_file_scan is true, enable_document_classification is required")
            if precision_level is None:
                raise ValueError("When enable_upload_file_scan is true, document_classification_precision_level is required")
            if discovery_ids is None:
                raise ValueError("When enable_upload_file_scan is true, discovery_policy_ids is required")
            if data_type_precision_level is None:
                raise ValueError("When enable_upload_file_scan is true, data_type_detection_precision_level is required")

            # Validate discovery_policy_ids
            if discovery_ids is not None:
                if not isinstance(discovery_ids, list):
                    raise ValueError("discovery_policy_ids must be an array of strings")
                if not discovery_ids:
                    raise ValueError("discovery_policy_ids cannot be empty")
                for policy_id in discovery_ids:
                    if not isinstance(policy_id, str):
                        raise ValueError("discovery_policy_ids must be an array of strings")
                    try:
                        uuid.UUID(policy_id)
                    except ValueError:
                        raise ValueError(f"Invalid discovery_policy_id format: {policy_id}")
        return values



class FOSDeviceInfoUpdate(BaseModel, IPListValidator):
    iplist: Optional[List[str]] = Field(None, description="List of IP addresses")
    enable_client_validation: Optional[bool] = Field(None, description="Whether to enable client validation")
    cert_id: Optional[str] = Field(None, description="Certificate ID when client validation is enabled")
    file_type_categories: Optional[List[str]] = Field(None, description="List of file type categories")
    min_file_size_in_bytes: Optional[int] = Field(None, description="Minimum file size in bytes")
    max_file_size_in_bytes: Optional[int] = Field(None, description="Maximum file size in bytes")
    scan_ids: Optional[List[str]] = Field(None, description="List of scan IDs")

    class Config:
        extra = "forbid"

    @validator('iplist')
    def validate_ips(cls, v):
        if v is not None:
            return cls.validate_iplist(v)
        return v

    @validator('cert_id')
    def validate_cert_id_format(cls, v):
        if v is not None:
            try:
                uuid.UUID(v)
            except ValueError:
                raise ValueError(f"Invalid cert_id format: {v}")
        return v

    @root_validator(skip_on_failure=True)
    def validate_cert_id_relationship(cls, values):
        cert_id = values.get('cert_id')
        enable_client_validation = values.get('enable_client_validation', False)

        if cert_id is not None and not enable_client_validation:
            raise ValueError("When cert_id is provided, enable_client_validation must be true")
        if enable_client_validation and cert_id is None:
            raise ValueError("When enable_client_validation is true, cert_id is required")
        return values

    @root_validator(skip_on_failure=True)
    def validate_file_size_range(cls, values):
        min_size = values.get('min_file_size_in_bytes')
        max_size = values.get('max_file_size_in_bytes')
        if min_size is not None and max_size is not None:
            if min_size > max_size:
                raise ValueError("min_file_size_in_bytes cannot be greater than max_file_size_in_bytes")
        return values

    @validator('scan_ids')
    def validate_scan_ids(cls, v):
        if v is not None:
            if not isinstance(v, list):
                raise ValueError("scan_ids must be an array of strings")
            if not v:
                raise ValueError("scan_ids cannot be empty")
            for scan_id in v:
                if not isinstance(scan_id, str):
                    raise ValueError("scan_ids must be an array of strings")
                try:
                    uuid.UUID(scan_id)
                except ValueError:
                    raise ValueError(f"Invalid scan_id format: {scan_id}")
        return v


class FCTDeviceInfoUpdate(BaseModel, IPListValidator):
    iplist: Optional[List[str]] = Field(None, description="List of IP addresses")
    enable_client_validation: Optional[bool] = Field(None, description="Whether to enable client validation")

    class Config:
        extra = "forbid"

    @validator('iplist')
    def validate_ips(cls, v):
        if v is not None:
            return cls.validate_iplist(v)
        return v


class EMSDeviceInfoUpdate(BaseModel, IPListValidator):
    iplist: Optional[List[str]] = Field(None, description="List of IP addresses")
    enable_api_key: Optional[bool] = Field(None, description="Whether to enable API key")
    file_type_categories: Optional[List[str]] = Field(None, description="List of file type categories")
    min_file_size_in_bytes: Optional[int] = Field(None, description="Minimum file size in bytes")
    max_file_size_in_bytes: Optional[int] = Field(None, description="Maximum file size in bytes")
    scan_ids: Optional[List[str]] = Field(None, description="List of scan IDs")
    enable_upload_file_scan: Optional[bool] = Field(None, description="Whether to enable upload file scan")
    enable_document_classification: Optional[bool] = Field(None, description="Whether to enable document classification")
    document_classification_precision_level: Optional[PrecisionLevel] = Field(None, description="Document classification precision level")
    data_type_detection_precision_level: Optional[PrecisionLevel] = Field(None, description="Data type detection precision level")
    discovery_policy_ids: Optional[List[str]] = Field(None, description="List of discovery policy IDs")
    class Config:
        extra = "forbid"

    @validator('iplist')
    def validate_ips(cls, v):
        if v is not None:
            return cls.validate_iplist(v)
        return v

    @root_validator(skip_on_failure=True)
    def validate_file_size_range(cls, values):
        min_size = values.get('min_file_size_in_bytes')
        max_size = values.get('max_file_size_in_bytes')
        if min_size is not None and max_size is not None:
            if min_size > max_size:
                raise ValueError("min_file_size_in_bytes cannot be greater than max_file_size_in_bytes")
        return values

    @validator('scan_ids')
    def validate_scan_ids(cls, v):
        if v is not None:
            if not isinstance(v, list):
                raise ValueError("scan_ids must be an array of strings")
            if not v:
                raise ValueError("scan_ids cannot be empty")
            for scan_id in v:
                if not isinstance(scan_id, str):
                    raise ValueError("scan_ids must be an array of strings")
                try:
                    uuid.UUID(scan_id)
                except ValueError:
                    raise ValueError(f"Invalid scan_id format: {scan_id}")
        return v

    @root_validator(skip_on_failure=True)
    def validate_upload_scan_fields(cls, values):
        enable_upload = values.get('enable_upload_file_scan')
        if enable_upload is None:
            # When enable_upload is None, related fields should not be provided
            doc_classification = values.get('enable_document_classification')
            precision_level = values.get('document_classification_precision_level')
            discovery_ids = values.get('discovery_policy_ids')
            data_type_precision_level = values.get('data_type_detection_precision_level')

            if doc_classification is not None:
                raise ValueError("enable_document_classification should not be provided when enable_upload_file_scan is not set")
            if precision_level is not None:
                raise ValueError("document_classification_precision_level should not be provided when enable_upload_file_scan is not set")
            if discovery_ids is not None:
                raise ValueError("discovery_policy_ids should not be provided when enable_upload_file_scan is not set")
            if data_type_precision_level is not None:
                raise ValueError("data_type_detection_precision_level should not be provided when enable_upload_file_scan is not set")
            return values

        if enable_upload is True:
            # Check if required fields are provided when enable_upload_file_scan is being set to True
            doc_classification = values.get('enable_document_classification')
            precision_level = values.get('document_classification_precision_level')
            discovery_ids = values.get('discovery_policy_ids')
            data_type_precision_level = values.get('data_type_detection_precision_level')

            if doc_classification is None:
                raise ValueError("When enable_upload_file_scan is true, enable_document_classification is required")
            if precision_level is None:
                raise ValueError("When enable_upload_file_scan is true, document_classification_precision_level is required")
            if discovery_ids is None:
                raise ValueError("When enable_upload_file_scan is true, discovery_policy_ids is required")
            if data_type_precision_level is None:
                raise ValueError("When enable_upload_file_scan is true, data_type_detection_precision_level is required")

            # Validate discovery_policy_ids
            if discovery_ids is not None:
                if not isinstance(discovery_ids, list):
                    raise ValueError("discovery_policy_ids must be an array of strings")
                if not discovery_ids:
                    raise ValueError("discovery_policy_ids cannot be empty")
                for policy_id in discovery_ids:
                    if not isinstance(policy_id, str):
                        raise ValueError("discovery_policy_ids must be an array of strings")
                    try:
                        uuid.UUID(policy_id)
                    except ValueError:
                        raise ValueError(f"Invalid discovery_policy_id format: {policy_id}")
        return values


class FtntDeviceBaseModel(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="Device name")
    type: Literal['FOS', 'FCT', 'EMS'] = Field(..., description="Device type, must be one of: FOS, FCT, EMS")
    description: str = Field('', max_length=256, description="Device description")

    class Config:
        extra = "forbid"

    @validator('type')
    def validate_type(cls, v):
        allowed_types = ['FOS', 'FCT', 'EMS']
        if v not in allowed_types:
            raise ValueError(f"Type must be one of: {', '.join(allowed_types)}")
        return v


class FtntDeviceCreateReqModel(FtntDeviceBaseModel):
    info: Dict[str, Any] = Field(..., description="Device information")

    class Config:
        extra = "forbid"

    @root_validator(skip_on_failure=True)
    def validate_info_structure(cls, values):
        device_type = values.get('type')
        info = values.get('info', {})

        try:
            if device_type == 'FOS':
                # Validate and clean the info data using the model
                validated_info = FOSDeviceInfo(**info).dict(exclude_none=True)
                values['info'] = validated_info
            elif device_type == 'FCT':
                validated_info = FCTDeviceInfo(**info).dict(exclude_none=True)
                values['info'] = validated_info
            elif device_type == 'EMS':
                validated_info = EMSDeviceInfo(**info).dict(exclude_none=True)
                values['info'] = validated_info
        except ValueError as e:
            # raise ValueError(f"Invalid info structure for {device_type}: {str(e)}")
            raise e

        return values


class FtntDeviceUpdateReqModel(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Device name")
    type: Optional[Literal['FOS', 'FCT', 'EMS']] = Field(None, description="Device type, must be one of: FOS, FCT, EMS")
    description: Optional[str] = Field(None, max_length=256, description="Device description")
    info: Optional[Dict[str, Any]] = Field(None, description="Device information")

    class Config:
        extra = "forbid"

    @validator('type')
    def validate_type(cls, v):
        if v is not None:
            allowed_types = ['FOS', 'FCT', 'EMS']
            if v not in allowed_types:
                raise ValueError(f"Type must be one of: {', '.join(allowed_types)}")
        return v

    @root_validator(skip_on_failure=True)
    def validate_info_structure(cls, values):
        device_type = values.get('type')
        info = values.get('info')

        if info is None or device_type is None:
            return values

        try:
            if device_type == 'FOS':
                # Validate and clean the info data using the model
                validated_info = FOSDeviceInfoUpdate(**info).dict(exclude_none=True)
                values['info'] = validated_info
            elif device_type == 'FCT':
                validated_info = FCTDeviceInfoUpdate(**info).dict(exclude_none=True)
                values['info'] = validated_info
            elif device_type == 'EMS':
                validated_info = EMSDeviceInfoUpdate(**info).dict(exclude_none=True)
                values['info'] = validated_info
        except ValueError as e:
            # raise ValueError(f"Invalid info structure for {device_type}: {str(e)}")
            raise e
        return values


class FtntDeviceDeleteReqModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(..., description="Device ID in UUID format")

    class Config:
        extra = "forbid"

    @validator('id')
    def validate_uuid(cls, v):
        try:
            uuid.UUID(v)
        except ValueError:
            raise ValueError(f"Invalid UUID format: {v}")
        return v


class FtntDeviceQueryReqModel(BaseModel):
    id: Optional[constr(min_length=36, max_length=36)] = Field(None, description="Device ID in UUID format")
    name: Optional[str] = Field(None, description="Device name")
    type: Optional[Literal['FOS', 'FCT', 'EMS']] = Field(None, description="Device type")
    description: Optional[str] = Field(None, description="Device description")

    sort_field: str = Field('name', description="Field to sort by")
    sort_method: Literal['asc', 'desc'] = Field('asc', description="Sort direction")
    page: Optional[conint(ge=1)] = Field(1, description="Page number")
    per_page: conint(ge=1, le=100) = Field(10, description="Items per page")

    class Config:
        extra = "forbid"

    @validator('id')
    def validate_uuid(cls, v):
        if v is not None:
            try:
                uuid.UUID(v)
            except ValueError:
                raise ValueError(f"Invalid UUID format: {v}")
        return v

    @validator('sort_field')
    def validate_sort_field(cls, v):
        allowed_fields = ['name', 'type', 'created_at', 'updated_at']
        if v not in allowed_fields:
            raise ValueError(f"sort_field must be one of: {', '.join(allowed_fields)}")
        return v


def parse_ip_list(ip_list_str: str):
    ip_list = ip_list_str.split(",")
    ip_entries = []

    for ip in ip_list:
        ip = ip.strip()

        if "-" in ip:  # IP range
            ips = ip.split("-")
            if len(ips) != 2:
                raise ValueError(f"Invalid range format: {ip}")
            try:
                start_ip = ipaddress.ip_address(ips[0].strip())
                end_ip = ipaddress.ip_address(ips[1].strip())
                if int(start_ip) > int(end_ip):
                    raise ValueError(f"Invalid IP range (start IP is greater than end IP): {ip}")
            except ValueError:
                raise ValueError(f"Invalid IP range: {ip}")
            ip_entries.append((start_ip, end_ip))

        elif "/" in ip:  # CIDR block
            try:
                cidr = ipaddress.ip_network(ip, strict=False)
            except ValueError:
                raise ValueError(f"Invalid CIDR: {ip}")
            ip_entries.append(cidr)

        else:  # Single IP
            try:
                single_ip = ipaddress.ip_address(ip)
            except ValueError:
                raise ValueError(f"Invalid IP: {ip}")
            ip_entries.append(single_ip)

    return ip_entries
