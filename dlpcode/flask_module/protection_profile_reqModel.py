import uuid
from util.common_log import get_logger
from pydantic import BaseModel, validator
from pydantic import Field, conint, constr
from typing import List, Optional, Literal
from util.enum_ import StorageType, CredentialType

logger = get_logger("api")

class ProfileModel(BaseModel):
    storage_type: StorageType = Field(..., description="1 : AWS S3, 2 : SharePoint(On Line) 3 : SharePoint(ON-Prem) 4:SMB")
    path: str = Field(..., min_length=1, max_length=1024, example="https://dlp103.sharepoint.com/sites/Ltest/Shared%20Documents/", description="path of a folder")
    credential_type: CredentialType = Field(..., description="Credential type 1: use same credential with scan policy, 2: use custom credential")
    notification_file: Optional[str] = Field("default", min_length=1, max_length=1024, description="notification file")
    storage_id: Optional[str] = Field(None, min_length=36, max_length=36, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="uuid for the storage profile")

    @validator('storage_id')
    def validate_storage_id(cls, v, values):
        credential_type = values.get('credential_type')
        if credential_type == 2 and not v:
            raise ValueError('storage_id is required when use custom credential')
        if v:
            try:
                uuid.UUID(v)
            except ValueError as e:
                raise ValueError(f"Invalid uuid {v}")
        return v


class ProtectionProfileCreateReqModel(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, example="Profile1", description="profile name")
    description: str = Field("", min_length=0, max_length=512, example="This is a discover rule")

    profile_type: conint(ge=1, le=2) = Field(..., description="Protection Profile Type")
    profile: ProfileModel = Field(...)

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"


class ProtectionProfileUpdateReqModel(BaseModel):
    id: str = Field(..., min_length=36, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="valid UUID format string")
    name: str = Field(None, min_length=1, max_length=100, example="Profile1", description="profile name")
    description: str = Field("", min_length=0, max_length=512, example="This is a discover rule")

    profile_type: conint(ge=1, le=2) = Field(None, description="Protection Profile Type")
    profile: ProfileModel = Field(...)

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class ProtectionProfileDeleteReqModel(BaseModel):
    id: List[constr(min_length=36, max_length=36)]  = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class ProtectionProfileGetReqModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    page: conint(ge=0) = Field(None, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")
    name: str = Field(None, min_length=1, max_length=100, example="Profile1", description="profile name")
    sort_field: str = Field('created_at', max_length=32, description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('asc', description="sort method")
    profile_type: conint(ge=1, le=2) = Field(None, description="Protection Profile Type")
    storage_type: StorageType = Field(None, description="1 : AWS S3, 2 : SharePoint(On Line) 3 : SharePoint(ON-Prem) 4:SMB")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"