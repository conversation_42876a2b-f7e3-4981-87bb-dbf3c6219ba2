from flask import jsonify, request, Blueprint
from util.common_log import get_logger
from pydantic import ValidationError
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
import traceback

logger = get_logger("api")
dlp_events = Blueprint("dlp_events", __name__)


@dlp_events.route("/", methods=["GET"])
def handle_dlp_events_get():
    from flask_module.dlp_events_reqmodel import DlpEventsParams
    from service.dlp_engine_service import get_dlp_events
    
    if request.method == 'GET':        
        try:
            validated_params = DlpEventsParams(**request.args.to_dict())            
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:    
            return success_response(get_dlp_events(validated_params))
        except Exception as e:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@dlp_events.route("/log_statistics", methods=["GET"])
def handle_dlp_events_log_statistics():
    from flask_module.dlp_events_reqmodel import DlpEventLogParams
    from service.dlp_engine_service import EventLogStatistics

    if request.method == 'GET':
        try:
            validated_params = DlpEventLogParams(**request.args.to_dict())
            if str(validated_params.type).lower() not in ["deny", "alert"]:
                return error_response(Ecodes.VALIDATION_ERROR, 400, "Type is invalid")
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            return success_response({"ErrorCode": 0, "Data": EventLogStatistics(**request.args.to_dict()).get_log_data()})
        except Exception as e:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

