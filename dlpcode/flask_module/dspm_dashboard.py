from flask import Blueprint, request, jsonify
from util.err_codes import error_response, success_response
from flask_module import session
import util.err_codes as Ecodes
import json
from pydantic import ValidationError
from sqlalchemy.exc import SQLAlchemyError, DBAPIError, OperationalError
from service import dspm_dashboard_service
from util.common_log import get_logger
from exts import Base, Session

dspmdashboard = Blueprint("dspmdashboard", __name__)
logger = get_logger("dlp")

# Mapping to return the specified widgetnames
widget_to_method_mapping = {
    "totalfilesbycompliancepercentage": dspm_dashboard_service.get_total_count_by_compliance,
    "piitotalpercentage": dspm_dashboard_service.get_PII_total_percentage,
    "pcitotalpercentage": dspm_dashboard_service.get_PCI_total_percentage,
    "phitotalpercentage": dspm_dashboard_service.get_PHI_total_percentage,
    "confidentialtotalpercentage": dspm_dashboard_service.get_confidential_count_and_percentage,
    "highlyconfidentialtotalpercentage": dspm_dashboard_service.get_highly_confidential_count_and_percentage,
    "totalfilesbycompliancebystorageandfilecategory": dspm_dashboard_service.get_total_files_by_compliance_count_and_percentage_by_storage_and_file_category,
    "piibystorageandfilecategory": dspm_dashboard_service.get_PII_total_count_and_percentage_by_storage_and_file_category,
    "pcibystorageandfilecategory": dspm_dashboard_service.get_PCI_total_count_and_percentage_by_storage_and_file_category,
    "phibystorageandfilecategory": dspm_dashboard_service.get_PHI_total_count_and_percentage_by_storage_and_file_category,
    "confidentialbystorageandfilecategory": dspm_dashboard_service.get_confidential_total_count_and_percentage_by_storage_and_file_category,
    "highlyconfidentialbystorageandfilecategory": dspm_dashboard_service.get_highly_confidential_total_count_and_percentage_by_storage_and_file_category,
    "filebyfiletype": dspm_dashboard_service.get_file_types_distribution,
    "cataloguedfilecountbytime": dspm_dashboard_service.get_catalogued_file_count_by_time,
    "cataloguedfilecount": dspm_dashboard_service.get_catalogued_file_count,
    "uncataloguedfilecount": dspm_dashboard_service.get_uncatalogued_file_count,
    "cataloguedfilesbystorage": dspm_dashboard_service.get_catalogued_files_by_storage,
}


@dspmdashboard.route("/fetch_dashboard_data", methods=["GET", "POST"])
def fetch_dashboard_data():
    from flask_module.dspm_dashboard_reqmodel import DSPMDashboardModel

    response = dspm_dashboard_service.DSPMDashboardObj()
    validated_data = {}

    if request.method == "POST":
        try:
            data = request.get_json()
            validated_data = DSPMDashboardModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

    try:
        with Session() as session:
            # When widgetnames list is empty, populate all dashboard widgets
            if request.method == "GET" or len(validated_data.widgetnames) == 0:
                logger.info("Fetching all the dashboard widgets!")
                # Total files by compliance
                response.totalfilesbycompliancepercentage = (
                    dspm_dashboard_service.get_total_count_by_compliance(session)
                )

                # PII total percentage
                response.piitotalpercentage = (
                    dspm_dashboard_service.get_PII_total_percentage(session)
                )

                # PCI total percentage
                response.pcitotalpercentage = (
                    dspm_dashboard_service.get_PCI_total_percentage(session)
                )

                # PHI total percentage
                response.phitotalpercentage = (
                    dspm_dashboard_service.get_PHI_total_percentage(session)
                )

                # Confidential total percentage
                response.confidentialtotalpercentage = (
                    dspm_dashboard_service.get_confidential_count_and_percentage(session)
                )

                # Highly-Confidential total percentage
                response.highlyconfidentialtotalpercentage = (
                    dspm_dashboard_service.get_highly_confidential_count_and_percentage(session)
                )

                # Total files by compliance by storage and file category                 
                response.totalfilesbycompliancebystorageandfilecategory = (              
                    dspm_dashboard_service.get_total_files_by_compliance_count_and_percentage_by_storage_and_file_category(session)
                ) 

                # PII by storage and file category
                response.piibystorageandfilecategory = (
                    dspm_dashboard_service.get_PII_total_count_and_percentage_by_storage_and_file_category(session)
                )

                # PCI by storage and file category
                response.pcibystorageandfilecategory = (
                    dspm_dashboard_service.get_PCI_total_count_and_percentage_by_storage_and_file_category(session)
                )

                # PHI by storage and file category
                response.phibystorageandfilecategory = (
                    dspm_dashboard_service.get_PHI_total_count_and_percentage_by_storage_and_file_category(session)
                )

                # Confidential by storage and file category
                response.confidentialbystorageandfilecategory = (
                    dspm_dashboard_service.get_confidential_total_count_and_percentage_by_storage_and_file_category(session)
                )

                # Highly-Confidential by storage and file category
                response.highlyconfidentialbystorageandfilecategory = (
                    dspm_dashboard_service.get_highly_confidential_total_count_and_percentage_by_storage_and_file_category(session)
                )

                # File by file types
                response.filebyfiletype = (
                    dspm_dashboard_service.get_file_types_distribution(session)
                )

                # Catalogued file count by time
                response.cataloguedfilecountbytime = (
                    dspm_dashboard_service.get_catalogued_file_count_by_time(session)
                )

                # Catalogued file count
                response.cataloguedfilecount = (
                    dspm_dashboard_service.get_catalogued_file_count(session)
                )

                # Uncatalogued file count
                response.uncataloguedfilecount = (
                    dspm_dashboard_service.get_uncatalogued_file_count(session)
                )

                # Catalogued files by storage
                response.cataloguedfilesbystorage = (
                    dspm_dashboard_service.get_catalogued_files_by_storage(session)
                )

                logger.debug(f"Response for the dashboard: All widgets: {response}")
                return success_response(response.to_dict())
            elif request.method == "POST" and len(validated_data.widgetnames) != 0:
                # Fetch only the requested widgets
                all_widgets = widget_to_method_mapping.keys()
                for fieldname in all_widgets:
                    if (
                        fieldname in validated_data.widgetnames
                        and fieldname in widget_to_method_mapping
                    ):
                        setattr(response, fieldname, widget_to_method_mapping[fieldname](session))
                    else:
                        setattr(response, fieldname, [])
                logger.debug(f"Response for the dashboard: Specific widgets: {response}")
                response_dict = response.to_dict()
                return success_response({k: v for k, v in response_dict.items() if k in validated_data.widgetnames})
    except SQLAlchemyError as e:
        logger.error(f"SQLalchemy error while fetching DSPM dashboard data: {str(e)}")
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Error while fetching DSPM dashboard data: {str(e)}",
        )
    except (DBAPIError, OperationalError) as e:
        logger.error(f"Database error while fetching DSPM dashboard data: {str(e)}")
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Error while fetching DSPM dashboard data: {str(e)}",
        )
    except Exception as e:
        logger.error(f"Error while fetching DSPM dashboard data: {e}")
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Error while fetching DSPM dashboard data: {str(e)}",
        )
    return success_response({})
