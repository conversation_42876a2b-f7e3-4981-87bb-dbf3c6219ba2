from flask import Blueprint, jsonify, request
from util.common_log import get_logger
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from pydantic import ValidationError
from service.predefined_datatype_service import GLOBAL_CONFIG
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from flask_module import session
from system.system_upload import save_upload_file
import os
from psycopg2.errors import UniqueViolation
from domain_model.edm_rule import (
    create_edm_rule,
    update_edm_rule,
    get_edm_rules,
    get_edm_rule,
    delete_edm_rules
)
from service.edm_service import get_reference_cnt
from domain_model.edm_template import get_edm_template

logger = get_logger("api")
edm_rule = Blueprint("edm_rule", __name__)

@edm_rule.route('/', methods=['POST'])
def handle_edm_rule_create():
    try:
        from flask_module.edm_rule_reqmodel import CreateEDMRuleReqModel
        data = request.get_json()
        req_model = CreateEDMRuleReqModel(**data)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        if len(req_model.secondary_key) < req_model.match_count:
            return error_response(Ecodes.VALIDATION_ERROR, 400, f"Match count should not be more than selected fields")
        match_criteria = {
            "primary_key": req_model.primary_key,
            "secondary_key": req_model.secondary_key,
            "match_count":  req_model.match_count
        }
        edm_rule = {
            "name": req_model.name,
            "description": req_model.description,
            "edm_template_id": req_model.edm_template_id,
            "match_criteria": match_criteria,
            "status": req_model.status
        }
        logger.error(f" match_criteria = {match_criteria}, edm_rule = {edm_rule}")

        edm_template = get_edm_template(id = req_model.edm_template_id)
        if not edm_template.attributes_ext["status"]:
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, f"The EDM hash task is still in progress. The current EDM dataset is unavailable.")

        rule = create_edm_rule(payload=edm_rule)
        if rule:
            record_event_log(
                user=session.get_user(request),
                level=LogLevel.INFO.value,
                message=f"Create EDM rule {rule.name}",
                desc="Create new EDM rule",
                action=LogAction.CREATE.value,
                type=LogType.EDM.value
            )
            return success_response({"ErrorCode": 0, "Data": rule.to_dict()})
        else:
            return error_response(Ecodes.INTERNAL_ERROR, 400, "create EDM rule failed")
    except UniqueViolation:
        return error_response(Ecodes.DUPLICATE_RESOURCE, 409, "A duplicate EDM Rule Name already exists.")
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)

@edm_rule.route('/', methods=['PUT'])
def handle_edm_rule_update():
    try:
        from flask_module.edm_rule_reqmodel import UpdateEDMRuleReqModel
        data = request.get_json()
        req_model = UpdateEDMRuleReqModel(**data)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        discover_policy_cnt, dlp_policy_cnt, scan_cnt = get_reference_cnt(edm_ids=[req_model.id])
        if discover_policy_cnt > 0 or dlp_policy_cnt > 0 or scan_cnt > 0:
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, f"Editing is not allowed when the EDM rule is being referenced.")
        if len(req_model.secondary_key) < req_model.match_count:
            return error_response(Ecodes.VALIDATION_ERROR, 400, f"Match count should not be more than selected fields")
        
        edm_template = get_edm_template(id = req_model.edm_template_id)
        if not edm_template.attributes_ext["status"]:
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, f"The EDM hash task is still in progress. The current EDM dataset is unavailable.")
        
        match_criteria = {
            "primary_key": req_model.primary_key,
            "secondary_key": req_model.secondary_key,
            "match_count":  req_model.match_count
        }
        edm_rule = {
            "name": req_model.name,
            "description": req_model.description,
            "edm_template_id": req_model.edm_template_id,
            "match_criteria": match_criteria,
            "status": req_model.status
        }
        rule =  update_edm_rule(rule_id=req_model.id, updates=edm_rule)
        if rule:
            record_event_log(
                user=session.get_user(request),
                level=LogLevel.INFO.value,
                message=f"Update EDM rule {rule.name}",
                desc="Update EDM rule",
                action=LogAction.UPGRADE.value,
                type=LogType.EDM.value
            )
            return success_response({"ErrorCode": 0, "Data": "EDM rule updated"})
        else:
            return error_response(Ecodes.INTERNAL_ERROR, 400, "update EDM rule failed")

    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)

@edm_rule.route("/", methods=['DELETE'])
def handle_edm_rule_delete():
    try:
        from flask_module.edm_rule_reqmodel import DeleteEDMRuleReqModel
        args = request.args.to_dict()
        req_model = DeleteEDMRuleReqModel(**args)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        discover_policy_cnt, dlp_policy_cnt, scan_cnt = get_reference_cnt(edm_ids=[req_model.id])
        if discover_policy_cnt > 0 or dlp_policy_cnt > 0 or scan_cnt > 0:
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, f"Deleting is not allowed when the EDM rule is being referenced.")
        rule = get_edm_rule(id=req_model.id)
        if not rule:
            return error_response(Ecodes.NOT_FOUND, 404, f"EDM rule {req_model.id} does not exist")
        res = delete_edm_rules(id=req_model.id)
        if not res:
            raise Exception("Delete EDM rule failed")
        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Delete EDM rule",
            desc="Delete EDM rule",
            action=LogAction.DELETE.value,
            type=LogType.EDM.value
        )
        return success_response({"ErrorCode": 0, "Data": "EDM rule deleted"})
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)

@edm_rule.route("/", methods=['GET'])
def handle_edm_rule_query():
    from flask_module.edm_rule_reqmodel import QueryEDMRuleReqModel
    try:
        args = request.args.to_dict()
        reqmodel = QueryEDMRuleReqModel(**args)
    except ValidationError as e:
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    logger.info(f"reqmodel: {reqmodel}")

    try:
        rules, total = get_edm_rules(
            conditions=reqmodel.dict(exclude_none=True),
            sort_field=reqmodel.sort_field,
            sort_method=reqmodel.sort_method,
            page=reqmodel.page,
            per_page=reqmodel.per_page
        )
        ret_rules = []
        for rule in rules:
            discover_policy_cnt, dlp_policy_cnt, scan_cnt = get_reference_cnt(edm_ids=[str(rule.id)])
            ret_rule = rule.to_dict()
            ret_rule["discover_policy_cnt"] = discover_policy_cnt
            ret_rule["dlp_policy_cnt"] = dlp_policy_cnt
            ret_rule["scan_cnt"] = scan_cnt
            ret_rules.append(ret_rule)

        return success_response({
            "list":ret_rules,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
            "total":total
        })
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)
