import traceback

from flask import Blueprint
from flask import request
from pydantic import ValidationError

import util.err_codes as Ecodes
from flask_module.controller import fabric as ctrl_fabric
from util.err_codes import error_response, success_response
from flask_module.fabric_reqmodel import (FabricCfg)
from system.system_log import record_event_log, LogLevel, LogType, LogAction
from util.common_log import get_logger
from flask_module import session

logger = get_logger("api")

fabric = Blueprint("fabric", __name__)
fabric_fgt = Blueprint("fabric_fgt", __name__)
fabric_system = Blueprint("fabric_system", __name__)


@fabric_fgt.route("/device/status", methods=["GET"])
def get_device_status():
    """
    Called by FGT
    """
    if request.method == 'GET':
        try:
            data = ctrl_fabric.get_device_status()
            return success_response(data)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@fabric_fgt.route("/local/message", methods=["GET"])
def get_local_message():
    """
    Called by nstd
    """
    if request.method == 'GET':
        try:
            ctrl_fabric.save_local_message(request)
            return success_response({"ErrorCode": 0})
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@fabric.route("/widget", methods=["GET"])
def get_widget():
    if request.method == 'GET':
        try:
            data = ctrl_fabric.get_widget()
            return success_response(data)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@fabric.route("/widget/sysinfo", methods=["GET"])
def get_widget_sys_info():
    if request.method == 'GET':
        try:
            data = ctrl_fabric.get_widget_sys_info()
            return success_response(data)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@fabric.route("/widget/caseinfo", methods=["GET"])
@fabric.route("/widget/licenses", methods=["GET"])
def get_widget_case_info():
    if request.method == 'GET':
        try:
            data = ctrl_fabric.get_widget_case_info()
            return success_response(data)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@fabric.route("/", methods=["GET", "POST"])
def setup_fabric_config():
    if request.method == 'GET':
        try:
            data = ctrl_fabric.get_csf_setting()
            return success_response({"ErrorCode": 0, "Data": data})
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == 'POST':
        try:
            validated_params = FabricCfg(**request.json)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            errno, errmsg = ctrl_fabric.setup_csf_config(validated_params.dict())
            if errno != 0:
                record_event_log(user=session.get_user(request), level=LogLevel.WARNING.value,
                                 message=f"Fabric setup failed: {str(errmsg)}",
                                 desc='Setup fabric', type=LogType.SYSTEM.value, action=LogAction.SETUP.value)
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, str(errmsg))

            record_event_log(user=session.get_user(request), level=LogLevel.INFO.value,
                             message=f"Fabric setup successfully",
                             desc='Setup fabric', type=LogType.SYSTEM.value, action=LogAction.SETUP.value)
            return success_response({"ErrorCode": 0})
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@fabric_system.route("/dlp/labels", methods=["GET"])
def get_dlp_labels():
    if request.method == 'GET':
        try:
            data = ctrl_fabric.get_dlp_labels()
            return success_response(data)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

@fabric_system.route("/dlp/h2info", methods=["GET"])
def get_dlp_h2info():
    try:
        data = ctrl_fabric.get_h2_info()
        logger.info(f"get_dlp_h2info: {data}")
        return success_response(data)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)
