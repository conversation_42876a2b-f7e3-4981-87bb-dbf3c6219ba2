import uuid, re
from util.common_log import get_logger
from pydantic import BaseModel, validator, root_validator
from pydantic import Field, constr, conint, confloat
from typing import List, Optional, Literal, Union, Dict
from flask_module.base import validate_condition_relation_string
from util.enum_ import ConfidenceLevel

logger = get_logger("api")


# Define a Pydantic model for individual entries
class MatchConditionEntryModel(BaseModel):
    # required fields
    type: Literal[
        'data_type', 'custom_data_type', 'ml_category', 
        'file_type', 'file_ext', 'idm', 'edm', 'file_path'
    ] = Field(..., example="", description="condition type")


    # optional fields
    confidence: Optional[conint(ge=0, le=100)] = Field(0, example=60, description="confidence score threshold")
    confidence_level: Literal[0, 1, 2, 3] = Field(ConfidenceLevel.CUSTOM.value, description="confidence level threshold")

    #for data_type and custom_data_type
    entity: str = Field(None, example="U.S. Social Security number (SSN)", description="data type name")
    entity_id: str = Field(None, example="20001", description="data type id")
    match_count: int = Field(None, example=1, description="match times")
    category: str = Field(None, examples="10001", description="data type category")

    # for idm
    idm_id: str = Field(None, description="idm template id")
    idm_name: str = Field(None, description="idm template name")
    similarity_level: Literal[0, 1, 2, 3] = Field(ConfidenceLevel.CUSTOM.value, description="idm match similarity level")
    similarity: Optional[conint(ge=0, le=100)] = Field(None, examples=60, description="idm match similarity threshold")

    # for edm
    edm_id: str = Field(None, description="edm template id")
    edm_name: str = Field(None, description="edm template name")

    # for ml_category
    main_class_id: str = Field(None, example="10000", description="main category of the plain text")
    sub_class_id: str = Field(None, example="100001", description="sub category of the plain text")

    # for file_ext
    file_ext: str = Field(None, example="xlsx", description="file type")

    #for file_type
    mime_type: str = Field(None, examples="tif/tiff", description="file mime type")

    # for path_matching
    file_path_pattern: str = Field(None, example="/backup/**/2024/*.zip", description="file path regex or glob pattern")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
    @root_validator(skip_on_failure=True)
    def check_datatypes(cls, values):
        type = values.get('type')
        attrs = ['entity', 'entity_id', 'match_count', 'category']
        if type in ["data_type", "custom_data_type"]:
            if not all(attr in values.keys() and values.get(attr) for attr in attrs):
                raise ValueError(f'the field entity/entity_id/match_count/category must be specified when type is {type}')
        elif type == 'file_ext':
            if not values.get('file_ext'): 
                raise ValueError(f'the field file_ext must be specified when type is {type}')
        elif type == 'file_type':
            if not values.get('mime_type'): 
                raise ValueError(f'the field mime_type must be specified when type is {type}')
        elif type == 'file_path':
            # Todo: validate the file_path_pattern
            # Here we assume the file_path_pattern is a valid regex or glob pattern
            path_pattern = values.get('file_path_pattern')
            if not path_pattern:
                raise ValueError(f'the field file_path_pattern must be specified when type is {type}')
            
        return values
    
    # Available only for type is data_type and custom_data_type
    @validator('entity', 'entity_id', 'match_count', 'category')
    def data_type_fields_validator(cls, v, values, **kwargs):
        type = values['type']
        if type in ["data_type", "custom_data_type"]:
            if not v:
                raise ValueError(f'the field entity/entity_id/match_count/category must be specified when type is {type}')
            return v
        else:
            return None

class MatchConditionRelationReqModel(BaseModel):
    type: Literal[1, 2, 3] = Field(..., description="Condition relations 1: all conditions are matched, 2: at least one condition is matched, 3: custom")
    custom_relations: str = Field(None, min_length=1, max_length=2048, example="( 1 and 2 ) or ( 1 and 3 ) or 4", description="custom condition relation")
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
    @validator('custom_relations')
    def relation_validator(cls, v, values, **kwargs):
        type = values['type']
        # only for type is 3
        if type == 3:
            result, error = validate_condition_relation_string(v)
            if result:
                return v
            else:
                raise ValueError(f'Invalid custom_relations {v}. error: {error}')
        else:
            # keep the custom_relation 
            return v

class DataClassifierCreateReqModel(BaseModel):
    # required fields
    name: str = Field(..., min_length=1, max_length=512, example="Rule1", description="discover rule name")

    match_condition: Dict[str, MatchConditionEntryModel]
    match_condition_relation: MatchConditionRelationReqModel
    
    # optional fields
    description: str = Field("", min_length=0, max_length=512, example="This is a discover rule")
    sensitivity: Literal[0, 1, 2, 3] = Field(2, description="The severity of this rule. 0: Public, 1: Internal, 2: Confidential, 3: Restricted")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
    @root_validator(skip_on_failure=True)
    def check_custom_relations(cls, values):
        match_condition = values.get('match_condition')
        match_condition_relation = values.get('match_condition_relation')
        if match_condition and match_condition_relation and match_condition_relation.type == 3:
            condition_ids = set(match_condition.keys())
            custom_relations = match_condition_relation.custom_relations.replace('(', '').replace(')', '').replace('AND', '').replace('OR', '').replace('NOT', '').split()
            for cond_id in custom_relations:
                if cond_id not in condition_ids:
                    raise ValueError(f"the condition id '{cond_id}' in match_condition_relation is not present in rule conditions")
            
        return values

class DataClassifierQueryReqModel(BaseModel):
    id: constr(min_length=36, max_length=36)  = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")

# Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class DataClassifierSummaryQueryReqModel(BaseModel):
    # optional fields
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    name: str = Field(None, min_length=1, max_length=512, example="Rule-1", description="discover rule name")
    description: str = Field(None, min_length=0, max_length=512, example="This is a discover rule")
    is_predefined: bool = Field(None, examples="True")
    status: bool = Field(None, examples=True)
    category: str = Field(None, examples="Credentials")
    region: str = Field(None, examples="US")
    #severity: Literal[1, 2, 3] = Field(None, description="The severity of this rule. 1:High, 2 : Medium 3:Low")
    sensitivity: conint(ge=0, le=3) = Field(None, description="The severity of this engine. 0: Public, 1: Internal, 2: Confidential, 3: Restricted")

    sort_field: Literal['id', 'name', 'description', 'policy_id', 'severity', 'created_at', 'updated_at'] = Field('updated_at', description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: conint(ge=0) = Field(None, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")

# Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
        
class DataClassifierUpdateReqModel(BaseModel):
    # required fields
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    
    # optional fields
    name: str = Field(None, min_length=1, max_length=512, example="Rule1", description="discover rule name")    
    match_condition: Dict[str, MatchConditionEntryModel] = None
    match_condition_relation: MatchConditionRelationReqModel = None
    
    description: str = Field(None, min_length=0, max_length=512, example="This is a discover rule")
    sensitivity: Literal[0, 1, 2, 3] = Field(None, description="The severity of this rule. 0: Public, 1: Internal, 2: Confidential, 3: Restricted")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

    @root_validator(skip_on_failure=True)
    def check_custom_relations(cls, values):
        match_condition = values.get('match_condition')
        match_condition_relation = values.get('match_condition_relation')
        if match_condition and match_condition_relation:
            if match_condition_relation.type == 3:
                condition_ids = set(match_condition.keys())
                custom_relations = match_condition_relation.custom_relations.replace('(', '').replace(')', '').replace('AND', '').replace('OR', '').replace('NOT', '').split()
                for cond_id in custom_relations:
                    if cond_id not in condition_ids:
                        raise ValueError(f"the condition id '{cond_id}' in match_condition_relation is not present in rule conditions")
        elif (match_condition and not match_condition_relation) or (not match_condition and match_condition_relation):
            raise ValueError(f"the match_condition and match_condition_relation fields must be commited at the same time")
            
        return values
    
class DataClassifierDeleteReqModel(BaseModel):
    # required fields
    id: List[constr(min_length=36, max_length=36)]  = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
