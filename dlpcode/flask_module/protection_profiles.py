import traceback
from flask import Blueprint, request
from pydantic import ValidationError
from util.common_log import get_logger
from util.err_codes import error_response, success_response
from util.err_codes import ModErrCode
from service import protection_profile_service
from util.enum_ import StorageType
from flask_module import session
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from util.enum_ import CredentialType
from storage.service.profiles import get_storage_profile_by_id

logger = get_logger("api")
protection_profiles = Blueprint("protection_profiles", __name__)

@protection_profiles.route('/', methods=['POST'])
def handle_protection_profiles_create():
    from flask_module.protection_profile_reqModel import ProtectionProfileCreateReqModel
    from huey_worker.protection_action_task import get_connector_from_protection_profile
    from psycopg2.errors import UniqueViolation

    try:
        data = request.get_json()
        reqmodel = ProtectionProfileCreateReqModel(**data)
        logger.info(f"reqmodel: {reqmodel}")

        payload = reqmodel.dict(exclude_none=True)
        profile = payload['profile']
        path = profile['path']
        credential_type = profile['credential_type']

        if credential_type != CredentialType.USE_SAME_CREDENTIAL_WITH_SCAN_POLICY:
            storage = get_storage_profile_by_id(profile['storage_id'])
            if storage == None:
                return error_response(ModErrCode.ErrCode02050036, 500)

            connector = get_connector_from_protection_profile(payload)
            if connector and not connector.check_folder_exist(path, None):
                return error_response(ModErrCode.ErrCode02050008, 404, extend_message=path)
            if connector and not connector.check_write_access(path, None):
                return error_response(ModErrCode.ErrCode02050033, 500, extend_message=path)

        create_success = protection_profile_service.create_protection_profile(payload)
        if create_success:
            logger.info(f"Protection profile created, {reqmodel.name}")
            user = session.get_user(request)
            record_event_log(user=user, level=LogLevel.INFO.value, message=f"Add a protection profile {reqmodel.name}",
                     desc='Add protection profile', type=LogType.PROTECTION_PROFILES.value, action=LogAction.CREATE.value)
            return success_response({'message': 'Protection profile created'}, 201)
        else:
            return error_response(ModErrCode.ErrCode02050009, 500)
    except UniqueViolation:
        return error_response(ModErrCode.ErrCode02050010, 409)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02050011, 500)


@protection_profiles.route('/', methods=['PUT'])
def handle_protection_profiles_update():
    from flask_module.protection_profile_reqModel import ProtectionProfileUpdateReqModel
    from huey_worker.protection_action_task import get_connector_from_protection_profile

    try:
        data = request.get_json()
        try:
            reqmodel = ProtectionProfileUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02050012, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        ref = protection_profile_service.check_ref([reqmodel.id], logger)
        if ref:
            logger.info(f'the protection profile is being referred by the scan policy. refer_map: {ref}')
            return error_response(ModErrCode.ErrCode02050013, 400)

        payload = reqmodel.dict(exclude_none=True)
        payload.pop("id")
        profile = payload['profile']
        path = profile['path']
        credential_type = profile['credential_type']

        if credential_type != CredentialType.USE_SAME_CREDENTIAL_WITH_SCAN_POLICY:
            storage = get_storage_profile_by_id(profile['storage_id'])
            if storage == None:
                return error_response(ModErrCode.ErrCode02050037, 500)

            connector = get_connector_from_protection_profile(payload)
            if not connector.check_folder_exist(path, None):
                return error_response(ModErrCode.ErrCode02050014, 404, extend_message=path)
            if not connector.check_write_access(path, None):
                return error_response(ModErrCode.ErrCode02050034, 500, extend_message=path)

        update_success = protection_profile_service.update_scan_protection_profile(reqmodel.id, **payload)
        if update_success:
            logger.info(f"Updated protection profile: {reqmodel.name}")
            user = session.get_user(request)
            record_event_log(user=user, level=LogLevel.INFO.value, message=f"Edit the protection profile {reqmodel.name}",
                     desc='Edit protection profile', type=LogType.PROTECTION_PROFILES.value, action=LogAction.EDIT.value)
            return success_response({'message': 'Protection profile updated'}, 200)
        else:
            return error_response(ModErrCode.ErrCode02050015, 500)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02050016, 500)


@protection_profiles.route('/', methods=['DELETE'])
def handle_protection_profiles_delete():
    from flask_module.protection_profile_reqModel import ProtectionProfileDeleteReqModel

    try:
        args = request.args.to_dict(flat=False)
        logger.info(f"Deleting protection_profiles by args {args}")
        try:
            reqmodel = ProtectionProfileDeleteReqModel(**args)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02050017, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        ref = protection_profile_service.check_ref(reqmodel.id, logger)
        if ref:
            logger.info(f'the protection profile is being referred by the scan policy. refer_map: {ref}')
            return error_response(ModErrCode.ErrCode02050018, 400)

        deleted_ids, failed_ids = protection_profile_service.delete_protection_profiles(reqmodel.id)
        name_list_str = ", ".join(deleted_ids)
        logger.info(f"Deleted protection_profiles {deleted_ids}, failed_ids {failed_ids}")
        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Delete protection profile {name_list_str}",
                     desc='Delete protection profile', type=LogType.PROTECTION_PROFILES.value, action=LogAction.DELETE.value)
        return success_response({'message': 'Profile deleted', 'deleted_ids': deleted_ids, 'failed_ids': failed_ids}, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02050019, 500)


@protection_profiles.route('/', methods=['GET'])
def handle_protection_profiles_get():
    from flask_module.protection_profile_reqModel import ProtectionProfileGetReqModel

    try:
        conditions = request.args.to_dict()
        reqmodel = ProtectionProfileGetReqModel(**conditions)
        logger.info(f"reqmodel: {reqmodel}")

        results, total = protection_profile_service.get_protection_profiles(reqmodel.dict(exclude_none=True))
        if results is None:
            return error_response(ModErrCode.ErrCode02050020, 500)

        return success_response({
            "list": results,
            "total": total,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
        }, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02050021, 500)