import os
import shlex
import re

import subprocess

from system import system_upload
from system import dlp_license
from system import dlp_shell
from util.config import get_global_config
from flask_module import session
from system.system_log import record_event_log, LogLevel, LogType, LogAction

GLOBAL_CONFIG = get_global_config()
SYSTEM_LIC_PATH = GLOBAL_CONFIG.get("system_license", {}).get("file_path")

INVALID_CHARS = '\&|\<|\>|\"|\'|\/'

import os
def is_safe_path(basedir, path, follow_symlinks=True):
    if follow_symlinks:
        return os.path.realpath(path).startswith(os.path.realpath(basedir))
    else:
        return os.path.abspath(path).startswith(os.path.abspath(basedir))

def upload_system_license(request, user):
    
    allowed_file_type = ('.lic')

    upload_names = sorted(list(request.files.keys()))
    if len(upload_names) != 1:
        ret = {
            'ErrorCode': -1,
            'ErrorMessage': 'License upload failed: Invalid file count.'
        }
        return ret

    upload_name = upload_names[0]
    upload = request.files.get(upload_name)
    filename = os.path.basename(shlex.quote(upload.filename))

    if not re.match(r'^[\w\.\_]+$', filename) or filename.count('.') > 1:
        ret = {
            'ErrorCode': -2,
            'ErrorMessage': 'License upload failed: File names can only include letters, numbers, and underscores.'
        }
        return ret

    if re.search(INVALID_CHARS, filename):
        ret = {
            'ErrorCode': -5,
            'ErrorMessage': 'Invalid file name'
        }
        return ret

    file_path = os.path.join(SYSTEM_LIC_PATH, os.path.normpath(filename))

    if not is_safe_path(SYSTEM_LIC_PATH, file_path):
        raise ValueError("Unsafe file path")


    fname, fext = os.path.splitext(filename)
    if fext not in allowed_file_type:
        ret = {
            'ErrorCode': -2,
            'ErrorMessage': 'License upload failed: Invalid file type [' + fext + ']'
        }
        return ret

    try:
        if not os.path.exists(SYSTEM_LIC_PATH.strip()) or not os.path.isdir(SYSTEM_LIC_PATH):
            os.mkdir(SYSTEM_LIC_PATH)
        elif os.path.exists(file_path.strip()) and os.path.isfile(file_path):
            os.remove(file_path)

        errno, errmsg = system_upload.save_upload_file(request, upload, file_path, SYSTEM_LIC_PATH)
        if errno != 0:
            ret = {'ErrorCode': errno, 'ErrorMessage': f"License upload failed: {errmsg}"}
            return ret
    except:
        ret = {'ErrorCode': -1, 'ErrorMessage': 'License upload failed: File save fail.'}
        return ret

    dlp_license.upload_new_license(file_path)
    errno, errmsg = dlp_license.generate_new_license_data(file_path)
    if errno != 0:
        errno, errmsg = dlp_license.get_active_code(file_path)
        if errno != 0:
            system_upload.clear_upload_path(file_path)
            ret = {'ErrorCode': -1, 'ErrorMessage': 'License upload failed: Get active code fail.'}
            return ret

        active_code = errmsg
        dlp_license.upload_license(active_code)
        errno, errmsg = dlp_license.generate_license_data(active_code)
        if errno != 0:
            system_upload.clear_upload_path(file_path)
            ret = {'ErrorCode': -1, 'ErrorMessage': 'License upload failed: Generate license data fail.'}
            return ret
        # system_upload.clear_upload_path(file_path)
        # ret = {'ErrorCode': -1, 'ErrorMessage': 'License upload failed: Generate license data fail.'}
        # return ret

    dlp_shell.call("cp -a {} /etc/vm.lic".format(file_path))
    system_upload.clear_upload_path(file_path)
    ret = {'ErrorCode': 0, 'ErrorMessage': 'License uploaded successfully.', 'Data': 0}
    return ret

def get_upload_license_status(request, user):
    ret = dlp_license.get_upload_status()
    return ret

def upload_fds_server(request, user):
    from system import dlp_fds
    import json
    try:
        builder=dlp_fds.UpdateFDSServerController()
        request_body = request.get_data()
        data = json.loads(request_body)
        is_valid, err_msg = builder.post(data)
        if not is_valid:
            return {'ErrorCode': -1, 'ErrorMessage': 'Upload fds server failed', 'Data': 0}
        return {'ErrorCode': 0, 'ErrorMessage': 'Upload fds server successfully', 'Data': 0}
    except Exception as e:
        ret = {'ErrorCode': -1, 'ErrorMessage': f'Upload fds server failed: {str(e)}', 'Data': 0}
        return ret

def update_fgd_controller(request, user):
    from system import dlp_autoupdate

    if not dlp_license.is_licensed():
        record_event_log(
            user=session.get_user(request),
            level=LogLevel.WARNING.value,
            message="Auto-upgrading packages is prohibited without a valid license.",
            desc="Auto-upgrade packages is prohibited",
            action=LogAction.UPGRADE.value,
            type=LogType.UPDATE_MODELS.value
        )
        return {'ErrorCode': -1, 'ErrorMessage': 'Upload models failed: No valid license.', 'Data': 0}

    status = dlp_autoupdate.get_ML_upload_status()
    if status['ErrorCode'] == 0 and status['Data']['Status'] == 'Running':
        return status
    status = dlp_autoupdate.get_NLP_upload_status()
    if status['ErrorCode'] == 0 and status['Data']['Status'] == 'Running':
        return status
    status = dlp_autoupdate.get_DTD_upload_status()
    if status['ErrorCode'] == 0 and status['Data']['Status'] == 'Running':
        return status

    if 'block' in request.args and request.args['block'] == '1':
        # redmine-1059 easier to finish time
        subprocess.call('python3 /dlpcode/auto_update_monitor.py -i', shell=True)
    else:
        subprocess.Popen('python3 /dlpcode/auto_update_monitor.py -i', shell=True)

    ret = {"ErrorCode": 0}
    return ret

def server_get_fdg_version(request, user):
    errno, errmsg = dlp_license.get_fdg_version(request)
    if errno != 0:
        ret = {'ErrorCode': -1, 'Data': 'License upload failed: Generate license data fail.'}
        return ret
    ret = {'ErrorCode': 0, 'Data': errmsg}
    return ret

def ML_upload_status(request, user):
    from system import dlp_autoupdate
    status = dlp_autoupdate.get_ML_upload_status()

    if status['ErrorCode'] != 0:
        # If there's an error, update the status to 'Stopped'
        # and ensure the error code is returned only once
        update_status = {
            'ErrorCode': 0,
            'Data': {
                'Status': 'Stopped',
            }
        }
        dlp_autoupdate.set_ML_upload_status(update_status)
        return update_status

    return status

def NLP_upload_status(request, user):
    from system import dlp_autoupdate
    status = dlp_autoupdate.get_NLP_upload_status()

    if status['ErrorCode'] != 0:
        # If there's an error, update the status to 'Stopped'
        # and ensure the error code is returned only once
        update_status = {
            'ErrorCode': 0,
            'Data': {
                'Status': 'Stopped',
            }
        }
        dlp_autoupdate.set_NLP_upload_status(update_status)
        return update_status

    return status

def DTD_upload_status(request, user):
    from system import dlp_autoupdate
    status = dlp_autoupdate.get_DTD_upload_status()

    if status['ErrorCode'] != 0:
        # If there's an error, update the status to 'Stopped'
        # and ensure the error code is returned only once
        update_status = {
            'ErrorCode': 0,
            'Data': {
                'Status': 'Stopped',
            }
        }
        dlp_autoupdate.set_DTD_upload_status(update_status)
        return update_status

    return status