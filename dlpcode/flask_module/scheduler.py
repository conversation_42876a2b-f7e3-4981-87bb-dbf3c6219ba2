
from flask import Blueprint


scheduler = Blueprint("scheduler", __name__)


@scheduler.route("/index", methods=["GET"])
def index():
    return "This is a schedule index page"

# TODO: Implement scheduler API
# 1. Implement a scheduler API that returns a list of scheduled tasks
# 2. Implement a scheduler API that schedules a task
# 3. Implement a scheduler API that unschedules a task
# 4. Implement a scheduler API that updates a task schedule
# 5. .......