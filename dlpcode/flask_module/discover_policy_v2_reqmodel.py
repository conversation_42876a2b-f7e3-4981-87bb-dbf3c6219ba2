import uuid
from util.common_log import get_logger
from pydantic import BaseModel, validator, root_validator
from pydantic import Field, constr, conint, confloat
from typing import List, Optional, Literal, Union, Dict
from flask_module.base import validate_condition_relation_string

logger = get_logger("api")


# Define a Pydantic model for individual entries
class DiscoverPolicyMatchConditionEntryModel(BaseModel):
    # required fields
    type: Literal['file_location', 'collaborator', 'share_link', 'encryption'] = Field(..., example="", description="condition type")

    # for collaborator, share_link, file localtion, encryption
    value: List[str] = Field(None, description="collaborator type: internal, extenal")

    class Config:
        extra = "forbid"
    
    @root_validator(skip_on_failure=True)
    def check_conditions(cls, values):
        type = values.get('type')
        if type in ['collaborator', 'share_link', 'encryption', 'file_location']:
            if values.get('value') is None: 
                raise ValueError(f'the field value must be specified when type is {type}')
        return values

class MatchConditionRelationReqModel(BaseModel):
    type: Literal[1, 2, 3] = Field(..., description="Condition relations 1: all conditions are matched, 2: at least one condition is matched, 3: custom")
    custom_relations: str = Field(None, min_length=1, max_length=2048, example="( 1 and 2 ) or ( 1 and 3 ) or 4", description="custom condition relation")
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
    @validator('custom_relations')
    def relation_validator(cls, v, values, **kwargs):
        type = values['type']
        # only for type is 3
        if type == 3:
            result, error = validate_condition_relation_string(v)
            if result:
                return v
            else:
                raise ValueError(f'Invalid custom_relations {v}. error: {error}')
        else:
            # keep the custom_relation 
            return v

class LabelsModel(BaseModel):
    custom: List[str] = Field(..., description="custom tag")
    
    class Config:
        extra = "forbid"
        
class LabelModel(BaseModel):
    labels: LabelsModel = Field(None, description="")
    ml_label_mode: Literal[0, 1] = Field(0, description="0: disable (default), 1: enable")
    protection_framework_label_mode: Literal[0, 1] = Field(0, description="0: disable (default), 1: enable")
    sensitivity_label_mode: Literal[0, 1] = Field(0, description="0: disable (default), 1: enable")
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

class RemediationModel(BaseModel):
    file_copy : bool = Field(True, example=True, description="Indicates whether to enable file copy action")
    file_quarantine : bool = Field(False, example=True, description="Indicates whether to enable file quarantine action")
    remove_external_share_link: bool = Field(False, examples=True, description="Indicates whether to remove external share link")
    remove_public_share_link: bool = Field(False, examples=True, description="Indicates whether to remove public share link")
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
class DiscoverPolicyActionReqModel(BaseModel):
    label: LabelModel = Field(None, description="label information")
    remediation: RemediationModel = Field(None, description="remediation information")
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

class DiscoverPolicyCreateReqModel(BaseModel):
    # required fields
    data_classifier_ids: List[constr(min_length=36, max_length=36)] = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    name: str = Field(..., min_length=1, max_length=512, example="Rule1", description="discover rule name")
    storage_type: List[int] = Field([], description="storage type list")
    match_condition: Dict[str, DiscoverPolicyMatchConditionEntryModel]
    match_condition_relation: MatchConditionRelationReqModel
    action: DiscoverPolicyActionReqModel = None
    
    # optional fields
    description: str = Field("", min_length=0, max_length=512, example="This is a discover rule")
    risk: Literal[0, 1, 2, 3] = Field(2, description="The risk of this rule. 0: Critical, 1: High, 2: Medium, 3: Low")
    protection_framework: List[str] = Field([], description="protection framework")
    file_exclusion: Optional[List[str]] = Field(None, description="Paths to skip")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
    @validator("storage_type")
    def check_allowed_storage_type(cls, v):
        allowed = {1, 2, 3, 4, 5, 6}
        if not all(item in allowed for item in v):
            raise ValueError("Each value must be one of: 1, 2, 3, 4, 5, 6")
        return v
    
    @validator('data_classifier_ids', each_item=True)
    def validate_each_uuid(cls, v):
        try:
            uuid.UUID(v)
        except ValueError:
            raise ValueError(f"Invalid UUID: {v}")
        return v

class DiscoverPolicyQueryReqModel(BaseModel):
    id: constr(min_length=36, max_length=36)  = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")

# Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class DiscoverPolicySummaryQueryReqModel(BaseModel):
    # optional fields
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    name: str = Field(None, min_length=1, max_length=512, example="Rule-1", description="discover rule name")
    description: str = Field(None, min_length=0, max_length=512, example="This is a discover rule")
    storage_type: conint(ge=1, le=6) = Field(None, examples=1)
    status: bool = Field(None, examples=True)
    #risk: Literal[1, 2, 3] = Field(None, description="The risk of this rule. 1:High, 2 : Medium 3:Low")
    risk: conint(ge=0, le=3) = Field(None, description="The risk of this rule. 0: Critical, 1: High, 2: Medium, 3: Low")
    is_predefined: bool = Field(None, examples="True")
    sort_field: Literal['id', 'name', 'description', 'policy_id', 'risk', 'created_at', 'updated_at'] = Field('updated_at', description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: conint(ge=0) = Field(None, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")

# Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
        
class DiscoverPolicyUpdateReqModel(BaseModel):
    # required fields
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    
    # optional fields
    storage_type: List[int] = Field(None, description="storage type list")
    name: str = Field(None, min_length=1, max_length=512, example="Rule1", description="discover rule name")    
    match_condition: Dict[str, DiscoverPolicyMatchConditionEntryModel] = None
    match_condition_relation: MatchConditionRelationReqModel
    data_classifier_ids: List[constr(min_length=36, max_length=36)]  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    action: DiscoverPolicyActionReqModel = None
    
    description: str = Field(None, min_length=0, max_length=512, example="This is a discover rule")
    risk: Literal[0, 1, 2, 3] = Field(None, description="The risk of this rule. 0: Critical, 1: High, 2: Medium, 3: Low")
    protection_framework: List[str] = Field([], description="protection framework")
    file_exclusion: Optional[List[str]] = Field(None, description="Paths to skip")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
    
    @validator("storage_type")
    def check_allowed_storage_type(cls, v):
        allowed = {1, 2, 3, 4, 5, 6}
        if not all(item in allowed for item in v):
            raise ValueError("Each value must be one of: 1, 2, 3, 4, 5, 6")
        return v
    
    @validator('data_classifier_ids', each_item=True)
    def validate_each_uuid(cls, v):
        try:
            uuid.UUID(v)
        except ValueError:
            raise ValueError(f"Invalid UUID: {v}")
        return v
    
class DiscoverPolicyDeleteReqModel(BaseModel):
    # required fields
    id: List[constr(min_length=36, max_length=36)]  = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
