import subprocess
import traceback
from flask import Blueprint, jsonify, request
from pydantic import ValidationError
from util.common_log import get_logger
from util.config import get_global_config
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes

GLOBAL_CONFIG = get_global_config()
logger = get_logger("api")
analyzer_version = Blueprint("version_info", __name__)


@analyzer_version.route("/current", methods=['GET'])
def get_current_version():
    from service import predefined_datatype_service

    try:
        versions = predefined_datatype_service.get_current_versions()
        return success_response({"ErrorCode": 0, "Data": versions})
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@analyzer_version.route("/all", methods=['GET'])
def get_version_info():
    from service import predefined_datatype_service

    try:
        versions = predefined_datatype_service.get_all_versions()
        return success_response({"ErrorCode": 0, "Data": versions})
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@analyzer_version.route("/", methods=['DELETE'])
def delete_version():
    from service import predefined_datatype_service
    from .analyzer_version_reqmodel import VersionModel

    try:
        try:
            args = request.args.to_dict()
            reqmodel = VersionModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        predefined_datatype_service.delete_version(name=reqmodel.name, version=reqmodel.version)

        return success_response({"ErrorCode": 0, "Data": "Analyzer package deleted"})
    except PermissionError as e:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 403)
    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@analyzer_version.route("/", methods=['PUT'])
def update_version():
    from service import predefined_datatype_service
    from .analyzer_version_reqmodel import VersionModel

    try:
        try:
            args = request.args.to_dict()
            reqmodel = VersionModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        version = predefined_datatype_service.update_current_version(name=reqmodel.name, version=reqmodel.version)
        if version is None:
            return error_response(Ecodes.INTERNAL_ERROR, 400, f"update {reqmodel.name} version failed")

        restart_result = subprocess.run(['supervisorctl', 'restart', 'analyze_worker'], capture_output=True, text=True)
        logger.info(f"analyzer restart: {restart_result}")

        return success_response({"ErrorCode": 0, "Data": version.to_dict()})
    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

