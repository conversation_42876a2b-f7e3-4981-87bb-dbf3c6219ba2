import subprocess
import traceback
import os
from flask import Blueprint, jsonify, request
from util.common_log import get_logger
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from pydantic import ValidationError
from service.predefined_datatype_service import G<PERSON><PERSON><PERSON><PERSON>_CONFIG
from util.enum_ import AnalyzerPackageUpdateMode
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from flask_module import session
from system.dlp_license import is_licensed
from system.system_upload import save_upload_file
from file_analyzer.util.file_op import clear_directory
from service.analyzer_package_service import AnalyzerPackageService
from file_analyzer.util.enum import AnalyzerPackageType, PACKAGE_NAME_MAPPING 
from file_analyzer.util.file_op import delete_folder_recursive
from flask_module.controller.system import check_disk_space
import shutil

logger = get_logger("api")
analyzer_package = Blueprint("analyzer_package", __name__)

@analyzer_package.route("/upload", methods=['POST'])
def upload_analyzer_package():
    if not is_licensed():
        return error_response(Ecodes.UNAUTHORIZED_ERROR, 403, "Uploading package is prohibited without a valid license.")
    try:
        from flask_module.analyzer_package_reqmodel import AnalyzerPackageUploadModel
        args = request.args.to_dict()
        req_model = AnalyzerPackageUploadModel(**args)

        if not request.files:
            return error_response(Ecodes.VALIDATION_ERROR, 400, "No package specified")
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        if not check_disk_space(threshold_gb=15):
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, f"Disk space is less than 15GB.")

        tmp_path = GLOBAL_CONFIG.get("file_analyzer", {}).get("tmp_config_path")
        if req_model.clear:
            delete_folder_recursive(tmp_path)
        upload = request.files.get("file")
        if not upload.filename.endswith(".pkg"):
            return error_response(Ecodes.VALIDATION_ERROR, 400, "Invalid file format: expected a .pkg file.")

        file_size = int(request.headers['Content-Range'].split('/')[-1])
        _, _, free = shutil.disk_usage("/var/log")
        if free-file_size < 10*1024*1024*1024:
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, f"Not enough disk space.")
        
        file_path = os.path.join(tmp_path, os.path.basename(upload.filename))
        errno, errmsg = save_upload_file(request=request, upload=upload, pathfile=file_path, upload_path=tmp_path)
        if errno != 0:
            return error_response(Ecodes.VALIDATION_ERROR, 400, errmsg)
        if errmsg.startswith("Chunk saved"):
            return success_response({"ErrorCode": 0, "Data": "Chunk saved"})
        
        try:
            version, _, package_type = AnalyzerPackageService.get_package_info(file_path=file_path)
        except Exception as e:
            logger.error(e)
            delete_folder_recursive(folder_path=file_path)
            return error_response(Ecodes.VALIDATION_ERROR, 400, f"Invalid package")
        
        if package_type != req_model.type:
            delete_folder_recursive(folder_path=file_path)
            return error_response(Ecodes.VALIDATION_ERROR, 400, f"Package type mismatch: expected '{PACKAGE_NAME_MAPPING.get(req_model.type, '')}', but got '{PACKAGE_NAME_MAPPING.get(package_type, '')}'.")
        
        return success_response({"ErrorCode": 0, "Data": f"Package uploaded, version: {version}"})
    except Exception as e:
        logger.error(e)
        delete_folder_recursive(folder_path=file_path)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)

@analyzer_package.route("/update", methods=['POST'])
def update_analyzer_package():
    if not is_licensed():
        return error_response(Ecodes.UNAUTHORIZED_ERROR, 403, "Updating package is prohibited without a valid license.")

    try:
        from flask_module.analyzer_package_reqmodel import AnalyzerPackageUpdateModel
        data = request.get_json()
        req_model = AnalyzerPackageUpdateModel(**data)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)

    try:
        updated_packages = {}
        error_msg = {}
        package_service = AnalyzerPackageService()
        for package_type in AnalyzerPackageType:
            try:
                if getattr(req_model, package_type.value):
                    updated = False
                    package_file = getattr(req_model, package_type.value)
                    if req_model.mode == AnalyzerPackageUpdateMode.MANUAL:
                        package_file = os.path.join(GLOBAL_CONFIG.get("file_analyzer", {}).get("tmp_config_path"), package_file)
                    updated, updated_version = package_service.load_package(
                        package_type=package_type.value, 
                        file_path=package_file, 
                        overwrite=req_model.overwrite
                    )
                    if updated:
                        package_service.update_version(package_type=package_type.value, version=updated_version)
                        updated_packages[PACKAGE_NAME_MAPPING[package_type.value]] = updated_version
            except Exception as e:
                logger.error(e)
                error_msg[PACKAGE_NAME_MAPPING[package_type.value]] = str(e)
            
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)

    logger.info(f"Analyzer package modified: {updated_packages}")

    if updated_packages:
        restart_result = subprocess.run(['supervisorctl', 'restart', 'analyze_worker'], capture_output=True, text=True)
        output = restart_result.stdout.strip()
        if "analyze_worker: started" not in output:
            logger.error(output)
            return error_response(Ecodes.INTERNAL_ERROR, 500, output)
        logger.info(f"analyzer restart: {restart_result}")
        formatted_str = ', '.join([f"{key}: {value}" for key, value in updated_packages.items()])
        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Upgrade packages: {formatted_str}",
            desc="Upgrade packages",
            action=LogAction.UPGRADE.value,
            type=LogType.ANALYZER_PACKAGE.value
        )
        if error_msg:
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, f"The following packages were updated successfully: {updated_packages}, The following packages failed to update: {error_msg}")
        return success_response({"ErrorCode": 0, "Data": f"Package loaded successfully: {updated_packages}"})
    else:
        if error_msg:
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, error_msg)
        return success_response({"ErrorCode": 0, "Data": "No update required."})
