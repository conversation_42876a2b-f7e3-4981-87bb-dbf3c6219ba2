import uuid, re
from util.common_log import get_logger
from pydantic import BaseModel, validator, root_validator, EmailStr, ValidationError
from pydantic import Field, constr, conint, confloat
from typing import List, Optional, Literal, Union, Dict
from enum import Enum
import ipaddress
from flask_module.base import validate_condition_relation_string

logger = get_logger("api")

# Define a Pydantic model for individual entries
class MatchConditionEntryModel(BaseModel):
    # required fields
    type: Literal['data_type', 'custom_data_type', 'ml_category', 'file_type'] = Field(..., example="", description="condition type")

    # optional fields
    #for data_type and custom_data_type
    entity: str = Field(None, example="U.S. Social Security number (SSN)", description="data type name")
    entity_id: str = Field(None, example="20001", description="data type id")
    category: str = Field(None, example="10003", description="data type id")
    match_count: int = Field(None, example=1, description="match times")
    confidence: Optional[conint(ge=0, le=100)] = Field(0, example=60, description="confidence score threshold")

    # for ml_category
    main_class_id: str = Field(None, example="10000", description="main category of the plain text")
    sub_class_id: str = Field(None, example="100001", description="sub category of the plain text")

    # for file_type
    file_ext: str = Field(None, example="xlsx", description="file type")
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @root_validator(skip_on_failure=True)
    def check_datatypes(cls, values):
        type = values.get('type')
        attrs = ['entity', 'entity_id', 'match_count', 'category']
        if type in ["data_type", "custom_data_type"]:
            if not all(attr in values.keys() and values.get(attr) for attr in attrs):
                raise ValueError(f'the field entity/entity_id/match_count/category must be specified when type is {type}')
        elif type == 'file_type':
            if not values.get('file_ext'):
                raise ValueError(f'the field file_ext must be specified when type is {type}')
        return values

    # Available only for type is data_type and custome_data_type
    @validator('entity', 'entity_id', 'match_count', 'category')
    def data_type_fields_validator(cls, v, values, **kwargs):
        type = values['type']
        if type in ["data_type", "custom_data_type"]:
            if not v:
                raise ValueError(f'the field entity/entity_id/match_count/category must be specified when type is {type}')
            return v
        else:
            return None

    # Available only for type is file_type
    @validator('file_ext')
    def file_type_fields_validator(cls, v, values, **kwargs):
        type = values['type']
        if type == 'file_type':
            if not v:
                raise ValueError(f'the field file_ext must be specified when type is {type}')
            return v
        else:
            return None

class DLPRuleMatchConditionRelationReqModel(BaseModel):
    type: Literal[1, 2, 3] = Field(..., description="Condition relations 1: all conditions are matched, 2: at least one condition is matched, 3: custom")
    custom_relations: str = Field(None, min_length=1, max_length=512, example="( 1 and 2 ) or ( 1 and 3 ) or 4", description="custom condition relation")
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('custom_relations')
    def relation_validator(cls, v, values, **kwargs):
        type = values['type']
        # only for type is 3
        if type == 3:
            result, error = validate_condition_relation_string(v)
            if result:
                return v
            else:
                raise ValueError(f'Invalid custom_relations {v}. error: {error}')
        else:
            # keep the custome_relation
            return v

class Action(str, Enum):
    allow = "Allow"
    alert = "Alert"
    deny = "Deny"
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

class EndpointChannel(BaseModel):
    removable_device: Action
    printing: Action
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

class NetworkChannel(BaseModel):
    ftp: Action
    http: Action
    https: Action
    scp: Action
    email: Action
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

class DLPRuleActionReqModel(BaseModel):
    endpoint_channel: Optional[EndpointChannel]
    network_channel: NetworkChannel
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

class AddressType(str, Enum):
    email = "email"
    email_group = "email_group"
    ip = "ip"
    ip_range = "ip_range"
    ip_cidr = "ip_cidr"
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

class EmailModel(BaseModel):
    email: EmailStr

class Info(BaseModel):
    address_type: AddressType
    address: str

    @validator('address', always=True)
    def check_address(cls, v, values):
        #logger.info(f"check_address v: {v}, values: {values}")
        if v != 'any':
            address_type = values.get('address_type', 'NA')
            if address_type == AddressType.ip:
                try:
                    ipaddress.ip_address(v)
                except ValueError:
                    raise ValueError(f"Invalid ip address {v}")
            elif address_type == AddressType.ip_range:
                try:
                    start_ip, end_ip = v.split('-')
                    start_ip = ipaddress.ip_address(start_ip)
                    end_ip = ipaddress.ip_address(end_ip)
                    if start_ip.version != end_ip.version:
                        raise ValueError(f"Invalid ip range {v}, ip versions different")
                    if start_ip > end_ip:
                        raise ValueError(f"Invalid ip range {v}, start ip is greater than end ip")
                except ValueError:
                    raise ValueError(f"Invalid ip range {v}")
            elif address_type == AddressType.ip_cidr:
                try:
                    ipaddress.ip_network(v, strict=True)
                except ValueError:
                    raise ValueError(f"Invalid ip cidr {v}")
            elif address_type == AddressType.email:
                try:
                    EmailModel(email=v)
                except ValidationError:
                    raise ValueError(f"Invalid email address {v}")
        return v

class Address(BaseModel):
    src_info: Info
    dst_info: Info

class DLPRuleCreateReqModel(BaseModel):
    # required fields
    name: str = Field(..., min_length=1, max_length=100, example="Rule1", description="DLP rule name")
    policy_id: str = Field(..., min_length=36, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="valid UUID format string")
    match_condition: Dict[str, MatchConditionEntryModel]
    match_condition_relation: DLPRuleMatchConditionRelationReqModel
    action: DLPRuleActionReqModel = None

    address: Dict[str, List[Address]] = Field(..., example={
        "email": [
            {
                "src_info": {
                    "address": "any",
                    "address_type": "email"
                },
                "dst_info": {
                    "address": "group1",
                    "address_type": "email_group"
                }
            }
        ],
        "network": [
            {
                "src_info": {
                    "address": "*******",
                    "address_type": "ip"
                },
                "dst_info": {
                    "address": "***********-************",
                    "address_type": "ip_range"
                }
            }
        ]
    }, description="List of address information")

    match_finger_print: int = Field(1, example=1, description="match fingerprint")
    file_finger_print: List[uuid.UUID] = Field(..., example=['916aeae8-3b10-4b44-b597-b9eca337fbaf'], description="List of valid UUID format strings")
    @validator('file_finger_print', each_item=True)
    def ffp_uuid_validator(cls, v):
        try:
            uuid.UUID(str(v))
        except ValueError:
            raise ValueError(f"Invalid uuid {v}")
        return v

    # optional fields
    description: str = Field("", min_length=0, max_length=256, example="This is a DLP rule")
    severity: Literal[1, 2, 3] = Field(2, description="The severity of this rule. 1:High, 2 : Medium 3:Low")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('policy_id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

    @root_validator(skip_on_failure=True)
    def check_custom_relations(cls, values):
        match_condition = values.get('match_condition')
        match_condition_relation = values.get('match_condition_relation')
        if match_condition and match_condition_relation and match_condition_relation.type == 3:
            condition_ids = set(match_condition.keys())
            custom_relations = match_condition_relation.custom_relations.replace('(', '').replace(')', '').replace('AND', '').replace('OR', '').replace('NOT', '').split()
            for cond_id in custom_relations:
                if cond_id not in condition_ids:
                    raise ValueError(f"the condition id '{cond_id}' in match_condition_relation is not present in rule conditions")

        return values

class DLPRuleQueryReqModel(BaseModel):
    # optional fields
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    name: str = Field(None, min_length=1, max_length=100, example="Policy1", description="DLP rule name")
    description: str = Field(None, min_length=0, max_length=256, example="This is a DLP rule")
    severity: conint(ge=1, le=3) = Field(None, description="The severity of this rule. 1:High, 2 : Medium 3:Low")
    policy_id: constr(min_length=36, max_length=36) = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")

    sort_field: str = Field('updated_at', max_length=32, description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: conint(ge=0) = Field(None, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class DLPRuleUpdateReqModel(BaseModel):
    # required fields
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")

    # optional fields
    name: str = Field(None, min_length=1, max_length=100, example="Rule1", description="DLP rule name")
    policy_id: str = Field(None, min_length=36, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="valid UUID format string")
    match_condition: Dict[str, MatchConditionEntryModel] = None
    match_condition_relation: DLPRuleMatchConditionRelationReqModel = None
    action: DLPRuleActionReqModel = None

    address: Dict[str, List[Address]] = Field(None, example={
        "email": [
            {
                "src_info": {
                    "address": "any",
                    "address_type": "email"
                },
                "dst_info": {
                    "address": "group1",
                    "address_type": "email_group"
                }
            }
        ],
        "network": [
            {
                "src_info": {
                    "address": "*******",
                    "address_type": "ip"
                },
                "dst_info": {
                    "address": "***********-************",
                    "address_type": "ip_range"
                }
            }
        ]
    }, description="List of address information")

    match_finger_print: int = Field(None, example=1, description="match fingerprint")
    file_finger_print: List[uuid.UUID] = Field(None, example=['916aeae8-3b10-4b44-b597-b9eca337fbaf'], description="List of valid UUID format strings")
    @validator('file_finger_print', each_item=True)
    def ffp_uuid_validator(cls, v):
        try:
            uuid.UUID(str(v))
        except ValueError:
            raise ValueError(f"Invalid uuid {v}")
        return v
    description: str = Field(None, min_length=0, max_length=256, example="This is a DLP rule")
    severity: Literal[1, 2, 3] = Field(None, description="The severity of this rule. 1:High, 2 : Medium 3:Low")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

    @root_validator(skip_on_failure=True)
    def check_custom_relations(cls, values):
        match_condition = values.get('match_condition')
        match_condition_relation = values.get('match_condition_relation')
        if match_condition and match_condition_relation:
            if match_condition_relation.type == 3:
                condition_ids = set(match_condition.keys())
                custom_relations = match_condition_relation.custom_relations.replace('(', '').replace(')', '').replace('AND', '').replace('OR', '').replace('NOT', '').split()
                for cond_id in custom_relations:
                    if cond_id not in condition_ids:
                        raise ValueError(f"the condition id '{cond_id}' in match_condition_relation is not present in rule conditions")
        elif (match_condition and not match_condition_relation) or (not match_condition and match_condition_relation):
            raise ValueError(f"the match_condition and match_condition_relation fields must be committed at the same time")

        return values

class DLPRuleDeleteReqModel(BaseModel):
    # required fields
    id: List[constr(min_length=36, max_length=36)]  = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v