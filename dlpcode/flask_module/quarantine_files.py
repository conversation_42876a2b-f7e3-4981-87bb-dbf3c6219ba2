import traceback
from util.common_log import get_logger
from flask import Blueprint, request
from util.err_codes import error_response, success_response
from util.err_codes import ModErrCode
from huey_worker.protection_action_task import protection_task_file_quarantine
from flask_module import session
from domain_model.global_cache import GlobalCache
from util.enum_ import ProtectionQuarantineStatus, ProtectionCopyStatus

logger = get_logger("api")
quarantine_files = Blueprint("quarantine_files", __name__)

@quarantine_files.route('/', methods=['POST'])
def do_quarantine_files():
    try:
        data = request.get_json()
        user = session.get_user(request)
        logger.info(f"quarantine files {data}")
        for item in data:
            if "file_uuid" not in item or "scan_uuid" not in item:
                return error_response(ModErrCode.ErrCode02050024, 400) 
            file_uuid = item["file_uuid"]
            scan_uuid = item["scan_uuid"]
            cache = GlobalCache(scan_uuid)
            fr = cache.get_by_file_uuid(file_uuid=file_uuid)
            protection_status = fr.get("reserve_json3") or {}
            if protection_status.get('copy_status', ProtectionCopyStatus.INIT) == ProtectionCopyStatus.SUCCESS:
                return error_response(ModErrCode.ErrCode02050035, 400, extend_message=f"file name: {fr.get('file_name', '')}") 
            if protection_status.get("quarantine_status", ProtectionQuarantineStatus.INIT) == ProtectionQuarantineStatus.SUCCESS:                
                return error_response(ModErrCode.ErrCode02050029, 400, extend_message=f"file name: {fr.get('file_name', '')}") 

        for item in data:
            file_uuid = item["file_uuid"]
            scan_uuid = item["scan_uuid"]    
            protection_task_file_quarantine(scan_uuid, file_uuid, user=user)
     
        return success_response({'message': 'The quarantine file task has been successfully assigned, please refresh the page to view the status.'}, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02050025, 500)