""" Pydantic models for Queries APIs """

from typing import Optional, Dict, List, Literal, Any
from pydantic import BaseModel, Field, validator, constr, conint


class QueryBaseModel(BaseModel):
    """Class representing the base pydantic model for a query."""

    name: constr(regex=r"^[a-zA-Z0-9_-]+$")
    query_type: int = Field(..., ge=0, le=1, description="Query Type (0 or 1)")
    description: Optional[str] = Field(None, description="Query Description")
    query_string: Dict[str, List[Any]] = Field(
        ...,
        description="Query string with keys as strings and list values (any type inside list).",
    )

    @validator("query_string")
    def validate_query_string(cls, value):
        """Function to validate the query_string input parameter."""
        if not isinstance(value, dict):
            raise ValueError("query_string must be a dictionary.")
        for k, v in value.items():
            if not isinstance(k, str):
                raise ValueError("All keys in query_string must be strings.")
            if not isinstance(v, list):
                raise ValueError(
                    f"All values in query_string[{k}] must be lists (any type inside)."
                )
        return value


class QueryEditModel(BaseModel):
    """Class representing the pydantic model for editing a query."""

    id: str  # Add the query ID for editing
    name: Optional[constr(regex=r"^[a-zA-Z0-9_-]+$")] = None
    description: Optional[str] = Field(None, description="Query Description")
    query_string: Optional[Dict[str, List[Any]]] = Field(
        None,
        description="Query string with keys as strings and values as list of strings",
    )


class QueryDeleteModel(BaseModel):
    """Class representing the pydantic model for deleting a query."""

    ids: List[str] = Field(..., description="List of UUIDs of the query to update")


class QueryListModel(BaseModel):
    """Class representing the pydantic model for listing a query."""

    id: constr(min_length=36, max_length=36) = Field(
        None,
        example="916aeae8-3b10-4b44-b597-b9eca337fbaf",
        description="valid UUID format string",
    )
    name: str = Field(
        None,
        min_length=1,
        max_length=100,
        example="test_query_1",
        description="Query name",
    )
    description: str = Field(None, description="Query description")
    page: conint(ge=0) = Field(1, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")
    sort_field: str = Field("created_at", max_length=32, description="sort field name")
    sort_method: Literal["desc", "asc"] = Field("asc", description="sort method")
    query_type: int = Field(None, ge=0, le=1, description="Query Type (0 or 1)")

    # Extra inputs are not permitted
    class Config:
        """Config class of the QueryListModel class."""

        extra = "forbid"
