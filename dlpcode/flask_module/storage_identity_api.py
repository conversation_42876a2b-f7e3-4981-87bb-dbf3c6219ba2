from flask import Blueprint, request
from util.common_log import get_logger
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from pydantic import ValidationError

logger = get_logger("api")
storage_identity = Blueprint('storage_identity', __name__)

@storage_identity.route('/', methods=['GET'])
def handle_storage_identity_get():
    from .storage_identity_reqmodel import GetStorageIdentityReqModel
    from storage.service.identity import get_storage_identity, get_sensitive_file_stats
    try:
        data = request.args.to_dict()
        req_model = GetStorageIdentityReqModel(**data)
        data = req_model.dict(exclude_none=True)
        identities, count = get_storage_identity(conditions=data)

        for ident in identities:
            owned, shared, access = get_sensitive_file_stats(ident['sid'], ident['identifier'])
            ident['sensitive_owned'] = owned
            ident['sensitive_shared'] = shared
            ident['sensitive_access'] = access

        return success_response({
            'list': identities,
            'page': req_model.page,
            'per_page': req_model.per_page,
            'total': count
        })
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, str(e))
    except Exception as e:
        logger.exception(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, str(e))
    
# @storage_identity.route('/all', methods=['GET'])
# def handle_storage_identity_get_all():
#     from .storage_identity_reqmodel import GetAllStorageIdentityReqModel
#     from storage.service.identity import get_storage_identity_summary
#     try:
#         data = request.args.to_dict()
#         req_model = GetAllStorageIdentityReqModel(**data)
#         identities = get_storage_identity_summary(req_model.sid)
#         return success_response(identities)
#     except ValidationError as e:
#         logger.error(e)
#         return error_response(Ecodes.VALIDATION_ERROR, 400, str(e))
#     except Exception as e:
#         logger.exception(e)
#         return error_response(Ecodes.INTERNAL_ERROR, 500, str(e))

@storage_identity.route('/details', methods=['GET'])
def handle_storage_identity_details_get():
    from .storage_identity_reqmodel import GetStorageIdentityDetailReqModel
    from storage.service.identity import get_storage_identity_details
    try:
        data = request.args.to_dict()
        req_model = GetStorageIdentityDetailReqModel(**data)
        details = get_storage_identity_details(req_model.sid, req_model.identifier)
        if details is None:
             return error_response(Ecodes.NOT_FOUND, 404, "Storage identity not found")
        return success_response(details)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, str(e))
    except Exception as e:
        logger.exception(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, str(e))