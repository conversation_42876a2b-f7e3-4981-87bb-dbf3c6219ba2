import traceback
from flask import Blueprint
from flask import request, jsonify
from util.common_log import get_logger
from flask_module.controller import user as ctrl_user
from flask_module.user_reqmodel import (GetUserModel, PostUserModel, DeleteUserModel,
                                        EditUserModel, EditUserPasswordModel)
from pydantic import ValidationError
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes

logger = get_logger("api")

user = Blueprint("user", __name__)


@user.route("/setup", methods=["GET", "POST", "PUT", "DELETE"])
def setup():
    if request.method == 'GET':
        try:
            validated_params = GetUserModel(**request.args.to_dict())
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            user_id = validated_params.id
            if user_id:
                ret = ctrl_user.get_user_by_id(user_id)
                if ret.get('ErrorCode', 0) != 0:
                    return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
                return success_response(ret)

            # TODO: Open check admin permission
            # if ctrl_user.check_admin_permission(request):
            #     return success_response(ctrl_user.get_user_list())
            # else:
            #     return jsonify({'error': 'Permission denied!'}), 404

            return success_response(ctrl_user.get_user_list(validated_params))
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == 'POST':
        # if not ctrl_user.check_admin_permission(request):
        #     return jsonify({'error': 'Permission denied!'}), 404

        try:
            validated_params = PostUserModel(**request.json)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            user_config = {
                'name': validated_params.name,
                'password': validated_params.password,
                'roles': validated_params.roles,
                'type': validated_params.type
            }
            ret = ctrl_user.save_pre_check_config(user_config)
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
            ret = ctrl_user.add_user(user_config, request)
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
            return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == 'DELETE':
        # if not ctrl_user.check_admin_permission(request):
        #     return jsonify({'error': 'Permission denied!'}), 404
        try:
            validated_params = DeleteUserModel(**request.args.to_dict(flat=False))
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            ret = ctrl_user.delete_user(ids=validated_params.id, request=request)
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
            return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == 'PUT':
        # if not ctrl_user.check_greater_permission(request):
        #     return jsonify({'error': 'Permission denied!'}), 404

        try:
            validated_params = EditUserModel(**request.json)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            update_data = {
                '_id': validated_params.id,
                'name': validated_params.name,
                'roles': validated_params.roles,
                'type': validated_params.type
            }
            ret = ctrl_user.update_user_role(update_data, request)
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
            return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@user.route("/modify_password", methods=["POST"])
def modify_admin_user_password():
    # if not ctrl_user.check_admin_permission(request):
    #     return jsonify({'error': 'Permission denied!'}), 404

    try:
        validated_params = EditUserPasswordModel(**request.json)
    except ValidationError as e:
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)

    try:
        user_config = {
            '_id': validated_params.id,
            'oldPsw': validated_params.old_psw,
            'newPsw': validated_params.new_psw,
            'cfmNewPsw': validated_params.cfm_new_psw
        }
        ret = ctrl_user.update_user_password(user_config, request)
        if ret.get('ErrorCode', 0) != 0:
            return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
        return success_response(ret)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@user.route("/login", methods=["POST"])
def login():
    try:
        errcode, response = ctrl_user.login_user(request)
        if errcode != 0:
            return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, response)
        # When login success, the response will be updated.
        return response, 200
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@user.route("/logout", methods=["GET"])
def logout():
    try:
        # When logout success, the response will be updated.
        return ctrl_user.logout_user(request), 200
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@user.route("/checkLogin", methods=["GET"])
def check_login():
    try:
        ret = ctrl_user.check_login(request)
        if ret.get('ErrorCode', 0) != 0:
            return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
        return success_response(ret)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@user.route("/role_list", methods=["GET"])
def get_role_list():
    try:
        ret = ctrl_user.get_role_list(request.args.to_dict())
        return success_response(ret)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

