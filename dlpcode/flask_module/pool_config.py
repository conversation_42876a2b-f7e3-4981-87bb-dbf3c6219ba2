import traceback
from datetime import datetime
from flask import Blueprint, request, jsonify
from pydantic import ValidationError
from util.common_log import get_logger
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes

logger = get_logger("dlp")
pool_config = Blueprint("pool_config", __name__)


@pool_config.route('/', methods=['POST'])
def handle_pool_config_create():
    from flask_module.pool_config_reqmodel import PoolConfigCreateReqModel
    from service import pool_config_service
    from psycopg2.errors import UniqueViolation

    try:
        # create a new pool_config
        data = request.get_json()
        try:
            reqmodel = PoolConfigCreateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")

        new_config = pool_config_service.PoolConfig()
        new_config.pool_name = data.get('pool_name')
        new_config.description = data.get('description')
        new_config.email_group_list = data.get('email_group_list')
        pool_config = pool_config_service.create_pool_config(new_config)
        return success_response(pool_config.to_dict(), 201)
    except UniqueViolation:
        return error_response(Ecodes.DUPLICATE_RESOURCE, 409, "A duplicate Email Pool Name already exists.")
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@pool_config.route('/', methods=['GET'])
def handle_pool_config_get():
    from flask_module.pool_config_reqmodel import PoolConfigQueryReqModel
    from service import pool_config_service

    try:
        pool_list = []
        # read an existing pool_config
        try:
            args = request.args.to_dict()
            logger.info(f"Get pool config by args {args}")
            reqmodel = PoolConfigQueryReqModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")

        pool_id = reqmodel.id
        if pool_id is None:
            # If no id is provided, return all pool_configs
            page = reqmodel.page
            per_page = reqmodel.per_page
            pool_configs, total =  pool_config_service.read_all_pool_configs(page, per_page)
            pool_list = [pool_config.to_dict() for pool_config in pool_configs]
            return success_response({
                "list": pool_list,
                "total": total,
                "page": reqmodel.page,
                "per_page": reqmodel.per_page,
            }, 200)
        else:
            pool_config =  pool_config_service.read_pool_config(pool_id)
            if pool_config is None:
                return error_response(Ecodes.NOT_FOUND, 404)
            pool_list.append(pool_config.to_dict())
            return success_response({
                "list": pool_list,
                "total":1,
                "page": 1,
                "per_page": 10,
            }, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@pool_config.route('/', methods=['PUT'])
def handle_pool_config_update():
    from flask_module.pool_config_reqmodel import PoolConfigUpdateReqModel
    from service import pool_config_service

    try:
        # update an existing pool_config
        data = request.get_json()
        try:
            reqmodel = PoolConfigUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")

        updated_pool_config = pool_config_service.update_pool_config(reqmodel.id, reqmodel.dict(exclude_none=True))
        if updated_pool_config is None:
            return error_response(Ecodes.NOT_FOUND, 404)
        return success_response(updated_pool_config.to_dict(), 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@pool_config.route('/', methods=['DELETE'])
def handle_pool_config_delete():
    from flask_module.pool_config_reqmodel import PoolConfigDeleteReqModel   
    from service import pool_config_service

    try:
        # delete an existing pool_config
        try:
            args = request.args.to_dict()
            logger.info(f"Deleting pool config by args {args}")
            reqmodel = PoolConfigDeleteReqModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")

        pool_id = reqmodel.id
        success =  pool_config_service.delete_pool_config(pool_id)
        if not success:
            return error_response(Ecodes.NOT_FOUND, 404)
        return success_response({'message': 'Pool config deleted'}, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@pool_config.route('/list_groups', methods=['GET'])
def handle_list_groups():
    from domain_model.ad_cache import ADCache
    from flask_module.pool_config_reqmodel import ListGroupsQueryReqModel

    try:
        # list the folders for the storage
        try:
            args = request.args.to_dict()
            logger.info(f"List folders  for storage{args}")
            reqmodel = ListGroupsQueryReqModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")

        page = reqmodel.page
        per_page = reqmodel.per_page
        ad_cache = ADCache()
        groups_list, total = ad_cache.get_group_data(1, page, per_page)
        if groups_list is None:
            return success_response({
                "list": []
            }, 200)
        if page == None and per_page == None:
            return success_response({
                "list": groups_list
            }, 200)
        else:
            return success_response({
                "list": groups_list,
                "total": total,
                "page": reqmodel.page,
                "per_page": reqmodel.per_page,
            }, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)
