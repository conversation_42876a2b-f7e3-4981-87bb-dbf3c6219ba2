from flask import Blueprint, request
from notifications.notifier.factory import notifier_factory
from notifications.model.notification import NotificationConnector
from notifications.model.ticket import Ticket
from exts import Session
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from util.common_log import get_logger
from notifications.enums import TicketTypeEnum

logger = get_logger("api")
ticket = Blueprint('ticket', __name__)


@ticket.route('/', methods=['POST'])
def create_ticket():
    """
    Create a new ticket in an external system, then record it locally.
    Expects JSON body with required fields:
      - template_id: Template ID
      - notifier_type: Notifier type (e.g. jira/servicenow)
      - context: dict
    """
    data = request.get_json()
    if not data:
        return error_response(Ecodes.VALIDATION_ERROR, 400, "Missing JSON body")
    required_fields = ['template_id', 'notifier_type', 'context']
    for field in required_fields:
        if not data.get(field):
            return error_response(Ecodes.VALIDATION_ERROR, 400, f"Missing required field: {field}")
    if not TicketTypeEnum.is_valid(data['notifier_type']):
        return error_response(Ecodes.VALIDATION_ERROR, 400, f"Invalid notifier_type: {data['notifier_type']}")

    session = Session()
    try:
        context = data.get('context', {})
        result = notifier_factory.send_notification(template_id=data['template_id'], context=context)
        if result.get('success') and 'data' in result:
            result = result['data']
        if not result.get('success', True):
            return error_response(Ecodes.INTERNAL_ERROR, 500, result.get('error', 'Failed to create ticket'))

        # Map result fields to local Ticket
        # Handle nested recipient and creator data
        recipient_data = result.get('recipient', {})
        creator_data = result.get('creator', {})
        
        # Handle different field names from different notifiers
        ticket_link = result.get('ticket_link') or result.get('ticket_url')
        recipient_name = recipient_data.get('recipient_name') or recipient_data.get('recipient_display_name')
        
        t = Ticket(
            ticket_key=result.get('ticket_key'),
            ticket_link=ticket_link,
            ticket_id=result.get('ticket_id'),
            notifier_type=data.get("notifier_type"),
            recipient_email=recipient_data.get('recipient_email'),
            recipient_name=recipient_name,
            ticket_creator=creator_data.get('ticket_creator'),  # May be None if not provided
            ticket_creator_email=creator_data.get('ticket_creator_email'),
            template_id=data.get('template_id')
        )
        session.add(t)
        session.commit()
        return success_response(t.to_dict())
    except Exception as e:
        session.rollback()
        logger.error(f"Failed to create ticket: {e}")
        return error_response(Ecodes.INTERNAL_ERROR, 500, f"Failed to create ticket: {e}")
    finally:
        session.close()

@ticket.route('/', methods=['GET'])
def get_ticket():
    """
    Query Ticket: filter by id if provided, otherwise return all tickets.
    """
    ticket_id = request.args.get('id')
    session = Session()
    try:
        if ticket_id:
            # Filter by id if provided
            tickets = session.query(Ticket).filter_by(id=ticket_id).all()
        else:
            # Return all tickets if no id
            tickets = session.query(Ticket).all()
        if not tickets:
            return error_response(Ecodes.NOT_FOUND, 404, 'Ticket not found')
        return success_response({"list": [t.to_dict() for t in tickets]})
    finally:
        session.close()

@ticket.route('/', methods=['DELETE'])
def unlink_ticket():
    """
    Delete a ticket by its id (UUID). Expects JSON body: {"id": "..."}
    Just delete local DB, not delete server ticket.
    """
    data = request.get_json()
    if not data or not data.get('id'):
        return error_response(Ecodes.VALIDATION_ERROR, 400, "Missing ticket id in request body")
    ticket_id = data['id']
    session = Session()
    try:
        t = session.query(Ticket).filter_by(id=ticket_id).first()
        if not t:
            return error_response(Ecodes.NOT_FOUND, 404, "Ticket not found")
        session.delete(t)
        session.commit()
        return success_response({"deleted": ticket_id})
    except Exception as e:
        session.rollback()
        logger.error(f"Failed to delete ticket: {e}")
        return error_response(Ecodes.INTERNAL_ERROR, 500, f"Failed to delete ticket: {e}")
    finally:
        session.close() 


@ticket.route('/project', methods=['GET'])
def list_projects():
    """
    Get projects list from jira/ServiceNow.
    """
    connector_id = request.args.get('connector_id')
    template_id = request.args.get('template_id')

    if not connector_id and not template_id:
        return error_response(Ecodes.VALIDATION_ERROR, 400, 'id is required')

    msg, notifier = notifier_factory.get_notifier_by_id(connector_id, template_id)
    if not notifier:
        return error_response(Ecodes.NOT_FOUND, 400, msg or 'Notifier not found')
    if not hasattr(notifier, 'get_project_list'):
        return error_response(Ecodes.NOT_FOUND, 400, 'API not supported for this notifier')

    result = notifier.get_project_list()
    if isinstance(result, dict) and result.get('success', True):
        return success_response({"list": result.get('data', [])})
    else:
        return error_response(Ecodes.INTERNAL_ERROR, 400, result.get('error', 'Failed to get project list'))


@ticket.route('/user', methods=['GET'])
def list_users():
    """
    Get user list from Jira/ServiceNow.
    
    Query parameters:
    - connector_id: Connector ID (required)
    - template_id: Template ID (required)
    - project_key: Project key (optional)
    - query: Search query (optional)
    - filter_system: whether to filter system/demo users (default true)
    """
    connector_id = request.args.get('connector_id')
    template_id = request.args.get('template_id')
    project_key = request.args.get('project_key')
    per_page = int(request.args.get('per_page', 0))
    page = int(request.args.get('page', 1)) - 1
    query = request.args.get('query', '')  # Optional search query
    filter_system = request.args.get('filter_system', 'true').lower() == 'true'

    if not connector_id and not template_id:
        return error_response(Ecodes.VALIDATION_ERROR, 400, 'id is required')
    if page < 0 or per_page < 0:
        return error_response(Ecodes.VALIDATION_ERROR, 400, 'page/per_page config error')

    msg, notifier = notifier_factory.get_notifier_by_id(connector_id, template_id)
    if not notifier:
        return error_response(Ecodes.NOT_FOUND, 400, msg or 'Notifier not found')
    if not hasattr(notifier, 'get_user_list'):
        return error_response(Ecodes.NOT_FOUND, 400, 'API not supported for this notifier')

    result = notifier.get_user_list(query, project_key, per_page, page, filter_system)
    if isinstance(result, dict) and result.get('success', True):
        users = result.get('users', [])
        simple_users = [
            {"accountId": u.get("accountId") or u.get("sys_id"), 
            "displayName": u.get("displayName") or u.get("name")} 
            for u in users if isinstance(u, dict)
        ]
        return success_response({"list": simple_users})
    else:
        return error_response(Ecodes.INTERNAL_ERROR, 400, result.get('error', 'Failed to get user list'))

@ticket.route('/label', methods=['GET'])
def list_labels():
    """
    Get label list from jira.
    """
    connector_id = request.args.get('connector_id')
    template_id = request.args.get('template_id')

    if not connector_id and not template_id:
        return error_response(Ecodes.VALIDATION_ERROR, 400, 'id is required')

    msg, notifier = notifier_factory.get_notifier_by_id(connector_id, template_id)
    if not notifier:
        return error_response(Ecodes.NOT_FOUND, 400, msg or 'Notifier not found')
    if not hasattr(notifier, 'get_labels'):
        return error_response(Ecodes.NOT_FOUND, 400, 'API not supported for this notifier')

    result = notifier.get_labels()
    if isinstance(result, dict) and result.get('success', True):
        return success_response({"list": result.get('labels', [])})
    else:
        return error_response(Ecodes.INTERNAL_ERROR, 400, result.get('error', 'Failed to get label list'))

@ticket.route('/parent', methods=['GET'])
def list_parent_candidates():
    """
    Get parent list from jira.
    """
    connector_id = request.args.get('connector_id')
    template_id = request.args.get('template_id')
    project_key = request.args.get('project_key')
    issue_type = request.args.get('issue_type')
    max_results = request.args.get('max_results', 50, type=int)

    if (not connector_id and not template_id) or not issue_type or not project_key:
        return error_response(Ecodes.VALIDATION_ERROR, 400, 'id, issue_type, project_key is required')

    msg, notifier = notifier_factory.get_notifier_by_id(connector_id, template_id)
    if not notifier:
        return error_response(Ecodes.NOT_FOUND, 400, msg or 'Notifier not found')
    if not hasattr(notifier, 'get_parent_candidates'):
        return error_response(Ecodes.NOT_FOUND, 400, 'API not supported for this notifier')

    jql = f"issuetype = '{issue_type}'"
    result = notifier.get_parent_candidates(project_key=project_key, jql=jql, max_results=max_results)
    if isinstance(result, dict) and result.get('success', True):
        return success_response({"list": result.get('data', [])})
    else:
        return error_response(Ecodes.INTERNAL_ERROR, 400, result.get('error', 'Failed to get parent candidates'))

@ticket.route('/issue_type', methods=['GET'])
def list_issue_types():
    """
    Get issue type list from jira.
    """
    connector_id = request.args.get('connector_id')
    template_id = request.args.get('template_id')
    project_key = request.args.get('project_key')

    if (not connector_id and not template_id) or not project_key:
        return error_response(Ecodes.VALIDATION_ERROR, 400, 'id and project_key are required')

    msg, notifier = notifier_factory.get_notifier_by_id(connector_id, template_id)
    if not notifier:
        return error_response(Ecodes.NOT_FOUND, 400, msg or 'Notifier not found')
    if not hasattr(notifier, 'get_issue_types'):
        return error_response(Ecodes.NOT_FOUND, 400, 'API not supported for this notifier')

    result = notifier.get_issue_types(project_key)
    if isinstance(result, dict) and result.get('success', True):
        return success_response({"list": result.get('data', [])})
    else:
        return error_response(Ecodes.INTERNAL_ERROR, 400, result.get('error', 'Failed to get issue types'))

@ticket.route('/priority', methods=['GET'])
def list_priority():
    """
    Get priority list from Jira/ServiceNow.
    """
    connector_id = request.args.get('connector_id')
    template_id = request.args.get('template_id')

    if not connector_id and not template_id:
        return error_response(Ecodes.VALIDATION_ERROR, 400, 'id is required')

    msg, notifier = notifier_factory.get_notifier_by_id(connector_id, template_id)
    if not notifier:
        return error_response(Ecodes.NOT_FOUND, 400, msg or 'Notifier not found')
    if not hasattr(notifier, 'get_priorities'):
        return error_response(Ecodes.NOT_FOUND, 400, 'API not supported for this notifier')

    result = notifier.get_priorities()
    if isinstance(result, dict) and result.get('success', True):
        return success_response({"list": result.get('data', [])})
    else:
        return error_response(Ecodes.INTERNAL_ERROR, 400, result.get('error', 'Failed to get priority'))

@ticket.route('/category', methods=['GET'])
def list_categories():
    """
    Get category map from ServiceNow.
    """
    connector_id = request.args.get('connector_id')
    template_id = request.args.get('template_id')

    if not connector_id and not template_id:
        return error_response(Ecodes.VALIDATION_ERROR, 400, 'id is required')

    msg, notifier = notifier_factory.get_notifier_by_id(connector_id, template_id)
    if not notifier:
        return error_response(Ecodes.NOT_FOUND, 400, msg or 'Notifier not found')
    if not hasattr(notifier, 'get_categories_with_subcategories'):
        return error_response(Ecodes.NOT_FOUND, 400, 'API not supported for this notifier')

    result = notifier.get_categories_with_subcategories()
    if isinstance(result, dict) and result.get('success', True):
        return success_response({"list": result.get('data', [])})
    else:
        return error_response(Ecodes.INTERNAL_ERROR, 400, result.get('error', 'Failed to get categories'))

@ticket.route('/metadata', methods=['GET'])
def get_metadata():
    """
    Get metadata list from ServiceNow.
    """
    connector_id = request.args.get('connector_id')
    template_id = request.args.get('template_id')
    element = request.args.get('element', 'contact_type')
    table = request.args.get('table', 'incident')

    if not connector_id and not template_id:
        return error_response(Ecodes.VALIDATION_ERROR, 400, 'id is required')
    if not element:
        return error_response(Ecodes.VALIDATION_ERROR, 400, 'element is required')

    msg, notifier = notifier_factory.get_notifier_by_id(connector_id, template_id)
    if not notifier:
        return error_response(Ecodes.NOT_FOUND, 400, msg or 'Notifier not found')
    if not hasattr(notifier, 'get_metadata'):
        return error_response(Ecodes.NOT_FOUND, 400, 'API not supported for this notifier')

    result = notifier.get_metadata(name=table, element=element)
    if isinstance(result, dict) and result.get('success', True):
        return success_response({"list": result.get('data', [])})
    else:
        return error_response(Ecodes.INTERNAL_ERROR, 400, result.get('error', 'Failed to get metadata'))

@ticket.route('/service', methods=['GET'])
def list_services():
    """
    Get service list from ServiceNow.
    """
    connector_id = request.args.get('connector_id')
    template_id = request.args.get('template_id')
    per_page = int(request.args.get('per_page', 100))
    page = int(request.args.get('page', 1)) - 1

    if not connector_id and not template_id:
        return error_response(Ecodes.VALIDATION_ERROR, 400, 'id is required')
    if page < 0 or per_page < 0:
        return error_response(Ecodes.VALIDATION_ERROR, 400, 'page/per_page config error')

    msg, notifier = notifier_factory.get_notifier_by_id(connector_id, template_id)
    if not notifier:
        return error_response(Ecodes.NOT_FOUND, 400, msg or 'Notifier not found')
    if not hasattr(notifier, 'get_service_list'):
        return error_response(Ecodes.NOT_FOUND, 400, 'API not supported for this notifier')

    result = notifier.get_service_list(limit=per_page, page=page)
    if isinstance(result, dict) and result.get('success', True):
        return success_response({"list": result.get('services', [])})
    else:
        return error_response(Ecodes.INTERNAL_ERROR, 400, result.get('error', 'Failed to get service list'))
