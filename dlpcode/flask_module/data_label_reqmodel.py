from pydantic import BaseModel, validator
from pydantic import Field, constr, conint
from typing import List, Literal
import uuid


class PredefinedDataLabelQueryReqModel(BaseModel):
    # optional fields
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    name: str = Field(None, min_length=1, max_length=120, example="Label", description="predefined data label name")
    description: str = Field(None, min_length=0, max_length=512, example="This is a predefined data label")
    category: str = Field(None, min_length=1, max_length=256, example="Sensitivity", description="predefined data label category")
    status: conint(ge=0, le=1) = Field(None, description="predefined data label status")
    
    sort_field: Literal['id', 'name', 'description', 'category', 'status'] = Field('category', description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: conint(ge=0) = Field(None, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class PredefinedDataLabelUpdateReqModel(BaseModel):
    # required fields
    id: str = Field(..., min_length=36, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="valid UUID format string")
    status: conint(ge=0, le=1) = Field(..., description="predefined data label status")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class CustomDataLabelCreateReqModel(BaseModel):
    # required fields
    name: str = Field(..., min_length=1, max_length=120, example="Label", description="custom data label name")
    category: str = Field(..., min_length=1, max_length=256, example="Custom", description="custom data label category")
    status: conint(ge=0, le=1) = Field(1, description="custom data label status")

    # optional fields
    description: str = Field("", min_length=0, max_length=512, example="This is a custom data label")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

class CustomDataLabelQueryReqModel(BaseModel):
    # optional fields
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    name: str = Field(None, min_length=1, max_length=120, example="Label", description="custom data label name")
    description: str = Field(None, min_length=0, max_length=512, example="This is a custom data label")
    category: str = Field(None, min_length=1, max_length=256, example="Custom", description="custom data label category")
    status: conint(ge=0, le=1) = Field(None, description="custom data label status")
    
    sort_field: Literal['id', 'name', 'description', 'category', 'status', 'created_at'] = Field('created_at', description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: conint(ge=0) = Field(None, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class CustomDataLabelUpdateReqModel(BaseModel):
    # required fields
    id: str = Field(..., min_length=36, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="valid UUID format string")
    status: conint(ge=0, le=1) = Field(..., description="custom data label status")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class CustomDataLabelDelReqModel(BaseModel):
    # required fields
    id: List[constr(min_length=36, max_length=36)]  = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class CheckDataLabelExistenceReqModel(BaseModel):
    label: str = Field(..., min_length=1, max_length=120, example="Label", description="data label name")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"