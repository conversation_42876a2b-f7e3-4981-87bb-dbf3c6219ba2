import traceback

from flask import Blueprint, request
from pydantic import ValidationError

import util.err_codes as Ecodes
from flask_module import session
from util.err_codes import error_response, success_response
from system.system_log import record_event_log, LogLevel, LogType, LogAction
from util.common_log import get_logger

logger = get_logger("api")

device_access_stats = Blueprint('device_access_stats', __name__)


@device_access_stats.route('/', methods=['GET'])
def get_list():
    from flask_module.device_access_stats_reqmodel import DeviceAccessStatsQueryReqModel
    from service import device_access_stats_service

    try:
        conditions = request.args.to_dict()
        try:
            reqmodel = DeviceAccessStatsQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"Query parameters: {reqmodel}")
        results, total = device_access_stats_service.get_device_access_stats_by_conditions(
            reqmodel.dict(exclude_none=True), logger)

        if results is None:
            return error_response(Ecodes.INTERNAL_ERROR, 500)

        resp = {
            "ErrorCode": 0,
            "list": results,
            "total": total,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
        }
        return success_response(resp, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@device_access_stats.route('/', methods=['DELETE'])
def delete_by_device_identifier_and_type():
    from flask_module.device_access_stats_reqmodel import DeviceAccessStatsDeleteReqModel
    from service import device_access_stats_service

    try:
        data = request.args.to_dict()
        try:
            reqmodel = DeviceAccessStatsDeleteReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"Deleting device access stats with ID: {reqmodel.id}")
        device_identifier, deleted_count = device_access_stats_service.delete_device_access_stats(
            reqmodel.id, logger)

        if deleted_count > 0:
            user = session.get_user(request)
            record_event_log(user=user, level=LogLevel.INFO.value,
                             message=f"Deleted device access stat: device identifier: {device_identifier}",
                             desc='Delete device access stat', type=LogType.SYSTEM.value,
                             action=LogAction.DELETE.value)

            return success_response({
                "ErrorCode": 0,
                "Data": {
                    "id": reqmodel.id,
                    "device_identifier": device_identifier
                }
            }, 200)
        else:
            return error_response(Ecodes.NOT_FOUND, 404, 'device access stats not found')
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)
