""" Flask module for Dashboard v2 Reports"""

from flask import Blueprint, request, jsonify
from util.err_codes import error_response, success_response
from flask_module import session
import util.err_codes as Ecodes
import json
import shutil
import os
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from psycopg2.errors import UniqueViolation
from pydantic import ValidationError
from sqlalchemy.exc import SQLAlchemyError, DBAPIError, OperationalError, IntegrityError
from sqlalchemy.orm.exc import NoResultFound, StaleDataError
from util.common_log import get_logger
from exts import Base, Session
from flask_module.dashboard_reports_reqmodel import (
    DashboardReportsModel,
    EditDashboardReportsModel,
    ReportActionRequest,
    DeleteRequestModel,
    QueryDashboardReportsModel,
)
from domain_model.tracker.report_tracker import ReportTracker

dashboardreports = Blueprint("dashboardreports", __name__)

logger = get_logger("dlp")


@dashboardreports.route("/index", methods=["GET"])
def index():
    return "This is the Dashboard v2 Reports index page"


@dashboardreports.route("/create_report", methods=["POST"])
def create_report():
    from service import dashboard_reports_service

    try:
        created_report = {}
        data = request.get_json()
        report = DashboardReportsModel(**data)
        sql_data = dashboard_reports_service.DashboardReport(**report.dict())

        # Ensure that status is always set to 0 (scheduled) for a new report
        if sql_data.status != 0:
            sql_data.status = 0

        created_report = dashboard_reports_service.create_report(sql_data)
        logger.debug(f"Created report data: {created_report}")

        try:
            user = session.get_user(request)
            record_event_log(
                user=user,
                level=LogLevel.INFO.value,
                message=f"Report task created successfully: {sql_data.name}",
                desc="Create report",
                type=LogType.REPORTS.value,
                action=LogAction.CREATE.value,
            )
        except Exception as e:
            logger.error(f"Error in event log {e}")

        return success_response(created_report)
    except ValidationError as e:
        return error_response(
            Ecodes.VALIDATION_ERROR,
            400,
            f"Validation error: create report: {e}",
        )
    except IntegrityError as e:
        # Check if the IntegrityError is due to a unique constraint violation
        if isinstance(e.orig, UniqueViolation):
            return error_response(
                Ecodes.VALIDATION_ERROR,
                400,
                "A report with this name already exists.",
            )
        else:
            # Re-raise the exception if it's not a unique constraint violation
            raise
    except Exception as e:
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Failed to create report: {e}",
        )


@dashboardreports.route("/edit_report", methods=["PUT"])
def edit_report():
    from service import dashboard_reports_service

    try:
        data = request.get_json()
        report_data = EditDashboardReportsModel(**data)
        updated_report = dashboard_reports_service.DashboardReport(**report_data.dict())
        result = dashboard_reports_service.edit_report(updated_report)
        if result:
            ReportTracker(report_data.id).destroy()
            try:
                user = session.get_user(request)
                record_event_log(
                    user=user,
                    level=LogLevel.INFO.value,
                    message=f"Report task edited: {updated_report.name}",
                    desc="Edit report",
                    type=LogType.REPORTS.value,
                    action=LogAction.EDIT.value,
                )
            except Exception as e:
                logger.error(f"Error in event log {e}")
            return success_response(result)
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Failed to update report",
        )
    except StaleDataError as e:
        return error_response(
            Ecodes.VALIDATION_ERROR,
            400,
            f"Report has been modified by another user, please try again!",
        )
    except ValueError as e:
        return error_response(
            Ecodes.VALIDATION_ERROR,
            400,
            f"Failed to update report: {e}",
        )
    except ValidationError as e:
        return error_response(
            Ecodes.VALIDATION_ERROR,
            400,
            f"Validation error: update report: {e}",
        )
    except Exception as e:
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Failed to update report: {e}",
        )


@dashboardreports.route("/list_report", methods=["GET"])
def list_report():
    from service import dashboard_reports_service

    try:
        data = request.args.to_dict()
        report_data = QueryDashboardReportsModel(**data)
        reports, total = (
            dashboard_reports_service.get_report_tasks_with_latest_file_and_count(
                conditions=report_data.dict(exclude_none=True),
                sort_field=report_data.sort_field,
                sort_method=report_data.sort_method,
                page=report_data.page,
                per_page=report_data.per_page,
            )
        )
        if reports:
            reports_data = []
            for r in reports:
                report_dict = {
                    "id": str(r.id),
                    "name": r.name,
                    "status": r.status,
                    "format": r.format,
                    "period": r.period,
                    "schedule_info": r.schedule_info,
                    "report_type": r.report_type,
                    "report_details": r.report_details,
                    "reportgen_count": r.reportgen_count,
                    "notification_id": r.notification_id,
                    "created_by": r.created_by,
                    "created_at": (
                        int(r.created_at.timestamp()) if r.created_at else None
                    ),
                    "last_report_status": getattr(r, "last_report_status", None),
                    "last_report_run": (
                        int(r.last_report_run.timestamp())
                        if r.last_report_run
                        else None
                    ),
                    "last_reports": getattr(r, "last_reports", None),
                    "previous_reports": getattr(r, "previous_reports", 0),
                    "expiring_reports_count": getattr(r, "expiring_reports_count", 0),
                    "notes": getattr(r, "notes", None),
                }
                reports_data.append(report_dict)
            logger.debug(f"reports: {reports_data} {type(reports_data)}")
            return success_response(
                {
                    "list": reports_data,
                    "total": total,
                    "page": report_data.page,
                    "per_page": report_data.per_page,
                },
                200,
            )
        else:
            return success_response({"reports": []})
    except Exception as e:
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Failed to list reports: report listing API",
        )


@dashboardreports.route("/delete_report", methods=["DELETE"])
def delete_report():
    from service import dashboard_reports_service, dashboard_reports_list_service

    try:
        data = request.get_json()
        report_data = DeleteRequestModel(**data)
        report_id = report_data.id

        # Delete report files from the filesystem
        report_dir = f"/var/log/reports/{report_id}"
        if os.path.exists(report_dir):
            shutil.rmtree(report_dir)
            logger.info(f"Deleted report files at {report_dir}")
        else:
            logger.warning(f"Report directory not found: {report_dir}")

        # Delete the report task record from the db
        deleted, name = dashboard_reports_service.delete_report(report_data.id)
        if deleted:
            ReportTracker(report_data.id).destroy()
            if dashboard_reports_list_service.delete_reports_by_task_id(report_data.id):
                logger.info(
                    f"Deleted all files from the report files table for name and task_id: {name} {report_data.id}"
                )
            else:
                logger.info(
                    f"Failed to delete all files from the report files table for name and task_id: {name} {report_data.id}"
                )
            try:
                user = session.get_user(request)
                record_event_log(
                    user=user,
                    level=LogLevel.INFO.value,
                    message=f"Report task deleted: {name}",
                    desc="Delete report",
                    type=LogType.REPORTS.value,
                    action=LogAction.DELETE.value,
                )
            except Exception as e:
                logger.error(f"Error in event log {e}")
            return success_response({"message": f"Report {name} deleted"})
        logger.error(f"Error while deleting dashboard report: {e}")
        return error_response(
            Ecodes.INTERNAL_ERROR,
            404,
            f"Report not found",
        )
    except ValidationError as e:
        return error_response(
            Ecodes.VALIDATION_ERROR,
            400,
            f"Validation error: delete report: {e}",
        )
    except Exception as e:
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Failed to delete report",
        )


@dashboardreports.route("/modify_state", methods=["PUT"])
def change_state():
    from service import dashboard_reports_service

    try:
        data = request.get_json()
        action_data = ReportActionRequest(**data)
        result, name = dashboard_reports_service.update_report_state(
            action_data.id, action_data.action, True
        )
        if result:
            try:
                user = session.get_user(request)
                record_event_log(
                    user=user,
                    level=LogLevel.INFO.value,
                    message=f"Report task status changed to: {action_data.action} for report {name}",
                    desc="Report change status",
                    type=LogType.REPORTS.value,
                    action=LogAction.EDIT.value,
                )
            except Exception as e:
                logger.error(f"Error in event log {e}")
            return success_response(
                {
                    "message": f"Report state changed to {action_data.action} for {name} successfully"
                }
            )
        else:
            if name:
                return error_response(
                    Ecodes.INTERNAL_ERROR,
                    400,
                    f"Error while changing dashboard report state to {action_data.action}: Report has been modified, try again in some time",
                )
            else:
                return error_response(
                    Ecodes.INTERNAL_ERROR,
                    400,
                    f"Error while changing dashboard report state to {action_data.action}: Report not found",
                )
    except ValidationError as e:
        return error_response(
            Ecodes.VALIDATION_ERROR,
            400,
            f"Validation error: update report state: {e}",
        )
    except Exception as e:
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Failed to update report state",
        )
