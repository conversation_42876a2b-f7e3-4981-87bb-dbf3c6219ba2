import uuid
from util.common_log import get_logger
from pydantic import BaseModel, validator
from pydantic import Field, constr, conint, confloat, IPvAnyAddress
from typing import List, Optional, Literal, Union
import re

logger = get_logger("api")

class ADConfigCreateReqModel(BaseModel):
    id: Optional[str] = Field(None, min_length=36, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="valid UUID format string")
    # required fields
    ad_server: str = Field(..., example="*************", description="AD server address")
    ad_username: str = Field(..., min_length=1, max_length=100, example="CN=test1,CN=Users,DC=test-dlp,DC=com", description="AD username")
    ad_password: str = Field(..., min_length=1, max_length=100, example="xxxxxx", description="AD password")
    ad_group_prefix: str = Field(..., min_length=1, max_length=100, example="test*", description="AD group prefix")
    ad_fetch_interval: int = Field(..., example=24, description="AD fetch interval in hours")
    ad_status: bool = Field( ..., example=True, description="Indicates whether ad fetch is enabled.")    
    pwd_encrypted: conint(ge=0, le=1) = Field(..., description="1: Password Encrypted, 0: Password Not Encrypted")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

    @validator('ad_server')
    def validate_ad_server(cls, v):
        # Check if the value is a valid IP address
        try:
            IPvAnyAddress.validate(v)
            return v
        except ValueError:
            # If not a valid IP, check for domain name format using regex
            domain_pattern = r"^(?!-)[A-Za-z0-9-]{1,63}(?<!-)(\.[A-Za-z0-9-]{1,63})*(\.[A-Za-z]{2,})$"
            if re.match(domain_pattern, v):
                return v
            raise ValueError(f"Invalid AD server address: must be a valid IP address or domain name. Got: {v}")

class ADConfigQueryReqModel(BaseModel):
    # optional fields
    id: Optional[str] = Field(None, min_length=36, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="valid UUID format string")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class ADConfigTestReqModel(BaseModel):
    # required fields
    ad_server: str = Field(..., example="*************", description="AD server address")
    ad_username: str = Field(..., min_length=1, max_length=100, example="CN=test1,CN=Users,DC=test-dlp,DC=com", description="AD username")
    ad_password: str = Field(..., min_length=1, max_length=100, example="xxxxxx", description="AD password")
    pwd_encrypted: conint(ge=0, le=1) = Field(..., description="1: Password Encrypted, 0: Password Not Encrypted")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('ad_server')
    def validate_ad_server(cls, v):
        # Check if the value is a valid IP address
        try:
            IPvAnyAddress.validate(v)
            return v
        except ValueError:
            # If not a valid IP, check for domain name format using regex
            domain_pattern = r"^(?!-)[A-Za-z0-9-]{1,63}(?<!-)(\.[A-Za-z0-9-]{1,63})*(\.[A-Za-z]{2,})$"
            if re.match(domain_pattern, v):
                return v
            raise ValueError(f"Invalid AD server address: must be a valid IP address or domain name. Got: {v}")

"""
class ADConfigUpdateReqModel(BaseModel):
    # required fields
    id: str = Field(..., min_length=36, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="valid UUID format string")
    ad_server: str = Field(..., min_length=1, max_length=100, example="*************", description="AD server address")
    ad_username: str = Field(..., min_length=1, max_length=100, example="CN=test1,CN=Users,DC=test-dlp,DC=com", description="AD username")
    ad_password: str = Field(..., min_length=1, max_length=100, example="xxxxxx", description="AD password")
    ad_group_prefix: str = Field(..., min_length=1, max_length=100, example="test*", description="AD group prefix")
    ad_fetch_interval: int = Field(..., example=24, description="AD fetch interval in hours")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
        
class ADConfigDeleteReqModel(BaseModel):
    # required fields
    id: str = Field(..., min_length=36, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
"""
