# import uuid, re
# from util.common_log import get_logger
# from pydantic import BaseModel, validator, root_validator, EmailStr, ValidationError
# from pydantic import Field, constr, conint, confloat
# from typing import List, Optional, Literal, Union, Dict
# from enum import Enum
# import ipaddress
# from flask_module.base import validate_condition_relation_string

# logger = get_logger("api")

# class QueryFwIncidentReqModel(BaseModel):
#     # optional fields
#     id: constr(min_length=36, max_length=36) = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
#     start_ctime: str = Field(None, description="")
#     end_ctime: str = Field(None, description="")
#     sid: constr(min_length=36, max_length=36) = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="")
#     pid: constr(min_length=36, max_length=36) = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="")
#     rid: constr(min_length=36, max_length=36) = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="")
#     severity: int = Field(None, description="")
#     ignored: bool = Field(None, description="")
#     status: int = Field(None, description="")
#     action: str = Field(None, description="")
#     message: str = Field(None, min_length=0, max_length=512, description="")
#     comments: str = Field(None, min_length=0, max_length=512, example="This is a incident")
#     data_source_type: str = Field(None, description="")

#     fid: constr(min_length=36, max_length=36) = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
#     file_name: str = Field(None, min_length=1, max_length=128, example="test", description="")
#     file_path: str = Field(None, min_length=1, max_length=512, example="test", description="")
#     ftype: str = Field(None, min_length=1, max_length=128, example="test", description="")
#     file_owner: str = Field(None, min_length=1, max_length=128, example="test", description="")
#     fowner_email: str = Field(None, min_length=1, max_length=256, example="test", description="")
    
#     dtype: str = Field(None, min_length=1, max_length=128, example="test", description="")
#     dsn: str = Field(None, min_length=1, max_length=128, example="test", description="")
#     daddr: str = Field(None, min_length=1, max_length=128, example="test", description="")
#     app_protocol: str = Field(None, min_length=1, max_length=128, example="test", description="")
#     app_src_addr: str = Field(None, min_length=1, max_length=128, example="test", description="")
#     app_dst_addr: str = Field(None, min_length=1, max_length=128, example="test", description="")
    
#     sort_field: Literal['ctime', 'utime', 'severity', 'status'] = Field('ctime', description="sort field name")
#     sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
#     page: conint(ge=0) = Field(None, description="page")
#     per_page: conint(ge=0, le=1024) = Field(10, description="page size")
        
#     @validator('id', 'fid', 'sid', 'pid', 'rid')
#     def uuid_validator(cls, v):
#         try:
#             uuid.UUID(v)
#         except ValueError as e:
#             raise ValueError(f"Invalid uuid {v}")
#         return v

# class DeleteFwIncidentReqModel(BaseModel):
#     id: List[constr(min_length=36, max_length=36)] = Field(description="data scanning incident uuid list")

#     @validator('id')
#     def uuid_validator(cls, v):
#         try:
#             for id in v:
#                 uuid.UUID(id)
#         except ValueError as e:
#             raise ValueError(f"Invalid uuid {v}")
#         return v
    
# class UpdateFwIncidentStatusReqModel(BaseModel):
#     ids: List[constr(min_length=36, max_length=36)] = Field(..., description="data scanning incident uuid list")
#     status: int = Field(..., description="")

#     @validator("ids")
#     def uuid_validator(cls, v):
#         try:
#             for id in v:
#                 uuid.UUID(id)
#         except ValueError as e:
#             raise ValueError(f"Invalid uuid {v}")
#         return v
    
# class UpdateFwIncidentSeverityReqModel(BaseModel):
#     ids: List[constr(min_length=36, max_length=36)] = Field(..., description="data scanning incident uuid list")
#     severity: int = Field(..., description="")

#     @validator("ids")
#     def uuid_validator(cls, v):
#         try:
#             for id in v:
#                 uuid.UUID(id)
#         except ValueError as e:
#             raise ValueError(f"Invalid uuid {v}")
#         return v
    
# class UpdateFwIncidentIgnoredReqModel(BaseModel):
#     ids: List[constr(min_length=36, max_length=36)] = Field(..., description="data scanning incident uuid list")
#     ignored: bool = Field(..., description="")

#     @validator("ids")
#     def uuid_validator(cls, v):
#         try:
#             for id in v:
#                 uuid.UUID(id)
#         except ValueError as e:
#             raise ValueError(f"Invalid uuid {v}")
#         return v
    
# class UpdateFwIncidentFalsePositiveReqModel(BaseModel):
#     ids: List[constr(min_length=36, max_length=36)] = Field(..., description="data scanning incident uuid list")
#     false_positive: bool = Field(..., description="")

#     @validator("ids")
#     def uuid_validator(cls, v):
#         try:
#             for id in v:
#                 uuid.UUID(id)
#         except ValueError as e:
#             raise ValueError(f"Invalid uuid {v}")
#         return v
    
# class UpdateFwIncidentAssignReqModel(BaseModel):
#     ids: List[constr(min_length=36, max_length=36)] = Field(..., description="data scanning incident uuid list")
#     assign: str = Field(..., description="")

#     @validator("ids")
#     def uuid_validator(cls, v):
#         try:
#             for id in v:
#                 uuid.UUID(id)
#         except ValueError as e:
#             raise ValueError(f"Invalid uuid {v}")
#         return v
    
# class UpdateFwIncidentEscalateReqModel(BaseModel):
#     ids: List[constr(min_length=36, max_length=36)] = Field(..., description="data scanning incident uuid list")
#     escalate: str = Field(..., description="")

#     @validator("ids")
#     def uuid_validator(cls, v):
#         try:
#             for id in v:
#                 uuid.UUID(id)
#         except ValueError as e:
#             raise ValueError(f"Invalid uuid {v}")
#         return v
    
# class UpdateFwIncidentWhitelistReqModel(BaseModel):
#     ids: List[constr(min_length=36, max_length=36)] = Field(..., description="data scanning incident uuid list")
#     add_whitelist: bool = Field(..., description="")

#     @validator("ids")
#     def uuid_validator(cls, v):
#         try:
#             for id in v:
#                 uuid.UUID(id)
#         except ValueError as e:
#             raise ValueError(f"Invalid uuid {v}")
#         return v
    
# class DisableFwIncidentPolicyReqModel(BaseModel):
#     ids: List[constr(min_length=36, max_length=36)] = Field(..., description="data scanning incident uuid list")
#     disable_policy: bool = Field(..., description="")

#     @validator("ids")
#     def uuid_validator(cls, v):
#         try:
#             for id in v:
#                 uuid.UUID(id)
#         except ValueError as e:
#             raise ValueError(f"Invalid uuid {v}")
#         return v
    
# class DisableFwIncidentRuleReqModel(BaseModel):
#     ids: List[constr(min_length=36, max_length=36)] = Field(..., description="data scanning incident uuid list")
#     disable_rule: bool = Field(..., description="")

#     @validator("ids")
#     def uuid_validator(cls, v):
#         try:
#             for id in v:
#                 uuid.UUID(id)
#         except ValueError as e:
#             raise ValueError(f"Invalid uuid {v}")
#         return v
