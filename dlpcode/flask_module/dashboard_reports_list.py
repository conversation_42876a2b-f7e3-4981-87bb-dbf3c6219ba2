""" Flask module for Dashboard v2 Reports Listing API"""

from flask import Blueprint, request, jsonify, send_file
from util.err_codes import error_response, success_response
from flask_module import session
import util.err_codes as Ecodes
import json
import os
import mimetypes
from pydantic import ValidationError
from sqlalchemy.exc import SQLAlchemyError, DBAPIError, OperationalError
from util.common_log import get_logger
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from exts import Base, Session
from flask_module.dashboard_reports_list_reqmodel import (
    DashboardReportsListModel,
    DownloadRequestListModel,
    CheckIDRequestListModel,
    RetentionPeriodRequestModel,
)
from util.config import configs

dashboardreportslist = Blueprint("dashboardreportslist", __name__)

logger = get_logger("dlp")


@dashboardreportslist.route("/index", methods=["GET"])
def index():
    return "This is the Dashboard v2 Reports Listing API index page"


@dashboardreportslist.route("/list_report", methods=["GET"])
def list_report():
    from service import dashboard_reports_list_service

    try:
        data = request.args.to_dict()
        report_data = CheckIDRequestListModel(**data)
        reports, total = dashboard_reports_list_service.list_reports(
            conditions=report_data.dict(exclude_none=True),
            sort_field=report_data.sort_field,
            sort_method=report_data.sort_method,
            page=report_data.page,
            per_page=report_data.per_page,
        )
        if reports:
            reports_data = [r.to_dict(exclude={"path"}) for r in reports]
            return success_response(
                {
                    "list": reports_data,
                    "total": total,
                    "page": report_data.page,
                    "per_page": report_data.per_page,
                },
                200,
            )
        else:
            return success_response({"reports": []})
    except Exception as e:
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Failed to list report file history: report listing API",
        )


@dashboardreportslist.route("/delete_report", methods=["DELETE"])
def delete_report():
    from service import dashboard_reports_list_service

    try:
        data = request.get_json()
        report_data = CheckIDRequestListModel(**data)
        deleted, name = dashboard_reports_list_service.delete_report(report_data.id)
        if deleted:
            try:
                user = session.get_user(request)
                record_event_log(
                    user=user,
                    level=LogLevel.INFO.value,
                    message=f"Report file deleted: {name}",
                    desc="Delete report file",
                    type=LogType.REPORTS.value,
                    action=LogAction.DELETE.value,
                )
            except Exception as e:
                logger.error(f"Error in event log {e}")
            return success_response({"message": f"Report {name} deleted"})
        else:
            logger.error(
                f"Error while deleting dashboard report: report listing API: Report not found"
            )
            return error_response(
                Ecodes.INTERNAL_ERROR,
                404,
                f"Report not found",
            )
    except ValidationError as e:
        return error_response(
            Ecodes.VALIDATION_ERROR,
            400,
            f"Validation error: delete report: report listing API: {e}",
        )
    except Exception as e:
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Failed to delete report: report listing API",
        )


@dashboardreportslist.route("/download_report", methods=["GET"])
def download_report():
    from service import dashboard_reports_list_service

    try:
        report_id = request.args.get("id")
        if not report_id:
            return error_response(
                Ecodes.INTERNAL_ERROR,
                404,
                f"Report not found",
            )

        # Fetch the report path using service layer
        report_file = dashboard_reports_list_service.download_report(report_id)
        if report_file:
            # report_file_dict = report_file.to_dict()
            report_path = report_file.path
            full_path = os.path.join("/var", report_path)
            if full_path and os.path.exists(full_path):
                # Detect content type (MIME type)
                mime_type, _ = mimetypes.guess_type(full_path)
                download_name = os.path.basename(full_path)
                try:
                    user = session.get_user(request)
                    record_event_log(
                        user=user,
                        level=LogLevel.INFO.value,
                        message=f"Report file downloaded: {report_file.name}",
                        desc="Report file Downloaded",
                        type=LogType.REPORTS.value,
                        action=LogAction.DOWNLOAD.value,
                    )
                except Exception as e:
                    logger.error(f"Error in event log {e}")
                return send_file(
                    report_path,
                    mimetype=mime_type or "application/octet-stream",
                    as_attachment=True,
                    download_name=download_name,
                )
            else:
                logger.error(
                    f"Download report: Error, report file not found at the path: {report_file.name}"
                )
                return error_response(
                    Ecodes.INTERNAL_ERROR,
                    404,
                    f"Report file does not exist at the path: {report_file.name}",
                )
        else:
            logger.error(f"Download report: Error, report not found")
            return error_response(
                Ecodes.INTERNAL_ERROR,
                400,
                f"Error while downloading report: Report not found",
            )

    except ValidationError as e:
        logger.error(f"Download report: Error, {e}")
        return error_response(
            Ecodes.VALIDATION_ERROR,
            400,
            f"Validation error: download report: {e}",
        )
    except Exception as e:
        logger.error(f"Download report: Error, {str(e)}")
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Failed to download report: {str(e)}",
        )


@dashboardreportslist.route("/settings/get_retention", methods=["GET"])
def get_retention():
    from service import dashboard_reports_list_service

    try:
        # Fetch from DB (assuming single row in retention table)
        result = dashboard_reports_list_service.get_retention_config()

        if result:
            try:
                user = session.get_user(request)
                record_event_log(
                    user=user,
                    level=LogLevel.INFO.value,
                    message=f"Report task retention period value fetched",
                    desc="Get Retention period for Reports",
                    type=LogType.REPORTS.value,
                )
            except Exception as e:
                logger.error(f"Error in event log {e}")
            return success_response(result.to_dict())
        else:
            retention = dashboard_reports_list_service.ReportRetentionobj()
            retention.retention_all = configs["reports"]["report_retention_days"]
            return success_response(
                retention.to_dict()
            )  # Fetch from the config if not found in the db

    except Exception as e:
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Failed to fetch retention period config: {str(e)}",
        )


@dashboardreportslist.route("/settings/configure_retention", methods=["POST"])
def configure_retention():
    from service import dashboard_reports_list_service

    try:
        data = request.get_json()
        action_data = RetentionPeriodRequestModel(**data)
        updated_data = dashboard_reports_list_service.ReportRetentionobj(
            **action_data.dict()
        )
        result = dashboard_reports_list_service.configure_retention(updated_data)
        if result:
            try:
                user = session.get_user(request)
                record_event_log(
                    user=user,
                    level=LogLevel.INFO.value,
                    message=f"Report task retention period value changed",
                    desc="Configure Retention period for Reports",
                    type=LogType.REPORTS.value,
                    action=LogAction.EDIT.value,
                )
            except Exception as e:
                logger.error(f"Error in event log {e}")
            return success_response(
                {
                    "message": f"Report retention period configured successfully: {action_data}"
                }
            )
        else:
            return error_response(
                Ecodes.INTERNAL_ERROR,
                400,
                f"Error while configuring retention period for reports",
            )
    except ValidationError as e:
        return error_response(
            Ecodes.VALIDATION_ERROR,
            400,
            f"Validation error while configuring retention period for reports: {e}",
        )
    except Exception as e:
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Failed to configure retention period for reports",
        )
