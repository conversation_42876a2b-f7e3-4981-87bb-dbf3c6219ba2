import traceback
from filelock import FileLock
import os
from flask import Blueprint, request, jsonify
from pydantic import ValidationError
from util.common_log import get_logger
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from flask_module import session
from system.system_log import record_event_log, LogLevel, LogAction, LogType

logger = get_logger("api")
data_label = Blueprint("data_label", __name__)


@data_label.route("/predefined_data_label", methods=['GET'])
def handle_predefined_data_label_get():
    from flask_module.data_label_reqmodel import PredefinedDataLabelQueryReqModel
    from service import data_label_service
    
    try:
        conditions = request.args.to_dict()
        try:
            reqmodel = PredefinedDataLabelQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")

        total_count, results = data_label_service.get_predefined_data_label_by_conditions(reqmodel.dict(exclude_none=True))
        if results is None:
            return error_response(Ecodes.INTERNAL_ERROR, 500)
        
        return success_response({
            "list": results,
            "total": total_count,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
        })
    
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@data_label.route('/predefined_data_label', methods=['PUT'])
def handle_predefined_data_label_update():
    from flask_module.data_label_reqmodel import PredefinedDataLabelUpdateReqModel
    from domain_model.data_label import get_predefined_data_label
    from service import data_label_service

    try:
        data = request.get_json()
        try:
            reqmodel = PredefinedDataLabelUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)   
        logger.info(f"reqmodel: {reqmodel}")
        
        predefined_data_label = get_predefined_data_label(reqmodel.id)
        if not predefined_data_label:
            return error_response(Ecodes.NOT_FOUND, 404, 'Predefined data label not found')

        new_predefined_data_label = data_label_service.update_predefined_data_label(reqmodel.id, reqmodel.dict(exclude_none=True))
        
        logger.info(f"Updated predefined_data_label: {new_predefined_data_label}")
        return success_response(new_predefined_data_label)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@data_label.route("/ml_data_label", methods=['GET'])
def handle_ml_data_label_get():
    from service import data_label_service
    
    try:
        results = data_label_service.get_ml_data_label()
        
        return success_response(results)
    
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@data_label.route("/custom_data_label", methods=['GET'])
def handle_custom_data_label_get():
    from flask_module.data_label_reqmodel import CustomDataLabelQueryReqModel
    from service import data_label_service
    
    try:
        conditions = request.args.to_dict()
        try:
            reqmodel = CustomDataLabelQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")

        total_count, results = data_label_service.get_custom_data_label_by_conditions(reqmodel.dict(exclude_none=True))
        if results is None:
            return error_response(Ecodes.INTERNAL_ERROR, 500)
        
        return success_response({
            "list": results,
            "total": total_count,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
        })
    
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@data_label.route('/custom_data_label', methods=['POST'])
def handle_custom_data_label_create():
    from flask_module.data_label_reqmodel import CustomDataLabelCreateReqModel
    from service import data_label_service
    
    lock_file = "/tmp/custom_data_label.lock"
    lock = FileLock(lock_file, timeout=10) 

    try:
        data = request.get_json()
        try:
            reqmodel = CustomDataLabelCreateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")

        payload = reqmodel.dict(exclude_none=True)
        
        with lock:
            exists =  data_label_service.check_data_label_existence(payload.get("name"))
            if exists is True:
                return error_response(Ecodes.VALIDATION_ERROR, 400, "Label duplicates with existing labels")
            
            elif exists is False:
                custom_data_label = data_label_service.create_custom_data_label(payload)
                if custom_data_label == None:
                    logger.error(f"Create custom data label failed!")
                    return error_response(Ecodes.INTERNAL_ERROR, 400, f'Create custom data label failed')

                user = session.get_user(request)
                record_event_log(user=user, level=LogLevel.INFO.value, message=f"Add a new custom label {custom_data_label.to_dict().get('name', '')}", 
                desc='Add custom label', type=LogType.LABELS.value, action=LogAction.CREATE.value)
                return success_response(custom_data_label.to_dict())
            else:
                return error_response(Ecodes.INTERNAL_ERROR, 500)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)
    finally:
        if os.path.exists(lock_file):
            os.remove(lock_file)


@data_label.route('/custom_data_label', methods=['PUT'])
def handle_custom_data_label_update():
    from flask_module.data_label_reqmodel import CustomDataLabelUpdateReqModel
    from domain_model.data_label import get_custom_data_label
    from service import data_label_service

    try:
        data = request.get_json()
        try:
            reqmodel = CustomDataLabelUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)    
        logger.info(f"reqmodel: {reqmodel}")
        
        custom_data_label = get_custom_data_label(reqmodel.id)
        if not custom_data_label:
            return error_response(Ecodes.NOT_FOUND, 404, 'Custom data label not found')

        new_custom_data_label = data_label_service.update_custom_data_label(reqmodel.id, reqmodel.dict(exclude_none=True))
        
        logger.info(f"Updated custom_data_label: {new_custom_data_label}")
        return success_response(new_custom_data_label)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@data_label.route('/custom_data_label', methods=['DELETE'])
def handle_custom_data_label_delete():
    from domain_model.data_label import delete_custom_data_labels, get_custom_data_label
    from flask_module.data_label_reqmodel import CustomDataLabelDelReqModel
    from service import data_label_service
    
    try:
        args = request.args.to_dict(flat=False)
        logger.info(f"Deleting custom data label by args {args}")

        try:
            reqmodel = CustomDataLabelDelReqModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")

        custom_data_label_name = ""
        not_found = []
        for custom_data_label_id in reqmodel.id:
            custom_data_label = get_custom_data_label(id=custom_data_label_id)
            custom_data_label_name += custom_data_label.to_dict().get('name', '')
            if not custom_data_label:
                not_found.append(custom_data_label_id)
        if not_found:
            return error_response(Ecodes.NOT_FOUND, 404, f'Custom data label not found: {not_found}')
            
        
        for custom_data_label_id in reqmodel.id:
            ref_count = data_label_service.check_ref_by_id(custom_data_label_id)
            
        if ref_count is None:
            return error_response(Ecodes.INTERNAL_ERROR, 500)
        elif ref_count > 0:
            return error_response(Ecodes.RESOURCE_REFERRED, 400, 'The label has been referenced by Discover rule.')

        row_count = 0
        for custom_data_label_id in reqmodel.id:
            row_count = row_count + delete_custom_data_labels(id=custom_data_label_id)
        
        if row_count == 0:
            return error_response(Ecodes.NOT_FOUND, 404, 'Custom data label not found')
        
        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Delete custom label {custom_data_label_name}",
        desc='Delete custom label', type=LogType.LABELS.value, action=LogAction.DELETE.value)
        return success_response({'message': 'Custom data labels deleted'})
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@data_label.route('/check_existence', methods=['POST'])
def handle_check_data_label_existence():
    from flask_module.data_label_reqmodel import CheckDataLabelExistenceReqModel
    from service import data_label_service
    
    try:
        data = request.get_json()
        try:
            reqmodel = CheckDataLabelExistenceReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")

        payload = reqmodel.dict(exclude_none=True)
        exists = data_label_service.check_data_label_existence(payload.get('label'))
        if exists is True:
            return error_response(Ecodes.VALIDATION_ERROR, 400, "Label duplicates with existing labels")
        elif exists is False:
            return success_response({"message": "Label added"} )
        else:
            return error_response(Ecodes.INTERNAL_ERROR, 500)
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)