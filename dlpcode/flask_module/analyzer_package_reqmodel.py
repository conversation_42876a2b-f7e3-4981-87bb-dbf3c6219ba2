from util.common_log import get_logger
from pydantic import BaseModel, root_validator, validator
from pydantic import Field, constr, conint, confloat
from typing import List, Optional, Literal, Union
from util.enum_ import AnalyzerPackageUpdateMode 
from file_analyzer.util.enum import AnalyzerPackageType 

class AnalyzerPackageUpdateModel(BaseModel):
    overwrite: bool = Field(True, description="Overwrite exist package")
    compliance_model: str = Field(None, description="compliance_model package tar file path")
    compliance_recognizer: str = Field(None, description="compliance_recognizer package tar file path")
    classification_model: str = Field(None, description="classification_model package tar file path")
    mode: AnalyzerPackageUpdateMode = Field(..., description="Update mode (AUTO or MANUAL)")

    @root_validator(pre=False)
    def check_mode(cls, values):
        compliance_model = values.get('compliance_model','')
        compliance_recognizer = values.get('compliance_recognizer','')
        classification_model = values.get('classification_model','')
        if not compliance_model and not compliance_recognizer and not classification_model:
            raise ValueError("At least one package file path has to be specified")
        return values
    
    @validator("mode", pre=True)
    def validate_mode(cls, value):
        return AnalyzerPackageUpdateMode(int(value))
    
class AnalyzerPackageUploadModel(BaseModel):
    clear: bool = Field(False, description="clear the destinate directory")
    type: str = Field(..., description="specify the package type")

    @validator("type")
    def check_package_type(cls, v):
        try:
            AnalyzerPackageType(v)
            return v
        except ValueError:
            raise ValueError(f"Invalid package type {v}")