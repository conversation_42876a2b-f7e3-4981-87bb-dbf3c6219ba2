import uuid, re
from util.common_log import get_logger
from pydantic import BaseModel, validator, root_validator
from pydantic import Field, constr, conint, confloat, conlist
from typing import List, Optional, Literal, Union, Dict
from flask_module.base import validate_condition_relation_string
from flask_module.collector_reqmodel import FileSizeLimitMdl, AnalyzeSettingMdl, ProtectionProfilesModel
from util.enum_ import StorageType, ScanInterval

logger = get_logger("api")


# Define a Pydantic model for individual entries
class DDRPolicyMatchConditionEntryModel(BaseModel):
    # required fields
    type: Literal['collaborator', 'share_link'] = Field(..., example="", description="condition type")

    # for collaborator, share_link, file localtion, encryption
    value: List[str] = Field(None, description="collaborator type: internal, extenal")

    class Config:
        extra = "forbid"

    @root_validator(skip_on_failure=True)
    def check_conditions(cls, values):
        type = values.get('type')
        if type in ['collaborator', 'share_link']:
            if values.get('value') is None:
                raise ValueError(f'the field value must be specified when type is {type}')
        return values


class MatchConditionRelationReqModel(BaseModel):
    type: Literal[1, 2, 3] = Field(..., description="Condition relations 1: all conditions are matched, 2: at least one condition is matched, 3: custom")
    custom_relations: str = Field(None, min_length=1, max_length=2048, example="( 1 and 2 ) or ( 1 and 3 ) or 4", description="custom condition relation")
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('custom_relations')
    def relation_validator(cls, v, values, **kwargs):
        type = values['type']
        # only for type is 3
        if type == 3:
            result, error = validate_condition_relation_string(v)
            if result:
                return v
            else:
                raise ValueError(f'Invalid custom_relations {v}. error: {error}')
        else:
            # keep the custom_relation
            return v


class RemediationModel(BaseModel):
    file_copy : bool = Field(True, example=True, description="Indicates whether to enable file copy action")
    file_quarantine : bool = Field(False, example=True, description="Indicates whether to enable file quarantine action")
    remove_external_share_link: bool = Field(False, examples=True, description="Indicates whether to remove external share link")
    remove_public_share_link: bool = Field(False, examples=True, description="Indicates whether to remove public share link")
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"


class DDRPolicyActionReqModel(BaseModel):
    remediation: RemediationModel = Field(None, description="remediation information")
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"


class DDRPolicyCreateReqModel(BaseModel):
    # required fields
    data_classifier_ids: List[constr(min_length=36, max_length=36)] = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    name: str = Field(..., min_length=1, max_length=512, example="Policy1", description="ddr policy name")
    storage_type: List[int] = Field([], description="storage type list")
    match_condition: Dict[str, DDRPolicyMatchConditionEntryModel]
    match_condition_relation: MatchConditionRelationReqModel
    action: DDRPolicyActionReqModel = None
    event_type: Literal['file_download', 'file_access', 'file_shared', 'file_copy', 'file_move','share_link_create'] = Field(..., description="event type")
    notification_id: constr(min_length=36, max_length=36) = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")

    # optional fields
    description: str = Field("", min_length=0, max_length=512, example="This is a ddr rule")
    risk: Literal[0, 1, 2, 3] = Field(2, description="The risk of this rule. 0: Critical, 1: High, 2: Medium, 3: Low")
    protection_framework: List[str] = Field([], description="protection framework")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator("storage_type")
    def check_allowed_storage_type(cls, v):
        allowed = {0, 1, 2, 3}
        if not all(item in allowed for item in v):
            raise ValueError("Each value must be one of: 0, 1, 2, 3")
        return v

    @validator('data_classifier_ids', each_item=True)
    def validate_each_uuid(cls, v):
        try:
            uuid.UUID(v)
        except ValueError:
            raise ValueError(f"Invalid UUID: {v}")
        return v


class DDRPolicyUpdateReqModel(BaseModel):
    # required fields
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")

    # optional fields
    storage_type: List[int] = Field(None, description="storage type list")
    name: str = Field(None, min_length=1, max_length=512, example="Policy1", description="ddr policy name")
    match_condition: Dict[str, DDRPolicyMatchConditionEntryModel] = None
    match_condition_relation: MatchConditionRelationReqModel
    data_classifier_ids: List[constr(min_length=36, max_length=36)]  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    action: DDRPolicyActionReqModel = None
    event_type: Literal['file_download', 'file_access', 'file_shared', 'file_copy', 'file_move','share_link_create'] = Field(None, description="event type")
    notification_id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")

    description: str = Field(None, min_length=0, max_length=512, example="This is a ddr rule")
    risk: Literal[0, 1, 2, 3] = Field(None, description="The risk of this rule. 0: Critical, 1: High, 2: Medium, 3: Low")
    protection_framework: List[str] = Field([], description="protection framework")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

    @validator('data_classifier_ids', each_item=True)
    def validate_each_uuid(cls, v):
        try:
            uuid.UUID(v)
        except ValueError:
            raise ValueError(f"Invalid UUID: {v}")
        return v


class DDRTaskCreateReqModel(BaseModel):
    name: str = Field(..., min_length=1, max_length=512, example="Task1", description="ddr task name")
    description: str = Field("", min_length=0, max_length=256, example="This is a discover rule")
    scan_scope: int = Field(1, description="0 any, 1 included")
    scan_folders: conlist(str) = Field(..., example=["folder1", "folder2"], description="list of folders to scan")
    excluded_scan_folders: conlist(str) = Field([], example=["folder1", "folder2"], description="list of exclude folders")
    storage_type: StorageType = Field(..., description="1 : AWS S3, 2 : SharePoint(On Line) 3 : SharePoint(ON-Prem) 4:SMB")
    storage_id: constr(min_length=36, max_length=36) = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="uuid for the storage profile")
    file_size_limit: FileSizeLimitMdl = Field(..., description="Specify the minimum and maximum file sizes")
    scan_interval: ScanInterval = Field(...,example=1,description="Scan interval: 1 (short 10ms), 2 (long 20ms), 3 (none)")
    scan_file_type: List[Literal['office documents', 'pictures', 'text files', 'source code', 'configuration files', 'certificate files']] = Field(
        ...,
        example=['office documents', 'pictures'],
        description="Types of scan files: 'office documents', 'pictures', 'text files', 'source code', 'configuration files', 'certificate files'"
    )
    analyze_setting: AnalyzeSettingMdl = Field(..., description="File analysis configurations")
    ddr_policy_ids: List[str] = Field(
        ...,
        example=['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
        description="List of UUIDs for ddr policies"
    )
    protection_profiles: Optional[ProtectionProfilesModel] = Field(None, description="Specify the protection profiles")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @root_validator(pre=True)
    def rewrite_analyze_setting(cls, values):
        ddr_policy_ids = values.get('ddr_policy_ids', None)
        analyze_setting = values.get('analyze_setting', {}).copy()
        if not ddr_policy_ids:
            raise ValueError("In advanced mode, at least one policy must be selected.")
        analyze_setting['regions'] = ["AF", "AS", "EU", "NA", "OC", "SA"]
        analyze_setting['scan_category'] = []
        values['analyze_setting'] = analyze_setting
        return values


class DDRTaskUpdateReqModel(BaseModel):
    # required fields
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")

    # optional fields
    description: str = Field("", min_length=0, max_length=256, example="This is a discover rule")
    scan_scope: int = Field(1, description="0 any, 1 included")
    scan_folders: conlist(str) = Field(..., example=["folder1", "folder2"], description="list of folders to scan")
    excluded_scan_folders: conlist(str) = Field([], example=["folder1", "folder2"], description="list of exclude folders")
    file_size_limit: FileSizeLimitMdl = Field(None, description="Specify the minimum and maximum file sizes")
    scan_interval: ScanInterval = Field(None,example=1,description="Scan interval: 1 (short 10ms), 2 (long 20ms), 3 (none)")
    scan_file_type: List[Literal['office documents', 'pictures', 'text files', 'source code', 'configuration files', 'certificate files']] = Field(
        None,
        example=['office documents', 'pictures'],
        description="Types of scan files: 'office documents', 'pictures', 'text files', 'source code', 'configuration files', 'certificate files'"
    )
    analyze_setting: AnalyzeSettingMdl = Field(None, description="File analysis configurations")
    ddr_policy_ids: List[str] = Field(
        None,
        example=['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
        description="List of UUIDs for ddr policies"
    )

    storage_type: StorageType = Field(None, description="1 : AWS S3, 2 : SharePoint(On Line) 3 : SharePoint(ON-Prem) 4:SMB")
    protection_profiles: Optional[ProtectionProfilesModel] = Field(None, description="Specify the protection profiles")

    class Config:
        extra = "forbid"

    @root_validator(pre=True)
    def rewrite_analyze_setting(cls, values):
        ddr_policy_ids = values.get('ddr_policy_ids', None)
        if ddr_policy_ids and len(ddr_policy_ids) == 0:
            raise ValueError("In advanced mode, at least one policy must be selected.")
        analyze_setting = values.get('analyze_setting', {}).copy()
        if analyze_setting:
            analyze_setting['regions'] = ["AF", "AS", "EU", "NA", "OC", "SA"]
            analyze_setting['scan_category'] = []
            values['analyze_setting'] = analyze_setting
        return values


class DDRPolicyQueryReqModel(BaseModel):
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    name: str = Field(None, min_length=1, max_length=512, example="Policy1", description="ddr policy name")
    description: str = Field(None, min_length=0, max_length=512, example="This is a ddr policy")
    storage_type: StorageType = Field(None, description="1 : AWS S3, 2 : SharePoint(On Line) 3 : SharePoint(ON-Prem) 4:SMB")

    sort_field: str = Field('updated_at', max_length=32, description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: conint(ge=0) = Field(None, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class DDRDeleteReqModel(BaseModel):
    # required fields
    id: List[constr(min_length=36, max_length=36)]  = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class DDRQueryReqModel(BaseModel):
    id: constr(min_length=36, max_length=36)  = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")

# Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v