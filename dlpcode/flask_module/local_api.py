# local_api.py

"""
Local-only API for communication with internal Go process via 127.0.0.1.

This API is NOT exposed to external clients. It's used strictly for internal service calls.
"""

import traceback
from flask import Blueprint, jsonify
from system import dlp_license
from system import dlp_cmd
from flask import request
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes


from util.common_log import get_logger

# Blueprint for internal IPC API used only by local Go module
local_api = Blueprint("local_api", __name__)

logger = get_logger("api")

@local_api.route("/license/status", methods=["GET"])
def get_license_status():
    if request.method == 'GET':
        try:
            version = dlp_cmd.get_sys_prod_name()
            parts = version.split()
            if len(parts) > 1:
                version = f"{parts[1]} {parts[2]}"
            else:
                version = "Unknown"
                
            data = {
                "status": dlp_license.get_license_status(),
                "sn": dlp_cmd.get_sys_sn(),
                "version": version,
            }
            return success_response({"ErrorCode": 0, "Data": data})
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)