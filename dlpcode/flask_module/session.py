import time
import uuid
import base64
import json
from itsdangerous import URLSafeSerializer
from domain_model.psql_obj import global_dbobj
from util.postgresql_db.db_wrapper import DBWrapper
db = DBWrapper()

cookie_key = 'sid'
cookie_path = '/'
cookie_secret = base64.b64encode(bytes(str(uuid.uuid5(uuid.uuid4(), 'fortidata')), 'utf-8'))
serializer = URLSafeSerializer(cookie_secret)
session_expire = 30 * 60 * 1000  # Default 30 minutes

'''
session db: session
session record:
{
    username: ...,
    expire: ..., (ms)
    last-access: ..., (ms)
}
'''


# start session
def start(init_only=False):
    # Code change by <PERSON><PERSON><PERSON><PERSON> for DB Migration
    db = DBWrapper()
    if not db:
        raise Exception('Exception::You need to have a valid db connection')

    # get session collection from db into store
    try:
        # Code change by <PERSON><PERSON><PERSON><PERSON> for DB Migration
        ret = global_dbobj.table_detect('dlpsession')
        if not ret:
            sql_command = f"""
                create table if not exists dlpsession (
                    _id uuid default uuid_generate_v4(),
                    tm timestamp default now()::timestamp(3),
                    info jsonb not null,
                    primary key (_id)
                );
            """
            global_dbobj.exec_command(sql_command)

        if not init_only:
            # when sever start and restart, clear all sessions
            db.dlpsession.remove({})
    except:
        raise Exception('Exception::Create session table error')


def get_session_idle_time():
    from system.system_config import admin_setting_config as sys_conf
    system_certification = sys_conf.get_admin_setting_config()
    return int(system_certification.get("idletime", 30)) * 60 * 1000


# check session
def check(request, renew=True):
    sid = request.cookies.get(cookie_key)
    if not sid:
        return False

    try:
        sid = serializer.loads(sid)
    except:
        return False

    db = DBWrapper()
    session = db.dlpsession.find_one({'sess_id': sid})
    if not session:
        return False

    # Skip session update for dashboard Usage API or others (auto_flash=1)
    auto_flash = request.headers.get('auto_flash', '0')
    if renew:
        return touch(session, auto_flash == '1')
    else:
        return session


# touch user session
def touch(session, auto_flash):
    """Update session timestamp unless auto_flash is set.

    Args:
        session (dict): User session dictionary containing '_id' and 'expire'
        auto_flash (bool): If True, skip session update (used by dashboard Usage API ...)

    Returns:
        dict/bool: Updated session dict if successful, False if session expired
    """
    # renew this session
    now = int(time.time() * 1000)
    if now > session['expire']:
        session_timeout_event(session)
        # remove old session
        db.dlpsession.delete_many({'_id': session['_id']})
        return False

    if auto_flash:
        return session

    # update
    idle_time = get_session_idle_time()

    query_str = f"WHERE _id='{session['_id']}'"
    update_str = {'last-access': now, 'expire': now + idle_time}
    sql_str = f"UPDATE dlpsession SET info = info::jsonb || '{json.dumps(update_str)}'::jsonb {query_str} RETURNING _id;"
    session_id = global_dbobj.exec_command(sql_str)
    return session


def store_data(session, name, data):
    db.dlpsession.update({'_id': session['_id']}, {'$set': {name: data}})


def session_timeout_event(sess):
    # from system_control import system_log as sys_log

    if 'created_by' in sess and sess['created_by'] == 'attck':
        return
    remote_ip = ""
    if 'remote_ip' in sess:
        remote_ip = "({})".format(sess['remote_ip'])
    username = ""
    if 'username' in sess:
        username = sess["username"]
    else:
        username = "none"


# create new session
def add(request, response, user):
    sess_id = str(uuid.uuid4())
    # remove old session
    remove_old_session(request)
    if not user or 'name' not in user:
        return

    # Code change by AngelaShih for DB Migration
    query_json = {'sess_id': sess_id}
    query_str = f"WHERE info @> '{json.dumps(query_json)}'"
    sql_str = f"SELECT * FROM dlpsession {query_str};"
    results = global_dbobj.query_all(sql_str)
    records = [dict(row) for row in results]

    while True:
        if not records:
            break
        sess_id = str(uuid.uuid4())

    username = user['name']

    now = int(time.time() * 1000)
    session = {
        'sess_id': sess_id,
        'username': username,
        'roles': user.get('roles', ['tester']),
        'account': user.get('account', ''),
        'radiusServer': user.get('radiusServer', ''),
        'expire': now + get_session_idle_time(),
        'last-access': now
    }
    if 'created_by' in user:
        session['created_by'] = user['created_by']

    if 'remote_ip' in user:
        session['remote_ip'] = user['remote_ip']

    sql_str = f"INSERT INTO dlpsession(info) VALUES ('{json.dumps(session)}') RETURNING _id;"
    session_data = global_dbobj.exec_command(sql_str)
    session['_id'] = str(session_data['_id'])
    encrypted_value = serializer.dumps(session['sess_id'])
    # scheme, host, q_path, qs, fragment = request.urlparts
    scheme = request.scheme
    if scheme.lower() == 'https':
        response.set_cookie(
            cookie_key,
            encrypted_value,
            path=cookie_path,
            secure=True,
            httponly=True,
            samesite='Strict'
        )
    else:
        response.set_cookie(
            cookie_key,
            encrypted_value,
            path=cookie_path,
            httponly=True,
            samesite='Strict'
        )

    return response


# delete one session
def remove(request, response):
    sid = request.cookies.get(cookie_key)
    if sid:
        try:
            sid = serializer.loads(sid)
        except:
            pass
        db.dlpsession.delete_many({'sess_id': sid})
        response.delete_cookie(cookie_key, path=cookie_path)


def remove_all_session():
    # When the certificate file was changed.
    db.dlpsession.remove({})


def get_cookie_last4char(request):
    sid = request.cookies.get(cookie_key)
    return str(sid)[-4:]


# get session user
def get_user(request):
    sid = request.cookies.get(cookie_key)
    if not sid:
        return None
    try:
        sid = serializer.loads(sid)
    except:
        pass
    session = db.dlpsession.find_one({'sess_id': sid})
    if session:
        return session['username']
    return None


#get session
def get_session(request):
    sid = request.cookies.get(cookie_key)
    if not sid:
        return None
    try:
        sid = serializer.loads(sid)
    except:
        pass
    return db.dlpsession.find_one({'sess_id': sid})


# delete one session by user
def remove_by_user(username):
    db.dlpsession.delete_many({'username': username})


# delete old session
def remove_old_session(request):
    sid = request.cookies.get(cookie_key)
    if sid:
        try:
            sid = serializer.loads(sid)
        except:
            pass
        db.dlpsession.delete_many({'sess_id': sid})


def check_session_expired():
    # Need to register schedule
    # Like: session_scheduler = BlockingScheduler() in app.py
    expire = int(time.time() * 1000)
    sql_str = f"SELECT * FROM dlpsession WHERE (info::jsonb->>'expire')::bigint < {expire};"
    results = global_dbobj.query_all(sql_str)
    if not results:
        return

    for sess in results:
        session_timeout_event(dict(sess))
    sql_str = f"DELETE FROM dlpsession WHERE (info::jsonb->>'expire')::bigint < {expire};"
    global_dbobj.exec_command(sql_str)
