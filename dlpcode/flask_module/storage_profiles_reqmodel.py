import uuid
from util.common_log import get_logger
from pydantic import BaseModel, root_validator, validator
from pydantic import Field, conint, constr
from util.enum_ import StorageType
from typing import List, Optional, Literal
import re

logger = get_logger("api")

class AuthInfoModel(BaseModel):
    server: Optional[str] = Field(None, example="************", description="The Address of SMB Server")
    port: Optional[int] = Field(None, example=1, description="The port of the server")
    share_name: Optional[str] = Field(None, example="Share", description="The SMB Share Name")
    anonymous: Optional[bool] = Field(None, example=False, description="Indicates whether anonymous access is allowed")
    username: Optional[str] = Field(None, example="smb_user", description="The SMB User Name")
    password: Optional[str] = Field(None, example="password", description="The password of SMB User")

    key_id: Optional[str] = Field(None, example="AKIAIOSFODNN7EXAMPLE", description="AWS Access Key ID")
    access_key: Optional[str] = Field(None, example="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY", \
                                        description="aws_secret_access_key")

    site_url: Optional[str] = Field(None, example="https://dlp103.sharepoint.com/sites/DLP/", \
                                             description="The URL of the On Line SharePoint site")
    usecredentials : Optional[bool] = Field(None, example=False, description="Indicates whether user credentials or tenant/clientId to be used for authentication")

    tenantid : Optional[str] = Field(None, example="d96bcd1e-a33a-47f2-be46-038b0eaccc3b", description="Tenant Id of the user's Cloud sharepoint account")
    clientid : Optional[str] = Field(None, example="48e51699-030b-453c-b117-4d663a7f8bbb", description="Client Id of the user's Cloud sharepoint account")
    clientsecret: Optional[str] = Field(None, description='Base64 encoded encrypted client secret')

    customer_id : Optional[str] = Field(None, example="C01birj3q", description="Customer Id of the Google Workspace Organization")
    delegated_admin_email: Optional[str] = Field(None, example="<EMAIL>", description="The admin email of the Google Workspace Organization")
    service_account_info: Optional[str] = Field(None, description='The service account info of the Google Workspace Organization')

    cloudtrail_arn: Optional[str] = Field(None, example="arn:aws:s3:::aws-cloudtrail-logs-************-210acd6e/AWSLogs/************/CloudTrail/", description="AWS Cloudtrail s3 bucket ARN")
    audit_log_monitoring : Optional[bool] = Field(True, example=False, description="Indicates whether to enable monitoring audit log")

    @validator('cloudtrail_arn')
    def validate_cloudtrail_arn(cls, v):
        if v is not None:
            # Regular expression pattern for ARN format validation
            # Format: arn:aws:s3:::<bucket-name>/<prefix>/AWSLogs/<account-id>/CloudTrail/
            pattern = r'^arn:aws:s3:::[\w.-]+/.*AWSLogs/\d+/CloudTrail/.*$'
            if not re.match(pattern, v):
                raise ValueError("Invalid AWS CloudTrail ARN format. Expected format: arn:aws:s3:::bucket-name/prefix/AWSLogs/account-id/CloudTrail/")
        return v

class StorageProfileCreateReqModel(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, example="storage1", description="storage name")
    type: StorageType = Field(..., description="1 : AWS S3, 2 : SharePoint(On Line) 3 : SharePoint(ON-Prem) 4:SMB, 6: Google Drive")
    notes: str = Field("", min_length=0, max_length=512, example="This is a storage profile")
    pwd_encrypted: conint(ge=0, le=1) = Field(..., description="1: Password Encrypted, 0: Password Not Encrypted")
    pwd_encrypted2: conint(ge=0, le=1) = Field(..., description="1: Password Encrypted, 0: Password Not Encrypted")
    auth_info: AuthInfoModel = Field(...)

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @root_validator
    def validate_auth_info(cls, values):
        type_ = values.get("type")
        auth = values.get("auth_info")

        if type_ == 6:
            if not auth or not auth.delegated_admin_email or not auth.customer_id or not auth.service_account_info:
                raise ValueError("Google : missing necessary configuration items")
        elif type_ == 4:
            if not auth or not auth.server or not auth.port or not auth.share_name \
                or (not auth.anonymous and (not auth.username or not auth.password)):
                raise ValueError('SMB : missing necessary configuration items')
        elif type_ == 1:
            if not auth or not auth.key_id or not auth.access_key:
                raise ValueError('AWS : missing necessary configuration items')
        elif type_ == 2:
            if not auth or not hasattr(auth, 'usecredentials'):
                raise ValueError('SharePoint(On Line) : missing usecredentials flag')
            usecredentials = auth.usecredentials
            if usecredentials:
                if not auth.site_url or not auth.username or not auth.password:
                    raise ValueError('SharePoint(On Line) : missing necessary configuration items')
            else:
                if not auth.tenantid or not auth.clientid or not auth.clientsecret:
                    raise ValueError('SharePoint(On Line) : missing necessary configuration items')
        elif type_ == 3:
            if not auth or not auth.site_url or not auth.username or not auth.password:
                raise ValueError('SharePoint(ON-Prem) : missing necessary configuration items')
        return values

class StorageProfileGetReqModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    page: conint(ge=0) = Field(None, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")
    name: str = Field(None, min_length=1, max_length=100, example="test1", description="profile name")
    sort_field: str = Field('created_at', max_length=32, description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('asc', description="sort method")
    type: StorageType = Field(None, description="1 : AWS S3, 2 : SharePoint(On Line) 3 : SharePoint(ON-Prem) 4:SMB, 6: Google Drive")

    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class StorageProfileDeleteReqModel(BaseModel):
    id: List[constr(min_length=36, max_length=36)]  = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class StorageProfileUpdateReqModel(BaseModel):
    id: str = Field(..., min_length=36, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="valid UUID format string")
    name: str = Field(None, min_length=1, max_length=100, example="test1", description="profile name")
    type: StorageType = Field(..., description="Storage Profile Type")
    auth_info: AuthInfoModel = Field(...)
    notes: str = Field("", min_length=0, max_length=512, example="This is a storage profile")
    pwd_encrypted: conint(ge=0, le=1) = Field(..., description="1: Password Encrypted, 0: Password Not Encrypted")
    pwd_encrypted2: conint(ge=0, le=1) = Field(..., description="1: Password Encrypted, 0: Password Not Encrypted")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class TestConnectionReqModel(BaseModel):
    type: StorageType = Field(..., description="1 : AWS S3, 2 : SharePoint(On Line) 3 : SharePoint(ON-Prem) 4:SMB, 6: Google Drive")
    pwd_encrypted: conint(ge=0, le=1) = Field(..., description="1: Password Encrypted, 0: Password Not Encrypted")
    pwd_encrypted2: conint(ge=0, le=1) = Field(..., description="1: Password Encrypted, 0: Password Not Encrypted")
    auth_info: AuthInfoModel = Field(...)

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @root_validator
    def validate_auth_info(cls, values):
        type_ = values.get("type")
        auth = values.get("auth_info")

        if type_ == 6:
            if not auth or not auth.delegated_admin_email or not auth.customer_id or not auth.service_account_info:
                raise ValueError("Google : missing necessary configuration items")
        elif type_ == 4:
            if not auth or not auth.server or not auth.port or not auth.share_name \
                or (not auth.anonymous and (not auth.username or not auth.password)):
                raise ValueError('SMB : missing necessary configuration items')
        elif type_ == 1:
            if not auth or not auth.key_id or not auth.access_key:
                raise ValueError('AWS : missing necessary configuration items')
        elif type_ == 2:
            if not auth or not hasattr(auth, 'usecredentials'):
                raise ValueError('SharePoint(On Line) : missing usecredentials flag')
            usecredentials = auth.usecredentials
            if usecredentials:
                if not auth.site_url or not auth.username or not auth.password:
                    raise ValueError('SharePoint(On Line) : missing necessary configuration items')
            else:
                if not auth.tenantid or not auth.clientid or not auth.clientsecret:
                    raise ValueError('SharePoint(On Line) : missing necessary configuration items')
        elif type_ == 3:
            if not auth or not auth.site_url or not auth.username or not auth.password:
                raise ValueError('SharePoint(ON-Prem) : missing necessary configuration items')
        return values