import socket
import psutil

from flask import Blueprint, request
from pydantic import ValidationError

import util.err_codes as Ecodes
from flask_module import session
from util.err_codes import error_response, success_response
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from util.common_log import get_logger
from system import interface

http2_config = Blueprint('http2_config', __name__)
logger = get_logger("dlp")


@http2_config.route('/index', methods=['GET'])
def index():
    return 'This is a http2 config index page'


@http2_config.route('/http2-config', methods=['GET'])
def get_http2_config():
    from service import http2_config_service

    try:
        conf = http2_config_service.get_config()
        if not conf:
            return success_response([])

        return success_response(conf.to_dict())
    except Exception as e:
        logger.error(f"Error in fetch http2 config: {str(e)}")
        return error_response(Ecodes.INTERNAL_ERROR, 500)


def is_port_in_use(port, host="0.0.0.0"):
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.settimeout(1)
        try:
            s.bind((host, port))
        except OSError:
            return True
        return False


def get_process_name(ip, port):
    for conn in psutil.net_connections(kind='inet'):
        if conn.laddr.ip == ip and conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
            try:
                return psutil.Process(conn.pid).name()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                return None
    return None


@http2_config.route('/http2-config', methods=['PUT'])
def put_http2_config():
    from flask_module.http2.config_reqmodel import HTTP2ConfigModel
    from service import http2_config_service
    try:
        data = request.get_json()
        validated_data = HTTP2ConfigModel(**data)
    except ValidationError as e:
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)


    # Check port conflict based on service enabled status and config changes
    if validated_data.serviceenabled:
        # Check reserved ports first
        if validated_data.port in [22, 80, 443, 8088]:
            return error_response(Ecodes.RESOURCE_REFERRED, 409, custom_message="The specified port is already in use.")
        # Get interface IP address
        interface_ip = interface.get_port_ipv4_address_by_name(validated_data.interface)
        if not interface_ip:
            return error_response(Ecodes.INTERNAL_ERROR, 500, custom_message="Please configure the IP address for the interface first.")

        # Get current HTTP2 config from database
        http2_config_in_db = http2_config_service.get_config()

        if http2_config_in_db and http2_config_in_db.port == validated_data.port and http2_config_in_db.interface == validated_data.interface:
            # If port and interface haven't changed, only check if it's not http2-server
            process_name = get_process_name(interface_ip, validated_data.port)
            if process_name and process_name != 'http2-server':
                return error_response(Ecodes.RESOURCE_REFERRED, 409, custom_message="The specified port is already in use.")
        else:
            # If port or interface changed, check if port is in use
            if is_port_in_use(validated_data.port, interface_ip):
                return error_response(Ecodes.RESOURCE_REFERRED, 409, custom_message="The specified port is already in use.")

    try:
        payload = validated_data.dict()
        newconfig = http2_config_service.update_config(payload)
        if newconfig == None:
            return error_response(Ecodes.INTERNAL_ERROR, 500)
    except:
        return error_response(Ecodes.INTERNAL_ERROR, 500)

    try:
        notify_config_update(newconfig)
    except Exception as e:
        logger.error(f"Failed to notify config changes to http2 service: {str(e)}")

    try:
        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value, message="HTTP2 server configuration modified",
                         desc='Configure the HTTP2 server', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
    except Exception as e:
        logger.error(f"Error in event log {e}")

    return success_response(newconfig.to_dict())


def notify_config_update(conf, msg_type='config_update'):
    from util.redis_stream import RedisStream

    logger.info(f"{conf}")
    message_dict = {}
    message_dict['msg_type'] = msg_type
    message_dict['interface'] = conf.interface
    message_dict['port'] = conf.port
    message_dict['serviceenabled'] = str(conf.serviceenabled)
    message_dict['event_record_enable'] = str(conf.event_record_enable)

    if conf.certid is not None:
        message_dict['certid'] = str(conf.certid)
    else:
        message_dict['certid'] = ""

    redis_stream = RedisStream("dlp_http2_config_update")

    if not redis_stream.send_redis_stream_msg(message_dict, maxlen=1000, approximate=True):
        logger.error(f"Failed to send Redis stream msg: {message_dict}")
        return False

    logger.info(f"Send Redis stream msg: {message_dict}")
    return True
