import uuid
from typing import Optional

from pydantic import BaseModel, Field, validator, conint


class HTTP2ConfigModel(BaseModel):
    id: Optional[str] = Field(None, min_length=36, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf',
                              description="valid UUID format string")
    interface: str = Field(..., min_length=3, max_length=32, example='port1',
                           description="data traffic port for the service to bind to")
    port: conint(ge=1, le=65535) = Field(..., description="port number")
    serviceenabled: bool = Field(..., example=True, description="Indicates whether to enable or disable http2 service")
    event_record_enable: bool = Field(..., example=True, description="Indicates whether to enable or disable http2 service log records")
    certid: Optional[str] = Field(None, min_length=0, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf',
                                  description="valid UUID format string")
    info: Optional[dict] = Field(None, example={"key": "value"}, description="additional information")

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

    # if cert_id is provided, check if it is a valid uuid
    @validator('certid')
    def certid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
