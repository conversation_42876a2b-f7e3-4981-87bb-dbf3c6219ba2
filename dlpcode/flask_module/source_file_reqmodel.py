import uuid
from util.common_log import get_logger
from pydantic import BaseModel, validator
from pydantic import Field, constr, conint, confloat, SecretStr
from typing import List, Optional, Literal, Union

logger = get_logger("api")

class CreateSourceFileReqModel(BaseModel):
    name: constr(min_length=1, max_length=1024) = Field(default="")
    template_type: Literal['edm','idm'] = Field(...)
    file_type: Literal['txt','doc','docx','pdf','csv','xlsx'] = Field(...)
    template_id: constr(min_length=36, max_length=36) = Field(None, description="edm template id")
    source_file_id: constr(min_length=36, max_length=36) = Field(None, description="source file id")

class GetSourceFileReqModel(BaseModel):
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
    
class DeleteSourceFileReqModel(BaseModel):
    id: constr(min_length=36, max_length=36)  = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v