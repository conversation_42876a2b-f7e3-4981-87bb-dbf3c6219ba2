import uuid
import re
from util.common_log import get_logger
from pydantic import BaseModel, validator
from pydantic import Field, constr, conint, confloat, SecretStr
from typing import List, Optional, Literal, Union
from enum import Enum
from ipaddress import IPv4Address, IPv4Network, IPv6Address, IPv6Network

logger = get_logger("api")

class CaseInsensitiveEnum(str, Enum):
    def __new__(cls, value):
        obj = str.__new__(cls, value.lower())
        obj._value_ = value.lower()
        return obj

    @classmethod
    def from_string(cls, name):
        try:
            return cls[name.upper()]
        except KeyError:
            raise ValueError(f"{name} is not a valid {cls.__name__}")

class PortName(CaseInsensitiveEnum):
    PORT1 = "port1"
    PORT2 = "port2"
    PORT3 = "port3"
    PORT4 = "port4"
    MGMT = "mgmt"

class InterfaceCfg(BaseModel):
    name: PortName = Field(default=PortName.MGMT)
    ip: Optional[str] = Field(default="")
    netmask: Optional[str] = Field(default="")
    ipv6: str = Field(default="::")
    ipv6Netmask: str = Field(default="64")
    mode: str = Field(default="manual")
    ipv6Mode: str = Field(default="manual")

    class Config:
        extra = "forbid"

    @validator('mode', 'ipv6Mode')
    def check_modes(cls, v):
        if v not in ["manual", "dhcp"]:
            raise ValueError("mode and ipv6Mode must be either 'manual' or 'dhcp'")
        return v

    @validator("ip")
    def validate_ip(cls, value, values):
        if values.get('mode') == 'manual':
            try:
                IPv4Address(value)
            except ValueError:
                raise ValueError("Invalid IPv4 address")
        return value

    @validator('netmask')
    def validate_netmask(cls, v, values):
        if values.get('mode') == 'manual' and v:
            try:
                IPv4Network(f'0.0.0.0/{v}')
            except ValueError:
                raise ValueError("Invalid netmask")
        return v

    @validator('ipv6')
    def validate_ipv6(cls, value, values):
        if values.get('ipv6Mode') == 'manual':
            try:
                IPv6Address(value)
            except ValueError:
                raise ValueError("Invalid IPv6 address")
        return value

    @validator('ipv6Netmask')
    def validate_ipv6_netmask(cls, v, values):
        if values.get('ipv6Mode') == 'manual' and v:
            try:
                IPv6Network(f'::/{v}')
            except ValueError:
                raise ValueError("Invalid IPv6 netmask")
        return v


class SetGatewayModel(BaseModel):
    gateway: str = Field(..., alias="Gateway", description="Gateway")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator("gateway")
    def validate_gateway(cls, value):
        try:
            IPv4Address(value)
        except ValueError:
            raise ValueError(f"Invalid gateway address")


class PostStaticRouteModel(BaseModel):
    dst: str = Field(..., alias="destination", description="Destination")
    gateway: str = Field(..., alias="gateway", description="Gateway")
    interface: str = Field(None, alias="interface", description="Interface")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('dst')
    def validate_dst(cls, value):
        if value == "default":
            return value
        try:
            IPv4Network(value)
        except ValueError:
            raise ValueError("Invalid network segment")
        return value

    @validator('gateway')
    def validate_gateway(cls, value):
        try:
            IPv4Address(value)
        except ValueError:
            raise ValueError("Invalid IP address")
        return value

    @validator('interface')
    def validate_interface(cls, value):
        if value is None or (value in list(PortName)):
            return value
        raise ValueError("Interface must be None or a valid PortName")

class PutStaticRouteModel(BaseModel):
    dst: str = Field(..., alias="destination", description="Destination")
    gateway: str = Field(..., alias="gateway", description="Gateway")
    interface: str = Field(None, alias="interface", description="Interface")
    id: Optional[str] = Field(None, description="id")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('dst')
    def validate_dst(cls, value):
        if value == "default":
            return value
        try:
            IPv4Network(value)
        except ValueError:
            raise ValueError("Invalid network segment")
        return value

    @validator('gateway')
    def validate_gateway(cls, value):
        try:
            IPv4Address(value)
        except ValueError:
            raise ValueError("Invalid IP address")
        return value

    @validator('interface')
    def validate_interface(cls, value):
        if value is None or (value in list(PortName)):
            return value
        raise ValueError("Interface must be None or a valid PortName")

class DelStaticRouteModel(BaseModel):
    id: List[constr(min_length=36, max_length=36)] = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"],
                                                           description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class SetNameServerModel(BaseModel):
    name_server: List[str] = Field(..., alias="NameServer", description="NameServer")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('name_server')
    def check_name_servers(cls, v):
        if len(v) != 2:
            raise ValueError("name_server must contain exactly two elements")
        for ip in v:
            try:
                IPv4Address(ip)
            except ValueError:
                raise ValueError(f"Invalid nameserver address")
        return v


class SetHostnameModel(BaseModel):
    host_name: str = Field(..., alias="hostname", description="hostname")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('host_name')
    def validate_hostname(cls, value):
        if not re.match(r'^[A-Za-z0-9_][A-Za-z0-9_-]*$', value):
            raise ValueError('Invalid hostname. Only letters, numbers, hyphens and underscores are allowed, and hyphens cannot be the first character.')
        return value


class SetTimeModel(BaseModel):
    time: Optional[str] = Field(None, description="time")
    time_zone: Optional[str] = Field(None, description="time_zone")
    type: str = Field(..., description="type")
    interval: Optional[str] = Field(None, description="interval")
    ntp_server: Optional[str] = Field(None, description="ntp_server")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('interval')
    def check_interval(cls, v):
        try:
            v = int(v)
        except ValueError:
            raise TypeError("interval must be an integer")

        if v < 1 or v > 3600:
            raise ValueError('interval must be between 1 and 3600')
        return v

class AdminSettingModel(BaseModel):
    https_cert: str = Field('default', description="https_cert")
    idle_time: conint(ge=1, le=960) = Field(30, description="idle_timeout")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"


class GetSystemCertificateModel(BaseModel):
    sort_field: Literal["name", "comment", "expire"] = Field('name', description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('asc', description="sort method")
    page: conint(ge=0) = Field(1, description="page")
    per_page: conint(ge=0, le=1024) = Field(999, description="page size")
    name: Optional[str] = Field(None, description="certificate name")
    subject: Optional[str] = Field(None, description="certificate subject")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"


class DeleteSystemCertificateModel(BaseModel):
    # required fields
    id: List[constr(min_length=36, max_length=36)] = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"],
                                                           description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class ExportSystemCertificateModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"],
                                                     description="valid UUID format string")

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class ExportSystemConfigModel(BaseModel):
    type: List[Literal["system", "all_data_configuration"]] = Field(...,
                                                                    description="export system or all_data_configuration")


class DownloadGUIFiles(BaseModel):
    name: str = Field(..., description="download file from system gui")

