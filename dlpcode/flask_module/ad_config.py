import traceback
from datetime import datetime
from flask import Blueprint, request, jsonify
from pydantic import ValidationError
from util.common_log import get_logger
from util.redis_stream import RedisStream
from util.random_password.service import RandomPasswordService
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from flask_module import session

ad_config = Blueprint("ad_config", __name__)
logger = get_logger("dlp")


@ad_config.route('/', methods=['POST'])
def handle_ad_config_create():
    from flask_module.ad_config_reqmodel import (
        ADConfigCreateReqModel,
    )    
    from service import ad_config_service

    try:
        # create a new ad_config
        data = request.get_json()
        try:
            reqmodel = ADConfigCreateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")

        # encrypt the password to store in the db
        # save the decrypted password for ad fetch task notif
        if reqmodel.pwd_encrypted == 0:
            decrypted_ad_pwd = reqmodel.ad_password
            reqmodel.ad_password = RandomPasswordService.encrypt_ad_pwd(reqmodel.ad_password)
        else:
            decrypted_ad_pwd = RandomPasswordService.decrypt_ad_pwd(reqmodel.ad_password)
        # this should be treated like a PUT
        if reqmodel.id is not None:
            ad_config = ad_config_service.update_ad_config(reqmodel.id, reqmodel.dict(exclude_none=True))
            if ad_config is None:
                return error_response(Ecodes.NOT_FOUND, 404)
        # this is a POST
        else:
            new_config = ad_config_service.ADConfig()
            new_config.ad_server = reqmodel.ad_server
            new_config.ad_username = reqmodel.ad_username
            new_config.ad_password = reqmodel.ad_password
            new_config.ad_group_prefix = reqmodel.ad_group_prefix
            new_config.ad_fetch_interval = reqmodel.ad_fetch_interval
            new_config.ad_status = reqmodel.ad_status
            ad_config = ad_config_service.create_ad_config(new_config)
            if ad_config is None:
                return error_response(Ecodes.LIMIT_REACHED, 400)
        msg_dict = {}
        msg_dict["msg_type"] = "config_update"
        msg_dict["ad_server"] = reqmodel.ad_server
        msg_dict["ad_username"] = reqmodel.ad_username
        msg_dict["ad_password"] = decrypted_ad_pwd
        msg_dict["ad_group_prefix"] = reqmodel.ad_group_prefix
        msg_dict["ad_fetch_interval"] = reqmodel.ad_fetch_interval
        msg_dict["ad_status"] = reqmodel.ad_status
        notify_ad_config_update(msg_dict)
        try:
            user = session.get_user(request)
            record_event_log(user=user, level=LogLevel.INFO.value, message=f"Configure Active Directory server {reqmodel.ad_server}",
                 desc='Configure the Active Directory server', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
        except Exception as e:
            logger.error(f"Error in event log {e}")
        return success_response(ad_config.to_dict(), 201)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@ad_config.route('/', methods=['GET'])
def handle_ad_config_get():
    from flask_module.ad_config_reqmodel import (
        ADConfigQueryReqModel,
    )    
    from service import ad_config_service

    try:
        # read an existing ad_config
        try:
            args = request.args.to_dict()
            logger.info(f"Get AD config by args {args}")
            reqmodel = ADConfigQueryReqModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")

        ad_id = reqmodel.id
        if ad_id is None:
            # If no id is provided, return all ad_configs
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 10, type=int)
            ad_configs =  ad_config_service.read_all_ad_configs(page, per_page)
            return success_response([ad_config.to_dict() for ad_config in ad_configs], 200)
        else:
            ad_config =  ad_config_service.read_ad_config(ad_id)
            if ad_config is None:
                return error_response(Ecodes.NOT_FOUND, 404)
            return success_response(ad_config.to_dict(), 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@ad_config.route('/test_connection', methods=['POST'])
def test_connection():
    from domain_model.ad_interface import ADInterface
    from flask_module.ad_config_reqmodel import (
        ADConfigTestReqModel,
    )
    try:
        # create a new ad_config
        data = request.get_json()
        try:
            reqmodel = ADConfigTestReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")

        # check if encrypted, if so decrypt the password
        if data.get('pwd_encrypted') == 1:
            reqmodel.ad_password = RandomPasswordService.decrypt_ad_pwd(reqmodel.ad_password)

        init_params = dict(reqmodel)
        ad_obj = ADInterface(init_params)
        if ad_obj.test_connection():
            result = {
                "Errcode": 0,
                "Data": "Connection test passed",
            }
            return success_response(result, 200)
        else:
            result = {
                "Errcode": -1,
                "ErrorMessage": "Connection test failed",
            }
            return error_response(Ecodes.TEST_CONNECTION_ERROR, 400)
    except Exception as e:
        result = {
            "Errcode": -1,
            "ErrorMessage": str(e),
        }
        return error_response(Ecodes.TEST_CONNECTION_ERROR, 400, f"{str(e)}")


"""
@ad_config.route('/', methods=['DELETE'])
def handle_ad_config_delete():
    try:
        # delete an existing ad_config
        try:
            args = request.args.to_dict()
            logger.info(f"Deleting AD config by args {args}")
            reqmodel = ADConfigDeleteReqModel(**args)
        except ValidationError as e:
            params_error = format_param_errors(e)
            logger.error(f"parameter validation error, {params_error}")
            return jsonify({'error': f'parameter validation error, {params_error}'}), 400
        logger.info(f"reqmodel: {reqmodel}")

        ad_config_id = reqmodel.id
        success =  ad_config_service.delete_ad_config(ad_config_id)
        if not success:
            return jsonify({'error': 'AD config not found'}), 404
        msg_dict = {}
        msg_dict["msg_type"] = "config_delete"
        notify_ad_config_update(msg_dict)
        return jsonify({'message': 'AD config deleted'}), 200
    except:
        logger.exception(traceback.format_exc())
        return jsonify({'error': 'internal error'}), 500

@ad_config.route('/', methods=['PUT'])
def handle_ad_config_update():
    try:
        # update an existing ad_config
        data = request.get_json()
        try:
            reqmodel = ADConfigUpdateReqModel(**data)
        except ValidationError as e:
            params_error = format_param_errors(e)
            logger.error(f"parameter validation error, {params_error}")
            return jsonify({'error': f'parameter validation error, {params_error}'}), 400
        logger.info(f"reqmodel: {reqmodel}")

        new_config = ad_config_service.ADConfig()
        new_config.id = data.get('id')
        new_config.ad_server = data.get('ad_server')
        new_config.ad_username = data.get('ad_username')
        new_config.ad_password = data.get('ad_password')
        new_config.ad_group_prefix = data.get('ad_group_prefix')
        new_config.ad_fetch_interval = data.get('ad_fetch_interval')
        updated_ad_config = ad_config_service.update_ad_config(new_config.id, new_config.to_dict())
        if updated_ad_config is None:
            return jsonify({'error': 'AD config not found'}), 404
        msg_dict = {}
        msg_dict["msg_type"] = "config_update"
        msg_dict["ad_server_url"] = new_config.ad_server
        msg_dict["username"] = new_config.ad_username
        msg_dict["password"] = new_config.ad_password
        msg_dict["group_prefix"] = new_config.ad_group_prefix
        msg_dict["fetch_interval"] = new_config.ad_fetch_interval
        notify_ad_config_update(msg_dict)
        return jsonify(updated_ad_config.to_dict()), 200
    except:
        logger.exception(traceback.format_exc())
        return jsonify({'error': 'internal error'}), 500
"""
def notify_ad_config_update(msg_dict):
        message_dict = dict(msg_dict)
        # convert to string as redis does not support bool transfer
        message_dict['ad_status'] = str(message_dict['ad_status'])
        redis_stream = RedisStream("ad_config_update")

        if redis_stream.send_redis_stream_msg(message_dict) == False:
            logger.error(f"notify_ad_config_update: Failed to send redis stream msg: {message_dict}")
            return False

        return True
