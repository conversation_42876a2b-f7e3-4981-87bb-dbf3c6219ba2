import traceback
from flask import Blueprint, request
from pydantic import ValidationError
from util.common_log import get_logger
from util.config import configs
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from flask_module import session
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from service.predefined_datatype_service import check_category_status


logger = get_logger("api")
data_classifier = Blueprint("data_classifier", __name__)


@data_classifier.route('/', methods=['POST'])
def handle_data_classifier_create():
    """
    Create a new Data Classifier from JSON payload.

    Validates input, verifies ML category status, saves the classifier,
    and logs the creation event.
    
    Request JSON:
        Must conform to DataClassifierCreateReqModel schema.

    Returns:
        201 Created on success
        400 Bad Request for validation or ML category issues
        409 Conflict if name already exists
        500 Internal Server Error otherwise
    """
    from flask_module.data_classifier_reqmodel import DataClassifierCreateReqModel
    from domain_model.data_classifier import create_data_classifier
    from psycopg2.errors import UniqueViolation

    try:
        data = request.get_json()
        try:
            reqmodel = DataClassifierCreateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"reqmodel: {reqmodel}")
        
        payload = reqmodel.dict(exclude_none=True)
        payload["is_predefined"] = False
        payload["status"] = True

        ml_categories = []
        for _, condition in payload.get("match_condition", {}).items():
            if "type" in condition and condition["type"] == "ml_category":
                if "main_class_id" in condition:
                    ml_categories.append(condition["main_class_id"])
                if "sub_class_id" in condition:
                    ml_categories.append(condition["sub_class_id"])
        if ml_categories and not check_category_status(ml_categories):
            return error_response(Ecodes.VALIDATION_ERROR, 400, f'Discarded ML categories cannot be used as conditions.')
        
        new_classifier = create_data_classifier(payload, logger)
        
        user = session.get_user(request)    
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Add a new discovery policy {new_classifier.name}", 
                     desc='Add data classifier', type=LogType.DATA_CLASSIFIER.value, action=LogAction.CREATE.value)
        return success_response(new_classifier.to_dict(), 201)
    except UniqueViolation:
        return error_response(Ecodes.DUPLICATE_RESOURCE, 409, "A duplicate Data Classifier name already exists.")
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)
    
@data_classifier.route('/', methods=['DELETE'])
def handle_data_classifier_delete():
    from flask_module.data_classifier_reqmodel import DataClassifierDeleteReqModel
    from domain_model.data_classifier import delete_data_classifier, get_data_classifier_by_id
    from service.data_classifier_service import get_scan_policy_ref
    
    try:
        args = request.args.to_dict(flat=False)
        logger.info(f"Deleting data_classifier by args {args}")
        
        try:
            reqmodel = DataClassifierDeleteReqModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        for id in reqmodel.id:
            classifier = get_data_classifier_by_id(id=id, logger=logger)
            if not classifier:
                return error_response(Ecodes.NOT_FOUND, 404, f"Data classifier not found")
            if classifier.is_predefined:
                return error_response(Ecodes.VALIDATION_ERROR, 400, f"Predefined data classifier cannot be deleted")
            ref = get_scan_policy_ref(id=id)
            if ref:
                return error_response(Ecodes.VALIDATION_ERROR, 400, f"Data classifier refered by scan policy cannot be deleted")
        
        delete_names = delete_data_classifier(reqmodel.id, logger)

        user = session.get_user(request)    
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Delete data_classifier {delete_names}", 
                     desc='Delete data classifier', type=LogType.DATA_CLASSIFIER.value, action=LogAction.DELETE.value)
        return success_response({'message': 'Data classifier deleted'}, 200)
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)
    
@data_classifier.route('/', methods=['PUT'])
def handle_data_classifier_update():
    """
    Update an existing Data Classifier.

    Validates the request, ensures the classifier is editable (not predefined
    or used by a scan policy), checks ML category references, and updates the
    classifier accordingly.

    Request JSON:
        Must conform to DataClassifierUpdateReqModel:

    Returns:
        200 OK: Classifier updated successfully.
        400 Bad Request:
            - Invalid input
            - Discarded ML categories referenced
            - Classifier is predefined or used by a scan policy
        404 Not Found: Classifier ID does not exist.
        500 Internal Server Error: Unexpected server error.
    """
    from flask_module.data_classifier_reqmodel import DataClassifierUpdateReqModel
    from service.data_classifier_service import get_scan_policy_ref
    from domain_model.data_classifier import get_data_classifier_by_id, update_data_classifier
    try:
        data = request.get_json()
        try:
            reqmodel = DataClassifierUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"reqmodel: {reqmodel}")
        
        classifier = get_data_classifier_by_id(id=reqmodel.id, logger=logger)
        if not classifier:
            return error_response(Ecodes.NOT_FOUND, 404)
        if classifier.is_predefined:
            return error_response(Ecodes.VALIDATION_ERROR, 400, f"Predefined data classifier cannot be edit")
        
        if get_scan_policy_ref(id=reqmodel.id, logger=logger):
            return error_response(Ecodes.VALIDATION_ERROR, 400, f"Data classifier refered by scan policy cannot be edit")
        
        payload = reqmodel.dict(exclude_none=True)
        
        ml_categories = []
        for _, condition in payload.get("match_condition", {}).items():
            if "type" in condition and condition["type"] == "ml_category":
                if "main_class_id" in condition:
                    ml_categories.append(condition["main_class_id"])
                if "sub_class_id" in condition:
                    ml_categories.append(condition["sub_class_id"])
        if ml_categories and not check_category_status(ml_categories):
            return error_response(Ecodes.VALIDATION_ERROR, 400, f'Discarded ML categories cannot be used as conditions.')
        
        new_classifier = update_data_classifier(id=reqmodel.id, data=payload, logger=logger)

        user = session.get_user(request)    
        record_event_log(user=user, 
                         level=LogLevel.INFO.value, message=f"Edit data classifier {new_classifier.get('name')}", 
                         desc='Edit data classifier', 
                         type=LogType.DATA_CLASSIFIER.value, 
                         action=LogAction.EDIT.value
                    )
        return success_response(new_classifier, 200)
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@data_classifier.route('/', methods=['GET'])
def handle_data_classifier_query():
    from flask_module.data_classifier_reqmodel import DataClassifierQueryReqModel
    from domain_model.data_classifier import get_data_classifier_by_id

    try:
        # get all conditions
        conditions = request.args.to_dict()
        logger.info(f"conditions: {conditions}")
        try:
            reqmodel = DataClassifierQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        logger.info(f"reqmodel: {reqmodel}")
        classifier = get_data_classifier_by_id(reqmodel.id, logger)
        if classifier is None:
            return error_response(Ecodes.NOT_FOUND, 404)
        
        return success_response(classifier.to_dict(), 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@data_classifier.route('/summary', methods=['GET'])
def handle_data_classifier_summary_query():
    """Get data classifier summary by conditions.
    This endpoint retrieves a summary of data classifiers based on the provided query parameters.
    It supports pagination and filtering based on various attributes of the data classifiers.
    """

    from flask_module.data_classifier_reqmodel import DataClassifierSummaryQueryReqModel
    from service.data_classifier_service import get_data_classifier_summary_by_conditions

    try:
        # get all conditions
        conditions = request.args.to_dict()
        logger.info(f"conditions: {conditions}")
        try:
            reqmodel = DataClassifierSummaryQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        logger.info(f"reqmodel: {reqmodel}")
        results, total = get_data_classifier_summary_by_conditions(reqmodel.dict(exclude_none=True), logger)
        if results is None:
            return error_response(Ecodes.INTERNAL_ERROR, 500)
        
        return success_response({
            "list": results,
            "total": total,
            "page": request.args.get('page'),
            "per_page": request.args.get('per_page'),
        }, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)
    

