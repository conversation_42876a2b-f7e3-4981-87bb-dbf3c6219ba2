from typing import Optional, List, Literal
from pydantic import BaseModel, Field, validator, constr, conint, root_validator
from ipaddress import ip_address
import uuid


class DeviceAccessStatsQueryReqModel(BaseModel):
    ip: Optional[str] = Field(None, description="Device IP address")
    device_identifier: Optional[str] = Field(None, description="Device identifier")
    device_type: Optional[Literal['FOS', 'FCT', 'EMS']] = Field(None, description="Device type (FOS, FCT, or EMS)")
    sort_field: str = Field('updated_at', description="Sort field")
    sort_method: str = Field('desc', description="Sort order")
    page: conint(ge=1) = Field(1, description="Page number")
    per_page: conint(ge=1, le=100) = Field(10, description="Items per page")

    class Config:
        extra = "forbid"

    @validator('sort_field')
    def validate_sort_field(cls, v):
        allowed_fields = [
            'req_query_tag_total_count', 'req_query_tag_total_count_24h',
            'files_in_req_total_count', 'files_in_req_total_count_24h',
            'file_with_tags_total_count', 'file_with_tags_total_count_24h',
            'req_query_config_total_count', 'req_query_config_total_count_24h',
            'req_upload_total_count', 'req_upload_total_count_24h',
            'req_ztna_sync_total_count', 'req_ztna_sync_total_count_24h',
            'last_file_tag_query_time', 'last_upload_time',
            'last_config_query_time', 'last_ztna_sync_time',
            'created_at', 'updated_at'
        ]
        if v not in allowed_fields:
            raise ValueError(f"Sort field must be one of {allowed_fields}")
        return v

    @validator('sort_method')
    def validate_sort_method(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError("Sort method must be 'asc' or 'desc'")
        return v

    @validator('ip')
    def validate_ip(cls, v):
        if v is not None:
            try:
                ip_address(v)
            except ValueError:
                raise ValueError("Invalid IP address format")
        return v


class DeviceAccessStatsDeleteReqModel(BaseModel):
    id: uuid.UUID = Field(..., description="UUID of the device access stats to delete")

    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(str(v))
        except ValueError:
            raise ValueError(f"Invalid UUID: {v}")
        return v