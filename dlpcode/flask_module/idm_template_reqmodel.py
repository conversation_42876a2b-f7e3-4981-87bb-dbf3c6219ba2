import uuid
from util.common_log import get_logger
from pydantic import BaseModel, validator
from pydantic import Field, constr, conint, confloat, SecretStr
from typing import List, Optional, Literal, Union

logger = get_logger("api")

class CreateIDMemplateReqModel(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, example="idm-template-1", description="IDM template name")
    description: str = Field("", max_length=256, description="This is the IDM template name")
    source_file_id: constr(min_length=36, max_length=36)  = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")

    @validator('source_file_id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class UpdateIDMTemplateReqModel(BaseModel):
    id: constr(min_length=36, max_length=36)  = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    name: str = Field(..., min_length=1, max_length=100, example="idm-template-1", description="IDM template name")
    description: str = Field("", max_length=256, description="IDM template notes")
    source_file_id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")

    @validator('id', 'source_file_id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class DeleteIDMTemplateReqModel(BaseModel):
    id: List[constr(min_length=36, max_length=36)]  = Field(description="valid UUID format string")

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
    
class QueryIDMTemplateReqModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(None, example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")
    name: str = Field(None, min_length=1, max_length=100, example="Custom-dtype-01", description="idm template name")
    description: Optional[str] = Field(None, min_length=1, max_length=100, description="idm template note")
    status: Optional[Literal[1,2,3]] = Field(None, description="idm template status")
    source_file_name: Optional[str] = Field(None, min_length=1, max_length=100, description="idm data file name")
    
    sort_field: str = Field('updated_at', max_length=32, description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: conint(ge=0) = Field(1, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
    
    @validator("status", pre=True)
    def parse_status(cls, v):
        if isinstance(v, str):
            if v.isdigit():
                return int(v)
        return v
