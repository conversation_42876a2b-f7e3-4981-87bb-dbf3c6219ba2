# import os, sys
# sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))
import uuid
from util.common_log import get_logger
from pydantic import BaseModel, validator
from pydantic import Field, constr, conint, confloat
from typing import List, Optional, Literal
from uuid import UUID

logger = get_logger("api")

    # Scheme:
    # {
    # 	"title": "DiscoverPolicyCreatReqModel",
    # 	"type": "object",
    # 	"properties": {
    # 		"name": {
    # 			"title": "Name",
    # 			"description": "discover policy name",
    # 			"maxLength": 512,
    # 			"minLength": 1,
    # 			"example": "Policy1",
    # 			"type": "string"
    # 		},
    # 		"status": {
    # 			"title": "Status",
    # 			"description": "discover policy status",
    # 			"default": 0,
    # 			"enum": [
    # 				0,
    # 				1
    # 			],
    # 			"type": "integer"
    # 		},
    # 		"description": {
    # 			"title": "Description",
    # 			"default": "",
    # 			"maxLength": 512,
    # 			"minLength": 0,
    # 			"example": "This is a discover policy",
    # 			"type": "string"
    # 		}
    # 	},
    # 	"required": [
    # 		"name"
    # 	],
    # 	"additionalProperties": false
    # }
class DiscoverPolicyCreateReqModel(BaseModel):
    # required fields
    name: str = Field(..., min_length=1, max_length=100, example="Policy1", description="discover policy name")
    
    # optional fields
    description: str = Field("", min_length=0, max_length=256, example="This is a discover policy")
    status: Literal[0, 1] = Field(1, description="discover policy status")
    copy_id: UUID = Field(None, example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="ID of policy to copy") 
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
class DiscoverPolicyQueryReqModel(BaseModel):
    # optional fields
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    name: str = Field(None, min_length=1, max_length=100, example="Policy1", description="discover policy name")
    description: str = Field(None, min_length=0, max_length=256, example="This is a discover policy")
    #status: Literal[0, 1] = Field(None, description="discover policy status")
    status: conint(ge=0, le=1) = Field(None, description="discover policy status")
    
    sort_field: Literal['id', 'name', 'description', 'created_at', 'updated_at', 'status', 'scan_count', 'rule_count'] = Field('updated_at', description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: conint(ge=0) = Field(None, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
        
class DiscoverPolicyUpdateReqModel(BaseModel):
    # required fields
    id: str = Field(..., min_length=36, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="valid UUID format string")
    
    # optional fields
    name: str = Field(None, min_length=1, max_length=100, example="Policy1", description="discover policy name")
    description: str = Field(None, min_length=0, max_length=256, example="This is a discover policy")
    status: Literal[0, 1] = Field(None, description="discover policy status")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
        
class DiscoverPolicyDeleteReqModel(BaseModel):
    # required fields
    id: List[constr(min_length=36, max_length=36)]  = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
# if __name__ == "__main__":
#     print(DiscoverPolicyCreateReqModel.schema())