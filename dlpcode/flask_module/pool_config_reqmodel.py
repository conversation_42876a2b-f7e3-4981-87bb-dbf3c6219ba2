import uuid
from util.common_log import get_logger
from pydantic import BaseModel, validator, root_validator
from pydantic import Field, constr, conint, confloat
from typing import List, Optional, Literal, Union

logger = get_logger("api")

class PoolConfigCreateReqModel(BaseModel):
    # required fields
    pool_name: str = Field(..., min_length=1, max_length=100, example="pool-1", description="Email Pool Name")
    description: str = Field("", min_length=0, max_length=256, example="This is a finance email pool")
    email_group_list: List[str] = Field(..., min_length=1, max_length=100, example=["fts","fts1"], description="List of email groups in the email pool")
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
class PoolConfigQueryReqModel(BaseModel):
    # optional fields
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    page: conint(ge=0) = Field(1, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
        
class PoolConfigUpdateReqModel(BaseModel):
    # required fields
    id: str = Field(..., min_length=36, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="valid UUID format string")
    pool_name: str = Field(..., min_length=1, max_length=100, example="pool-1", description="Email Pool Name")
    description: str = Field("", min_length=0, max_length=256, example="This is a finance email pool")
    email_group_list: List[str] = Field(..., min_length=1, max_length=100, example=["fts","fts1"], description="List of email groups")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
        
class PoolConfigDeleteReqModel(BaseModel):
    # required fields
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class ListGroupsQueryReqModel(BaseModel):
    page: conint(ge=0) = Field(None, description="page")
    per_page: conint(ge=0, le=1024) = Field(None, description="page size")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @root_validator
    def check_required_fields(cls, values):
        if values.get('page') and not values.get('per_page'):
            values['per_page'] = 10
        if values.get('per_page') and not values.get('page'):
            values['page'] = 1

        return values
