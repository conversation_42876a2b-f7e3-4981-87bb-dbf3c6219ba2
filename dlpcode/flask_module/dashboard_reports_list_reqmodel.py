from pydantic import BaseModel, Field, validator, root_validator, conint, constr

from typing import Dict, Optional, List, Literal, Union
from uuid import UUID


# Main request model
class RetentionPeriodRequestModel(BaseModel):
    retention_all: Optional[int] = None
    once: Optional[int] = None
    daily: Optional[int] = None
    weekly: Optional[int] = None
    monthly: Optional[int] = None

    @root_validator
    def validate_retention_fields(cls, values):
        retention_all = values.get("retention_all")
        components = [values.get(k) for k in ["once", "daily", "weekly", "monthly"]]
        has_components = all(v is not None for v in components)
        has_partial_components = any(v is not None for v in components)

        if retention_all is not None and has_partial_components:
            raise ValueError(
                "Specify either 'retention_all' or all of 'once', 'daily', 'weekly', 'monthly', not both."
            )

        if retention_all is None and not has_components:
            raise ValueError(
                "You must specify either 'retention_all' or all of 'once', 'daily', 'weekly', 'monthly'."
            )

        return values


class DashboardReportsListModel(BaseModel):
    name: str
    status: str
    reportinfo: Dict = Field(default_factory=dict)
    resultset: Dict = Field(default_factory=dict)
    result: str
    attributes: Dict = Field(default_factory=dict)


class DownloadRequestListModel(BaseModel):
    id: str


class CheckIDRequestListModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(
        None,
        example="916aeae8-3b10-4b44-b597-b9eca337fbaf",
        description="valid UUID format string",
    )
    name: str = Field(
        None,
        min_length=1,
        max_length=100,
        example="test_report",
        description="report file nae",
    )
    result: conint(ge=0, le=1) = Field(None, description="result")
    page: conint(ge=0) = Field(1, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")
    sort_field: str = Field("created_at", max_length=32, description="sort field name")
    sort_method: Literal["desc", "asc"] = Field("asc", description="sort method")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
