from util.common_log import get_logger
from pydantic import BaseModel,validator
from pydantic import Field
from typing import List

logger = get_logger("api")

class DiscoverRuleTemplateListQueryReqModel(BaseModel):
    # optional
    name: str = Field(None, examples='PII', description="rule name")
    region: List[str] = Field(None, examples=["US,AU"], description="region list")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
    @validator('region', pre=True)
    def split_values(cls, value):
        if isinstance(value, str):
            return value.split(',')
        return value

class DiscoverRuleTemplateQueryReqModel(BaseModel):
    # required
    id: str = Field(..., description="rule id")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
class DiscoverRuleSummaryQueryReqModel(BaseModel):
    # optional
    id: List[str] = Field(None, examples=["480000,510000"], description="rule id list")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
    @validator('id', pre=True)
    def split_values(cls, value):
        if isinstance(value, str):
            return value.split(',')
        return value