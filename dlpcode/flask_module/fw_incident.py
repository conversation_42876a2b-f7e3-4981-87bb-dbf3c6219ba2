# from flask import Blueprint, request, jsonify
# from pydantic import ValidationError
# from util.common_log import get_logger
# from util.config import configs
# from util.err_codes import error_response, success_response
# import util.err_codes as Ecodes
# from flask_module import session
# from system.system_log import record_event_log, LogLevel, LogAction, LogType
# from service import incidents_common
# from service.incidents_firewall import get_fw_incidents_by_conditions, send_fw_incident

# logger = get_logger("api")
# fw_incident = Blueprint("fw_incident", __name__)

# @fw_incident.route('/status', methods=['PUT'])
# def handle_fw_incident_update_status():
#     try:
#         from flask_module.fw_incident_reqmodel import UpdateFwIncidentStatusReqModel
#         data = request.get_json()
#         req_model = UpdateFwIncidentStatusReqModel(**data)
#     except ValidationError as e:
#         logger.error(e)
#         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
#     try:
#         from service.incidents_firewall import update_fw_incident_status
#         ret = update_fw_incident_status(ids=req_model.ids, value=req_model.status, logger=logger)
#         if ret is None:
#             raise ValueError("Update status failed")
        
#         status_name = incidents_common.ICDStatus(req_model.status).name
#         for id in req_model.ids:
#             record_event_log(
#                 user=session.get_user(request),
#                 level=LogLevel.INFO.value,
#                 message = f"Change status to '{status_name}' for incident {id}",
#                 desc="Change status",
#                 action=LogAction.EDIT.value,
#                 type=LogType.FW_INCIDENT.value,
#                 extend={"incident": id}
#             )
#         incidents, _ = get_fw_incidents_by_conditions({"id": req_model.ids})
#         for incident in incidents:
#             send_fw_incident(incident=incident)
#         return success_response({"ErrorCode": 0, "Data": ret})
#     except Exception as e:
#         logger.error(e)
#         return error_response(Ecodes.INTERNAL_ERROR, 500)
    
# @fw_incident.route('/severity', methods=['PUT'])
# def handle_fw_incident_update_severity():
#     try:
#         from flask_module.fw_incident_reqmodel import UpdateFwIncidentSeverityReqModel
#         data = request.get_json()
#         req_model = UpdateFwIncidentSeverityReqModel(**data)
#     except ValidationError as e:
#         logger.error(e)
#         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
#     try:
#         from service.incidents_firewall import update_fw_incident_severity
#         ret = update_fw_incident_severity(ids=req_model.ids, value=req_model.severity, logger=logger)
#         if ret is None:
#             raise ValueError("Update severity failed")
        
#         severity = incidents_common.SEVERIRY_MAPPING.get(req_model.severity).value
#         for id in req_model.ids:
#             record_event_log(
#                 user=session.get_user(request),
#                 level=LogLevel.INFO.value,
#                 message=f"Change severity to '{severity}' for incident {id}",
#                 desc="Change severity",
#                 action=LogAction.EDIT.value,
#                 type=LogType.FW_INCIDENT.value,
#                 extend={"incident": id}
#             )
#         incidents, _ = get_fw_incidents_by_conditions({"id": req_model.ids})
#         for incident in incidents:
#             send_fw_incident(incident=incident)
#         return success_response({"ErrorCode": 0, "Data": ret})
#     except Exception as e:
#         logger.error(e)
#         return error_response(Ecodes.INTERNAL_ERROR, 500)
    
# @fw_incident.route('/ignored', methods=['PUT'])
# def handle_fw_incident_update_ignored():
#     try:
#         from flask_module.fw_incident_reqmodel import UpdateFwIncidentIgnoredReqModel
#         data = request.get_json()
#         req_model = UpdateFwIncidentIgnoredReqModel(**data)
#     except ValidationError as e:
#         logger.error(e)
#         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
#     try:
#         from service.incidents_firewall import update_fw_incident_ignored
#         ret = update_fw_incident_ignored(ids=req_model.ids, value=req_model.ignored, logger=logger)
#         if ret is None:
#             raise ValueError("Update ignored failed")
        
#         for id in req_model.ids:
#             record_event_log(
#                 user=session.get_user(request),
#                 level=LogLevel.INFO.value,
#                 message=f"Change ignored to '{req_model.ignored}' for incident {id}",
#                 desc="Change ignored",
#                 action=LogAction.EDIT.value,
#                 type=LogType.FW_INCIDENT.value,
#                 extend={"incident": id}
#             )
#         incidents, _ = get_fw_incidents_by_conditions({"id": req_model.ids})
#         for incident in incidents:
#             send_fw_incident(incident=incident)
#         return success_response({"ErrorCode": 0, "Data": ret})
#     except Exception as e:
#         logger.error(e)
#         return error_response(Ecodes.INTERNAL_ERROR, 500)
    
# @fw_incident.route('/false_positive', methods=['PUT'])
# def handle_fw_incident_update_false_positive():
#     try:
#         from flask_module.fw_incident_reqmodel import UpdateFwIncidentFalsePositiveReqModel
#         data = request.get_json()
#         req_model = UpdateFwIncidentFalsePositiveReqModel(**data)
#     except ValidationError as e:
#         logger.error(e)
#         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
#     try:
#         from service.incidents_firewall import update_fw_incident_false_positive
#         ret = update_fw_incident_false_positive(ids=req_model.ids, value=req_model.false_positive, logger=logger)
#         if ret is None:
#             raise ValueError("Update false positive failed")
        
#         for id in req_model.ids:
#             record_event_log(
#                 user=session.get_user(request),
#                 level=LogLevel.INFO.value,
#                 message=f"Change false positive to '{req_model.false_positive}' for incident {id}",
#                 desc="Change false positive",
#                 action=LogAction.EDIT.value,
#                 type=LogType.FW_INCIDENT.value,
#                 extend={"incident": id}
#             )
#         incidents, _ = get_fw_incidents_by_conditions({"id": req_model.ids})
#         for incident in incidents:
#             send_fw_incident(incident=incident)
#         return success_response({"ErrorCode": 0, "Data": ret})
#     except Exception as e:
#         logger.error(e)
#         return error_response(Ecodes.INTERNAL_ERROR, 500)
    
# # @fw_incident.route('/assign', methods=['PUT'])
# # def handle_fw_incident_update_assign():
# #     try:
# #         from flask_module.fw_incident_reqmodel import UpdateFwIncidentAssignReqModel
# #         data = request.get_json()
# #         req_model = UpdateFwIncidentAssignReqModel(**data)
# #     except ValidationError as e:
# #         logger.error(e)
# #         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
# #     try:
# #         return success_response("success")
# #     except Exception as e:
# #         logger.error(e)
# #         return error_response(Ecodes.INTERNAL_ERROR, 500)
    
# # @fw_incident.route('/escalate', methods=['PUT'])
# # def handle_fw_incident_update_escalate():
# #     try:
# #         from flask_module.fw_incident_reqmodel import UpdateFwIncidentEscalateReqModel
# #         data = request.get_json()
# #         req_model = UpdateFwIncidentEscalateReqModel(**data)
# #     except ValidationError as e:
# #         logger.error(e)
# #         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
# #     try:
# #         return success_response("success")
# #     except Exception as e:
# #         logger.error(e)
# #         return error_response(Ecodes.INTERNAL_ERROR, 500)
    
# # @fw_incident.route('/add_whitelist', methods=['PUT'])
# # def handle_fw_incident_add_whitelist():
# #     try:
# #         from flask_module.fw_incident_reqmodel import UpdateFwIncidentWhitelistReqModel
# #         data = request.get_json()
# #         req_model = UpdateFwIncidentWhitelistReqModel(**data)
# #     except ValidationError as e:
# #         logger.error(e)
# #         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
# #     try:
# #         return success_response("success")
# #     except Exception as e:
# #         logger.error(e)
# #         return error_response(Ecodes.INTERNAL_ERROR, 500)
    
# # @fw_incident.route('/disable_policy', methods=['PUT'])
# # def handle_fw_incident_disable_policy():
# #     try:
# #         from flask_module.fw_incident_reqmodel import DisableFwIncidentPolicyReqModel
# #         data = request.get_json()
# #         req_model = DisableFwIncidentPolicyReqModel(**data)
# #     except ValidationError as e:
# #         logger.error(e)
# #         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
# #     try:
# #         return success_response("success")
# #     except Exception as e:
# #         logger.error(e)
# #         return error_response(Ecodes.INTERNAL_ERROR, 500)
    
# # @fw_incident.route('/disable_rule', methods=['PUT'])
# # def handle_fw_incident_disable_rule():
# #     try:
# #         from flask_module.fw_incident_reqmodel import DisableFwIncidentRuleReqModel
# #         data = request.get_json()
# #         req_model = DisableFwIncidentRuleReqModel(**data)
# #     except ValidationError as e:
# #         logger.error(e)
# #         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
# #     try:
# #         return success_response("success")
# #     except Exception as e:
# #         logger.error(e)
# #         return error_response(Ecodes.INTERNAL_ERROR, 500)
        

# @fw_incident.route('/delete', methods=['DELETE'])
# def handle_fw_incident_delete():
#     try:
#         from flask_module.fw_incident_reqmodel import DeleteFwIncidentReqModel
#         args = request.args.to_dict(flat=False)
#         req_model = DeleteFwIncidentReqModel(**args)
#     except ValidationError as e:
#         logger.error(e)
#         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
#     try:
#         from service.incidents_firewall import delete_fw_incidents_by_ids
#         deleted_rows = delete_fw_incidents_by_ids(ids=req_model.id, logger=logger)
#         return success_response({"ErrorCode": 0, "Data": f"{deleted_rows} rows deleted"})
#     except Exception as e:
#         logger.error(e)
#         return error_response(Ecodes.INTERNAL_ERROR, 500)
    

# @fw_incident.route('/list', methods=['GET'])
# def handle_fw_incident_query():
#     try:
#         from flask_module.fw_incident_reqmodel import QueryFwIncidentReqModel
#         args = request.args.to_dict()
#         req_model = QueryFwIncidentReqModel(**args)
#     except ValidationError as e:
#         logger.error(e)
#         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
#     try:
#         from service.incidents_firewall import get_fw_incidents_by_conditions
#         ret = get_fw_incidents_by_conditions(conditions=req_model.dict(exclude_none=True), logger=logger)
#         if ret is None:
#             raise ValueError("Get firewall incidents failed")
#         return success_response({
#             "list": ret[0],
#             "total": ret[1],
#             "page": req_model.page,
#             "per_page": req_model.per_page
#         })
#     except Exception as e:
#         logger.error(e)
#         return error_response(Ecodes.INTERNAL_ERROR, 500)
