""" Flask module for Analysis Dashboard v2 """

from flask import Blueprint, request, jsonify
from util.err_codes import error_response, success_response
from flask_module import session
import util.err_codes as Ecodes
import json
from pydantic import ValidationError
from sqlalchemy.exc import SQL<PERSON>lchemy<PERSON>rror, DBAPIError, OperationalError
from service import analysis_dashboard_v2_service
from util.common_log import get_logger
from exts import Base, Session

analysisdashboardv2 = Blueprint("analysisdashboardv2", __name__)
logger = get_logger("dlp")

# Mapping to return the specified widgetnames
widget_to_method_mapping = {
    "scaninfo": analysis_dashboard_v2_service.get_scan_info,
    "topscanpolicies": analysis_dashboard_v2_service.get_top_scan_policies,
    "sensitivefilesbyfiletypes": analysis_dashboard_v2_service.get_sensitive_files_by_file_types,
    "sensitivefilesbyfiletypesshowmore": analysis_dashboard_v2_service.get_sensitive_files_by_file_types_show_more,
    "scanincidentsbyseverity": analysis_dashboard_v2_service.get_scan_incidents_by_severity,
    "compliancefiles": analysis_dashboard_v2_service.get_compliance_files,
    "aifilecategories": analysis_dashboard_v2_service.get_ai_file_categories,
    "aifilecategoriesshowmore": analysis_dashboard_v2_service.get_ai_file_categories_show_more,
    "sensitivefileowners": analysis_dashboard_v2_service.get_top_sensitive_file_owners,
    "sensitivefileownersshowmore": analysis_dashboard_v2_service.get_top_sensitive_file_owners_show_more,
    "sharedsensitivefileowners": analysis_dashboard_v2_service.get_top_shared_sensitive_file_owners,
    "sharedsensitivefileownersshowmore": analysis_dashboard_v2_service.get_top_shared_sensitive_file_owners_show_more,
    "dormantsensitivefiles": analysis_dashboard_v2_service.get_top_dormant_sensitive_files,
    "dormantsensitivefilesshowmore": analysis_dashboard_v2_service.get_top_dormant_sensitive_files_show_more,
}


@analysisdashboardv2.route("/fetch_dashboard_data", methods=["POST"])
def fetch_dashboard_data():
    from flask_module.analysis_dashboard_v2_reqmodel import AnalysisDashboardv2Model

    response = analysis_dashboard_v2_service.AnalysisDashboardv2Obj()
    validated_data = {}

    if request.method == "POST":
        try:
            data = request.get_json()
            validated_data = AnalysisDashboardv2Model(**data)
            logger.info(f"Analysis Dashboard v2: Validated Data: {validated_data}")
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            with Session() as session:
                if len(validated_data.widgetnames) != 0:
                    # Fetch only the requested widgets
                    all_widgets = widget_to_method_mapping.keys()
                    for fieldname in all_widgets:
                        if (
                            fieldname in validated_data.widgetnames
                            and fieldname in widget_to_method_mapping
                        ):
                            widget_details = validated_data.widgetnames[fieldname]
                            setattr(
                                response,
                                fieldname,
                                widget_to_method_mapping[fieldname](
                                    session, widget_details.dict()
                                ),
                            )
                        else:
                            setattr(response, fieldname, [])
                    logger.debug(
                        f"Response for the Analysis Dashboard v2: Specific widgets: {response}"
                    )
                    response_dict = response.to_dict()
                    return success_response(
                        {
                            k: v
                            for k, v in response_dict.items()
                            if k in validated_data.widgetnames
                        }
                    )
        except SQLAlchemyError as e:
            logger.error(
                f"SQLalchemy error while fetching Analysis Dashboard v2 data: {str(e)}"
            )
            return error_response(
                Ecodes.INTERNAL_ERROR,
                500,
                f"Error while fetching Analysis Dashboard v2 data: {str(e)}",
            )
        except (DBAPIError, OperationalError) as e:
            logger.error(
                f"Database error while fetching Analysis Dashboard v2 data: {str(e)}"
            )
            return error_response(
                Ecodes.INTERNAL_ERROR,
                500,
                f"Error while fetching Analysis Dashboard v2 data: {str(e)}",
            )
        except Exception as e:
            logger.error(f"Error while fetching Analysis Dashboard v2 data: {e}")
            return error_response(
                Ecodes.INTERNAL_ERROR,
                500,
                f"Error while fetching Analysis Dashboard v2 data: {str(e)}",
            )
        return success_response({})
    else:
        return error_response(
            Ecodes.NOT_FOUND,
            404,
            f"Error while fetching Analysis Dashboard v2 data: Method Not Found {str(e)}",
        )
