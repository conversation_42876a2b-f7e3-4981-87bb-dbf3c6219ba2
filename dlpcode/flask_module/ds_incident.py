from flask import Blueprint, request, jsonify
from pydantic import ValidationError
from util.common_log import get_logger
from util.config import configs
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from flask_module import session
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from service import incidents_common
from service.incidents_data_scanning import send_ds_incident, get_ds_incidents_by_conditions

logger = get_logger("api")
ds_incident = Blueprint("ds_incident", __name__)

@ds_incident.route('/status', methods=['PUT'])
def handle_ds_incident_update_status():
    try:
        from flask_module.ds_incident_reqmodel import UpdateDsIncidentStatusReqModel
        data = request.get_json()
        req_model = UpdateDsIncidentStatusReqModel(**data)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        from service.incidents_data_scanning import update_ds_incident_status
        ret = update_ds_incident_status(ids=req_model.ids, value=req_model.status, logger=logger)
        if ret is None:
            raise ValueError("Update status failed")
        
        status_name = incidents_common.ICDStatus(req_model.status).name
        for id in req_model.ids:
            record_event_log(
                user=session.get_user(request),
                level=LogLevel.INFO.value,
                message = f"Change status to '{status_name}'",
                desc="Change status",
                action=LogAction.EDIT.value,
                type=LogType.DS_INCIDENT.value,
                extend={"incident": id}
            )
        incidents, _ = get_ds_incidents_by_conditions(conditions={"id":req_model.ids})
        for incident in incidents:
            send_ds_incident(incident=incident)
        return success_response({"ErrorCode": 0, "Data": ret})
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)
    
@ds_incident.route('/severity', methods=['PUT'])
def handle_ds_incident_update_severity():
    try:
        from flask_module.ds_incident_reqmodel import UpdateDsIncidentSeverityReqModel
        data = request.get_json()
        req_model = UpdateDsIncidentSeverityReqModel(**data)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        from service.incidents_data_scanning import update_ds_incident_severity
        ret = update_ds_incident_severity(ids=req_model.ids, value=req_model.severity, logger=logger)
        if ret is None:
            raise ValueError("Update severity failed")
        
        severity = incidents_common.SEVERIRY_MAPPING.get(req_model.severity).value
        for id in req_model.ids:
            record_event_log(
                user=session.get_user(request),
                level=LogLevel.INFO.value,
                message=f"Change severity to '{severity}'",
                desc="Change severity",
                action=LogAction.EDIT.value,
                type=LogType.DS_INCIDENT.value,
                extend={"incident": id}
            )
        incidents, _ = get_ds_incidents_by_conditions(conditions={"id":req_model.ids})
        for incident in incidents:
            send_ds_incident(incident=incident)
        return success_response({"ErrorCode": 0, "Data": ret})
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)
    
@ds_incident.route('/ignored', methods=['PUT'])
def handle_ds_incident_update_ignored():
    try:
        from flask_module.ds_incident_reqmodel import UpdateDsIncidentIgnoredReqModel
        data = request.get_json()
        req_model = UpdateDsIncidentIgnoredReqModel(**data)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        from service.incidents_data_scanning import update_ds_incident_ignored
        ret = update_ds_incident_ignored(ids=req_model.ids, value=req_model.ignored, logger=logger)
        if ret is None:
            raise ValueError("Update ignored failed")
        
        for id in req_model.ids:
            record_event_log(
                user=session.get_user(request),
                level=LogLevel.INFO.value,
                message=f"Change ignored to '{req_model.ignored}'",
                desc="Change ignored",
                action=LogAction.EDIT.value,
                type=LogType.DS_INCIDENT.value,
                extend={"incident": id}
            )
        incidents, _ = get_ds_incidents_by_conditions(conditions={"id":req_model.ids})
        for incident in incidents:
            send_ds_incident(incident=incident)
        return success_response({"ErrorCode": 0, "Data": ret})
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)
    
@ds_incident.route('/false_positive', methods=['PUT'])
def handle_ds_incident_update_false_positive():
    try:
        from flask_module.ds_incident_reqmodel import UpdateDsIncidentFalsePositiveReqModel
        data = request.get_json()
        req_model = UpdateDsIncidentFalsePositiveReqModel(**data)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        from service.incidents_data_scanning import update_ds_incident_false_positive
        ret = update_ds_incident_false_positive(ids=req_model.ids, value=req_model.false_positive, logger=logger)
        if ret is None:
            raise ValueError("Update false positive failed")
        
        for id in req_model.ids:
            record_event_log(
                user=session.get_user(request),
                level=LogLevel.INFO.value,
                message=f"Change false positive to '{req_model.false_positive}'",
                desc="Change false positive",
                action=LogAction.EDIT.value,
                type=LogType.DS_INCIDENT.value,
                extend={"incident": id}
            )
        incidents, _ = get_ds_incidents_by_conditions(conditions={"id":req_model.ids})
        for incident in incidents:
            send_ds_incident(incident=incident)
        return success_response({"ErrorCode": 0, "Data": ret})
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)
    
# @ds_incident.route('/assign', methods=['PUT'])
# def handle_ds_incident_update_assign():
#     try:
#         from flask_module.ds_incident_reqmodel import UpdateDsIncidentAssignReqModel
#         data = request.get_json()
#         req_model = UpdateDsIncidentAssignReqModel(**data)
#     except ValidationError as e:
#         logger.error(e)
#         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
#     try:
#         return success_response("success")
#     except Exception as e:
#         logger.error(e)
#         return error_response(Ecodes.INTERNAL_ERROR, 500)
    
# @ds_incident.route('/escalate', methods=['PUT'])
# def handle_ds_incident_update_escalate():
#     try:
#         from flask_module.ds_incident_reqmodel import UpdateDsIncidentEscalateReqModel
#         data = request.get_json()
#         req_model = UpdateDsIncidentEscalateReqModel(**data)
#     except ValidationError as e:
#         logger.error(e)
#         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
#     try:
#         return success_response("success")
#     except Exception as e:
#         logger.error(e)
#         return error_response(Ecodes.INTERNAL_ERROR, 500)
    
# @ds_incident.route('/add_whitelist', methods=['PUT'])
# def handle_ds_incident_add_whitelist():
#     try:
#         from flask_module.ds_incident_reqmodel import UpdateDsIncidentWhitelistReqModel
#         data = request.get_json()
#         req_model = UpdateDsIncidentWhitelistReqModel(**data)
#     except ValidationError as e:
#         logger.error(e)
#         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
#     try:
#         return success_response("success")
#     except Exception as e:
#         logger.error(e)
#         return error_response(Ecodes.INTERNAL_ERROR, 500)
    
# @ds_incident.route('/disable_policy', methods=['PUT'])
# def handle_ds_incident_disable_policy():
#     try:
#         from flask_module.ds_incident_reqmodel import DisableDsIncidentPolicyReqModel
#         data = request.get_json()
#         req_model = DisableDsIncidentPolicyReqModel(**data)
#     except ValidationError as e:
#         logger.error(e)
#         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
#     try:
#         return success_response("success")
#     except Exception as e:
#         logger.error(e)
#         return error_response(Ecodes.INTERNAL_ERROR, 500)
    
# @ds_incident.route('/disable_rule', methods=['PUT'])
# def handle_ds_incident_disable_rule():
#     try:
#         from flask_module.ds_incident_reqmodel import DisableDsIncidentRuleReqModel
#         data = request.get_json()
#         req_model = DisableDsIncidentRuleReqModel(**data)
#     except ValidationError as e:
#         logger.error(e)
#         return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
#     try:
#         return success_response("success")
#     except Exception as e:
#         logger.error(e)
#         return error_response(Ecodes.INTERNAL_ERROR, 500)
        

@ds_incident.route('/delete', methods=['DELETE'])
def handle_ds_incident_delete():
    try:
        from flask_module.ds_incident_reqmodel import DeleteDsIncidentReqModel
        args = request.args.to_dict(flat=False)
        req_model = DeleteDsIncidentReqModel(**args)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        from service.incidents_data_scanning import delete_ds_incidents_by_ids
        deleted_rows = delete_ds_incidents_by_ids(req_model.id, logger=logger)
        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Delete scan incident {req_model.id}",
            desc="Delete scan incident",
            action=LogAction.DELETE.value,
            type=LogType.DS_INCIDENT.value
        )
        return success_response({"ErrorCode": 0, "Data": f"{deleted_rows} rows deleted"})

    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)
    

@ds_incident.route('/list', methods=['GET'])
def handle_ds_incident_query():
    try:
        from flask_module.ds_incident_reqmodel import QueryDsIncidentReqModel
        args = request.args.to_dict()
        req_model = QueryDsIncidentReqModel(**args)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        from service.incidents_data_scanning import get_ds_incidents_by_conditions
        from system.dlp_license import is_licensed
        response_data = {}
        limit = -1
        conditions = req_model.dict(exclude_none=True)
        if not is_licensed():
            limit = configs.get('no_license_limitation', {}).get('max_scan_incidents', 1000)
            _, real_count = get_ds_incidents_by_conditions(conditions=conditions, logger=logger)
            response_data['real_count'] = real_count
            ret = get_ds_incidents_by_conditions(conditions=conditions, subquery_limit=limit, logger=logger)
        else:
            ret = get_ds_incidents_by_conditions(conditions=conditions, logger=logger)
        if ret is None:
            raise ValueError("Get data scanning incidents failed")
        
        response_data.update({
            "list": ret[0],
            "total": ret[1],
            "page": req_model.page,
            "per_page": req_model.per_page
        })
        return success_response(response_data)
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)
    
@ds_incident.route('/scans', methods=['GET'])
def handle_ds_incident_scan():
    try:
        from service.incidents_data_scanning import get_ds_incident_scan_info
        ret = get_ds_incident_scan_info(logger=logger)
        if ret is None:
            raise ValueError("Get data scanning incidents scan info failed")
        return success_response(ret)
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)
