import traceback
from util.common_log import get_logger
from system import dlp_cmd
from system import interface
from system import certificate as sys_cert
from system import system_log as sys_log
from system import system_util as sys_util
from system import route as sys_route
from system import system_setting as sys_setting
from system.system_log import record_event_log, LogLevel, LogType, LogAction
from flask_module.system_reqmodel import (InterfaceCfg, SetGatewayModel, SetNameServerModel,
                                          SetHostnameModel, SetTimeModel, AdminSettingModel,
                                          GetSystemCertificateModel, DeleteSystemCertificateModel,
                                          ExportSystemConfigModel, PostStaticRouteModel, PutStaticRouteModel, DelStaticRouteModel,
                                          ExportSystemCertificateModel, DownloadGUIFiles)

from flask_module.controller import system as ctrl_system
from flask import Blueprint
from flask import request, send_from_directory
from flask_module import session

from pydantic import ValidationError
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes

logger = get_logger("api")

system = Blueprint("system", __name__)


@system.route("/portcfg", methods=["GET", "POST"])
def get_port_config():
    if request.method == 'GET':
        try:
            interface_config = {
                "ports": interface.get_all_interfaces(),
                "gateway": sys_route.get_default_ipv4_route()
            }
            return success_response({"ErrorCode": 0, "Data": interface_config})
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == 'POST':
        try:
            validated_params = InterfaceCfg(**request.json)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            check_msg = ctrl_system.check_interface_port(validated_params.dict())
            if check_msg:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, str(check_msg))

            erro_code, message = interface.update_interface_config(validated_params)
            if erro_code.value != 0:
                record_event_log(user=session.get_user(request), level=LogLevel.WARNING.value,
                                 message=f"Set port {validated_params.name} config failed: {message}",
                                 desc='Set port config', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, message)

            record_event_log(user=session.get_user(request), level=LogLevel.INFO.value,
                             message=f"Set port {validated_params.name} config successfully: {message}",
                             desc='Set port config', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
            return success_response({"ErrorCode": 0, "Data": message})
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/setGateway", methods=["POST"])
def set_gateway():
    import ipaddress
    try:
        validated_params = SetGatewayModel(**request.json)
    except ValidationError as e:
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)

    gateway = request.json.get('Gateway', '')
    try:
        gw_addr = ipaddress.ip_address(gateway)
    except ValueError as e:
        return error_response(Ecodes.VALIDATION_ERROR, 400, f'invalid gateway address: {gateway}')

    try:
        # err, msg = sys_route.update_default_route(gateway, '')
        err, msg = sys_route.update_static_route(None, "default", gateway)
        if err != 0:
            record_event_log(user=session.get_user(request), level=LogLevel.WARNING.value,
                             message=f"Set gateway {gateway} failed: {msg}",
                             desc='Set gateway', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
            return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, msg)

        record_event_log(user=session.get_user(request), level=LogLevel.NOTICE.value,
                         message=f"Set gateway {gateway} successfully",
                         desc='Set gateway', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
        return success_response({'ErrorCode': 0})
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@system.route("/static_route", methods=["GET", "POST", "PUT", "DELETE"])
def static_route():
    user = session.get_user(request)

    if request.method == 'GET':
        try:
            return success_response({"ErrorCode": 0, "Data": sys_route.get_static_route()})
        except Exception as e:
            logger.exception("Failed to get static route")
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == 'POST':
        try:
            validated_params = PostStaticRouteModel(**request.json)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, str(e))

        dst = request.json.get('destination', '')
        gateway = request.json.get('gateway', '')
        intf = request.json.get('interface', '')

        err, msg = ctrl_system.is_valid_gateway(dst, gateway)
        if err != 0:
            record_event_log(user=user, level=LogLevel.WARNING.value, message=f"Add gateway {gateway} failed: {msg}",
                             desc='Add route', type=LogType.SYSTEM.value, action=LogAction.ADD.value)
            return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, msg)

        try:
            err, msg = sys_route.update_static_route(intf, dst, gateway)
            if err != 0:
                record_event_log(user=user, level=LogLevel.WARNING.value, message=f"Add gateway {gateway} failed: {msg}",
                                 desc='Add route', type=LogType.SYSTEM.value, action=LogAction.ADD.value)
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, msg)

            record_event_log(user=user, level=LogLevel.INFO.value, message=f"Added gateway {gateway} for destination {dst} successfully",
                             desc='Add route', type=LogType.SYSTEM.value, action=LogAction.ADD.value)
            return success_response({'ErrorCode': 0, "Data": "TO DO UPDATE"})
        except Exception as e:
            logger.exception("Failed to update static route")
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == 'PUT':
        try:
            validated_params = PutStaticRouteModel(**request.json)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, str(e))

        dst = request.json.get('destination', '')
        gateway = request.json.get('gateway', '')
        intf = request.json.get('interface', '')
        id = request.json.get('id', '')

        err, msg = ctrl_system.is_valid_gateway(dst, gateway)
        if err != 0:
            record_event_log(user=user, level=LogLevel.WARNING.value, message=f"Modify gateway {gateway} failed: {msg}",
                             desc='Edit route', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
            return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, msg)

        try:
            err, msg = sys_route.update_static_route(intf, dst, gateway, id)
            if err != 0:
                record_event_log(user=user, level=LogLevel.WARNING.value, message=f"Modify gateway {gateway} failed: {msg}",
                                 desc='Edit route', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, msg)

            record_event_log(user=user, level=LogLevel.INFO.value, message=f"Modified gateway {gateway} for destination {dst} successfully",
                             desc='Edit route', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
            return success_response({'ErrorCode': 0, "Data": "TO DO UPDATE"})
        except Exception as e:
            logger.exception("Failed to update static route")
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == 'DELETE':
        try:
            validated_params = DelStaticRouteModel(**request.args.to_dict(flat=False))
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, str(e))

        # dst = request.json.get('destination', '')
        # intf = request.json.get('interface', '')
        id = validated_params.id[0]
        try:
            # Assuming there is a delete logic for static routes
            err, msg = sys_route.delete_static_route(id)
            if err != 0:
                record_event_log(user=user, level=LogLevel.WARNING.value, message=f"Delete static route failed: {msg}",
                                 desc='Delete route', type=LogType.SYSTEM.value, action=LogAction.DELETE.value)
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, msg)

            record_event_log(user=user, level=LogLevel.INFO.value, message="Delete static route successfully",
                             desc='Delete route', type=LogType.SYSTEM.value, action=LogAction.DELETE.value)
            return success_response({'ErrorCode': 0, "Data": "Route deleted"})
        except KeyError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, f"Missing key: {str(e)}")
        except Exception as e:
            logger.exception("Failed to delete static route")
            return error_response(Ecodes.INTERNAL_ERROR, 500)

@system.route("/diskInfo", methods=["GET"])
def disk_info():
    try:
        return success_response({"ErrorCode": 0, "Data": dlp_cmd.get_disk_info()})
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


# @system.route("/trafficPorts", methods=["GET"])
# def traffic_ports():
#     try:
#         from system.portcfg import get_traffic_ports
#         ports = get_traffic_ports()
#         return success_response({"ErrorCode": 0, "Data": len(ports)})
#     except:
#         logger.exception(traceback.format_exc())
#         return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/nameServer", methods=["GET", "POST"])
def setup_name_server():
    if request.method == 'GET':
        try:
            return success_response({"ErrorCode": 0, "Data": ctrl_system.get_name_server()})
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == 'POST':
        try:
            validated_params = SetNameServerModel(**request.json)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            ret = ctrl_system.set_name_server(request.json)
            if ret.get('ErrorCode', 0) != 0:
                record_event_log(user=session.get_user(request), level=LogLevel.WARNING.value,
                                 message=f"Set DNS server failed: {ret.get('ErrorMessage', '')}",
                                 desc='Set DNS server', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))

            record_event_log(user=session.get_user(request), level=LogLevel.INFO.value,
                             message=f"Set DNS server {validated_params.name_server} successfully",
                             desc='Set DNS server', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
            return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/hostname", methods=["GET", "POST"])
def setup_host_name():
    if request.method == 'GET':
        try:
            return success_response({"ErrorCode": 0, "Data": dlp_cmd.get_sys_hostname()})
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == 'POST':
        try:
            validated_params = SetHostnameModel(**request.json)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            host_name = request.json.get('hostname', '')
            if not host_name:
                record_event_log(user=session.get_user(request), level=LogLevel.WARNING.value,
                                 message=f"Change the system name to {host_name} failed by admin",
                                 desc='Edit host name', type=LogType.SYSTEM.value,
                                 action=LogAction.SETUP.value)
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, 'Invalid hostname')

            ret = dlp_cmd.set_sys_hostname(host_name)
            if ret.get('ErrorCode', 0) != 0:
                record_event_log(user=session.get_user(request), level=LogLevel.WARNING.value,
                                 message=f"Change the system name to {host_name} failed by admin",
                                 desc='Edit host name', type=LogType.SYSTEM.value,
                                 action=LogAction.SETUP.value)
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
            record_event_log(user=session.get_user(request), level=LogLevel.INFO.value,
                             message=f"Change the host name to {host_name} successfully",
                             desc='Edit host name', type=LogType.SYSTEM.value,
                             action=LogAction.SETUP.value)
            return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/allSystemInfo", methods=["GET"])
def get_all_system_info():
    try:
        return success_response({"ErrorCode": 0, "Data": ctrl_system.get_all_system_info()})
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/info", methods=["GET"])
def get_system_info():
    try:
        return success_response({"ErrorCode": 0, "Data": ctrl_system.get_system_info()})
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/platform", methods=["GET"])
def get_platform():
    try:
        from system.hardware import get_platform
        return success_response({"ErrorCode": 0, "Data": get_platform()})
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/status", methods=["GET"])
def get_status_info():
    try:
        return success_response({"ErrorCode": 0, "Data": ctrl_system.get_status()})
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/reboot", methods=["POST"])
def set_reboot():
    try:
        record_event_log(user=session.get_user(request), level=LogLevel.INFO.value,
                         message=f"System reboot by {session.get_user(request)}",
                         desc='System reboot', type=LogType.SYSTEM.value, action=LogAction.REBOOT.value)
        return success_response(dlp_cmd.system_reboot())
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/shutdown", methods=["POST"])
def set_shutdown():
    try:
        record_event_log(user=session.get_user(request), level=LogLevel.INFO.value,
                         message=f"System shutdown by {session.get_user(request)}",
                         desc='System shutdown', type=LogType.SYSTEM.value, action=LogAction.SHUTDOWN.value)
        return success_response(dlp_cmd.system_shutdown())
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/time", methods=["GET", "PUT"])
def setup_time():
    if request.method == 'GET':
        try:
            return success_response({"ErrorCode": 0, "Data": ctrl_system.get_time_data()})
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == 'PUT':
        try:
            validated_params = SetTimeModel(**request.json)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            ret = ctrl_system.set_time_data(request)
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
            return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/getTimeString", methods=["GET"])
def get_time_string():
    try:
        return success_response({"ErrorCode": 0, "Data": dlp_cmd.get_sys_time()})
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/allZones", methods=["GET"])
def get_all_zones():
    try:
        return success_response({"ErrorCode": 0, "Data": dlp_cmd.get_all_zones()})
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/getUsage", methods=["GET"])
def get_system_usage():
    try:
        return success_response({"ErrorCode": 0, "Data": sys_util.get_system_usage()})
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/adminSetting", methods=["GET", "POST"])
def admin_setting():
    if request.method == 'GET':
        try:
            return success_response({"ErrorCode": 0, "Data": sys_setting.get_admin_setting()})
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == 'POST':
        from domain_model import cert_interface

        user = session.get_user(request)
        try:
            validated_params = AdminSettingModel(**request.json)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            if validated_params.https_cert != 'default':
                exist_data = cert_interface.find_exist_cert_name(validated_params.https_cert)
                if len(exist_data) == 0:
                    record_event_log(user=user, level=LogLevel.WARNING.value,
                                     message=f"The https server select certificate {validated_params.https_cert} is invalid",
                                     desc='https server config', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
                    return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, 'Cert name is invalid.')

            record_event_log(user=session.get_user(request), level=LogLevel.INFO.value,
                             message=f"Change the https server certificate to {validated_params.https_cert}, idle time to {validated_params.idle_time}",
                             desc='https server config', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
            sys_setting.set_admin_setting(validated_params.idle_time, validated_params.https_cert)
            return success_response({"ErrorCode": 0})
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/cert", methods=["GET", "POST", "DELETE"])
def setup_system_cert():
    from domain_model import certificate
    from domain_model import cert_interface
    if request.method == 'GET':
        try:
            validated_params = GetSystemCertificateModel(**request.args.to_dict())
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            return success_response(cert_interface.get_all_certs_detail(validated_params, "cert"))
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == "POST":
        try:
            ret = certificate.upload_system_cert(request, cert_type="cert")
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
            else:
                return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == 'DELETE':
        try:
            validated_params = DeleteSystemCertificateModel(**request.args.to_dict(flat=False))
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            ret = cert_interface.delete_system_certs(ids=validated_params.id, cert_type="cert")
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))

            record_event_log(user=session.get_user(request), level=LogLevel.INFO.value,
                             message=f"The certificate is successfully deleted by {session.get_user(request)}",
                             desc='certificate delete', type=LogType.SYSTEM.value, action=LogAction.DELETE.value)
            return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/certName", methods=["GET"])
def get_system_cert_lists():
    try:
        from domain_model import cert_interface
        return success_response({"ErrorCode": 0, "Data": cert_interface.get_certs_list("cert")})
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/cert/export", methods=["GET"])
def export_system_cert():
    from flask import make_response, send_from_directory
    if request.method == 'GET':
        try:
            validated_params = ExportSystemCertificateModel(**request.args.to_dict())
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            config_id = validated_params.id
            ret = ctrl_system.export_cert(request, config_id, "cert")
            if ret.get('ErrorCode') == 0:
                file_dir = ret.get('dl_path')
                file_name = ret.get('dl_file')
                return make_response(send_from_directory(file_dir, file_name))
            else:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage'))
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/cert/import", methods=["POST"])
def import_system_cert():
    from domain_model import certificate
    if request.method == 'POST':
        try:
            ret = certificate.import_cert(request)
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
            else:
                return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/caCert", methods=["GET", "POST", "DELETE"])
def setup_system_ca_cert():
    from domain_model import certificate
    from domain_model import cert_interface
    if request.method == 'GET':
        try:
            validated_params = GetSystemCertificateModel(**request.args.to_dict())
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            return success_response(cert_interface.get_all_certs_detail(validated_params, cert_type="ca_cert"))
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == "POST":
        try:
            ret = certificate.upload_system_cert(request, cert_type="ca_cert")
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
            else:
                return success_response(ret)

        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == 'DELETE':
        try:
            validated_params = DeleteSystemCertificateModel(**request.args.to_dict(flat=False))
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            ret = cert_interface.delete_system_certs(ids=validated_params.id, cert_type="ca_cert")
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))

            record_event_log(user=session.get_user(request), level=LogLevel.INFO.value,
                             message=f"The certificate is successfully deleted by {session.get_user(request)}",
                             desc='certificate delete', type=LogType.SYSTEM.value, action=LogAction.DELETE.value)
            return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/caCertName", methods=["GET"])
def get_system_ca_cert_lists():
    try:
        from domain_model import cert_interface
        return success_response({"ErrorCode": 0, "Data": cert_interface.get_certs_list("ca_cert")})
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/caCert/export", methods=["GET"])
def export_system_ca_cert():
    from flask import make_response, send_from_directory
    if request.method == 'GET':
        try:
            validated_params = ExportSystemCertificateModel(**request.args.to_dict())
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            config_id = validated_params.id
            ret = ctrl_system.export_cert(request, config_id, "ca_cert")
            if ret.get('ErrorCode') == 0:
                file_dir = ret.get('dl_path')
                file_name = ret.get('dl_file')
                return make_response(send_from_directory(file_dir, file_name))
            else:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage'))
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/firmware", methods=["POST"])
def upload_system_image():
    if request.method == "POST":
        try:
            errno, errmsg = ctrl_system.upload_sys_image(request)
            if errno != 0:
                record_event_log(user=session.get_user(request), level=LogLevel.WARNING.value,
                                 message=f"System file upload failed by {session.get_user(request)}",
                                 desc='System upgrade', type=LogType.SYSTEM.value, action=LogAction.UPGRADE.value)
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, errmsg)
            elif errmsg.startswith("Chunk saved"):
                return success_response({"ErrorCode": errno, "Data": errmsg})
            else:
                return success_response({"ErrorCode": 0})
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/upgradeStatus", methods=["GET"])
def upload_system_image_status():
    if request.method == "GET":
        try:
            ret = ctrl_system.get_upload_sys_image_status()
            if ret.get('ErrorCode', -1) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
            return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/license", methods=["GET", "POST"])
def setup_system_license():
    from flask_module import license
    if request.method == 'GET':
        try:
            ret = license.get_upload_license_status(request, "")
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
            else:
                return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == "POST":
        try:
            ret = license.upload_system_license(request, "")

            loglevel = LogLevel.INFO.value
            if ret.get('ErrorCode', 0) != 0:
                loglevel = LogLevel.ERROR.value
            user = session.get_user(request)
            record_event_log(user=user, level=loglevel,
                message=f"{ret.get('ErrorMessage', '')}",
                desc='License upload', type=LogType.SYSTEM.value, action=LogAction.EDIT.value)

            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
            else:
                return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/export", methods=["GET"])
def export_system_config():
    if request.method == "GET":
        try:
            validated_params = ExportSystemConfigModel(**request.args.to_dict(flat=False))
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        try:
            config_file_path, filename = ctrl_system.export_config(request, export_type=validated_params.type)
            return send_from_directory(config_file_path, filename)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/restore", methods=["POST"])
def restore_system_config():
    if request.method == "POST":
        try:
            errno, errmsg = ctrl_system.restore_sys_config(request)
            if errno != 0:
                record_event_log(user=session.get_user(request), level=LogLevel.WARNING.value,
                                 message=f"System file restore failed by {session.get_user(request)}",
                                 desc='System restore', type=LogType.SYSTEM.value, action=LogAction.SETUP.value)
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, errmsg)
            elif errmsg.startswith("Chunk saved"):
                return success_response({"ErrorCode": errno, "Data": errmsg})
            else:
                return success_response({"ErrorCode": 0})
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/restore_status", methods=["GET"])
def restore_system_config_status():
    if request.method == "GET":
        try:
            ret = ctrl_system.get_restore_sys_config_status()
            if ret.get('ErrorCode', -1) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('Data', '').get('Message', ''))
            return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/updateFDSServer", methods=["POST"])
def setup_fds_server():
    from flask_module import license
    if request.method == "POST":
        try:
            ret = license.upload_fds_server(request, "")
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
            else:
                return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/fortiguard/update", methods=["GET"])
def update_from_fortiguard():
    from flask_module import license
    if request.method == "GET":
        try:
            ret = license.update_fgd_controller(request, "")
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
            else:
                return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

@system.route("/getFdgVersion", methods=["GET"])
def serve_get_ftg_version():
    from flask_module import license
    if request.method == "GET":
        try:
            ret = license.server_get_fdg_version(request, "")
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('Data', ''))
            else:
                return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

@system.route("/ML/getUploadStatus", methods=["GET"])
def get_ML_upload_status():
    from flask_module import license
    if request.method == "GET":
        try:
            ret = license.ML_upload_status(request, "")
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('Data', ''))
            else:
                return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

@system.route("/NLP/getUploadStatus", methods=["GET"])
def get_NLP_upload_status():
    from flask_module import license
    if request.method == "GET":
        try:
            ret = license.NLP_upload_status(request, "")
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('Data', ''))
            else:
                return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

@system.route("/DTD/getUploadStatus", methods=["GET"])
def get_DTD_upload_status():
    from flask_module import license
    if request.method == "GET":
        try:
            ret = license.DTD_upload_status(request, "")
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('Data', ''))
            else:
                return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/guiFile/getStatus", methods=["GET"])
def get_gui_file_status():
    try:
        return success_response({"ErrorCode": 0, "Data": ctrl_system.get_system_mode_config("GUIUpload")})
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/guiFile", methods=["GET", "POST"])
def get_gui_file():
    if request.method == 'GET':
        try:
            return success_response(ctrl_system.get_gui_upload_files())
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    elif request.method == 'POST':
        try:
            from system.dlp_license import is_licensed
            # if not is_licensed():
            #     return error_response(Ecodes.UNAUTHORIZED_ERROR, 403,
            #                           "Updating the file is prohibited without a valid license.")

            if ctrl_system.get_system_mode_config("GUIUpload").lower() == 'disable':
                return error_response(Ecodes.UNAUTHORIZED_ERROR, 403,
                                      "Updating the file is prohibited without enabling the setting.")

            ret = ctrl_system.upload_gui_upload_files(request)
            if ret.get('ErrorCode', 0) != 0:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, ret.get('ErrorMessage', ''))
            else:
                return success_response(ret)
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)


@system.route("/guiFile/download", methods=["GET"])
def download_gui_file():
    import os
    from flask import make_response, send_from_directory
    from util.config import get_global_config
    if request.method == 'GET':
        try:
            validated_params = DownloadGUIFiles(**request.args.to_dict())
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            download_name = validated_params.name
            gui_upload_dir = get_global_config().get("system_gui_upload_dir")
            if os.path.exists(os.path.join(gui_upload_dir, download_name)):
                return make_response(send_from_directory(gui_upload_dir, download_name))
            else:
                return error_response(Ecodes.UNPROCESSABLE_ENTITY, 422, "No such file exists.")
        except:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)
