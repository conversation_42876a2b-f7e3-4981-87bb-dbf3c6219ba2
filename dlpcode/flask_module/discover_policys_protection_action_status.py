import traceback
from util.common_log import get_logger
from flask import Blueprint, request
from util.err_codes import error_response, success_response
from util.err_codes import ModErrCode

logger = get_logger("api")
discover_policys_protection_action_status = Blueprint("discover_policys_protection_action_status", __name__)

@discover_policys_protection_action_status.route('/', methods=['POST'])
def discover_protection_action_status():
   from service import discover_rule_service
   try:
      data = request.get_json()
      response = {
         "copy": False,
         "quarantine": False
      }

      for policy_id in data:
         discover_rules, _ = discover_rule_service.get_rules_by_conditions({"policy_id": policy_id}, logger)
         if not discover_rules:
            logger.info("there is no dicouver rule for policy {policy_id}")
            continue

         for rule in discover_rules:
            remediation = rule.get("action", {}).get("remediation", {})
            file_protection_status = remediation.get("file_protection_status")
            if not file_protection_status:
               continue
               
            if remediation.get("file_copy"):
               response["copy"] = True
            if remediation.get("file_quarantine"):
               response["quarantine"] = True
            if response["copy"] and response["quarantine"]:
               return success_response(response, 200)
         
      return success_response(response, 200)
   except:
      logger.exception(traceback.format_exc())
      return error_response(ModErrCode.ErrCode02050028, 500)