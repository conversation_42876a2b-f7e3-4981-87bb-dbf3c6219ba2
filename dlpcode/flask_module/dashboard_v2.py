""" Flask module for Dashboard v2 """

from flask import Blueprint, request, jsonify
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
import json
from pydantic import ValidationError
from sqlalchemy.exc import SQLAlchemyError, DB<PERSON>IError, OperationalError
from service import dashboard_v2_service
from util.common_log import get_logger
from exts import Base, Session

dashboardv2 = Blueprint("dashboardv2", __name__)
logger = get_logger("dlp")

# Mapping to return the specified widgetnames
widget_to_method_mapping = {
    "totalscannedfiles": dashboard_v2_service.get_total_scanned_files,
    "sensitivefileowners": dashboard_v2_service.get_sensitive_file_owners,
    "filestoscan": dashboard_v2_service.get_files_to_scan,
    "dailyfilescansensitiveresults": dashboard_v2_service.get_daily_file_scan_sensitive_results,
    "dailyfilescanscannedresults": dashboard_v2_service.get_daily_file_scan_scanned_results,
    "sensitivefileslabeled": dashboard_v2_service.get_sensitive_files_labeled,
    "sensitivefilesdistribution": dashboard_v2_service.get_sensitive_files_distribution,
    "compliancefiles": dashboard_v2_service.get_compliance_files,
    "compliancefilesshowmore": dashboard_v2_service.get_compliance_files_show_more,
    "aicategories": dashboard_v2_service.get_ai_categories,
}


@dashboardv2.route("/fetch_dashboard_data", methods=["GET", "POST"])
def fetch_dashboard_data():
    from flask_module.dashboard_v2_reqmodel import Dashboardv2Model

    response = dashboard_v2_service.Dashboardv2Obj()
    validated_data = {}

    if request.method == "POST":
        try:
            data = request.get_json()
            validated_data = Dashboardv2Model(**data)
            logger.info(f"Dashboard v2: Validated Data: {validated_data}")
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

    try:
        # Default time filter is Past 24 Hours (86400 epoch in seconds)
        time_filter = {"starttime": 0, "endtime": 86400}
        with Session() as session:
            # When widgetnames list is empty, populate all dashboard widgets
            if request.method == "GET" or len(validated_data.widgetnames) == 0:
                logger.info("Fetching all the Dashboard v2 widgets!")
                # Total scanned files
                response.totalscannedfiles = (
                    dashboard_v2_service.get_total_scanned_files(session)
                )

                # Sensitive file owners
                response.sensitivefileowners = (
                    dashboard_v2_service.get_sensitive_file_owners(session)
                )

                # Total files to scan
                response.filestoscan = (
                    dashboard_v2_service.get_files_to_scan(session)
                )

                # Daily file scan results-sensitive files
                response.dailyfilescansensitiveresults = (
                    dashboard_v2_service.get_daily_file_scan_sensitive_results(session, time_filter)
                )

                # Daily file scan results-scanned files                                   
                response.dailyfilescanscannedresults = (                              
                    dashboard_v2_service.get_daily_file_scan_scanned_results(session, time_filter) 
                )         

                # Sensitive files labeled
                response.sensitivefileslabeled = (
                    dashboard_v2_service.get_sensitive_files_labeled(session)
                )

                # Sensitive files distribution
                response.sensitivefilesdistribution = (
                    dashboard_v2_service.get_sensitive_files_distribution(session)
                )

                # Compliance files
                response.compliancefiles = (
                    dashboard_v2_service.get_compliance_files(session)
                )

                # Compliance files show more
                response.compliancefilesshowmore = (
                    dashboard_v2_service.get_compliance_files_show_more(session)
                )


                # AI Categories
                response.aicategories = (
                    dashboard_v2_service.get_ai_categories(session)
                )

                logger.debug(f"Response for the dashboard v2: All widgets: {response}")
                return success_response(response.to_dict())
            elif request.method == "POST" and len(validated_data.widgetnames) != 0:
                # Fetch only the requested widgets
                all_widgets = widget_to_method_mapping.keys()
                for fieldname in all_widgets:
                    if (
                        fieldname in validated_data.widgetnames
                        and fieldname in widget_to_method_mapping
                    ):
                        widget_details = validated_data.widgetnames[fieldname]
                        setattr(response, fieldname, widget_to_method_mapping[fieldname](session, widget_details.dict()))
                    else:
                        setattr(response, fieldname, [])
                logger.debug(f"Response for the dashboard v2: Specific widgets: {response}")
                response_dict = response.to_dict()
                return success_response({k: v for k, v in response_dict.items() if k in validated_data.widgetnames})
    except SQLAlchemyError as e:
        logger.error(f"SQLalchemy error while fetching Dashboard v2 data: {str(e)}")
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Error while fetching Dashboard v2 data: {str(e)}",
        )
    except (DBAPIError, OperationalError) as e:
        logger.error(f"Database error while fetching Dashboard v2 data: {str(e)}")
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Error while fetching Dashboard v2 data: {str(e)}",
        )
    except Exception as e:
        logger.error(f"Error while fetching Dashboard v2 data: {e}")
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Error while fetching Dashboard v2 data: {str(e)}",
        )
    return success_response({})
