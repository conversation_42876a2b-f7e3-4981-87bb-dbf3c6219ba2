import traceback
from flask import Blueprint, request
from pydantic import ValidationError
from util.common_log import get_logger
from util.config import configs
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from flask_module import session
from system.system_log import record_event_log, LogLevel, LogAction, LogType


logger = get_logger("api")
discover_policy = Blueprint("discover_policy", __name__)


# curl -X POST -H "Content-Type:application/json"  http://localhost:5000/api/v1/discover_policy/ -d '{"name": "my first discover policy", "status": 0, "description": "My first discover policy"}'
@discover_policy.route('/', methods=['POST'])
def handle_discovery_policy_create():
    from flask_module.discover_policy_reqmodel import DiscoverPolicyCreateReqModel
    from service import discover_policy_service
    from psycopg2.errors import UniqueViolation, NoDataFound

    try:
        # create a new discovery_policy
        data = request.get_json()
        try:
            reqmodel = DiscoverPolicyCreateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"reqmodel: {reqmodel}")

        max_discover_policy = configs.get("max_discover_policy", 128)
        if discover_policy_service.check_max_discover_policy(max_discover_policy):
            return error_response(Ecodes.LIMIT_REACHED, 400, f'You can only create a maximum of {max_discover_policy} discover policies')

        new_policy = discover_policy_service.create_policy(reqmodel.dict(exclude_none=True), logger)
        logger.info(f"Discover policy created, {new_policy}")

        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Add a new discovery policy {new_policy.get('name', '')}",
                     desc='Add discovery policy', type=LogType.DISCOVERY_POLICIES.value, action=LogAction.CREATE.value)
        return success_response(new_policy, 201)
    except UniqueViolation:
        return error_response(Ecodes.DUPLICATE_RESOURCE, 409, "A duplicate Discover Policy Name already exists.")
    except NoDataFound:
        return error_response(Ecodes.NOT_FOUND, 404, "Copy policy not found.")
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@discover_policy.route('/', methods=['GET'])
def handle_discovery_policy_get():
    from flask_module.discover_policy_reqmodel import DiscoverPolicyQueryReqModel
    from service import discover_policy_service

    try:
        #conditions = request.args.to_dict(flat=False)
        conditions = request.args.to_dict()
        try:
            reqmodel = DiscoverPolicyQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"reqmodel: {reqmodel}")
        results, total = discover_policy_service.get_policies_by_conditions(reqmodel.dict(exclude_none=True), logger)
        if results is None:
            return error_response(Ecodes.INTERNAL_ERROR, 500)

        return success_response({
            "list": results,
            "total": total,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
        }, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@discover_policy.route('/', methods=['PUT'])
def handle_discovery_policy_update():
    from flask_module.discover_policy_reqmodel import DiscoverPolicyUpdateReqModel
    from service import discover_policy_service
    from service.scan_policy_service import get_discover_policy_check_ref

    # update an existing discovery_policy
    try:
        data = request.get_json()
        logger.info(f"Update discovery_policy: {data}")
        try:
            reqmodel = DiscoverPolicyUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        ref_data = get_discover_policy_check_ref(reqmodel.id, logger)
        if ref_data:
            return error_response(Ecodes.INTERNAL_ERROR, 500, f"Can not edit this discovery policy, it has been used by scan")

        logger.info(f"reqmodel: {reqmodel}")

        # check if the discovery policy exists
        policy = discover_policy_service.read_policy(reqmodel.id)
        if not policy:
            return error_response(Ecodes.NOT_FOUND, 404)

        new_policy = discover_policy_service.update_policy(reqmodel.id, reqmodel.dict(exclude_none=True), logger)
        logger.info(f"Updated discovery_policy: {new_policy}")

        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Edit the discovery policy {new_policy.get('name', '')}",
                     desc='Edit discovery policy', type=LogType.DISCOVERY_POLICIES.value, action=LogAction.EDIT.value)
        return success_response(new_policy, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@discover_policy.route('/', methods=['DELETE'])
def handle_discovery_policy_delete():
    from flask_module.discover_policy_reqmodel import DiscoverPolicyDeleteReqModel
    from service import discover_policy_service

    try:
        # delete an existing discovery_policy
        args = request.args.to_dict(flat=False)
        logger.info(f"Deleting discovery_policy by args {args}")

        try:
            reqmodel = DiscoverPolicyDeleteReqModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        # check scan policy refer
        ref = discover_policy_service.check_ref(reqmodel.id, logger)
        if ref:
            logger.info(f'the discover policy is being referred by the scan policy. refer_map: {ref}')
            return error_response(Ecodes.RESOURCE_REFERRED, 400, f'the discover policy is being referred by the scan policy', refer_map=ref)

        # check EMS device refer
        ems_ref = discover_policy_service.check_ems_device_ref(reqmodel.id, logger)
        if ems_ref:
            logger.info(f'the discover policy is being referred by EMS devices. refer_map: {ems_ref}')
            return error_response(Ecodes.RESOURCE_REFERRED, 400, 'the discover policy is being referred by EMS devices', refer_map=ems_ref)

        policies = ""
        for id in reqmodel.id:
            p = discover_policy_service.read_policy(id)
            if p:
                policies += p.name

        rst = discover_policy_service.delete_policies(reqmodel.id, logger)
        if not rst:
            logger.error(f"delete discover policy failed.")
            return error_response(Ecodes.INTERNAL_ERROR, 500)

        logger.info(f"Deleted discovery_policy {reqmodel.id}")

        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Delete the discovery policy {policies}",
                     desc='Delete discovery policy', type=LogType.DISCOVERY_POLICIES.value, action=LogAction.DELETE.value)
        return success_response({'message': 'Policy deleted', 'deleted_ids': reqmodel.id, 'failed_ids': []}, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)