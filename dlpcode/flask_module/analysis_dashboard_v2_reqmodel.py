from pydantic import BaseModel, Field, validator, root_validator, conint
from typing import Dict, Optional, List, Literal
from uuid import UUID


class WidgetDetails(BaseModel):
    starttime: int = Field(0, description="Start time as epoch timestamp")
    endtime: Optional[int] = Field(None, description="End time as epoch timestamp")
    scanpolicy: Optional[List[UUID]] = Field(None, description="Scan Policy UUID")
    sharetype: Optional[List[str]] = Field(
        None, description="Share type chosen by the user-internal/external/public"
    )
    option: Optional[List[str]] = Field(None, description="Option chosen by the user")
    view: Optional[str] = Field(
        "top5",
        description="Fetches the view mentioned, can be either top5 for the main widget or top 10 for show more, default is main (top5)",
    )


class AnalysisDashboardv2Model(BaseModel):
    widgetnames: Optional[
        Dict[
            Literal[
                "scaninfo",
                "topscanpolicies",
                "sensitivefilesbyfiletypes",
                "sensitivefilesbyfiletypesshowmore",
                "scanincidentsbyseverity",
                "compliancefiles",
                "aifilecategories",
                "aifilecategoriesshowmore",
                "sensitivefileowners",
                "sensitivefileownersshowmore",
                "sharedsensitivefileowners",
                "sharedsensitivefileownersshowmore",
                "dormantsensitivefiles",
                "dormantsensitivefilesshowmore",
            ],
            WidgetDetails,
        ]
    ] = Field(
        None,
        description="Dictionary of widget names with their respective configurations requested to poplulate the analysis dashboard",
    )
