from flask import Blueprint, request
from sqlalchemy import exists
from notifications.notifier.factory import notifier_factory
from notifications.model.notification import Template, NotificationConnector
from exts import Session
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from util.common_log import get_logger
from notifications.enums import NotifierTypeEnum, NotificationTypeEnum
from notifications.service import encrypt_config, validate_connector
from domain_model.discover_policy_v2 import DiscoverPolicyV2
from service.dashboard_reports_service import DashboardReport

logger = get_logger("api")
notification = Blueprint('notification', __name__)

@notification.route('/template', methods=['GET'])
def list_templates():
    """List notification templates with filtering, sorting, and pagination."""
    try:
        # Parse query parameters
        name = request.args.get('name')
        notification_type = request.args.get('notification_type')
        notifier_type = request.args.get('notifier_type')
        connector_id = request.args.get('connector_id')
        sort_field = request.args.get('sort_field', 'created_at')
        sort_method = request.args.get('sort_method', 'desc')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 100))
        template_type = request.args.get('template_type')

        allowed_sort_fields = ['name', 'notifier_type', 'notification_type', 'created_at', 'updated_at']
        if sort_field not in allowed_sort_fields:
            sort_field = 'created_at'
        if sort_method not in ['asc', 'desc']:
            sort_method = 'desc'

        session = Session()
        try:
            q = session.query(Template).filter_by(is_default=False)
            if name:
                q = q.filter(Template.name.ilike(f"%{name}%"))
            if notification_type:
                q = q.filter(Template.notification_type == notification_type)
            if notifier_type:
                q = q.filter(Template.notifier_type == notifier_type)
            if connector_id:
                q = q.filter(Template.connector_id == connector_id)
            if template_type:
                q = q.filter(Template.template_type == template_type)

            total = q.count()
            sort_column = getattr(Template, sort_field)
            if sort_method == 'desc':
                q = q.order_by(sort_column.desc())
            else:
                q = q.order_by(sort_column.asc())
            q = q.offset((page - 1) * per_page).limit(per_page)

            templates = q.all()

            # Add reference information
            template_list = []
            for t in templates:
                template_dict = t.to_dict()
                # Check if template is referenced by report
                report = session.query(DashboardReport).filter_by(notification_id=t.id).all()
                # TODO：Check if template is referenced by DLP policy
                # discover_policy = session.query(DiscoverPolicyV2).filter_by(notification_id=t.id).all()
                template_dict['referenced'] = len(report) # + len(discover_policy)
                template_list.append(template_dict)

            return success_response({
                'list': template_list,
                'page': page,
                'per_page': per_page,
                'total': total
            })
        finally:
            session.close()
    except ValueError as e:
        return error_response(Ecodes.VALIDATION_ERROR, 400, 'Invalid pagination parameters')
    except Exception as e:
        return error_response(Ecodes.INTERNAL_ERROR, 500, 'Internal server error')

@notification.route('/template', methods=['POST'])
def create_template():
    """Create a new notification template."""
    try:
        data = request.get_json()
        if not data:
            return error_response(Ecodes.VALIDATION_ERROR, 400, 'Request body is required')
        
        errors = []
        required_fields = ['name', 'notifier_type', 'notification_type', 'template', 'connector_id']
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")
        if errors:
            return error_response(Ecodes.VALIDATION_ERROR, 400, errors)

        if not NotifierTypeEnum.is_valid(data['notifier_type']):
            errors.append(f"Invalid notifier_type: {data['notifier_type']}")
        if not NotificationTypeEnum.is_valid(data['notification_type']):
            errors.append(f"Invalid notification_type: {data['notification_type']}")
        if errors:
            return error_response(Ecodes.VALIDATION_ERROR, 400, errors)

        template = data['template']
        notifier_type = data['notifier_type']
        if notifier_type == 'email':
            if 'subject' not in template or 'content' not in template:
                errors.append('Email template must have subject and content')
            if not isinstance(template.get('subject', ''), str):
                errors.append('Email subject must be string')
            if not isinstance(template.get('content', ''), str):
                errors.append('Email content must be string')
        else:
            if 'content' not in template:
                errors.append(f"{notifier_type} template must have content field")
            if not isinstance(template.get('content', ''), str):
                errors.append(f"{notifier_type} content must be string")
        if errors:
            return error_response(Ecodes.VALIDATION_ERROR, 400, errors)

        session = Session()
        try:
            if session.query(exists().where(Template.name == data.get('name'))).scalar():
                return error_response(Ecodes.VALIDATION_ERROR, 400, 'A duplicate name already exists')
            t = Template(
                name=data['name'],
                notifier_type=data['notifier_type'],
                notification_type=data['notification_type'],
                template_type=data.get('template_type', 'message'),
                template=data['template'],
                connector_id=data.get('connector_id'),
                extra=data.get('extra', {})
            )
            session.add(t)
            session.commit()
            return success_response({'id': str(t.id)})
        except Exception as e:
            session.rollback()
            return error_response(Ecodes.INTERNAL_ERROR, 400, 'Database insert error')
        finally:
            session.close()
    except Exception as e:
        return error_response(Ecodes.INTERNAL_ERROR, 500, 'Internal server error')

@notification.route('/template', methods=['PUT'])
def update_template():
    """Update an existing notification template by id (preferred) or name."""
    try:
        data = request.get_json()
        if not data:
            return error_response(Ecodes.VALIDATION_ERROR, 400, 'Request body is required')
        
        template_id = data.get('id')
        name = data.get('name')
        if not template_id and not name:
            return error_response(Ecodes.VALIDATION_ERROR, 400, 'id or name is required')
        
        session = Session()
        try:
            t = None
            if template_id:
                t = session.query(Template).filter_by(id=template_id).first()
            if not t and name:
                t = session.query(Template).filter_by(name=name).first()
            if not t:
                session.close()
                return error_response(Ecodes.NOT_FOUND, 404, 'not found')
            t.notifier_type = data.get('notifier_type', t.notifier_type)
            t.notification_type = data.get('notification_type', t.notification_type)
            t.template = data.get('template', t.template)
            t.template_type = data.get('template_type', t.template_type)
            t.connector_id = data.get('connector_id', t.connector_id)
            t.extra = data.get('extra', t.extra)
            session.commit()
            return success_response(t.to_dict())
        finally:
            session.close()
    except Exception as e:
        return error_response(Ecodes.INTERNAL_ERROR, 500, 'Internal server error')

@notification.route('/template', methods=['DELETE'])
def delete_template():
    """Batch delete notification templates by id list."""
    try:
        data = request.get_json()
        if not data:
            return error_response(Ecodes.VALIDATION_ERROR, 400, 'Request body is required')
        
        template_ids = data.get('id')
        if not template_ids or not isinstance(template_ids, list):
            return error_response(Ecodes.VALIDATION_ERROR, 400, 'id (list) is required')
        
        session = Session()
        deleted = []
        not_found = []
        try:
            for tid in template_ids:
                t = session.query(Template).filter_by(id=tid).first()
                if t:
                    session.delete(t)
                    deleted.append(str(tid))
                else:
                    not_found.append(str(tid))
            session.commit()
            return success_response({"deleted": deleted, "not_found": not_found})
        finally:
            session.close()
    except Exception as e:
        return error_response(Ecodes.INTERNAL_ERROR, 500, 'Internal server error')

# Connector related API
@notification.route('/connector', methods=['GET'])
def list_connectors():
    """List all notification connectors with filtering, sorting, and pagination."""
    try:
        # Parse query parameters
        name = request.args.get('name')
        notifier_type = request.args.get('notifier_type')
        sort_field = request.args.get('sort_field', 'created_at')
        sort_method = request.args.get('sort_method', 'asc')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 100))

        allowed_sort_fields = ['name', 'notifier_type', 'created_at', 'updated_at']
        if sort_field not in allowed_sort_fields:
            sort_field = 'created_at'
        if sort_method not in ['asc', 'desc']:
            sort_method = 'asc'

        session = Session()
        try:
            q = session.query(NotificationConnector)
            if notifier_type:
                q = q.filter_by(notifier_type=notifier_type)
            if name:
                q = q.filter(NotificationConnector.name.ilike(f"%{name}%"))

            total = q.count()
            sort_column = getattr(NotificationConnector, sort_field)
            if sort_method == 'desc':
                q = q.order_by(sort_column.desc())
            else:
                q = q.order_by(sort_column.asc())
            q = q.offset((page - 1) * per_page).limit(per_page)

            connectors = q.all()

            # Add template reference information
            connector_list = []
            for c in connectors:
                connector_dict = c.to_dict()
                # Check if connector is referenced by templates
                templates = session.query(Template).filter_by(connector_id=c.id).all()
                connector_dict['referenced'] = len(templates)
                connector_list.append(connector_dict)

            return success_response({
                'list': connector_list,
                'page': page,
                'per_page': per_page,
                'total': total
            })
        finally:
            session.close()
    except ValueError as e:
        return error_response(Ecodes.VALIDATION_ERROR, 400, 'Invalid pagination parameters')
    except Exception as e:
        return error_response(Ecodes.INTERNAL_ERROR, 500, 'Internal server error')

@notification.route('/connector', methods=['POST'])
def create_connector():
    """Create a new notification connector."""
    data = request.get_json()
    success, msg = validate_connector(data['notifier_type'], data['config'])
    if not success:
        return error_response(Ecodes.VALIDATION_ERROR, 400, msg or 'Connection test failed, check configuration')
    if not NotifierTypeEnum.is_valid(data['notifier_type']):
        return error_response(Ecodes.VALIDATION_ERROR, 400, f"Invalid notifier_type: {data['notifier_type']}")

    config = encrypt_config(data['notifier_type'], data['config'])

    session = Session()
    try:
        if session.query(exists().where(NotificationConnector.name == data.get('name'))).scalar():
            return error_response(Ecodes.VALIDATION_ERROR, 400, 'A duplicate name already exists')
        c = NotificationConnector(
            name=data['name'],
            notifier_type=data['notifier_type'],
            config=config,
            extra=data.get('extra', {})
        )
        session.add(c)
        session.commit()
        return success_response({"id": str(c.id)})
    except Exception as e:
        session.rollback()
        return error_response(Ecodes.INTERNAL_ERROR, 400, "Database insert error")
    finally:
        session.close()

@notification.route('/connector', methods=['PUT'])
def update_connector():
    """Update a notification connector by id, using body data."""
    try:
        data = request.get_json()
        if not data:
            return error_response(Ecodes.VALIDATION_ERROR, 400, 'Request body is required')
        
        connector_id = data.get('id')
        if not connector_id:
            return error_response(Ecodes.VALIDATION_ERROR, 400, 'id is required')
        
        session = Session()
        try:
            c = session.query(NotificationConnector).filter_by(id=connector_id).first()
            if not c:
                return error_response(Ecodes.NOT_FOUND, 404, 'not found')
            success, msg = validate_connector(c.notifier_type, data.get('config', c.config))
            if not success:
                return error_response(Ecodes.VALIDATION_ERROR, 400, msg or 'Connection test failed, check configuration')
            config = encrypt_config(c.notifier_type, data.get('config', c.config))
            c.config = config
            c.extra = data.get('extra', c.extra)
            session.commit()
            notifier_factory.clear_notifier(c.id, c.notifier_type)
            return success_response(c.to_dict())
        finally:
            session.close()
    except Exception as e:
        return error_response(Ecodes.INTERNAL_ERROR, 500, 'Internal server error')

@notification.route('/connector', methods=['DELETE'])
def delete_connector():
    """Batch delete notification connectors by id list."""
    try:
        data = request.get_json()
        if not data:
            return error_response(Ecodes.VALIDATION_ERROR, 400, 'Request body is required')
        
        connector_ids = data.get('id')
        if not connector_ids or not isinstance(connector_ids, list):
            return error_response(Ecodes.VALIDATION_ERROR, 400, 'id (list) is required')
        
        session = Session()
        deleted = []
        not_found = []
        referenced = []
        try:
            for cid in connector_ids:
                templates = session.query(Template).filter_by(connector_id=cid).all()
                if templates:
                    template_names = [t.name for t in templates]
                    referenced.append(f"Connector {cid} referenced by Templates: {template_names}")
                    continue
                connector = session.query(NotificationConnector).filter_by(id=cid).first()
                if not connector:
                    not_found.append(str(cid))
                    continue
                session.delete(connector)
                deleted.append(str(cid))
            session.commit()
            if referenced:
                return error_response(Ecodes.VALIDATION_ERROR, 400, {
                    "deleted": deleted,
                    "not_found": not_found,
                    "referenced": referenced
                })
            return success_response({
                    "deleted": deleted,
                    "not_found": not_found
            })
        finally:
            session.close()
    except Exception as e:
        return error_response(Ecodes.INTERNAL_ERROR, 500, 'Internal server error')

@notification.route('/connector/test', methods=['POST'])
def test_connector():
    """Test notification connector configuration."""
    try:
        data = request.get_json()
        if not data:
            return error_response(Ecodes.VALIDATION_ERROR, 400, 'Request body is required')

        notifier_type = data.get('notifier_type')
        config = data.get('config')

        if not notifier_type:
            return error_response(Ecodes.VALIDATION_ERROR, 400, 'notifier_type is required')
        if not config:
            return error_response(Ecodes.VALIDATION_ERROR, 400, 'config is required')

        if not NotifierTypeEnum.is_valid(notifier_type):
            return error_response(Ecodes.VALIDATION_ERROR, 400, f"Invalid notifier_type: {notifier_type}")

        # Test connection using existing validate_connector function
        success, msg = validate_connector(notifier_type, config, True)

        if success:
            return success_response({'message': 'Connection test successful'})
        else:
            return error_response(Ecodes.VALIDATION_ERROR, 400, msg or 'Connection test failed')

    except Exception as e:
        return error_response(Ecodes.INTERNAL_ERROR, 500, 'Internal server error')
