import re
import json
import os
import time
from functools import wraps
from flask import make_response, send_from_directory
from system import dlp_cmd
from system import export
from flask_module import session
from util.config import get_global_config

# invalid characters
INVALID_CHARS = '\&|\<|\>|\"|\'|\/'

GLOBAL_CONFIG = get_global_config()


def detect_illegal_ip(ipaddr):
    for segment in ipaddr.split('.'):
        if len(segment) > 1 and segment.startswith('0'):
            raise ValueError('{} is illegal.'.format(ipaddr))


def check_interface_port(ports):
    from system.interface import InterfaceMode
    import ipaddress

    # validate name
    reg = re.compile('^[A-Za-z0-9:_-]*$')
    # validate netmask
    reg_mask = re.compile('^[0-9.]*$')
    name = ports.get('name')
    mode = ports.get('mode')
    if mode not in [InterfaceMode.MANUAL.value, InterfaceMode.DHCP.value]:
        return f'Invalid mode'
    if name == '' or reg.match(name) is None:
        return f'Invalid port name: {name}'
    if ports.get('ip', '') not in ['', None]:
        try:
            ipaddress.ip_address(ports.get('ip'))
        except ValueError as e:
            return f'Invalid ip address: {ports.get("ip")}'
        if ports.get('netmask') == '':
            return f'Netmask is empty'
        if reg_mask.match(ports.get('netmask')) is None:
            return f'Invalid netmask'
        if ports.get('netmask') == '0.0.0.0' and ports.get('ip') != '0.0.0.0':
            return f'Invalid ip/netmask: {ports.get("ip")}/{ports.get("netmask")}'
        try:
            ipaddress.ip_network(ports.get("ip") + '/' + ports.get('netmask'), strict=False)
        except ValueError as e:
            return f'Invalid IP/Netmask {ports.get("ip")}/{ports.get("netmask")} on {name}.'

    if ports.get('ipv6', '') not in ['', None]:
        if reg_mask.match(ports.get('ipv6Netmask')) is None:
            return f'Invalid IPv6 netmask'
    return ''


def get_name_server():
    with open('/etc/resolv.conf', 'r') as f:
        resolvconf = f.read().splitlines()
    nameserver = [line.split('nameserver ')[1] for line in resolvconf if line.startswith('nameserver ')]
    return nameserver


def set_name_server(nameserver):
    import ipaddress
    import fcntl

    if 'NameServer' not in nameserver or not nameserver['NameServer']:
        return {'ErrorCode': -1,
                'ErrorMessage': 'Get nameserver parameter error!'}

    if len(nameserver['NameServer']) > 2:
        return {'ErrorCode': -1,
                'ErrorMessage': 'DNS Server only support 2 lists.'}

    file_content = ''
    for line in nameserver['NameServer']:
        try:
            ipaddress.ip_address(line)
            detect_illegal_ip(line)
            file_content += "nameserver " + line + "\n"
        except ValueError:
            return {'ErrorCode': -1,
                    'ErrorMessage': 'Invalid DNS address: ' + line}
    with open('/etc/resolv.conf', 'w') as f:
        fcntl.flock(f, fcntl.LOCK_EX)
        f.write(file_content)
        fcntl.flock(f, fcntl.LOCK_UN)
    return {'ErrorCode': 0}

def get_zone_label(zone):
    with open('/dlpcode/system/config/zones.json') as json_data:
        zone_list = json.load(json_data)

    selected_zone = zone
    for zone_item in zone_list:
        if zone_item['value'] == zone:
            selected_zone = zone_item['label']
            break
    return selected_zone

def get_all_system_info():

    def system_status():
        system_status = dlp_cmd.get_system_status()
        info = {key: value for key, value in system_status.items()}
        return info

    def system_time():
        info = {}
        system_time = dlp_cmd.get_time()
        if system_time:
            info['systime'] = system_time
        return info

    def system_uptime():
        from system.dlp_time import get_system_uptime
        info = {}
        uptime = get_system_uptime()
        if uptime:
            info['uptime'] = uptime
        return info

    def time_zone():
        info = {}
        zone = dlp_cmd.get_zone()
        if zone:
            info['timezone'] = get_zone_label(zone)
        return info

    def disk_info():
        info = {}
        disk_info = dlp_cmd.get_disk_info()
        if disk_info:
            info['diskInfo'] = disk_info
        return info

    def license_status():
        from system import dlp_license
        info = {}
        info['license'] = dlp_license.get_license_status()
        return info

    info = {}
    info.update(system_status())
    info.update(system_time())
    info.update(system_uptime())
    info.update(time_zone())
    info.update(disk_info())
    info.update(license_status())
    return info


def get_system_info():
    #from system.portcfg import get_portcfg
    from system.hardware import get_platform
    from system.dlp_license import get_sys_license_status

    result = {
        "Platform": get_platform(),
        "Version": dlp_cmd.get_sys_prod_name(),
        "SN": dlp_cmd.get_sys_sn(),
        #"PortsCfg": get_portcfg(),
        "TimeZones": dlp_cmd.get_all_zones(),
        "License": get_sys_license_status()
    }
    return result


def get_status():
    from system.dlp_time import get_system_uptime
    status = {
        'hostname': dlp_cmd.get_sys_hostname(),
        'time': dlp_cmd.get_sys_time(),
        'uptime': get_system_uptime(),
        'logdisk': dlp_cmd.get_log_disk_status(),
        'diskInfo': dlp_cmd.get_disk_info()
    }
    return status


def get_time_data():
    from system import ntp
    ntp_data = ntp.ntp_get_service_config()
    time_type = 'ntp' if ntp_data.get('status', False) else 'manual'
    time_data = {
        'time': dlp_cmd.get_time(),
        'time_zone': dlp_cmd.get_zone(),
        'type': time_type
    }
    if time_type == 'ntp':
        time_data['ntp_server'] = ntp_data.get('server', '')
        time_data['interval'] = ntp_data.get('interval', '')
    return time_data


def is_ip_or_domain(value):
    # IP
    ip_pattern = re.compile(r"^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$")

    # domain
    domain_pattern = re.compile(r"^(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$")

    if ip_pattern.match(value):
        return all(0 <= int(part) <= 255 for part in value.split('.'))

    if domain_pattern.match(value):
        return True

    return False


def set_time_data(request):
    from system import ntp
    from system.system_log import record_event_log, LogLevel, LogType, LogAction

    user = session.get_user(request)
    config = request.json

    print(f"config: {config}")

    if 'time_zone' in config:
        time_zone = config.get('time_zone', '')
        record_event_log(user=user, level=LogLevel.INFO.value, message=f'Change the time zone to {time_zone} by {user}', desc='Set time zone',
                            type=LogType.SYSTEM.value, action=LogAction.EDIT.value)

        ret = setup_zone(config.get('time_zone', '').strip())
        if ret.get('ErrorCode', 0) != 0:
            return ret

    if config.get('type') == 'manual':
        if 'time' not in config:
            return {'ErrorCode': -1,
                    'ErrorMessage': 'Invalid time'}

        time = config.get('time')
        ret = dlp_cmd.set_time(config.get('time'))
        if ret.get('ErrorCode', 0) != 0:
            return ret
        record_event_log(user=user, level=LogLevel.INFO.value, message=f'Change the time to {time} by {user}', desc='Set time',
                         type=LogType.SYSTEM.value, action=LogAction.EDIT.value)

    elif config.get('type') == 'ntp':
        ntp_interval = config.get('interval', 0)
        ntp_server = config.get('ntp_server', '')

        if is_ip_or_domain(ntp_server) == False:

            return {'ErrorCode': -1, 'ErrorMessage': 'Invalid NTP server value. Please provide a valid domain name or IP address for the NTP server.'}

        # if not (1 <= ntp_interval <= 1440):
        #     return {'ErrorCode': -1, 'ErrorMessage': 'Interval must be 1-1440.'}

        record_event_log(user=user, level=LogLevel.INFO.value, message=f'Set the ntp server to {ntp_server} with a sync interval time of {ntp_interval} minutes by {user}', desc='Set NTP server',
                         type=LogType.SYSTEM.value, action=LogAction.EDIT.value)
        ntp.ntp_set_service_config(ntp_interval, ntp_server, True)

    else:
        return {'ErrorCode': -1,
                'ErrorMessage': 'Get type parameter error!'}


    return {'ErrorCode': 0}


def setup_zone(zone):
    # validate time zone format
    reg = re.compile('^[a-zA-Z0-9_/]+$')
    if reg.match(zone) is None:
        return {'ErrorCode': -1, 'ErrorMessage': 'Invalid time zone'}

    if dlp_cmd.set_zone(zone) < 0:
        return {'ErrorCode': -1, 'ErrorMessage': 'Set zone error'}

    return {'ErrorCode': 0}


def get_upload_sys_image_status():
    """
    To display the status of the image upgrade on the UI.
    Returns:

    """
    from system import system_upgrade
    from system import dlp_file
    status = system_upgrade.get_upgrade_status()
    if status.get('ErrorCode') != 0:
        dlp_file.clear_path('/var/log/upload/images/')
    return status


def is_first_chunk(request):
    """
    Determines whether the request is the first chunk of a file upload.

    - If the `Content-Range` header is missing, it is ignored in this function.
    - If `Content-Range` exists and starts with "bytes 0-", it indicates the first chunk.

    Returns:
        bool: True if it is the first chunk, False otherwise.
    """
    return 'Content-Range' in request.headers and request.headers['Content-Range'].startswith("bytes 0-")



def export_cert(request, config_id, cert_type):
    import os
    from system import export

    user = session.get_user(request)
    ret, dl_pathfile = export.export_cert(config_id, user, cert_type)
    if ret != 0:
        ret = {
            'ErrorCode': -2,
            'ErrorMessage': dl_pathfile
        }
    else:
        dl_file = os.path.basename(dl_pathfile)
        dl_path = os.path.dirname(dl_pathfile)
        ret = {
            'ErrorCode': 0,
            'dl_file': dl_file,
            'dl_path': dl_path
        }
    return ret


def cache_result(ttl_seconds=120):
    def decorator(func):
        cache = {}

        @wraps(func)
        def wrapper(*args, **kwargs):
            # Check if force_check is in kwargs
            force_check = kwargs.pop('force_check', False)

            # If force_check is True, bypass cache and execute function directly
            if force_check:
                return func(*args, **kwargs)

            # Create a cache key from the function arguments
            key = str(args) + str(sorted(kwargs.items()))
            current_time = time.time()

            # Check if we have a cached result that's still valid
            if key in cache:
                result, timestamp = cache[key]
                if current_time - timestamp < ttl_seconds:
                    return result
                else:
                    # Remove expired entry
                    del cache[key]

            # If no valid cache, call the function and cache the result
            result = func(*args, **kwargs)
            cache[key] = (result, current_time)
            return result
        return wrapper
    return decorator

def check_disk_space(path='/var/log', threshold_gb=10, force_check=False):
    try:
        stat = os.statvfs(path)
        available_gb = (stat.f_bavail * stat.f_frsize) / (1024 ** 3)  # Convert to GB

        if available_gb < threshold_gb:
            # print(f"Error: {path} has less than {threshold_gb}GB available. Current space: {available_gb:.2f}GB.")
            return False
        else:
            # print(f"{path} has sufficient space: {available_gb:.2f}GB.")
            return True
    except Exception as e:
        # print(f"An error occurred: {e}")
        return False


def upload_sys_image(request):
    """
    Upload the image file to /var/log/upload/images/ and perform the upgrade at the same time.
    Args:
        request:

    Returns:

    """
    import shlex
    import os
    from system import system_upload, system_upgrade
    from system.system_log import record_event_log, LogLevel, LogType, LogAction

    upload_path = '/var/log/upload/images/'
    upload_name = ""
    allowed_file_type = ('.deb', '.out')
    filename = ""
    file_path = ""

    try:
        # get upload filename
        upload_names = request.files
        if len(upload_names) == 0:
            return -1, "No any file upload"

        for name in upload_names:
            upload_name = name
            break
        if re.findall(r'' + INVALID_CHARS, upload_name):
            return -1, 'Invalid upload name'

        if not check_disk_space(threshold_gb=15):
            return -2, 'Upgrade failed: Disk space is less than 15GB.'

        upload = request.files.get(upload_name)
        filename = os.path.basename(shlex.quote(upload.filename))

        if not re.match(r'^[\w\.\_\-]+$', filename) or filename.count('.') > 1:
            return -2, 'Upgrade failed: File names must only contain letters, numbers, hyphen, and underscores.'

        # check is file type allowed
        fname, fext = os.path.splitext(filename)
        if allowed_file_type and fext.lower() not in allowed_file_type:
            return -2, "Invalid file type '{}'".format(filename)

        # Allow .tar.gz not .gz
        if ".gz" in allowed_file_type and \
                (fext.lower() == ".gz" and not filename.endswith(".tar.gz")):
            return -2, "Invalid file type '{}'".format(filename)

        if not os.path.exists(upload_path.strip()) or not os.path.isdir(upload_path):
            os.makedirs(upload_path, exist_ok=True)

        file_path = os.path.join(upload_path, filename)
        errno, errmsg = system_upload.save_upload_file(request, upload, file_path, upload_path)
        if errno != 0:
            return errno, errmsg

        if errmsg.startswith("Chunk saved"):
            if is_first_chunk(request):
                version = system_upgrade.extract_version(file_path)
                if version is None:
                    return -4, 'File upgrade error. Please try again.'
                else:
                    errmsg = system_upgrade.check_platform(version)
                    if errmsg != "":
                        return -4, errmsg

            # Chunk saved: continue to receive next chunk
            return errno, errmsg

    except OSError as e:
        if "filename" not in locals():
            filename = ""
        return -5, "File '{}' upload failed, OS errmsg: {}".format(filename, e.args)
    except Exception as e:
        return -178, "File '{}' upload failed, errmsg: {}".format(filename, e.args)

    record_event_log(user=session.get_user(request), level=LogLevel.INFO.value, message=f"The image file  {filename} upload successfully",
                                    desc='System upgrade', type=LogType.SYSTEM.value, action=LogAction.UPGRADE.value)
    try:
        system_upgrade.upgrade_image(file_path)
    except:
        return -4, 'File upgrade error. Please try again.'

    return 0, ""


def export_config(request, export_type):
    """
    Export system config
    Args:
        request:
        export_type: ["system","all_data_configuration"]
    Returns:

    """
    from pathlib import Path
    user = session.get_user(request)
    if not user:
        return {'ErrorCode': -1, 'ErrorMessage': 'No user found!'}

    base_dir = GLOBAL_CONFIG.get("sys_tmp_path", {}).get("download_path")
    conf_export_dir = os.path.join(base_dir, user)
    if not os.path.exists(conf_export_dir):
        Path(str(conf_export_dir)).mkdir(exist_ok=True, parents=True)

    filename = export.system_all_export(conf_export_dir, export_type)
    return conf_export_dir, filename


def restore_sys_config(request):
    """
    Upload the image file to /var/log/dlp_ui/upload and perform the upgrade at the same time.
    Args:
        request:

    Returns:

    """
    import shlex
    import os
    from pathlib import Path
    from system import system_upload
    from system.dlp_cmd import delay_exec_func

    user = session.get_user(request)
    if not user:
        return {'ErrorCode': -1, 'ErrorMessage': 'No user found!'}

    base_dir = GLOBAL_CONFIG.get("sys_tmp_path", {}).get("upload_path")
    upload_path = str(os.path.join(base_dir, user))
    if not os.path.exists(upload_path):
        Path(str(upload_path)).mkdir(exist_ok=True, parents=True)

    upload_name = ""
    allowed_file_type = ('.zip')

    filename = ""
    file_path = ""
    try:
        # get upload filename
        upload_names = request.files
        if len(upload_names) == 0:
            return -1, "No any file upload"

        for name in upload_names:
            upload_name = name
            break
        if re.findall(r'' + INVALID_CHARS, upload_name):
            return -1, 'Invalid upload name'

        upload = request.files.get(upload_name)
        filename = os.path.basename(shlex.quote(upload.filename))

        if not re.match(r'^[\w\.\_\-]+$', filename):
            return -3, "File names can only include letters, numbers, dots, hyphens, and underscores"

        # check is file type allowed
        fname, fext = os.path.splitext(filename)
        if allowed_file_type and fext.lower() not in allowed_file_type:
            return -2, "Invalid file type '{}'".format(filename)

        file_path = os.path.join(upload_path, filename)
        errno, errmsg = system_upload.save_upload_file(request, upload, file_path, upload_path)
        if errno != 0 or errmsg.startswith("Chunk saved"):
            # Chunk saved: continue to receive next chunk
            return errno, errmsg

    except OSError as e:
        if "filename" not in locals():
            filename = ""
        return -5, "File '{}' upload failed, OS errmsg: {}".format(filename, e.args)
    except Exception as e:
        return -178, "File '{}' upload failed, errmsg: {}".format(filename, e.args)

    status_str = {
        'ErrorCode': 0,
        'Data': {
            'Status': 'Running',
            'Message': 'System is restoring now. Please wait a moment ...'
        }
    }

    export.set_import_all_status(status=status_str)
    try:
        delay_exec_func(export.process_import_all, 0, {'file_path': file_path, 'user': user}, using_process=False)
    except:
        return -4, 'File upgrade error. Please try again.'

    return 0, ""


def get_restore_sys_config_status():
    """
    To display the status of the system restore on the UI.
    Returns:

    """
    from system import export
    status = export.get_import_all_status()
    return status


def is_valid_gateway(dst, gateway):
    import ipaddress
    try:
        if dst == "default":
            dst = "0.0.0.0"
        dst_network = ipaddress.ip_network(dst, strict=False)

        gateway_ip = ipaddress.ip_address(gateway)

        if isinstance(dst_network, ipaddress.IPv4Network) and not isinstance(gateway_ip, ipaddress.IPv4Address):
            return -1, "Gateway must be an IPv4 address to match the target network type"
        if isinstance(dst_network, ipaddress.IPv6Network) and not isinstance(gateway_ip, ipaddress.IPv6Address):
            return -2, "Gateway must be an IPv6 address to match the target network type"

        # # Check if gateway belongs to the dst network
        # if gateway_ip not in dst_network:
        #     return -3, f"Gateway {gateway} is not within the target network {dst}"

        return 0, "Gateway configuration is valid"

    except ValueError as e:
        return -4, f"Invalid IP address or network: {e}"


def get_static_route():
    from system.system_config import route_config

    config = route_config.get_route_config()
    static_routes = config.get("static", [])

    return static_routes


def get_system_mode_config(tag_name=''):
    from system.system_config import mode_setting_config as mode_setting
    config = mode_setting.get_mode_setting_config()
    return config.get(tag_name, 'Disable')


def get_gui_upload_files():
    from util.config import get_global_config
    gui_upload_dir = get_global_config().get("system_gui_upload_dir")
    gui_files = {'list': []}
    idx = 0
    for root, dirs, files in os.walk(gui_upload_dir):
        for file in files:
            idx += 1
            gui_files['list'].append({'_id': idx, 'Name': file})
    gui_files['total'] = idx
    return gui_files


def upload_gui_upload_files(request):
    from system import system_upload
    from util.config import get_global_config
    gui_upload_dir = get_global_config().get("system_gui_upload_dir")
    errno, errmsg = system_upload.web_upload_files(request, gui_upload_dir, [])
    if errno != 0:
        return {"ErrorCode": errno, "ErrorMessage": errmsg}
    return {"ErrorCode": 0}
