import re
import psutil
import os
import signal

from domain_model import user_interface
from flask_module import session
from system.dlp_file import file_to_json
from system import system_deny
from system import dlp_cmd

ROLE_CONFIG = file_to_json('system/config/role.json')

from system.system_log import record_event_log, LogLevel, LogAction, LogType


def get_remote_ip(request):
    # del '::ffff:' from client_ip
    client_ip = request.environ.get('REMOTE_ADDR')
    return re.sub(r'::ffff:', '', client_ip)


def get_current_db_user_config(user_name):
    user = user_interface.find_user({'name': user_name})
    return user

def check_license_version():
    try:
        with open("/etc/vm.lic", "r") as f:
            content = f.read()
            if "FDT LICENSE" in content:
                return "New"
        return "Old"
    except FileNotFoundError:
        return "Null"
    except Exception as e:
        return f"An unexpected error occurred: {str(e)}"

def login_user(request):
    from flask import jsonify, make_response

    login_user = request.json
    user_name = login_user.get('name', '')
    user_psw = login_user.get('password', '')
    client_ip = get_remote_ip(request)

    if len(user_name) == 0:
        return -1, "User name is empty"

    ret = system_deny.check_legal_ip(client_ip)
    if ret.get('ErrorCode', 0) == -1:
        return -1, ret.get('ErrorMessage', '')

    ret = user_interface.verify_user(user_name, user_psw)
    if ret.get('ErrorCode', 0) == -1:
        err_msg = ret.get('ErrorMessage', '')
        record_event_log(user=user_name, level=LogLevel.WARNING.value, message=f"User {user_name} logged in failed from {client_ip}",
                     desc=err_msg, type=LogType.USERS.value, action=LogAction.LOGIN.value)

        system_deny.update_illegal_ip(client_ip)

        return -1, err_msg

    user = get_current_db_user_config(user_name)
    system_deny.update_legal_ip(client_ip)

    # add remote ip
    user["remote_ip"] = client_ip

    # add system usage
    from system import system_util as sys_util
    system_usage = sys_util.get_system_usage()

    user_info = {
        '_id': user['_id'],
        'name': user['name'],
        'roles': user['roles'],
        'type': user.get('type', 'local'),
        'cpu_usage': system_usage.get('cpu', {}).get('usage'),
        'memory_usage': system_usage.get('memory', {}).get('usage'),
        'disk_usage': system_usage.get('disk', {}).get('usage'),
        'disk_free': system_usage.get('disk', {}).get('free'),
        'license_version': check_license_version()
    }

    # handle default password
    if user_psw == '':
        user_interface.set_is_default_pwd({'_id': user['_id'], 'name': user['name'], 'isDefaultPwd': True})
        user_info['isDefaultPwd'] = True

    if dlp_cmd.check_log_disk() == False:
        user_info['log_disk_in_memory'] = True
        record_event_log(user=user_name, level=LogLevel.WARNING.value, message=f"The data disk is invalid. Please check it and attach a valid data disk.",
                     desc='User login', type=LogType.USERS.value, action=LogAction.LOGIN.value)
    
    ret = {
        'ErrorCode': 0,
        'Data': user_info
    }
    # add session
    response = make_response(jsonify(ret))
    response = session.add(request, response, user)

    #logger.debug(f"User {user_name} logged in successfully from {client_ip}")
    print(f"User {user_name} logged in successfully from {client_ip}")
    record_event_log(user=user_name, level=LogLevel.INFO.value, message=f"User {user_name} logged in successfully from {client_ip}",
                     desc='User login', type=LogType.USERS.value, action=LogAction.LOGIN.value)

    return 0, response


def logout_user(request):
    from flask import jsonify, make_response

    user_name = session.get_user(request)
    client_ip = get_remote_ip(request)

    response = make_response(jsonify({'ErrorCode': 0}))
    session.remove(request, response)

    record_event_log(user=user_name, level=LogLevel.INFO.value, message=f"User {user_name} logged out successfully from {client_ip}",
                     desc='User logout', type=LogType.USERS.value, action=LogAction.LOGOUT.value)
    return response


def check_login(request):
    # check session
    session_dict = session.check(request)
    if not session_dict:
        # raise Exception('No session data')
        return {
            'ErrorCode': -1,
            'ErrorMessage': 'No session data'
        }
    else:
        # check if user exists
        user = user_interface.find_user({'name': session_dict['username']})
        if not user:
            raise Exception("User doesn't exists")
        else:
            user_info = {
                "_id": user['_id'],
                "name": user['name'],
                "roles": user['roles'],
                'type': user.get('type', 'local'),
                "isDefaultPwd": user.get('isDefaultPwd', False),
            }

            ret = {
                'ErrorCode': 0,
                'Data': user_info
            }
    return ret


def get_user_by_id(user_id):
    user = user_interface.find_user({'_id': user_id})
    if not user:
        return {'ErrorCode': -1,
                'ErrorMessage': 'No user found'}
    del user['password']
    return {'ErrorCode': 0,
            'Data': user}


def get_user_list(filter_param):
    find_pagr_param = {"skip": (filter_param.page - 1) * filter_param.per_page if filter_param.page else 0,
                       "sort_field": filter_param.sort_field,
                       "sort_method": filter_param.sort_method,
                       "name": filter_param.name}
    if filter_param.per_page:
        find_pagr_param["limit"] = filter_param.per_page
    users = user_interface.find_users(find_pagr_param)
    total = len(user_interface.find_users({"name": filter_param.name}))
    ret = {
        "list": users,
        "page": filter_param.page,
        "per_page": filter_param.per_page,
        "total": total
    }
    return ret


def save_pre_check_config(user_config):
    errmsg = ''
    if user_config.get('type', 'local') == 'local' and not user_config.get('password', None):
        errmsg = 'Empty password is not allowed'
    elif user_config.get('type', None) not in ['local', 'remote', 'remote-all']:
        errmsg = 'Type name is not allowed'
    elif not user_config.get('name', None):
        errmsg = 'Empty name is not allowed'
    elif re.compile('^[A-Za-z0-9.~:_-]*$').match(user_config['name']) is None:
        errmsg = 'Invalid name'
    elif user_config['name'] == 'maintainer':
        errmsg = 'The name maintainer is not allowed to be used'

    return {'ErrorCode': -1 if errmsg else 0,
            'ErrorMessage': errmsg}


def add_user(user_config, request):

    if not check_role_exists(user_config):
        return {'ErrorCode': -1, 'ErrorMessage': "Role is not recognized."}

    user_ret = user_interface.insert_user(user_config)
    user_name = session.get_user(request)

    if user_ret.get('success'):
        record_event_log(user=user_name, level=LogLevel.INFO.value, message=f"The user {user_config['name']} is successfully created by {user_name}",
                         desc='User added', type=LogType.USERS.value, action=LogAction.CREATE.value)

        ret = {
            'ErrorCode': 0,
            'Data': user_ret.get('_id')
        }
    else:
        err_msg = user_ret.get('msg')
        record_event_log(user=user_name, level=LogLevel.WARNING.value, message=f"The user {user_config['name']} is failed created by {user_name}",
                         desc=err_msg, type=LogType.USERS.value, action=LogAction.CREATE.value)
        ret = {
            'ErrorCode': -1,
            'ErrorMessage': err_msg
        }
    return ret


def terminate_ssh_session(user_name: str):
    """
    Terminates SSH sessions for the specified user.

    Parameters:
        user_name (str): The username whose SSH sessions are to be terminated.
    """
    target_command_prefix = f'python3 /dlpcode/system/cmd/login_eth.py -U {user_name}'
    terminated_pids = []

    try:
        # Iterate over all running processes
        for proc in psutil.process_iter(['pid', 'cmdline']):
            try:
                if proc.info['cmdline']:
                    # Convert the command line argument list to a string
                    cmdline = ' '.join(proc.info['cmdline'])
                    # Check if the command line starts with the target command
                    if cmdline.startswith(target_command_prefix):
                        pid = proc.info['pid']
                        os.kill(pid, signal.SIGTERM)  # Send termination signal
                        terminated_pids.append(pid)
                        print(f"Terminated process PID: {pid}")
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue

        if not terminated_pids:
            print(f"No active SSH sessions found for user '{user_name}'.")

    except Exception as e:
        print(f"An error occurred while terminating SSH sessions: {str(e)}")


def delete_user(ids, request):
    error_list = []
    running_list = []
    sess_username = session.get_user(request)
    for user_id in ids:
        user_config = user_interface.find_user({'_id': user_id})
        if not user_config:
            error_list.append(user_id)
            continue

        user_name = user_config['name']
        if user_name == 'admin':
            record_event_log(user=sess_username, level=LogLevel.WARNING.value,
                             message=f"The user {user_config['name']} deleted failed by {sess_username}",
                             desc="Cannot delete admin user.", type=LogType.USERS.value, action=LogAction.DELETE.value)
            return {'ErrorCode': -1, 'ErrorMessage': "Cannot delete admin user."}

        # delete user and session user
        user_interface.delete_user(user_id)
        session.remove_by_user(user_name)
        terminate_ssh_session(user_name)

        if user_config['type'] == 'remote-all':
            continue

    if error_list or running_list:
        errmsg = runmsg = ""
        if error_list:
            errmsg = 'User delete error.'
        if running_list:
            runmsg = ','.join(running_list) + ' is running a case, can not be deleted!'

        record_event_log(user=sess_username, level=LogLevel.WARNING.value,
                         message=f"The user {user_config['name']} deleted failed by {sess_username}",
                         desc="{} {}".format(errmsg, runmsg), type=LogType.USERS.value, action=LogAction.DELETE.value)
        ret = {'ErrorCode': -1, 'ErrorMessage': "{} {}".format(errmsg, runmsg)}
    else:
        record_event_log(user=sess_username, level=LogLevel.INFO.value,
                         message=f"The user {user_config['name']} is deleted by {sess_username}",
                         desc='User deleted', type=LogType.USERS.value, action=LogAction.DELETE.value)
        ret = {'ErrorCode': 0, 'Data': ''}
    return ret


def check_admin_permission(request):
    from flask_module import session
    user = session.get_user(request)
    if user_interface.ROLE_ADMIN in user_interface.get_user_role(user):
        return True
    return False


def update_user_password(user_config, request):
    current_user = session.get_user(request)
    old_psw = user_config.get('oldPsw', None)
    new_psw = user_config.get('newPsw', None)
    cfm_new_psw = user_config.get('cfmNewPsw', None)

    if not new_psw:
        return {'ErrorCode': -1, 'ErrorMessage': 'New password can not be empty'}
    if new_psw != cfm_new_psw:
        return {'ErrorCode': -1, 'ErrorMessage': 'New password and confirm new password does not match'}
    if old_psw == new_psw:
        return {'ErrorCode': -1, 'ErrorMessage': 'New and old password cannot be the same'}

    ret = user_interface.modify_user_password(user_config, current_user)
    user_id = '_id' in user_config and user_config['_id']
    modified_user = user_interface.find_user({'_id': user_id})
    if modified_user is None:
        return ret

    if ret.get('ErrorCode', 0) == 0:
        terminate_ssh_session(modified_user['name'])
        record_event_log(user=current_user, level=LogLevel.INFO.value, message=f"The password of user {modified_user['name']} is successfully modified by {current_user}",
                         desc='User password modified', type=LogType.USERS.value, action=LogAction.EDIT.value)
    else:
        record_event_log(user=current_user, level=LogLevel.WARNING.value, message=f"The password of user {modified_user['name']} is failed modified by {current_user}",
                         desc=ret.get('ErrorMessage', ''), type=LogType.USERS.value, action=LogAction.EDIT.value)
    return ret


def update_user_role(update_config, request):
    if '_id' not in update_config:
        return {'ErrorCode': -1, 'ErrorMessage': 'Invalid user _id'}

    current_user = session.get_user(request)
    user_config = user_interface.find_user({'_id': update_config['_id']})
    if not user_config:
        return {'ErrorCode': -1, 'ErrorMessage': 'Invalid user id.'}
    if user_config.get('name', '') == 'admin':
        return {'ErrorCode': -1, 'ErrorMessage': 'Super user profile is not editable.'}

    # TODO: check permission
    """
    if not user_interface.check_greater_permission(user_config, current_user):
        return {
            'ErrorCode': -1,
            'ErrorMessage': 'Not permission'
        }
    """
    if not check_role_exists(update_config):
        record_event_log(user=current_user, level=LogLevel.INFO.value,
                         message=f"The user {user_config.get('name', '')} is failed updated by {current_user}",
                         desc='User updated', type=LogType.USERS.value, action=LogAction.EDIT.value)
        return {'ErrorCode': -1, 'ErrorMessage': "Role is not recognized."}

    _id = user_interface.update_user_role(update_config)
    record_event_log(user=current_user, level=LogLevel.INFO.value,
                     message=f"The user {user_config.get('name', '')} is successfully updated by {current_user}",
                     desc='User updated', type=LogType.USERS.value, action=LogAction.EDIT.value)
    return {'ErrorCode': 0, 'Data': _id}


def check_role_exists(user_config):
    if not user_config.get('roles', []):
        return False

    allow_roles = set(role['name'] for role in ROLE_CONFIG)
    roles = user_config.get('roles', [])
    for role in roles:
        if role not in allow_roles:
            return False
    return True


def get_role_list(req_param) -> dict:
    """
    This function retrieves a list of roles from the database.
    """
    role_list = user_interface.get_role_list()
    page = int(req_param.get("page", 1))
    per_page = int(req_param.get("per_page", 10))
    show_role_list = role_list[page - 1: page - 1 + per_page]
    return {
        "list": show_role_list,
        "page": page,
        "per_page": per_page,
        "total": len(role_list)
    }
