from domain_model.certificate import get_cert_file_content_by_id
from exts import Session
from sqlalchemy import text
from system import dlp_license

def get_mgmt_ip():
    from system import interface
    ports = interface.get_all_interfaces()
    for port in ports:
        if port.get("name", "") == "mgmt":
            return port.get("address")
    return ""


def get_device_status():
    from system.dlp_cmd import get_sys_prod_name, get_sys_sn
    from system.hardware import get_platform
    version = get_sys_prod_name()
    # FortiTester-2500E 4.0.0 build5001 20200814
    ver_list = version.split(' ')
    ver_num = ver_list[1].split('.')
    ver_build = ver_list[2].replace('build', '')

    ret = {
        "serial": get_sys_sn(),
        "device_type": "fortidata",
        "model": get_platform(),
        "version": {"major": int(ver_num[0]), "minor": int(ver_num[1]), "patch": int(ver_num[2])},
        "build": {"number": int(ver_build), "release_life_cycle": "Beta"},
        "hostname": "FortiData",
        "supported_api_versions": ["1", "2"],
        "role": "lan"
    }
    return ret


def get_widget():
    # from system.dlp_cmd import get_sys_prod_name, get_sys_sn
    # version = get_sys_prod_name()
    ret = {
        "data": [{"id": "sysinfo", "lang_key": "sysinfo", "supported_visualization_types": ["key-value-pair"]}],
        "meta": {"language": {"en": {"sysinfo": "System Information"}}}}
    return ret


def get_widget_sys_info():
    from system.dlp_cmd import get_sys_hostname, get_sys_sn, get_sys_prod_name
    from system.hardware import get_platform

    mgmt_ip = get_mgmt_ip()
    ret = {"data": [
        {"lang_key": "Platform", "value": get_platform()},
        {"lang_key": "Mgmt IP", "value": mgmt_ip},
        {"lang_key": "Host Name", "value": get_sys_hostname()},
        {"lang_key": "Serial Number", "value": get_sys_sn()},
        {"lang_key": "Firmware version", "value": get_sys_prod_name()}],
        "meta": {"visualization_type": "key-value-pair", "language": {
            "en": {}}}}
    return ret


def get_widget_case_info():
    ret = {"data": [
        {"lang_key": "No IPS case result", "value": ''}],
        "meta": {"visualization_type": "key-value-pair", "language": {
            "en": {}}}}
    return ret


def save_local_message(request):
    from system import fabric
    connect_result = request.environ.get('HTTP_X_CSF_MSG')
    fabric.save_connection_status(connect_result)
    return ""


def get_csf_setting():
    from system import fabric
    fabric.update_connection_status()
    csf_config = fabric.get_csfd_config()
    mgmt_ip = get_mgmt_ip()
    csf_result = {
        "status": "enable" if csf_config.get("enable", False) else "disable",
        "upstreamIp": csf_config.get("upstream_ip", ""),
        "mgmtIp": mgmt_ip,
        "mgmtPort": csf_config.get("admin_port", 443),
        "authStatus": fabric.get_connection_status() or ''
    }
    return csf_result


def setup_csf_config(csf_config):
    from system import fabric
    return fabric.set_csf_config_process(csf_config)


def get_custom_tags():
    from exts import Session
    from sqlalchemy import text

    custom_tags = {}
    with Session() as session:
        query = text("SELECT name, description, category FROM tags_info_custom")
        result = session.execute(query)
        for row in result:
            if row.category not in custom_tags.keys():
                custom_tags[row.category] = [{
                    "name": row.name,
                    "description": row.description
                }]
            else:
                custom_tags[row.category].append({
                    "name": row.name,
                    "description": row.description
                })
    return custom_tags


def get_dlp_labels():
    from system.dlp_file import file_to_json
    from domain_model.data_label import get_all_predefined_data_label

    predefined_tags = get_all_predefined_data_label()

    combined_tags = {}
    for tag in predefined_tags:
        category = tag.category
        if category not in combined_tags:
            combined_tags[category] = []
        combined_tags[category].append({
            "name": tag.name,
            "description": tag.description
        })
    combined_tags.update(get_custom_tags())

    # Remove duplicates while preserving order
    for category in combined_tags:
        combined_tags[category] = list({tag['name']: tag for tag in combined_tags[category]}.values())
    return combined_tags


def get_ip_by_interface_name(interface_name):
    from system import interface
    ports = interface.get_all_interfaces()
    for port in ports:
        if port.get("name", "") == interface_name:
            return port.get("address")
    return ""

def get_h2_info():
    try:
        with Session() as session:
            # Query http2_config table
            h2_query = text(
                "SELECT serviceenabled, interface, port, certid FROM http2_config"
            )
            h2_result = session.execute(h2_query).fetchone()

            # Query ftnt_devices table for FOS type device
            device_query = text(
                "SELECT info FROM ftnt_devices WHERE type = 'FOS'"
            )
            device_result = session.execute(device_query).fetchone()

            # If either table has no records, return disabled service
            if not h2_result or not device_result:
                return {
                    "enable": False,
                }

            # Get device info
            device_info = device_result[0]
            client_validation = device_info.get("enable_client_validation", False)
            client_ranges = device_info.get("iplist", [])

            # Get host IP from interface
            host = get_ip_by_interface_name(h2_result[1])

            # Get certificate content if certid exists
            cert_file_content = ""
            if h2_result[3]:  # certid
                cert_file_content = get_cert_file_content_by_id(h2_result[3], session)

            h2_info = {
                "enable": h2_result[0],  # serviceenabled
                "host": host,
                "port": h2_result[2],  # port
                "client_ranges": client_ranges,
                "client_certificate_validation": client_validation,
                "cert": cert_file_content,
                "api_version": ["v1"],
                "license_status": dlp_license.get_license_status(),
            }

            return h2_info
    except Exception as e:
        print(f"Error fetching http2_config and device info: {e}")
        return None

