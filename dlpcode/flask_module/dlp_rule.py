
import traceback
from flask import Blueprint, request, jsonify
from pydantic import ValidationError
from util.common_log import get_logger
from util.config import configs
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from flask_module import session
from system.system_log import record_event_log, LogLevel, LogAction, LogType

logger = get_logger("api")
dlp_rule = Blueprint("dlp_rule", __name__)


@dlp_rule.route('/', methods=['POST'])
def handle_dlp_rule_create():
    from flask_module.dlp_rule_reqmodel import DLPRuleCreateReqModel
    from huey_worker.dlp_policy_engine import notify_update_policy
    from service import dlp_policy_service
    from service import dlp_rule_service
    from psycopg2.errors import UniqueViolation

    try:
        # create a new dlp_rule
        data = request.get_json()
        try:
            reqmodel = DLPRuleCreateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"reqmodel: {reqmodel}")
        
        # check if the specified dlp policy exists.
        policy = dlp_policy_service.read_policy(reqmodel.policy_id)
        if not policy:
            return error_response(Ecodes.NOT_FOUND, 404, f"DLP policy {reqmodel.policy_id} not found")
            
        
        max_dlp_rule_per_policy = configs.get("max_dlp_rule_per_policy", 256)
        if dlp_rule_service.check_max_dlp_rule(reqmodel.policy_id, max_dlp_rule_per_policy-1):
            return error_response(Ecodes.LIMIT_REACHED, 400, f'You can only create a maximum of {max_dlp_rule_per_policy} dlp rules')
        
        # create a new dlp rule
        new_rule = dlp_rule_service.create_dlp_rule(reqmodel.dict(exclude_none=True), logger)
        notify_update_policy(new_rule.get('policy_id'))
        logger.info(f"DLP rule created, {new_rule}")

        user = session.get_user(request)    
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Add a rule {new_rule.get('name', '')} for DLP policy {policy.name}", 
                     desc='Add rule for DLP policy', type=LogType.DLP_POLICIES.value, action=LogAction.CREATE.value)
        return success_response(new_rule, 201)
    except UniqueViolation:
        return error_response(Ecodes.DUPLICATE_RESOURCE, 409, "A duplicate Rule Name already exists.")
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@dlp_rule.route('/', methods=['GET'])
def handle_dlp_rule_get():
    from flask_module.dlp_rule_reqmodel import DLPRuleQueryReqModel
    from service import dlp_rule_service

    try:
        # get all conditions
        conditions = request.args.to_dict()
        logger.info(f"conditions: {conditions}")
        try:
            reqmodel = DLPRuleQueryReqModel(**conditions)
        except ValidationError as e:
            # Check if the validation error is due to an invalid UUID
            for error in e.errors():
                if error['loc'] == ('id',) and 'Invalid uuid' in error['msg']:
                    return success_response({
                        "list": [],
                        "total": 0,
                        "page": 0,
                        "per_page": 0,
                    }, 200)
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        logger.info(f"reqmodel: {reqmodel}")
        results, total = dlp_rule_service.get_rules_by_conditions(reqmodel.dict(exclude_none=True), logger)
        if results is None:
            return error_response(Ecodes.INTERNAL_ERROR, 500)
        return success_response({
            "list": results,
            "total": total,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
        }, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@dlp_rule.route('/', methods=['PUT'])
def handle_dlp_rule_update():
    from flask_module.dlp_rule_reqmodel import DLPRuleUpdateReqModel
    from huey_worker.dlp_policy_engine import notify_update_policy
    from service import dlp_policy_service
    from service import dlp_rule_service

    # update an existing dlp_rule
    try:
        data = request.get_json()
        logger.info(f"Update dlp_rule: {data}")
        try:
            reqmodel = DLPRuleUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        logger.info(f"reqmodel: {reqmodel}")
        
        # check if the dlp rule exists
        rule = dlp_rule_service.read_dlp_rule(reqmodel.id)
        if not rule:
            return error_response(Ecodes.NOT_FOUND, 404)

        new_rule = dlp_rule_service.update_dlp_rule(reqmodel.id, reqmodel.dict(exclude_none=True), logger)
        
        notify_update_policy(new_rule.get('policy_id'))
        logger.info(f"Updated dlp rule: {new_rule}")

        policy = dlp_policy_service.read_policy(new_rule.get('policy_id', ''))
        if not policy:
            return error_response(Ecodes.NOT_FOUND, 404, f"DLP policy {new_rule.get('id', '')} not found")
        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Edit the rule {new_rule.get('name', '')} of DLP policy {policy.name}", 
                     desc='Edit rule of DLP policy', type=LogType.DLP_POLICIES.value, action=LogAction.EDIT.value)
        return success_response(new_rule, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@dlp_rule.route('/', methods=['DELETE'])
def handle_dlp_rule_delete():
    from flask_module.dlp_rule_reqmodel import DLPRuleDeleteReqModel
    from huey_worker.dlp_policy_engine import notify_update_policy
    from service import dlp_policy_service
    from service import dlp_rule_service

    try:
        # delete an existing dlp_rule
        args = request.args.to_dict(flat=False)
        logger.info(f"Deleting dlp rule by args {args}")
        try:
            reqmodel = DLPRuleDeleteReqModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        logger.info(f"reqmodel: {reqmodel}")
        # Create a dictionary to store the mapping from rule_id to policy_id
        rule_policy_mapping = {}
        rules = []

        for rule_id in reqmodel.id:
            # check if the dlp rule exists
            rule = dlp_rule_service.read_dlp_rule(rule_id)            
            if not rule:
                continue
            policy = dlp_policy_service.read_policy(str(rule.policy_id))
            if not policy:
                continue
            # Store the mapping from rule_id to policy_id
            rule_policy_mapping[rule_id] = rule.policy_id
            rules.append(f"rule {rule.name} of DLP policy {policy.name}")
        
        rules_str = ",".join(rules)
                
        deleted_ids, failed_ids = dlp_rule_service.delete_dlp_rules(reqmodel.id, logger)
        rule_ids = request.args.getlist('id')
        logger.info(f"Deleting dlp rule by policy_ids {rule_ids}")

        for rule_id in deleted_ids:
            # Use the mapping to get the policy_id for the deleted rule
            if rule_id not in rule_policy_mapping:
                continue
            policy_id = rule_policy_mapping[rule_id]
            notify_update_policy(policy_id)

        logger.info(f"Deleted dlp rule {deleted_ids}, failed_ids {failed_ids}")

        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Delete the {rules_str}", 
                     desc='Delete rule of DLP policy', type=LogType.DLP_POLICIES.value, action=LogAction.DELETE.value)
        return success_response({'message': 'DLP rule deleted', 'deleted_ids': deleted_ids, 'failed_ids': failed_ids}, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)