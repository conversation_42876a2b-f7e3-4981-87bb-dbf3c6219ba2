import uuid
import re
from util.common_log import get_logger
from pydantic import BaseModel, validator
from pydantic import Field, constr, conint, confloat
from typing import List, Optional, Literal, Union

logger = get_logger("api")

class GetUserModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(None, example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"],
                                                     description="valid UUID format string")
    sort_field: Literal["name", "roles", "create_on"] = Field('name', description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: conint(ge=0) = Field(1, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")
    name: Optional[str] = Field(None, description="user name")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

def validate_password(value: str) -> str:
    if len(value) < 8:
        raise ValueError("Password must be at least 8 characters long")
    if not any(char.islower() for char in value):
        raise ValueError("Password must contain at least one lowercase letter")
    if not any(char.isupper() for char in value):
        raise ValueError("Password must contain at least one uppercase letter")
    if not any(char.isdigit() for char in value):
        raise ValueError("Password must contain at least one number")
    if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", value):
        raise ValueError("Password must contain at least one special character (!@#$%^&* etc.)")
    return value

class PostUserModel(BaseModel):
    name: str = Field(..., description="User name")
    password: str = Field(None, description="Password (must be at least 8 characters long, contain at least one lowercase letter, one uppercase letter, one number, and one special character)")
    roles: list = Field(..., description="Support multiple roles for authentication.")
    type: str = Field(..., description="Support local and RADIUS authentication. (local/remote/remote-all)")

    @validator("password")
    def validate_password(cls, value):
        return validate_password(value)

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"


class DeleteUserModel(BaseModel):
    # required fields
    id: List[constr(min_length=36, max_length=36)] = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"],
                                                           description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class EditUserModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(..., alias="_id",
                                                     example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"],
                                                     description="valid UUID format string")
    name: str = Field(..., description="User name")
    roles: list = Field(..., description="Support multiple roles for authentication.")
    type: Optional[str] = Field(None, description="Support local and RADIUS authentication. (local/remote/remote-all)")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class EditUserPasswordModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(..., alias="_id",
                                                     example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"],
                                                     description="valid UUID format string")
    old_psw: str = Field(..., alias="oldPsw",  description="Old password")
    new_psw: str = Field(..., alias="newPsw",  description="New password")
    cfm_new_psw: str = Field(..., alias="cfmNewPsw",  description="Confirm new password")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

    @validator("new_psw")
    def validate_password(cls, value):
        return validate_password(value)

