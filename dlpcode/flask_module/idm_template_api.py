from flask import Blueprint, request
from util.common_log import get_logger
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from pydantic import ValidationError
from service.predefined_datatype_service import GL<PERSON><PERSON>L_CONFIG
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from service.idm_template_service import get_reference_cnt, get_references
from domain_model.idm_template import (
    get_idm_templates,
    create_idm_template,
    delete_idm_templates,
    update_idm_template,
    get_idm_template
)
from flask_module.source_file_info import SOURCE_FILE_SERVICE
from flask_module import session
from psycopg2.errors import UniqueViolation
from util.enum_ import EDMTemplateStatus

logger = get_logger("api")
idm = Blueprint("idm", __name__)

@idm.route('/', methods=['POST'])
def handle_idm_template_create():
    try:
        from flask_module.idm_template_reqmodel import CreateIDMemplateReqModel
        data = request.get_json()
        req_model = CreateIDMemplateReqModel(**data)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        idm_template = {
            "name": req_model.name,
            "description": req_model.description,
            "source_file_id": req_model.source_file_id,
            "attributes_ext": {
                "status": EDMTemplateStatus.IN_PROGRESS.value,
                "err_msg": None
            }
        }
        template = create_idm_template(payload=idm_template)
        if template:
            SOURCE_FILE_SERVICE.generate_hash_source_file(new_source_file_id=str(template.source_file_id), template_id=str(template.id))
            record_event_log(
                user=session.get_user(request),
                level=LogLevel.INFO.value,
                message=f"Create IDM index {template.name}",
                desc="Create new IDM index",
                action=LogAction.CREATE.value,
                type=LogType.IDM.value
            )
            return success_response({"ErrorCode": 0, "Data": template.to_dict()})
        else:
            raise ValueError("create IDM template failed")
    except UniqueViolation:
        return error_response(Ecodes.DUPLICATE_RESOURCE, 409, "A duplicate IDM Index Name already exists.")
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)

@idm.route('/', methods=['PUT'])
def handle_idm_template_update():
    try:
        from flask_module.idm_template_reqmodel import UpdateIDMTemplateReqModel
        data = request.get_json()
        req_model = UpdateIDMTemplateReqModel(**data)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        old_template = get_idm_template(id = req_model.id)
        if not old_template:
            return error_response(Ecodes.NOT_FOUND, 404, f"IDM template {req_model.id} not exist")
        if old_template.attributes_ext["status"] == EDMTemplateStatus.IN_PROGRESS.value:
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, f"The IDM hash task is still in progress. The current IDM index is unavailable.")
        
        _, _, scan_cnt = get_reference_cnt(idm_id=req_model.id)
        if scan_cnt > 0:
            return error_response(Ecodes.FORBIDDEN_ERROR, 403, f"Editing is not allowed when the IDM index is being referenced by scan policy.")
        
        if str(old_template.source_file_id) != req_model.source_file_id:
            status = EDMTemplateStatus.IN_PROGRESS.value
        else:
            status = old_template.attributes_ext["status"]
        
        idm_template = {
            "name": req_model.name,
            "description": req_model.description,
            "source_file_id": req_model.source_file_id,
            "attributes_ext": {
                "status": status,
                "err_msg": None
            }
        }
        template = update_idm_template(template_id=req_model.id, updates=idm_template)
        if template:
            if str(old_template.source_file_id) != req_model.source_file_id:
                SOURCE_FILE_SERVICE.generate_hash_source_file(
                    new_source_file_id=str(req_model.source_file_id), 
                    old_source_file_id=str(old_template.source_file_id),
                    template_id=str(old_template.id)
                )
            record_event_log(
                user=session.get_user(request),
                level=LogLevel.INFO.value,
                message=f"Update IDM index {template.name}",
                desc="Update IDM index",
                action=LogAction.UPGRADE.value,
                type=LogType.IDM.value
            )
            return success_response({"ErrorCode": 0, "Data": "IDM template updated"})
        else:
            raise ValueError("update IDM template failed")

    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)

@idm.route("/", methods=['DELETE'])
def handle_idm_template_delete():
    try:
        from flask_module.idm_template_reqmodel import DeleteIDMTemplateReqModel
        args = request.args.to_dict(flat=False)
        req_model = DeleteIDMTemplateReqModel(**args)
    except ValidationError as e:
        logger.error(e)
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    
    try:
        delete_ids = []
        delete_names = []
        templates, _ = get_idm_templates(conditions={"id": req_model.id})
        for template in templates:
            discover_policy_cnt, dlp_policy_cnt, scan_cnt = get_reference_cnt(idm_id=str(template[0].id))
            if discover_policy_cnt > 0 or dlp_policy_cnt > 0 or scan_cnt > 0:
                continue
            if template[0].attributes_ext["status"] == EDMTemplateStatus.IN_PROGRESS.value:
                continue
            delete_ids.append(str(template[0].id))
            delete_names.append(template[0].name)
        
        if delete_ids:
            res = delete_idm_templates(id=delete_ids)
            if not res:
                raise Exception("Delete IDM template failed")
            record_event_log(
                user=session.get_user(request),
                level=LogLevel.INFO.value,
                message=f"Delete IDM index: {delete_names}",
                desc="Delete IDM index",
                action=LogAction.DELETE.value,
                type=LogType.IDM.value
            )
        return success_response({"ErrorCode": 0, "Data": "IDM templates deleted"})
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)
    
@idm.route("/", methods=['GET'])
def handle_idm_template_query():
    from flask_module.idm_template_reqmodel import QueryIDMTemplateReqModel
    try:
        args = request.args.to_dict()
        reqmodel = QueryIDMTemplateReqModel(**args)
    except ValidationError as e:
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    logger.info(f"reqmodel: {reqmodel}")
    
    try:
        templates, total = get_idm_templates(
            conditions=reqmodel.dict(exclude_none=True),
            sort_field=reqmodel.sort_field,
            sort_method=reqmodel.sort_method,
            page=reqmodel.page,
            per_page=reqmodel.per_page
        )
        ret_templates = []
        for template in templates:
            idm_id = str(template[0].Get_id())
            ref = get_references(idm_id)
            ret_template = template[0].to_dict()
            ret_template["source_file_name"] = template[1]
            ret_template["discover_policy_cnt"] = ref['discover_policy']['cnt']
            ret_template["discover_policy"] = ref['discover_policy']['name']
            ret_template["dlp_policy_cnt"] = ref['dlp_policy']['cnt']
            ret_template["scan_cnt"] = ref['scan']['cnt']
            ret_template["scans"] = ref['scan']['name']
            ret_templates.append(ret_template)
        return success_response({
            "list":ret_templates,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
            "total":total
        })
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500, e)
