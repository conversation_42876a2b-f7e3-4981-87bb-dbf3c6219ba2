from pydantic import BaseModel, Field, validator, root_validator, conint
from typing import Optional, List, Literal


class DSPMDashboardModel(BaseModel):
    widgetnames: Optional[
        List[
            Literal[
                "totalfilesbycompliancepercentage",
                "piitotalpercentage",
                "pcitotalpercentage",
                "phitotalpercentage",
                "confidentialtotalpercentage",
                "highlyconfidentialtotalpercentage",
                "totalfilesbycompliancebystorageandfilecategory",
                "piibystorageandfilecategory",
                "pcibystorageandfilecategory",
                "phibystorageandfilecategory",
                "confidentialbystorageandfilecategory",
                "highlyconfidentialbystorageandfilecategory",
                "filebyfiletype",
                "cataloguedfilecountbytime",
                "cataloguedfilecount",
                "uncataloguedfilecount",
                "cataloguedfilesbystorage",
            ]
        ]
    ] = Field(
        ..., example=[], description="Widget names requested to poplulate the dashboard"
    )
