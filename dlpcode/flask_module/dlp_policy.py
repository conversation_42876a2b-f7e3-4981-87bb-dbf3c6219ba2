import traceback
from flask import Blueprint, request, jsonify
from pydantic import ValidationError
from util.common_log import get_logger
from util.config import configs
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from flask_module import session
from system.system_log import record_event_log, LogLevel, LogAction, LogType

logger = get_logger("api")
dlp_policy = Blueprint("dlp_policy", __name__)


# curl -X POST -H "Content-Type:application/json"  http://localhost:5000/api/v1/dlp_policy/ -d '{"name": "my first dlp policy", "status": 0, "description": "My first dlp policy"}'
@dlp_policy.route('/', methods=['POST'])
def handle_dlp_policy_create():
    from flask_module.dlp_policy_reqmodel import DLPPolicyCreateReqModel
    from huey_worker.dlp_policy_engine import notify_update_policy
    from service import dlp_policy_service
    from psycopg2.errors import UniqueViolation

    try:
        # create a new dlp_policy
        data = request.get_json()
        try:
            reqmodel = DLPPolicyCreateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"reqmodel: {reqmodel}")

        max_dlp_policy = configs.get("max_dlp_policy", 128)
        if dlp_policy_service.check_max_dlp_policy(max_dlp_policy):
            return error_response(Ecodes.LIMIT_REACHED, 400, f'You can only create a maximum of {max_dlp_policy} dlp policies')

        new_policy = dlp_policy_service.create_policy(reqmodel.dict(exclude_none=True), logger)
        notify_update_policy(new_policy.get('id'))
        logger.info(f"DLP policy created, {new_policy}")

        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Add a new DLP policy {new_policy.get('name', '')}",
                     desc='Add DLP policy', type=LogType.DLP_POLICIES.value, action=LogAction.CREATE.value)
        return success_response(new_policy, 201)
    except UniqueViolation:
        return error_response(Ecodes.DUPLICATE_RESOURCE, 409, "A duplicate DLP Policy Name already exists.")
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@dlp_policy.route('/', methods=['GET'])
def handle_dlp_policy_get():
    from flask_module.dlp_policy_reqmodel import DLPPolicyQueryReqModel
    from service import dlp_policy_service
    from service import dlp_rule_service

    try:
        #conditions = request.args.to_dict(flat=False)
        conditions = request.args.to_dict()
        try:
            reqmodel = DLPPolicyQueryReqModel(**conditions)
        except ValidationError as e:
            # Check if the validation error is due to an invalid UUID
            for error in e.errors():
                if error['loc'] == ('id',) and 'Invalid uuid' in error['msg']:
                    return success_response({
                        "list": [],
                        "total": 0,
                        "page": 0,
                        "per_page": 0,
                    }, 200)
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"reqmodel: {reqmodel}")
        results, total = dlp_policy_service.get_policies_by_conditions(reqmodel.dict(exclude_none=True), logger)
        if results is None:
            return error_response(Ecodes.INTERNAL_ERROR, 500)
        items = []
        for r in results:
            item = r
            conditions = {"policy_id": r['id']}
            rules, total_rules = dlp_rule_service.get_rules_by_conditions(conditions, logger)
            if results is None:
                item['rules_count'] = 0
            else:
                item['rules_count'] = total_rules
            items.append(item)
        return success_response({
            "list": items,
            "total": total,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
        }, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@dlp_policy.route('/', methods=['PUT'])
def handle_dlp_policy_update():
    from flask_module.dlp_policy_reqmodel import DLPPolicyUpdateReqModel
    from huey_worker.dlp_policy_engine import notify_update_policy
    from service import dlp_policy_service

    # update an existing dlp_policy
    try:
        data = request.get_json()
        logger.info(f"Update dlp_policy: {data}")
        try:
            reqmodel = DLPPolicyUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"reqmodel: {reqmodel}")

        # check if the dlp policy exists
        policy = dlp_policy_service.read_policy(reqmodel.id)
        if not policy:
            return error_response(Ecodes.NOT_FOUND, 404)

        new_policy = dlp_policy_service.update_policy(reqmodel.id, reqmodel.dict(exclude_none=True), logger)

        notify_update_policy(new_policy.get('id'))
        logger.info(f"Updated dlp_policy: {new_policy}")

        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Edit the DLP policy {new_policy.get('name', '')}",
                     desc='Edit DLP policy', type=LogType.DLP_POLICIES.value, action=LogAction.EDIT.value)
        return success_response(new_policy, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@dlp_policy.route('/', methods=['DELETE'])
def handle_dlp_policy_delete():
    from flask_module.dlp_policy_reqmodel import DLPPolicyDeleteReqModel
    from huey_worker.dlp_policy_engine import notify_delete_policy
    from service import dlp_policy_service

    try:
        # delete an existing dlp_policy
        args = request.args.to_dict(flat=False)
        logger.info(f"Deleting dlp_policy by args {args}")

        try:
            reqmodel = DLPPolicyDeleteReqModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        policies = ""
        for id in reqmodel.id:
            p = dlp_policy_service.read_policy(id)
            if p:
                policies += p.name

        deleted_ids, failed_ids = dlp_policy_service.delete_policies(reqmodel.id, logger)
        for policy_id in deleted_ids:
            notify_delete_policy(policy_id)
        logger.info(f"Deleted dlp_policy {deleted_ids}, failed_ids {failed_ids}")

        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Delete the DLP policy {policies}",
                     desc='Delete DLP policy', type=LogType.DLP_POLICIES.value, action=LogAction.DELETE.value)
        return success_response({'message': 'Policy deleted', 'deleted_ids': deleted_ids, 'failed_ids': failed_ids}, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)