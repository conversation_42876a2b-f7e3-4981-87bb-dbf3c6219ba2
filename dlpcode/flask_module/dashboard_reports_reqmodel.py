from pydantic import BaseModel, Field, validator, root_validator, conint, constr
from typing import Dict, Optional, List, Literal, Union
import uuid
from util.enum_ import PeriodType, ReportType


class TimeBounds(BaseModel):
    starttime: int  # seconds since epoch
    endtime: Optional[int] = None
    maxruns: Optional[int] = None


class ScheduleInfo(BaseModel):
    period: PeriodType = Field(
        ...,
        example=1,
        description="Period of the report: 1 (once), 2 (daily), 3 (weekly), 4(monthly)",
    )
    dayoftheweek: Optional[List[conint(ge=1, le=7)]] = Field(
        None,
        example=[1, 2, 3, 4, 5, 6, 7],
        description="List of weekdays: 1 (Monday) to 7 (Sunday)",
    )
    dayofthemonth: Optional[List[conint(ge=1, le=31)]] = Field(
        None, example=12, description="Day of the month for the report: 0 to 31"
    )
    hour: Optional[int] = None
    min: Optional[int] = None
    bounds: TimeBounds
    timezone: Optional[str] = None

    @root_validator
    def check_schedule_conditions(cls, values):
        period = values.get("period")
        dayoftheweek = values.get("dayoftheweek")
        dayofthemonth = values.get("dayofthemonth")

        if period == PeriodType.WEEKLY and not dayoftheweek:
            raise ValueError("dayoftheweek must be specified for weekly period.")
        if period == PeriodType.MONTHLY and dayofthemonth is None:
            raise ValueError("dayofthemonth must be specified for monthly period.")
        if period in [PeriodType.DAILY, PeriodType.ONCE] and (
            dayoftheweek or dayofthemonth
        ):
            raise ValueError(
                "dayoftheweek or dayofthemonth should not be provided for daily or once schedules."
            )
        return values


# Dashboard components
class Dashboard(BaseModel):
    totalscannedfiles: Dict = Field(default_factory=dict)
    sensitivefileowners: Dict = Field(default_factory=dict)
    filestoscan: Dict = Field(default_factory=dict)
    dailyfilescanscannedresults: Dict = Field(default_factory=dict)
    dailyfilescansensitiveresults: Dict = Field(default_factory=dict)
    sensitivefileslabeled: Dict = Field(default_factory=dict)
    sensitivefilesdistribution: Dict = Field(default_factory=dict)
    compliancefiles: Dict = Field(default_factory=dict)
    compliancefilesshowmore: Dict = Field(default_factory=dict)
    aicategories: Dict = Field(default_factory=dict)


# Analytics Dashboard components
class AnalyticsDashboard(BaseModel):
    sensitivefileowners: Dict = Field(default_factory=dict)
    sensitivefileownersshowmore: Dict = Field(default_factory=dict)
    sharedsensitivefileowners: Dict = Field(default_factory=dict)
    sharedsensitivefileownersshowmore : Dict = Field(default_factory=dict)
    dormantsensitivefiles : Dict = Field(default_factory=dict)
    dormantsensitivefilesshowmore  : Dict = Field(default_factory=dict)
    scaninfo: Dict = Field(default_factory=dict)
    topscanpolicies: Dict = Field(default_factory=dict)
    topscanpoliciesshowmore: Dict = Field(default_factory=dict)
    sensitivefilesbyfiletypes: Dict = Field(default_factory=dict)
    sensitivefilesbyfiletypesshowmore: Dict = Field(default_factory=dict)
    scanincidentsbyseverity: Dict = Field(default_factory=dict)
    compliancefiles: Dict = Field(default_factory=dict)
    aifilecategories: Dict = Field(default_factory=dict)
    aifilecategoriesshowmore: Dict = Field(default_factory=dict)

class AnalyticsFiles(BaseModel):
    scaninfo: Dict = Field(default_factory=dict)
    tabledata: Dict = Field(default_factory=dict)

class AnalyticsScanIncidents(BaseModel):
    scaninfo: Dict = Field(default_factory=dict)
    tabledata: Dict = Field(default_factory=dict)

# Wrapper for report_details
class ReportDetails(BaseModel):
    dashboard: Optional[Dashboard]
    analytics_dashboard: Optional[AnalyticsDashboard]
    analytics_files: Optional[AnalyticsFiles]
    analytics_scan_incidents: Optional[AnalyticsScanIncidents]

# Main request model
class DashboardReportsModel(BaseModel):
    name: constr(regex=r"^[a-zA-Z0-9_-]+$")
    notification_id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    status: str
    period: Optional[str]
    format: List[Literal[0, 1, 2]]
    created_by: str = Field(..., alias="owner")  # "<login-user-id>"
    report_type: ReportType = Field(
        ...,
        example=0,
        description="Type of the report: 0 (Dashboard), 1 (Analytics dashboard) 2 (Analytics Files), 3 (Analytics Scan Incidents)",
    )
    report_details: ReportDetails
    schedule_info: ScheduleInfo
    notes: Optional[str]
    attributes: Dict = Field(default_factory=dict)

    @validator('notification_id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class ReportActionRequest(BaseModel):
    id: str
    action: Literal["suspend", "resume"]


# Model for editing
class EditDashboardReportsModel(BaseModel):
    id: str  # Add the report ID for editing
    notification_id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    name: constr(regex=r"^[a-zA-Z0-9_-]+$")
    period: Optional[str]
    format: List[Literal[0, 1, 2]]
    created_by: str = Field(..., alias="owner")  # "<login-user-id>"
    report_type: ReportType = Field(
        ...,
        example=0,
        description="Type of the report: 0 (Dashboard), 1 (Analytics dashboard), 2 (Analytics Files), 3 (Analytics Scan Incidents)",
    )
    report_details: ReportDetails
    schedule_info: ScheduleInfo
    notes: Optional[str]
    attributes: Dict = Field(default_factory=dict)

    @validator('notification_id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class DeleteRequestModel(BaseModel):
    id: str


class QueryDashboardReportsModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(
        None,
        example="916aeae8-3b10-4b44-b597-b9eca337fbaf",
        description="valid UUID format string",
    )
    name: str = Field(
        None,
        min_length=1,
        max_length=100,
        example="test_report",
        description="report task nae",
    )
    status: conint(ge=0, le=4) = Field(None, description="result")
    page: conint(ge=0) = Field(1, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")
    sort_field: str = Field("created_at", max_length=32, description="sort field name")
    sort_method: Literal["desc", "asc"] = Field("asc", description="sort method")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
