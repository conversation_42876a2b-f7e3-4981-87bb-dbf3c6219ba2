""" Flask module for Queries page """

import traceback

from flask import Blueprint, request
from pydantic import ValidationError
from sqlalchemy.exc import DBAPIError, OperationalError, SQLAlchemyError
from sqlalchemy.orm.exc import StaleDataError

from flask_module import session
from flask_module.query_reqmodel import (
    QueryListModel,
    QueryBaseModel,
    QueryEditModel,
    QueryDeleteModel,
)
from service import query_service

from system.system_log import LogAction, LogLevel, LogType, record_event_log
import util.err_codes as Ecodes
from util.common_log import get_logger
from util.err_codes import error_response, success_response


query = Blueprint("query", __name__)
logger = get_logger("dlp")


@query.route("/", methods=["GET"])
def list_queries():
    """Function to list the Queries"""

    try:
        data = request.args.to_dict()
        try:
            query_data = QueryListModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        queries, total = query_service.list_queries(
            conditions=query_data.dict(exclude_none=True)
        )
        if queries:
            logger.debug(f"Queries: {query_data} {type(query_data)}")
            return success_response(
                {
                    "list": queries,
                    "total": total,
                    "page": query_data.page,
                    "per_page": query_data.per_page,
                },
                200,
            )
        else:
            return success_response({"queries_list": []})
    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Failed to list queries: query listing API: {e}",
        )


@query.route("/", methods=["POST"])
def add_queries():
    """Function to add Queries"""

    try:
        data = request.get_json()
        validated_data = QueryBaseModel(**data)
        logger.info(f"Queries page API: Validated Data: {validated_data}")
    except ValidationError as e:
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)
    try:
        queries = {}
        sql_data = query_service.QueryData(**validated_data.dict())
        queries = query_service.add_queries(sql_data)
        if queries:
            try:
                user = session.get_user(request)
                record_event_log(
                    user=user,
                    level=LogLevel.INFO.value,
                    message=f"Added Query: {queries}",
                    desc="Add query",
                    type=LogType.QUERIES.value,
                    action=LogAction.ADD.value,
                )
            except Exception as e:
                logger.error(f"Error in event log {e}")

        return success_response(queries)
    except (DBAPIError, OperationalError) as e:
        logger.error(f"Database error while fetching query data: {str(e)}")
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Error while fetching query data: {str(e)}",
        )
    except SQLAlchemyError as e:
        logger.error(f"SQLalchemy error while fetching query data: {str(e)}")
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Error while fetching query data: {str(e)}",
        )
    except Exception as e:
        logger.error(f"Error while fetching query data: {e}")
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Error while fetching query data: {str(e)}",
        )


@query.route("/", methods=["PUT"])
def edit_queries():
    """Function to edit the Queries"""

    try:
        data = request.get_json()
        try:
            query_data = QueryEditModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        result = query_service.edit_queries(query_data)
        if result:
            try:
                user = session.get_user(request)
                record_event_log(
                    user=user,
                    level=LogLevel.INFO.value,
                    message=f"Query edited: {result.name}",
                    desc="Edit query",
                    type=LogType.QUERIES.value,
                    action=LogAction.EDIT.value,
                )
            except Exception as e:
                logger.error(f"Error in event log {e}")
            return success_response(result)
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            "Failed to update query",
        )
    except StaleDataError as e:
        return error_response(
            Ecodes.VALIDATION_ERROR,
            400,
            "Query has been modified by another user, please try again!",
        )
    except ValueError as e:
        return error_response(
            Ecodes.VALIDATION_ERROR,
            400,
            f"Failed to update query: {e}",
        )
    except ValidationError as e:
        return error_response(
            Ecodes.VALIDATION_ERROR,
            400,
            f"Validation error: update query: {e}",
        )
    except Exception as e:
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Failed to update query: {e}",
        )


@query.route("/", methods=["DELETE"])
def delete_queries():
    """Function to delete the Queries"""

    try:
        data = request.get_json()
        try:
            query_data = QueryDeleteModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        result = query_service.delete_queries(query_data.ids)
        try:
            user = session.get_user(request)
            record_event_log(
                user=user,
                level=LogLevel.INFO.value,
                message=f"Queries delete result: {result}",
                desc="Delete query",
                type=LogType.QUERIES.value,
                action=LogAction.DELETE.value,
            )
        except Exception as e:
            logger.error(f"Error in event log {e}")

        return success_response(
            {
                "message": "Delete operation completed.",
                "deleted_queries": result["deleted"],
                "not_found": result["not_found"],
            }
        )
    except ValidationError as e:
        return error_response(
            Ecodes.VALIDATION_ERROR,
            400,
            f"Validation error: delete query: {e}",
        )
    except Exception as e:
        return error_response(
            Ecodes.INTERNAL_ERROR,
            500,
            f"Failed to delete query: {e}",
        )
