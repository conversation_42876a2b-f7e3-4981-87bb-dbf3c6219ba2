import traceback
from flask import Blueprint, request
from pydantic import ValidationError
from util.common_log import get_logger
from util.err_codes import error_response, success_response
from util.err_codes import ModErrCode
from storage.service import profiles as storage_profiles_service
from util.enum_ import StorageType, SyncVersion
from flask_module import session
from system.system_log import record_event_log, LogLevel, LogAction, LogType

logger = get_logger("api")
storage_profiles = Blueprint("storage_profiles", __name__)

@storage_profiles.route('/', methods=['POST'])
def handle_storage_profiles_create():
    from flask_module.storage_profiles_reqmodel import StorageProfileCreateReqModel
    from psycopg2.errors import UniqueViolation
    from huey_worker.fetch_storage import sync_storage
    success = False

    try:
        data = request.get_json()
        reqmodel = StorageProfileCreateReqModel(**data)

        payload = reqmodel.dict(exclude_none=True)
        if payload["type"] == StorageType.SHAREPOINT_OL:
            payload["auth_info"]["allow_ntlm"] = False
        elif payload["type"] == StorageType.SHAREPOINT_OP:
            payload["auth_info"]["allow_ntlm"] = True
        pwd_encrypted = payload.pop("pwd_encrypted")
        pwd_encrypted2 = payload.pop("pwd_encrypted2")

        storage_id = storage_profiles_service.create_storage_profile(payload, pwd_encrypted, pwd_encrypted2)
        if storage_id:
            logger.info(f"Storage profile created, {reqmodel.name}")
            user = session.get_user(request)
            record_event_log(user=user, level=LogLevel.INFO.value, message=f"Add a Storage profile {reqmodel.name}",
                     desc='Add storage profile', type=LogType.STORAGE_PROFILES.value, action=LogAction.CREATE.value)
            success = True
            return success_response({'message': 'Storage profile created'}, 201)
        else:
            return error_response(ModErrCode.ErrCode02070001, 500)
    except UniqueViolation:
        return error_response(ModErrCode.ErrCode02070002, 409)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02070003, 500)
    finally:
        if success:
            task = sync_storage(storage_id)
            result = task.get()
            logger.info(f"result {result}")

@storage_profiles.route('/', methods=['GET'])
def handle_storage_profiles_get():
    from flask_module.storage_profiles_reqmodel import StorageProfileGetReqModel
    from storage.service.identity import get_storage_identity
    try:
        conditions = request.args.to_dict()
        reqmodel = StorageProfileGetReqModel(**conditions)

        results, total = storage_profiles_service.get_storage_profiles(reqmodel.dict(exclude_none=True))
        if results is None:
            return error_response(ModErrCode.ErrCode02070004, 500)

        for result in results:
            if result['type'] == 2 or result['type'] == 6:
                identities, count = get_storage_identity({'sid':result['id'], 'version':SyncVersion.INIT, 'sort_field':'updated_at','sort_method':'desc'})
                if identities:
                    result['user_count'] = count
                    result['last_sync'] = identities[0]['updated_at']

        return success_response({
            "list": results,
            "total": total,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
        }, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02070005, 500)

@storage_profiles.route('/summary', methods=['GET'])
def handle_storage_summary_get():
    try:
        summary = storage_profiles_service.get_storage_summary()
        return success_response(summary, 200)
    except Exception as e:
        logger.exception("Failed to get storage profile summaries: " + str(e))
        return error_response(ModErrCode.ErrCode02070005, 500)

@storage_profiles.route('/', methods=['DELETE'])
def handle_storage_profiles_delete():
    from flask_module.storage_profiles_reqmodel import StorageProfileDeleteReqModel

    try:
        args = request.args.to_dict(flat=False)
        try:
            reqmodel = StorageProfileDeleteReqModel(**args)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02070006, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        ref = storage_profiles_service.check_ref(reqmodel.id, logger)
        if ref == -1:
            return error_response(ModErrCode.ErrCode02070007, 400)
        elif ref == -2:
            return error_response(ModErrCode.ErrCode02070013, 400)

        deleted_ids, failed_ids = storage_profiles_service.delete_storage_profiles(reqmodel.id)
        name_list_str = ", ".join(deleted_ids)
        logger.info(f"Deleted storage_profiles {deleted_ids}, failed_ids {failed_ids}")
        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Delete storage profile {name_list_str}",
                    desc='Delete storage profile', type=LogType.STORAGE_PROFILES.value, action=LogAction.DELETE.value)
        return success_response({'message': 'Profile deleted', 'deleted_ids': deleted_ids, 'failed_ids': failed_ids}, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02070008, 500)

@storage_profiles.route('/', methods=['PUT'])
def handle_storage_profiles_update():
    from flask_module.storage_profiles_reqmodel import StorageProfileUpdateReqModel

    try:
        data = request.get_json()
        try:
            reqmodel = StorageProfileUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(ModErrCode.ErrCode02070009, 400, custom_message=e)
        logger.info(f"reqmodel: {reqmodel}")

        ref = storage_profiles_service.check_ref([reqmodel.id], logger)
        if ref == -1:
            return error_response(ModErrCode.ErrCode02070010, 400)
        elif ref == -2:
            return error_response(ModErrCode.ErrCode02070014, 400)

        payload = reqmodel.dict(exclude_none=True)
        if payload["type"] == StorageType.SHAREPOINT_OL:
            payload["auth_info"]["allow_ntlm"] = False
        elif payload["type"] == StorageType.SHAREPOINT_OP:
            payload["auth_info"]["allow_ntlm"] = True
        pwd_encrypted = payload.pop("pwd_encrypted")
        pwd_encrypted2 = payload.pop("pwd_encrypted2")

        update_success = storage_profiles_service.update_storage_profile(reqmodel.id, pwd_encrypted, pwd_encrypted2, **payload)
        if update_success:
            logger.info(f"Updated protection profile: {reqmodel.name}")
            user = session.get_user(request)
            record_event_log(user=user, level=LogLevel.INFO.value, message=f"Edit the protection profile {reqmodel.name}",
                     desc='Edit protection profile', type=LogType.PROTECTION_PROFILES.value, action=LogAction.EDIT.value)
            return success_response({'message': 'Protection profile updated'}, 200)
        else:
            return error_response(ModErrCode.ErrCode02070011, 500)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02070012, 500)


@storage_profiles.route('/test_connection', methods=['POST'])
def test_connection():
    from connector.aws_connector import AWSConnector
    from connector.sharepoint_connector import SharePointConnector
    from connector.sharepoint_token_connector import SharePointTokenConnector
    from connector.smb_connector import SMBConnector
    from connector.google_connector import GoogleConnector
    from flask_module.storage_profiles_reqmodel import TestConnectionReqModel

    try:
        conditions = request.json
        reqmodel = TestConnectionReqModel(**conditions)
    except ValidationError as e:
        return error_response(ModErrCode.ErrCode02000034, 400, custom_message=e)

    try:
        payload = reqmodel.dict(exclude_none=True)
        if payload["type"] == StorageType.SHAREPOINT_OL:
            payload["auth_info"]["allow_ntlm"] = False
        elif payload["type"] == StorageType.SHAREPOINT_OP:
            payload["auth_info"]["allow_ntlm"] = True
        pwd_encrypted = payload.pop("pwd_encrypted")
        pwd_encrypted2 = payload.pop("pwd_encrypted2")
        payload["auth_info"]["storage_id"] = ""

        connector_class_map = {
            1: AWSConnector,
            2: SharePointTokenConnector,
            3: SharePointConnector,
            4: SMBConnector,
            6: GoogleConnector
        }
        connector_class = connector_class_map.get(payload["type"])
        if payload["type"] == StorageType.SHAREPOINT_OL and conditions["auth_info"].get("usecredentials", True):
            connector_class = SharePointConnector
        connector = connector_class(payload["auth_info"], pwd_encrypted = pwd_encrypted, pwd_encrypted2 = pwd_encrypted2)
        (ret, errcode, errStr) = connector.test_connection()
        if ret:
            result = {
                "Errcode": 0,
                "Data": errStr,
            }
            return success_response(result)
        else:
            return error_response(errcode, 500, extend_message=errStr)
    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(errcode, 500, extend_message=errStr)