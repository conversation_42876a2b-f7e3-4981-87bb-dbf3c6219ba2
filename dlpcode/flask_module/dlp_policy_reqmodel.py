# import os, sys
# sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))
import uuid
from util.common_log import get_logger
from pydantic import BaseModel, validator
from pydantic import Field, constr, conint, confloat
from typing import List, Optional, Literal, Union

logger = get_logger("api")

    # Scheme:
    # {
    # 	"title": "DlpPolicyCreatReqModel",
    # 	"type": "object",
    # 	"properties": {
    # 		"name": {
    # 			"title": "Name",
    # 			"description": "dlp policy name",
    # 			"maxLength": 512,
    # 			"minLength": 1,
    # 			"example": "Policy1",
    # 			"type": "string"
    # 		},
    # 		"status": {
    # 			"title": "Status",
    # 			"description": "dlp policy status",
    # 			"default": 0,
    # 			"enum": [
    # 				0,
    # 				1
    # 			],
    # 			"type": "integer"
    # 		},
    # 		"description": {
    # 			"title": "Description",
    # 			"default": "",
    # 			"maxLength": 512,
    # 			"minLength": 0,
    # 			"example": "This is a dlp policy",
    # 			"type": "string"
    # 		}
    # 	},
    # 	"required": [
    # 		"name"
    # 	],
    # 	"additionalProperties": false
    # }
class DLPPolicyCreateReqModel(BaseModel):
    # required fields
    name: str = Field(..., min_length=1, max_length=100, example="Policy1", description="policy name")
    
    # optional fields
    description: str = Field("", min_length=0, max_length=256, example="This is a DLP policy")
    status: conint(ge=0, le=1) = Field(0, description="DLP policy status")
    priority: conint(ge=1, le=3) = Field(1, description="policy priority, 1:High 2:Medium 3:Low", example=1)

    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
class DLPPolicyQueryReqModel(BaseModel):
    # optional fields
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    name: str = Field(None, min_length=1, max_length=100, example="Policy1", description="DLP policy name")
    description: str = Field(None, min_length=0, max_length=256, example="This is a DLP policy")
    status: conint(ge=0, le=1) = Field(None, description="DLP policy status")
    priority: conint(ge=1, le=3) = Field(None, description="policy priority, 1:High 2:Medium 3:Low", example=1)
    
    sort_field: str = Field('name', max_length=32, description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('asc', description="sort method")
    page: conint(ge=0) = Field(None, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
        
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
        
class DLPPolicyUpdateReqModel(BaseModel):
    # required fields
    id: str = Field(..., min_length=36, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="valid UUID format string")
    
    # optional fields
    name: str = Field(None, min_length=1, max_length=100, example="Policy1", description="DLP policy name")
    description: str = Field(None, min_length=0, max_length=256, example="This is a DLP policy")
    status: conint(ge=0, le=1) = Field(None, description="DLP policy status")
    priority: conint(ge=1, le=3) = Field(None, description="policy priority, 1:High 2:Medium 3:Low", example=1)

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
        
class DLPPolicyDeleteReqModel(BaseModel):
    # required fields
    id: List[constr(min_length=36, max_length=36)]  = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
    

# if __name__ == "__main__":
#     print(DLPPolicyCreateReqModel.schema())