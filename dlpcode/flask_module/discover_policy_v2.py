import traceback
from flask import Blueprint, request
from pydantic import ValidationError
from util.common_log import get_logger
from util.config import configs
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from flask_module import session
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from service.data_label_service import check_custom_data_label_existence


logger = get_logger("api")
discovery_policy_v2 = Blueprint("discovery_policy", __name__)


@discovery_policy_v2.route('/', methods=['POST'])
def handle_discovery_policy_v2_create():
    from flask_module.discover_policy_v2_reqmodel import DiscoverPolicyCreateReqModel
    from domain_model.discover_policy_v2 import create_discover_policy_v2
    from psycopg2.errors import UniqueViolation

    try:
        data = request.get_json()
        try:
            reqmodel = DiscoverPolicyCreateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"reqmodel: {reqmodel}")
        
        payload = reqmodel.dict(exclude_none=True)
        if "action" in payload and payload["action"]["label"]["labels"]["custom"]:
            all_exists, not_found_labels = check_custom_data_label_existence(payload["action"]["label"]["labels"]["custom"])
            if not all_exists:
                return error_response(Ecodes.VALIDATION_ERROR, 400, f'Custom data label{not_found_labels} not found')
        
        payload["is_predefined"] = False
        payload["status"] = True
        
        new_policy = create_discover_policy_v2(payload, logger)
        
        user = session.get_user(request)    
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Add a new discovery policy {new_policy.name}", 
                     desc='Add discovery policy', type=LogType.DISCOVERY_POLICIES.value, action=LogAction.CREATE.value)
        return success_response(new_policy.to_dict(), 201)
    except UniqueViolation:
        return error_response(Ecodes.DUPLICATE_RESOURCE, 409, "A duplicate Discovery Policy name already exists.")
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)
    
@discovery_policy_v2.route('/', methods=['DELETE'])
def handle_discovery_policy_v2_delete():
    from flask_module.discover_policy_v2_reqmodel import DiscoverPolicyDeleteReqModel
    from domain_model.discover_policy_v2 import delete_discover_policy_v2, get_discover_policy_v2
    from service.discovery_policy_v2_service import get_scan_policy_ref
    
    try:
        args = request.args.to_dict(flat=False)
        logger.info(f"Deleting data_classifier by args {args}")
        
        try:
            reqmodel = DiscoverPolicyDeleteReqModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        for id in reqmodel.id:
            policy = get_discover_policy_v2(id)
            if not policy:
                return error_response(Ecodes.NOT_FOUND, 404, f'Discovery policy not found')
            if policy.is_predefined:
                return error_response(Ecodes.VALIDATION_ERROR, 400, f'Predefined discovery policy cannot be deleted.')
            
            ref = get_scan_policy_ref(id=id)
            if ref:
                return error_response(Ecodes.VALIDATION_ERROR, 400, f'Discovery policy referred by scan policy cannot be deleted.')
        
        delete_names = delete_discover_policy_v2(reqmodel.id, logger)

        user = session.get_user(request)    
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Delete discovery policy {delete_names}", 
                     desc='Delete discovery policy', type=LogType.DATA_CLASSIFIER.value, action=LogAction.DELETE.value)
        return success_response({'message': 'Discovery Policy deleted'}, 200)
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)
    
@discovery_policy_v2.route('/', methods=['PUT'])
def handle_discovery_policy_v2_update():
    from flask_module.discover_policy_v2_reqmodel import DiscoverPolicyUpdateReqModel
    from service.discovery_policy_v2_service import get_scan_policy_ref
    from domain_model.discover_policy_v2 import get_discover_policy_v2, update_disocvery_policy_v2
    try:
        data = request.get_json()
        try:
            reqmodel = DiscoverPolicyUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"reqmodel: {reqmodel}")
        
        policy = get_discover_policy_v2(id=reqmodel.id)
        if not policy:
            return error_response(Ecodes.NOT_FOUND, 404)
        
        if policy.is_predefined:
            return error_response(Ecodes.VALIDATION_ERROR, 400, f'Predefined policy cannot be edited.')

        if get_scan_policy_ref(id=reqmodel.id, logger=logger):
            return error_response(Ecodes.VALIDATION_ERROR, 400, f"Cannot edit this discovery_policy, it has been used by scan")
        
        payload = reqmodel.dict(exclude_none=True)
        
        if "action" in payload and payload["action"]["label"]["labels"]["custom"]:
            all_exists, not_found_labels = check_custom_data_label_existence(payload["action"]["label"]["labels"]["custom"])
            if not all_exists:
                return error_response(Ecodes.VALIDATION_ERROR, 400, f'Custom data label{not_found_labels} not found')
        
        new_policy = update_disocvery_policy_v2(id=reqmodel.id, data=payload, logger=logger)

        user = session.get_user(request)    
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Edit discovery policy {new_policy.get('name','')}", 
                     desc='Edit discovery policy', type=LogType.DISCOVERY_POLICIES.value, action=LogAction.EDIT.value)
        return success_response(new_policy, 200)
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@discovery_policy_v2.route('/', methods=['GET'])
def handle_discovery_policy_v2_query():
    from flask_module.discover_policy_v2_reqmodel import DiscoverPolicyQueryReqModel
    from domain_model.discover_policy_v2 import get_discover_policy_v2

    try:
        # get all conditions
        conditions = request.args.to_dict()
        logger.info(f"conditions: {conditions}")
        try:
            reqmodel = DiscoverPolicyQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        logger.info(f"reqmodel: {reqmodel}")
        policy = get_discover_policy_v2(reqmodel.id, logger)
        if policy is None:
            return error_response(Ecodes.NOT_FOUND, 404)
        
        return success_response(policy.to_dict(), 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@discovery_policy_v2.route('/summary', methods=['GET'])
def handle_discovery_policy_v2_summary_query():
    from flask_module.discover_policy_v2_reqmodel import DiscoverPolicySummaryQueryReqModel
    from service.discovery_policy_v2_service import get_discovery_policy_v2_summary_by_conditions

    try:
        # get all conditions
        conditions = request.args.to_dict()
        logger.info(f"conditions: {conditions}")
        try:
            reqmodel = DiscoverPolicySummaryQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        logger.info(f"reqmodel: {reqmodel}")
        results, total = get_discovery_policy_v2_summary_by_conditions(reqmodel.dict(exclude_none=True), logger)
        if results is None:
            return error_response(Ecodes.INTERNAL_ERROR, 500)
        
        return success_response({
            "list": results,
            "total": total,
            "page": request.args.get('page'),
            "per_page": request.args.get('per_page'),
        }, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)
    

