import traceback
from flask import Blueprint, request
from pydantic import ValidationError
from util.common_log import get_logger
from util.config import configs
import util.err_codes as Ecodes
from util.err_codes import error_response, success_response
from flask_module import session
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from service.predefined_datatype_service import check_category_status

logger = get_logger("api")
discover_rule = Blueprint("discover_rule", __name__)


@discover_rule.route('/', methods=['POST'])
def handle_discover_rule_create():
    from flask_module.discover_rule_reqmodel import DiscoverRuleCreateReqModel
    from service import discover_policy_service
    from service import discover_rule_service
    from service.data_label_service import check_custom_data_label_existence
    from psycopg2.errors import UniqueViolation

    try:
        # create a new discover_rule
        data = request.get_json()
        try:
            reqmodel = DiscoverRuleCreateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"reqmodel: {reqmodel}")
        
        # check if the specified discover policy exists.
        policy = discover_policy_service.read_policy(reqmodel.policy_id)
        if not policy:
            return error_response(Ecodes.NOT_FOUND, 404, f"Discover policy {reqmodel.policy_id} not found")
        
        max_discover_rule_per_policy = configs.get("max_discover_rule_per_policy", 256)
        if discover_rule_service.check_max_discover_rule(reqmodel.policy_id, max_discover_rule_per_policy-1):
            return error_response(Ecodes.LIMIT_REACHED, 400, f'You can only create a maximum of {max_discover_rule_per_policy} discover rules')
        
        # create a new discover rule
        payload = reqmodel.dict(exclude_none=True)
        if "action" in payload and payload["action"]["label"]["labels"]["custom"]:
            all_exists, not_found_labels = check_custom_data_label_existence(payload["action"]["label"]["labels"]["custom"])
            if not all_exists:
                return error_response(Ecodes.VALIDATION_ERROR, 400, f'Custom data label{not_found_labels} not found')
        
        ml_categories = []
        for _, condition in payload["match_condition"].items():
            if "type" in condition and condition["type"] == "ml_category":
                if "main_class_id" in condition:
                    ml_categories.append(condition["main_class_id"])
                if "sub_class_id" in condition:
                    ml_categories.append(condition["sub_class_id"])
        if ml_categories and not check_category_status(ml_categories):
            return error_response(Ecodes.VALIDATION_ERROR, 400, f'Discarded ML categories cannot be used as conditions.')

        new_rule = discover_rule_service.create_discover_rule(payload, logger)
        logger.info(f"Discover rule created, {new_rule}")
        
        user = session.get_user(request)    
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Add a rule {new_rule.get('name', '')} for discovery policy {policy.name}", 
                     desc='Add rule for discovery policy', type=LogType.DISCOVERY_POLICIES.value, action=LogAction.CREATE.value)
        return success_response(new_rule, 201)
    except UniqueViolation:
        return error_response(Ecodes.DUPLICATE_RESOURCE, 409, "A duplicate Rule Name already exists.")
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@discover_rule.route('/create_from_template', methods=['POST'])
def handle_discover_rule_create_from_template():
    from flask_module.discover_rule_reqmodel import DiscoverRuleCreateFromTemplateReqModel
    from service import discover_policy_service
    from service import discover_rule_service
    from service import predefined_rule_template_service
    from service.data_label_service import check_custom_data_label_existence

    try:
        # create a new discover_rule
        data = request.get_json()
        try:
            reqmodel = DiscoverRuleCreateFromTemplateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"reqmodel: {reqmodel}")
        
        # check if the specified discover policy exists.
        policy = discover_policy_service.read_policy(reqmodel.policy_id)
        if not policy:
            return error_response(Ecodes.NOT_FOUND, 404, f"Discover policy {reqmodel.policy_id} not found")
        
        selected_template_num = len(reqmodel.templates) 
        max_discover_rule_per_policy = configs.get("max_discover_rule_per_policy", 256)
        if discover_rule_service.check_max_discover_rule(reqmodel.policy_id, max_discover_rule_per_policy-selected_template_num):
            return error_response(Ecodes.LIMIT_REACHED, 400, f'You can only create a maximum of {max_discover_rule_per_policy} discover rules')
        
        # convert template to rule data
        payload = reqmodel.dict(exclude_none=True)
        if "action" in payload and payload["action"]["label"]["labels"]["custom"]:
            all_exists, not_found_labels = check_custom_data_label_existence(payload["action"]["label"]["labels"]["custom"])
            if not all_exists:
                return error_response(Ecodes.VALIDATION_ERROR, 400, f'Custom data label{not_found_labels} not found')
            
        rules_data = predefined_rule_template_service.convert_templates_to_discover_rule(payload, logger)
        if rules_data:
            # create a new discover rule
            new_rules = discover_rule_service.create_discover_rules(rules_data, logger) 
            logger.info(f"Discover rule created, {new_rules}")
            return success_response(new_rules, 201)
        else:
            logger.info(f"convert template to discover rule failed")
            return error_response(Ecodes.INTERNAL_ERROR, 500)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@discover_rule.route('/', methods=['GET'])
def handle_discover_rule_get():
    from flask_module.discover_rule_reqmodel import DiscoverRuleQueryReqModel
    from service import discover_rule_service

    try:
        # get all conditions
        conditions = request.args.to_dict()
        logger.info(f"conditions: {conditions}")
        try:
            reqmodel = DiscoverRuleQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        logger.info(f"reqmodel: {reqmodel}")
        results, total = discover_rule_service.get_rules_by_conditions(reqmodel.dict(exclude_none=True), logger)
        if results is None:
            return error_response(Ecodes.INTERNAL_ERROR, 500)
        
        return success_response({
            "list": results,
            "total": total,
            "page": request.args.get('page'),
            "per_page": request.args.get('per_page'),
        }, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@discover_rule.route('/', methods=['PUT'])
def handle_discover_rule_update():
    from flask_module.discover_rule_reqmodel import DiscoverRuleUpdateReqModel
    from service import discover_policy_service
    from service import discover_rule_service
    from service.data_label_service import check_custom_data_label_existence
    from service.scan_policy_service import get_discover_policy_check_ref

    # update an existing discover_rule
    try:
        data = request.get_json()
        logger.info(f"Update discover_rule: {data}")
        try:
            reqmodel = DiscoverRuleUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        logger.info(f"reqmodel: {reqmodel}")

        # check if the discover rule exists
        rule = discover_rule_service.read_discover_rule(reqmodel.id)
        if not rule:
            return error_response(Ecodes.NOT_FOUND, 404)
        
        ref_data = get_discover_policy_check_ref(str(rule.policy_id), logger)
        if ref_data:
            return error_response(Ecodes.INTERNAL_ERROR, 500, f"Can not edit this discovery rule, it has been used by scan")

        payload = reqmodel.dict(exclude_none=True)
        if "action" in payload and payload["action"]["label"]["labels"]["custom"]:
            all_exists, not_found_labels = check_custom_data_label_existence(payload["action"]["label"]["labels"]["custom"])
            if not all_exists:
                return error_response(Ecodes.VALIDATION_ERROR, 400, f'Custom data label{not_found_labels} not found')
        
        ml_categories = []
        for _, condition in payload["match_condition"].items():
            if "type" in condition and condition["type"] == "ml_category":
                if "main_class_id" in condition:
                    ml_categories.append(condition["main_class_id"])
                if "sub_class_id" in condition:
                    ml_categories.append(condition["sub_class_id"])
        if ml_categories and not check_category_status(ml_categories):
            return error_response(Ecodes.VALIDATION_ERROR, 400, f'Discarded ML categories cannot be used as conditions.')
        
        new_rule = discover_rule_service.update_discover_rule(reqmodel.id, payload, logger)
        logger.info(f"Updated discover rule: {new_rule}")
        
        policy = discover_policy_service.read_policy(new_rule.get('policy_id', ''))
        if not policy:
            return error_response(Ecodes.NOT_FOUND, 404, f"Discover policy {new_rule.get('id', '')} not found")
        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Edit the rule {new_rule.get('name', '')} of discovery policy {policy.name}", 
                     desc='Edit rule of discovery policy', type=LogType.DISCOVERY_POLICIES.value, action=LogAction.EDIT.value)
        return success_response(new_rule, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@discover_rule.route('/', methods=['DELETE'])
def handle_discover_rule_delete():
    from flask_module.discover_rule_reqmodel import DiscoverRuleDeleteReqModel
    from service import discover_policy_service
    from service import discover_rule_service

    try:
        # delete an existing discover_rule
        args = request.args.to_dict(flat=False)
        logger.info(f"Deleting discover rule by args {args}")
        try:
            reqmodel = DiscoverRuleDeleteReqModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        logger.info(f"reqmodel: {reqmodel}")

        rules = []
        for id in reqmodel.id:
            r = discover_rule_service.read_discover_rule(id)
            if not r:
                continue
            p = discover_policy_service.read_policy(str(r.policy_id))
            if not p:
                continue
            rules.append(f"rule {r.name} of discover policy {p.name}")
        rules_str = ",".join(rules)

        rst = discover_rule_service.delete_discover_rules(reqmodel.id, logger)
        if not rst:
            logger.error(f"delete discover rule failed.")
            return error_response(Ecodes.INTERNAL_ERROR, 500)

        logger.info(f"Deleted discover rule {reqmodel.id}")
        
        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value, message=f"Delete the {rules_str}", 
                     desc='Delete rule of discovery policy', type=LogType.DISCOVERY_POLICIES.value, action=LogAction.DELETE.value)
        return success_response({'message': 'Discover rule deleted', 'deleted_ids': reqmodel.id, 'failed_ids': []}, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@discover_rule.route('/check_ml_type', methods=['GET'])
def check_discover_policy_ml_type():
    from flask_module.discover_rule_reqmodel import DiscoverRuleMLCondition
    from service import discover_rule_service

    try:
        # delete an existing discover_rule
        args = request.args.to_dict(flat=False)
        logger.info(f"check_discover_policy_ml_type {args}")
        try:
            reqmodel = DiscoverRuleMLCondition(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"reqmodel: {reqmodel}")
        result = discover_rule_service.check_ml_type_rule(reqmodel.policy_id, logger)
        return success_response({
            "result": result,
            }, 200)

    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)
