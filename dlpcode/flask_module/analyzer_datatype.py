
import traceback
from datetime import datetime
from flask import Blueprint, request, jsonify
from pydantic import ValidationError
from util.common_log import get_logger
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from flask_module import session

analyzer_datatype = Blueprint("data_type", __name__)
logger = get_logger("api")


@analyzer_datatype.route("/index", methods=["GET"])
def index():
    return "This is a analyzer_datatype index page"

# TODO: Implement analyzer_datatype API
# 1. Implement a analyzer_datatype API that returns a list of scheduled tasks
# 2. Implement a analyzer_datatype API that schedules a task
# 3. Implement a analyzer_datatype API that unschedules a task
# 4. Implement a analyzer_datatype API that updates a task schedule
# 5. .......

@analyzer_datatype.route("/standard_data_type", methods=['GET'])
def handle_standard_dtype():
    from service import predefined_datatype_service
    from .analyzer_datatype_reqmodel import StandardDtypeCondition
    
    try:
        try:
            args = request.args.to_dict()
            reqmodel = StandardDtypeCondition(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        
        logger.info(f"reqmodel: {reqmodel}")
        sort_field = reqmodel.sort_field
        sort_method = reqmodel.sort_method
        page = reqmodel.page
        per_page = reqmodel.per_page
    
        res, total_nums, err = predefined_datatype_service.get_data_type_by_condition(
            condition=reqmodel.dict(exclude_none=True), 
            sort_field=sort_field, 
            sort_method=sort_method, 
            page=page, 
            per_page=per_page
        )
        if err is not None:
            raise ValueError(f"get data type error: {err}")
        if res is None:
            return error_response(Ecodes.NOT_FOUND, 404, "data type not found")
            
        return success_response({
            "list": res,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
            "total": total_nums
        })
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@analyzer_datatype.route("/supported_regions", methods=['GET'])
def get_supported_regions():
    from service import predefined_datatype_service

    try:
        res, err = predefined_datatype_service.get_supported_regions()
        if err is not None:
            raise ValueError(f"get supported regions failed: {err}")
        if res is None:
            return error_response(Ecodes.NOT_FOUND, 404, "supported regions not found")
        return success_response(res)
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@analyzer_datatype.route("/supported_type_categories", methods=['GET'])
def get_supported_type_categories():
    from service import predefined_datatype_service

    try:
        res, err = predefined_datatype_service.get_supported_categories()
        if err is not None:
            raise ValueError(f"get supported type categories failed: {err}")
        if res is None:
            return error_response(Ecodes.NOT_FOUND, 404, "supported categories not found")
        return success_response(res)
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@analyzer_datatype.route("/custom_dtype_group/summary", methods=['GET'])
def get_custom_dtype_group_summary():
    from service import custom_datatype_service

    try:
        res = custom_datatype_service.get_all_group_summary()
        return success_response(res)
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@analyzer_datatype.route("/custom_dtype_group", methods=['GET'])
def get_custom_dtype_group():
    from service import custom_datatype_service
    from .analyzer_datatype_reqmodel import CustomDtypeGroupQueryModel

    try:
        try:
            args = request.args.to_dict()
            reqmodel = CustomDtypeGroupQueryModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")
            
        groups, total = custom_datatype_service.get_group(
            conditions=reqmodel.dict(exclude_none=True),
            sort_field=reqmodel.sort_field,
            sort_method=reqmodel.sort_method,
            page=reqmodel.page,
            per_page=reqmodel.per_page,
            full_res=True
        )

        return success_response({
            "list":groups,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
            "total":total
        })
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)

    
@analyzer_datatype.route("/custom_dtype_group", methods=['POST'])
def post_custom_dtype_group():
    from service import custom_datatype_service
    from .analyzer_datatype_reqmodel import CustomDtypeGroupCreateModel
    from psycopg2.errors import UniqueViolation

    try:
        try:
            data = request.get_json()
            reqmodel = CustomDtypeGroupCreateModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")

        new_group = custom_datatype_service.Group()
        new_group.name = reqmodel.name
        new_group.description = reqmodel.description
        new_group.created_at = datetime.now()
        new_group.updated_at = datetime.now()
        group = custom_datatype_service.create_group(group=new_group)
        if group is None:
            return error_response(Ecodes.INTERNAL_ERROR, 500, "create dtype group failed")
        
        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Create custom data type group {new_group.name}",
            desc="Create new custom data type group",
            action=LogAction.CREATE.value,
            type=LogType.DATA_TYPES.value
        )
        return success_response(group)
    except UniqueViolation:
        return error_response(Ecodes.DUPLICATE_RESOURCE, 409, "A duplicate Group Name already exists.")
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@analyzer_datatype.route("/custom_dtype_group", methods=['PUT'])
def put_custom_dtype_group():
    from service import custom_datatype_service
    from .analyzer_datatype_reqmodel import CustomDtypeGroupUpdateModel

    try:
        try:
            data = request.get_json()
            reqmodel = CustomDtypeGroupUpdateModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")
        group = custom_datatype_service.update_group(id = reqmodel.id, group_data=reqmodel.dict(exclude_none=True))
        if group is None:
            return error_response(Ecodes.NOT_FOUND, 404, 'custom dtype group not found')
        
        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Edit custom data type group {group.get('name','')}",
            desc="Edit custom data type group",
            action=LogAction.EDIT.value,
            type=LogType.DATA_TYPES.value
        )
        return success_response(group)
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@analyzer_datatype.route("/custom_dtype_group", methods=['DELETE'])
def delete_custom_dtype_group():
    from service import custom_datatype_service
    from .analyzer_datatype_reqmodel import CustomDtypeGroupDeleteModel

    try:
        try:
            args = request.args.to_dict(flat=False)
            reqmodel = CustomDtypeGroupDeleteModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")
        groups, _ = custom_datatype_service.get_group(conditions={"id":reqmodel.id})
        delete_names = [group['name'] for group in groups]
        deleted_ids, failed_ids = custom_datatype_service.delete_group(ids=reqmodel.id)

        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Delete custom data type group {','.join(delete_names)}",
            desc="Delete custom data type group",
            action=LogAction.DELETE.value,
            type=LogType.DATA_TYPES.value
        )
        return success_response({"deleted_ids": deleted_ids, "failed_ids": failed_ids})
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@analyzer_datatype.route("/custom_data_type", methods=['GET'])
def get_custom_dtype():
    from service import custom_datatype_service
    from .analyzer_datatype_reqmodel import CustomDtypeQueryModel

    try:
        try:
            args = request.args.to_dict()
            reqmodel = CustomDtypeQueryModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")
        
        dtypes, total = custom_datatype_service.get_custom_datatype(
            conditions=reqmodel.dict(exclude_none=True),
            sort_field=reqmodel.sort_field,
            sort_method=reqmodel.sort_method,
            page=reqmodel.page,
            per_page=reqmodel.per_page
        )
        return success_response({
            "list": dtypes,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
            "total": total
        })
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@analyzer_datatype.route("/custom_data_type", methods=['POST'])
def post_custom_dtype():
    from service import custom_datatype_service
    from .analyzer_datatype_reqmodel import CustomDtypeCreateModel
    from psycopg2.errors import UniqueViolation

    try:
        try:
            data = request.get_json()
            reqmodel = CustomDtypeCreateModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")
        
        new_dtype = custom_datatype_service.CustomDatatype()
        new_dtype.name = reqmodel.name
        new_dtype.description = reqmodel.description
        new_dtype.group_uuid = reqmodel.group_uuid
        new_dtype.definition = reqmodel.definition.dict()
        new_dtype.created_at = datetime.now()
        new_dtype.updated_at = new_dtype.created_at
        dtype = custom_datatype_service.create_custom_datatype(dtype=new_dtype)
        if dtype is None:
            return error_response(Ecodes.NOT_FOUND, 404, "related group not found")
        
        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Create custom data type {new_dtype.name}",
            desc="Create custom data type",
            action=LogAction.CREATE.value,
            type=LogType.DATA_TYPES.value
        )
        return success_response(dtype)
    except UniqueViolation:
        return error_response(Ecodes.DUPLICATE_RESOURCE, 409, "A duplicate Data Type Name already exists.")
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@analyzer_datatype.route("/custom_data_type", methods=['PUT'])
def put_custom_dtype():
    from service import custom_datatype_service
    from .analyzer_datatype_reqmodel import CustomDtypeUpdateModel

    try:
        try:
            data = request.get_json()
            reqmodel = CustomDtypeUpdateModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")

        dtype = custom_datatype_service.update_custom_datatype(id = reqmodel.id, dtype_data=reqmodel.dict(exclude_none=True))
        if dtype is None:
            return error_response(Ecodes.NOT_FOUND, 404, "custom dtype or related group not found")
        
        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Edit custom data type {dtype.get('name','')}",
            desc="Edit custom data type",
            action=LogAction.EDIT.value,
            type=LogType.DATA_TYPES.value
        )
        return success_response(dtype)
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@analyzer_datatype.route("/custom_data_type", methods=['DELETE'])
def delete_custom_dtype():
    from service import custom_datatype_service
    from .analyzer_datatype_reqmodel import CustomDtypeDeleteModel

    try:
        try:
            args = request.args.to_dict(flat=False)
            reqmodel = CustomDtypeDeleteModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")
        dtypes, _ = custom_datatype_service.get_custom_datatype(conditions={"id":reqmodel.id})
        delete_names = [dtype['name'] for dtype in dtypes]
        deleted_ids, failed_ids = custom_datatype_service.delete_custom_datatype(ids = reqmodel.id)

        record_event_log(
            user=session.get_user(request),
            level=LogLevel.INFO.value,
            message=f"Delete custom data type {','.join(delete_names)}",
            desc="Delete custom data type",
            action=LogAction.DELETE.value,
            type=LogType.DATA_TYPES.value
        )
        return success_response({"deleted_ids": deleted_ids, "failed_ids": failed_ids})
    except Exception as e:
        logger.error(e)
        return error_response(Ecodes.INTERNAL_ERROR, 500)
