import uuid
from util.common_log import get_logger
from pydantic import BaseModel, validator
from pydantic import Field, constr, conint, confloat, SecretStr
from typing import List, Optional, Literal, Union

logger = get_logger("api")

class CreateEDMRuleReqModel(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, example="edm-rule-1", description="EDM rule name")
    description: str = Field("", max_length=256, description="EDM rule description.")
    status: Literal[0, 1] = Field(1, description="EDM rule status")
    edm_template_id: constr(min_length=36, max_length=36)  = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    primary_key: List[str] = Field(..., description="A list of JSON strings")
    secondary_key: List[str] = Field([], description="A list of JSON strings")
    match_count: int = Field(None, example=1, description="match times")

    @validator('edm_template_id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class UpdateEDMRuleReqModel(BaseModel):
    id: constr(min_length=36, max_length=36)  = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    name: str = Field(..., min_length=1, max_length=100, example="edm-rule-1", description="EDM rule name")
    description: str = Field("", max_length=256, description="This is the EDM rule name")
    status: Literal[0, 1] = Field(1, description="EDM rule status")
    edm_template_id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    primary_key: List[str] = Field(..., description="A list of JSON strings, eg: ssn, ccn")
    secondary_key: List[str] = Field(..., description="A list of JSON strings, eg: name, birthday")
    match_count: int = Field(None, example=1, description="match times")

    @validator('id', 'edm_template_id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class DeleteEDMRuleReqModel(BaseModel):
    id: constr(min_length=36, max_length=36)  = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class QueryEDMRuleReqModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(None, example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")
    edm_template_id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    name: str = Field(None, min_length=1, max_length=100, example="edm-rule-1", description="edm rule name")
    description: Optional[str] = Field(None, min_length=1, max_length=100, description="edm rule note")
    sort_field: str = Field('updated_at', max_length=32, description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: conint(ge=0) = Field(1, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', 'edm_template_id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
