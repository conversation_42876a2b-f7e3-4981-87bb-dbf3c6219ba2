import traceback
from util.common_log import get_logger
from flask import Blueprint, request
from util.err_codes import error_response, success_response
from util.err_codes import ModErrCode
from huey_worker.protection_action_task import protection_task_restore_file_quarantine
from system.system_log import record_event_log, LogLevel, LogAction, LogType
from flask_module import session
from domain_model.global_cache import GlobalCache
from domain_model.scan_policy import get_scan_policy
from util.enum_ import ProtectionQuarantineStatus

logger = get_logger("api")
restore_quarantine_files = Blueprint("restore_quarantine_files", __name__)

@restore_quarantine_files.route('/', methods=['POST'])
def restore_files():
    try:
        data = request.get_json()
        user = session.get_user(request)
        logger.info(f"restore_files {data}")
        for item in data:
            if "file_uuid" not in item or "scan_uuid" not in item:
                return error_response(ModErrCode.ErrCode02050026, 400) 
            file_uuid = item["file_uuid"]
            scan_uuid = item["scan_uuid"]
            cache = GlobalCache(scan_uuid)
            fr = cache.get_by_file_uuid(file_uuid=file_uuid)
            quarantineStatus = (fr.get('reserve_json3') or {}).get('quarantine_status', ProtectionQuarantineStatus.INIT)
            if quarantineStatus != ProtectionQuarantineStatus.SUCCESS:                
                return error_response(ModErrCode.ErrCode02050030, 400, extend_message=f"file name: {fr.get('file_name', '')}") 

        for item in data:
            file_uuid = item["file_uuid"]
            scan_uuid = item["scan_uuid"]   
            protection_task_restore_file_quarantine(scan_uuid, file_uuid, user=user)
            
        return success_response({'message': 'The restore quarantined file task has been successfully assigned, please refresh the page to view the status.'}, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(ModErrCode.ErrCode02050027, 500)
