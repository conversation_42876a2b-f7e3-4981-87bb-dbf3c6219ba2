import socket
import uuid

from pydantic import validator, constr, conint, root_validator

from util.common_log import get_logger
from typing import List, Dict, Optional, Literal
from pydantic import BaseModel, Field

logger = get_logger("api")

allowed_levels = {"EMERGENCY", "ALERT", "CR<PERSON><PERSON><PERSON>", "ERROR", "WARNING", "NOTICE", "INFORMATION", "DEBUG"}
allowed_severities = {"LOW", "MEDIUM", "HIGH", "CRITICAL"}
allowed_device_types = {"FOS", "FCT", "EMS"}
event_log_type = 'event_log'
data_scanning_incident_type = 'data_scanning_incident'
firewall_incident_type = 'firewall_incident'
http2_log_type = 'http2_log'

class LogTypeModel(BaseModel):
    status: bool = Field(..., description="Whether this log type is enabled")
    level: Optional[List[str]] = Field(None, description="List of log levels (if applicable)")
    severity: Optional[List[str]] = Field(None, description="List of log severities (if applicable)")
    device_type: Optional[List[str]] = Field(None, descritpion="List of device types (if applicable)")

    @root_validator(pre=True)
    def check_only_one_filter(cls, values):
        fields = ['level', 'severity', 'device_type']
        provided = [field for field in fields if values.get(field) is not None]

        if len(provided) == 0:
            raise ValueError(f"At least one of {fields} must be provided.")

        return values

    @validator("level", pre=True)
    def validate_level(cls, value):
        if value is None:
            return None

        if not isinstance(value, list):
            raise ValueError("Level must be a list of strings.")

        if not value:
            raise ValueError("Level cannot be empty.")

        normalized_values = [v.upper() for v in value]
        invalid_values = set(normalized_values) - allowed_levels

        if invalid_values:
            raise ValueError(f"Invalid level values: {invalid_values}. Allowed values: {allowed_levels}")

        return normalized_values

    @validator("severity", pre=True)
    def validate_severity(cls, value):
        if value is None:
            return None

        if not isinstance(value, list):
            raise ValueError("Severity must be a list of strings.")
        if not value:
            raise ValueError("Severity cannot be empty.")

        normalized_values = [v.upper() for v in value]
        invalid_values = set(normalized_values) - allowed_severities

        if invalid_values:
            raise ValueError(f"Invalid severity values: {invalid_values}. Allowed values: {allowed_severities}")

        return normalized_values

    @validator("device_type", pre=True)
    def validate_device_type(cls, value):
        if value is None:
            return None
        if not isinstance(value, list):
            raise ValueError("Device type must be a list of strings.")
        if not value:
            raise ValueError("Device type cannot be empty.")
        normalized_values = [v.upper() for v in value]
        invalid_values = set(normalized_values) - allowed_device_types
        if invalid_values:
            raise ValueError(f"Invalid device type values: {invalid_values}. Allowed values: {allowed_device_types}")
        return normalized_values

class AttributesModel(BaseModel):
    addr: str = Field(..., description="IP address or domain of the log server")
    port: int = Field(..., ge=1, le=65535, description="Port number of the log server (required)")
    protocol: Optional[Literal['udp', 'tcp']] = Field("TCP", description="Protocol used for log transmission")
    log_type: Dict[str, LogTypeModel] = Field(
        ..., description="Determines which logs should be sent to the server"
    )

    @validator("log_type")
    def validate_log_type(cls, value: Dict[str, LogTypeModel]):
        allowed_keys = {event_log_type, data_scanning_incident_type, firewall_incident_type, http2_log_type}

        invalid_keys = set(value.keys()) - allowed_keys
        if invalid_keys:
            raise ValueError(f"Invalid log_type keys: {invalid_keys}. Allowed keys: {allowed_keys}")

        for key, log in value.items():
            if key == event_log_type:
                if log.severity is not None:
                    raise ValueError(f"'event_log' cannot use 'severity' or 'device_type'. Use 'level'.")
            elif key in {data_scanning_incident_type, firewall_incident_type}:
                if log.level is not None:
                    raise ValueError(f"'{key}' cannot use 'level' or 'device_type'. Use 'severity'.")
            elif key == http2_log_type:
                if log.level is not None or log.severity is not None:
                    raise ValueError(f"'http2_log' can only use 'device_type'. Remove 'level' and 'severity'.")

        return value


class LogServerCreateReqModel(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="Log server name")
    status: bool = Field(..., description="Log server status (enabled/disabled)")
    type: Literal['syslog', 'faz'] = Field(..., description="Type of log server, e.g., 'syslog' or 'faz'")
    description: Optional[str] = Field("", max_length=256, description="Optional description of the log server")
    attributes: AttributesModel = Field(..., description="Server attributes including address, log type, etc.")
    conn_status: Optional[str] = Field("", description="Connection status of the log server")

    # @model_validator(mode="after")
    # def validate_syslog_log_type(self):
    #     if self.type == "syslog":
    #         allowed_syslog_keys = {event_log_type}
    #         actual_keys = set(self.attributes.log_type.keys())
    #         if actual_keys != allowed_syslog_keys:
    #             raise ValueError(
    #                 f"When type is 'syslog', log_type must only contain: {allowed_syslog_keys}, but got {actual_keys}.")
    #     return self

    class Config:
        extra = "forbid"


class LogServerQueryReqModel(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Log server name (fuzzy match)")
    type: Optional[str] = Field(None, description="Log server type")
    status: Optional[bool] = Field(None, description="Log server status")
    sort_field: Literal['ctime', 'utime', 'status', 'name', 'type'] = Field('ctime', description="Sort field")
    sort_method: Literal['asc', 'desc'] = Field('desc', description="Sort order")
    page: conint(ge=1) = Field(1, description="Page number")
    per_page: conint(ge=1, le=100) = Field(10, description="Items per page")

    class Config:
        extra = "forbid"


class LogServerUpdateReqModel(BaseModel):
    id: uuid.UUID = Field(..., description="UUID of log server")
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Log server name")
    status: Optional[bool] = Field(None, description="Log server status (enabled/disabled)")
    type: Optional[Literal['syslog', 'faz']] = Field(None, description="Type of log server, e.g., 'syslog' or 'faz'")
    description: Optional[str] = Field(None, max_length=256, description="Optional description of the log server")
    attributes: Optional[AttributesModel] = Field(None, description="Server attributes including address, log type, etc.")
    conn_status: Optional[str] = Field(None, description="Connection status of the log server")

    class Config:
        extra = "forbid"


class LogServerDeleteReqModel(BaseModel):
    id: List[constr(min_length=36, max_length=36)] = Field(..., description="List of log server UUIDs")

    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError:
            raise ValueError(f"Invalid UUID: {v}")
        return v


class LogServerTestReqModel(BaseModel):
    ip: str = Field(..., description="IP address of the log server to test")
    port: int = Field(..., gt=0, lt=65536, description="Port of the log server to test (1-65535)")
    protocol: str = Field(..., description="Protocol to use for connection, either 'tcp' or 'udp'")
    type: Literal['syslog', 'faz'] = Field(..., description="Type of log server, e.g., 'syslog', 'faz'")

    class Config:
        extra = "forbid"

    @validator('ip')
    def ip_validator(cls, v):
        try:
            socket.inet_aton(v)  # Validate if the IP address is valid
        except socket.error:
            raise ValueError(f"Invalid IP address: {v}")
        return v

    @validator('port')
    def port_validator(cls, v):
        if not (1 <= v <= 65535):
            raise ValueError(f"Invalid port: {v}. Port must be between 1 and 65535.")
        return v

    @validator('protocol')
    def protocol_validator(cls, v, values):
        if values.get('type') != 'faz':
            if v not in ['tcp', 'udp']:
                raise ValueError(f"Invalid protocol: {v}. Protocol must be 'tcp' or 'udp'.")
        return v
