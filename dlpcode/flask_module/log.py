import json
import traceback
import uuid

from flask import request, Blueprint
from pydantic import ValidationError

import util.err_codes as Ecodes
from flask_module import session
from flask_module.log_reqmodel import LogSettingQueryReqModel
from system.log_setting import (
    get_system_log_setting,
    get_ds_incident_log_setting,
    # get_fw_incident_log_setting
    get_http2_service_log_setting
)
from system.system_log import LogLevel, record_event_log, LogAction, LogType
from util.common_log import get_logger
from util.err_codes import error_response, success_response
from util.redis_stream import RedisStream

logger = get_logger("api")
log = Blueprint("log", __name__)


@log.route("/system_events", methods=["GET"])
def handle_system_events_get():
    from flask_module.log_reqmodel import SystemEventsParams
    from system.system_log import query_event_log

    if request.method == 'GET':
        try:
            logger.info(f'args: {request.args.to_dict()}')
            validated_params = SystemEventsParams(**request.args.to_dict())
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            return success_response(query_event_log(validated_params.dict(exclude_none=True)))
        except Exception:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

@log.route("/http2_events", methods=["GET"])
def handle_http2_events_get():
    from flask_module.log_reqmodel import Http2EventsParams
    from domain_model.http2_event_service import query_http2_event_log

    if request.method == 'GET':
        try:
            logger.info(f'args: {request.args.to_dict()}')
            validated_params = Http2EventsParams(**request.args.to_dict())
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        try:
            return success_response(query_http2_event_log(validated_params.dict(exclude_none=True)))
        except Exception:
            logger.exception(traceback.format_exc())
            return error_response(Ecodes.INTERNAL_ERROR, 500)

@log.route("/logsetting", methods=["PUT"])
def set_log_setting():
    from system.log_setting import update_log_setting
    from flask_module.log_reqmodel import SetLogSettingModel
    try:
        validated_params = SetLogSettingModel(**request.json)
    except ValidationError as e:
        return error_response(Ecodes.VALIDATION_ERROR, 400, e)

    ret = update_log_setting(validated_params.name, validated_params.config)
    if ret == None:
        return error_response(Ecodes.INTERNAL_ERROR, 500)
    return success_response(ret, 200)


@log.route("/logsetting", methods=["GET"])
def get_log_setting():
    try:
        # Extract query parameters
        conditions = request.args.to_dict()

        # Validate using Pydantic model
        reqmodel = LogSettingQueryReqModel(**conditions)

        # Based on the validated 'name', call the appropriate function
        if reqmodel.name == "system":
            return success_response(get_system_log_setting(), 200)
        elif reqmodel.name == "incident_data_scanning":
            return success_response(get_ds_incident_log_setting(), 200)
        # elif reqmodel.name == "incident_firewall":
        #     return success_response(get_fw_incident_log_setting(), 200)
        elif reqmodel.name == "http2_service":
            return success_response(get_http2_service_log_setting(), 200)
        else:
            return error_response(Ecodes.INTERNAL_ERROR, 500)

    except ValidationError as e:
        # Handle validation error
        return error_response(Ecodes.VALIDATION_ERROR, 400, e.errors())

    except Exception as e:
        # Handle unexpected errors
        return error_response(Ecodes.INTERNAL_ERROR, 500, str(e))


@log.route('/setting/servers', methods=['GET'])
def get_log_servers():
    from flask_module.log_server_reqmodel import LogServerQueryReqModel
    from service import log_server_service

    try:
        conditions = request.args.to_dict()
        try:
            reqmodel = LogServerQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"Query parameters: {reqmodel}")
        results, total = log_server_service.get_log_servers_by_conditions(reqmodel.dict(exclude_none=True), logger)
        resp = {
            "ErrorCode": 0,
            "list": results,
            "total": total,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,

        }
        return success_response(resp, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@log.route('/setting/servers', methods=['POST'])
def create_log_server():
    from flask_module.log_server_reqmodel import LogServerCreateReqModel
    from service import log_server_service
    from sqlalchemy.exc import IntegrityError

    try:
        data = request.get_json()
        try:
            reqmodel = LogServerCreateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"Creating log server: {reqmodel}")
        new_server = log_server_service.create_log_server(None, reqmodel.dict(exclude_none=True), logger)

        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value,
                         message=f"Added log server {new_server.get('name', '')}",
                         desc='Add log server', type=LogType.SYSTEM.value,
                         action=LogAction.CREATE.value)
        ret = notify_config_update()
        if not ret:
            logger.error("Failed to notify log daemon to reload server list")
        return success_response({"ErrorCode": 0, "Data": new_server}, 201)

    except IntegrityError:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.DUPLICATE_RESOURCE, 409, "A log server with this name already exists.")
    except Exception:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@log.route('/setting/servers', methods=['PUT'])
def update_log_server():
    from flask_module.log_server_reqmodel import LogServerUpdateReqModel
    from service import log_server_service

    try:
        data = request.get_json()
        try:
            reqmodel = LogServerUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"Updating log server: {reqmodel}")
        updated_server = log_server_service.update_log_server(reqmodel.id, reqmodel.dict(exclude_none=True), logger)

        if not updated_server:
            return error_response(Ecodes.NOT_FOUND, 404, "Log server not found")
        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value,
                         message=f"Updated log server {updated_server.get('name', '')}",
                         desc='Update log server', type=LogType.SYSTEM.value,
                         action=LogAction.EDIT.value)
        ret = notify_config_update()
        if not ret:
            logger.error("Failed to notify log daemon to reload server list")
        return success_response({"ErrorCode": 0, "Data": updated_server}, 200)

    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@log.route('/setting/servers', methods=['DELETE'])
def delete_log_server():
    from flask_module.log_server_reqmodel import LogServerDeleteReqModel
    from service import log_server_service

    try:
        args = request.args.to_dict(flat=False)
        try:
            reqmodel = LogServerDeleteReqModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"Deleting log servers: {reqmodel.id}")
        success, failed, deleted_names = log_server_service.delete_log_servers(reqmodel.id, logger)

        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value,
                         message=f"Deleted log servers: {', '.join(deleted_names)}",
                         desc='Delete log server', type=LogType.SYSTEM.value,
                         action=LogAction.DELETE.value)
        ret = notify_config_update()
        if not ret:
            logger.error("Failed to notify log daemon to reload server list")
        return success_response({"ErrorCode": 0, "Data": {'deleted_ids': success, 'failed_ids': failed}}, 200)

    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@log.route('/setting/servers/test', methods=['POST'])
def test_log_server():
    from flask_module.log_server_reqmodel import LogServerTestReqModel
    from service import log_server_service

    try:
        data = request.get_json()
        try:
            reqmodel = LogServerTestReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"Testing log server connectivity: {reqmodel}")
        test_result = log_server_service.test_connection(reqmodel.dict(exclude_none=True), logger)
        return success_response({"ErrorCode": 0, "Data": test_result}, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


def notify_config_update():
    message = {"command": "reload_server_list", "id": str(uuid.uuid4()), }
    message_dict = {"message": json.dumps(message)}
    redis_stream = RedisStream("log_daemon_control_stream")

    if not redis_stream.send_redis_stream_msg(message_dict, maxlen=1000, approximate=True):
        logger.error(f"Failed to send Redis stream message: {message_dict}")
        return False

    return True
