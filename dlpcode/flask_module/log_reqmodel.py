from typing import Optional, Literal

from pydantic import BaseModel, validator
from pydantic import Field

from util.enum_ import LogSettingName


class SystemEventsParams(BaseModel):
    start_time: Optional[str] = Field(default="")
    end_time: Optional[str] = Field(default="")
    level: Literal["-1", "0", "1", "2", "3", "4", "5", "6", "7"] = Field(default="-1", description="The level of this log. -1: all, 0: emergency, 1: alert,  2: critical, 3: error, 4: warning, 5: notice, 6: information, 7: debug")
    user: Optional[str] = Field(default="")
    type: Optional[str] = Field(default="")
    description: Optional[str] = Field(default="")
    action: Optional[str] = Field(default="")
    message: Optional[str] = Field(default="")
    incident: Optional[str] = Field(default="")     
    sort_field: str = Field('time', max_length=32, description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: Optional[int] = Field(default=1)
    per_page: Optional[int] = Field(default=10)

    @validator('start_time')
    def start_time_validator(cls, v, values, **kwargs):
        if v.isdigit():
            return v
        else:
            raise ValueError(f'The field start_time must be a number, given: {v}.')
        
    @validator('end_time')
    def end_time_validator(cls, v, values, **kwargs):
        if v.isdigit():
            start_time = values.get('start_time', '')
            if start_time != "" and int(v) < int(start_time):
                raise ValueError(f'end_time must be greater than or equal to start_time, given start_time: {int(start_time)}, end_time: {v}.')
            else:     
                return v
        else:
            raise ValueError(f'The field end_time must be a number, given: {v}.')

class Http2EventsParams(BaseModel):
    start_time: Optional[str] = Field(default="")
    end_time: Optional[str] = Field(default="")
    device_type: Optional[str] = Field(default="")
    device_id: Optional[str] = Field(default="")
    device_ip: Optional[str] = Field(default="")
    endpoint: Optional[str] = Field(default="")
    status_code: Optional[int] = Field(default=0)
    message: Optional[str] = Field(default="")
    sort_field: str = Field('time', max_length=32, description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: Optional[int] = Field(default=1)
    per_page: Optional[int] = Field(default=10)

    @validator('start_time')
    def start_time_validator(cls, v, values, **kwargs):
        if v.isdigit():
            return v
        else:
            raise ValueError(f'The field start_time must be a number, given: {v}.')

    @validator('end_time')
    def end_time_validator(cls, v, values, **kwargs):
        if v.isdigit():
            start_time = values.get('start_time', '')
            if start_time != "" and int(v) < int(start_time):
                raise ValueError(f'end_time must be greater than or equal to start_time, given start_time: {int(start_time)}, end_time: {v}.')
            else:
                return v
        else:
            raise ValueError(f'The field end_time must be a number, given: {v}.')

# class LogSettingConfigServerModel(BaseModel):
#     name: str = Field(..., description="name")
#     type: int = Field(..., description="type")
#     status: int = Field(..., description="status, 0: disable, 1: enable")
#     host: str = Field(..., description="host")
#     port: conint(ge=1, le=65535) = Field(..., description="port")
#     protocol: Literal["TCP", "UDP"] = Field(..., description="protocol")
#     level: Literal["0", "1", "2", "3", "4", "5", "6", "7"] = Field(default="7", description="The level of this log. 0: emergency, 1: alert,  2: critical, 3: error, 4: warning, 5: notice, 6: information, 7: debug")
#     class Config:
#         extra = "forbid"

# class LogSettingConfigModel(BaseModel):
#     level: Literal["0", "1", "2", "3", "4", "5", "6", "7"] = Field(default="7", description="The level of this log. 0: emergency, 1: alert,  2: critical, 3: error, 4: warning, 5: notice, 6: information, 7: debug")
#     saving_days: conint(ge=1, le=28) = Field(..., description="log saving days")
#     select_type: conint(ge=0, le=1) = Field(..., description="select type, 0: All, 1: Custom")
#     types: Dict = Field(..., description="types")
#     # servers: List[LogSettingConfigServerModel] = Field(None, description="servers")
#     class Config:
#         extra = "forbid"
#
# class SetLogSettingModel(BaseModel):
#     name: str = Field(..., description="name")
#     config: LogSettingConfigModel = Field(..., description="config")
#
#     # Extra inputs are not permitted
#     class Config:
#         extra = "forbid"


class SystemLogConfig(BaseModel):
    level: str = Field(..., description="Log level")
    saving_days: int = Field(..., description="Days to keep logs")
    select_type: int = Field(..., description="Type selection mode")
    types: dict = Field(..., description="Log types mapping")

    class Config:
        extra = "forbid"

class DSIncidentLogConfig(BaseModel):
    saving_days: int = Field(..., description="Days to keep data scanning incident logs")

    class Config:
        extra = "forbid"

class HTTP2ServerConfig(BaseModel):
    saving_days: int = Field(..., description="Days to keep http2 service logs")

    class Config:
        extra = "forbid"

class FWIncidentLogConfig(BaseModel):
    saving_days: int = Field(..., description="Days to keep firewall incident logs")

    class Config:
        extra = "forbid"

class SetLogSettingModel(BaseModel):
    name: str = Field(..., description="name")
    config: dict = Field(..., description="Configuration settings")

    @validator("name")
    def validate_name(cls, value):
        if value not in {e.value for e in LogSettingName}:
            raise ValueError("Invalid log setting name")
        return value

    @validator("config")
    def validate_config(cls, value, values):
        if "name" in values:
            name = values["name"]
            if name == LogSettingName.SYSTEM_LOG.value:
                return SystemLogConfig(**value).dict()
            elif name == LogSettingName.DS_INCIDENT.value:
                return DSIncidentLogConfig(**value).dict()
            elif name == LogSettingName.HTTP2_SERVICE.value:
                return HTTP2ServerConfig(**value).dict()
            # elif name == LogSettingName.FW_INCIDENT.value:
            #     return FWIncidentLogConfig(**value).dict()
        raise ValueError("Invalid configuration for log setting")

    class Config:
        extra = "forbid"

class LogSettingQueryReqModel(BaseModel):
    name: Literal["system", "incident_data_scanning", "incident_firewall", "http2_service"] = Field(..., description="Log setting name")

