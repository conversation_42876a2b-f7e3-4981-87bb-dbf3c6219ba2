import uuid
from util.common_log import get_logger
from pydantic import BaseModel, validator
from pydantic import Field, constr, conint, confloat
from typing import List, Optional, Literal, Union


class DlpEventsParams(BaseModel):
    start_time: Optional[str] = Field(default="", alias='StartTime')
    end_time: Optional[str] = Field(default="", alias='EndTime')
    device_type: Optional[str] = Field(default="", alias='DetectDeviceType')
    device_ip: Optional[str] = Field(default="", alias='DetectDeviceIP')
    scan_policy_name: Optional[str] = Field(default="", alias='ScanPolicyName')
    policy_name: Optional[str] = Field(default="", alias='PolicyName')
    rule_name: Optional[str] = Field(default="", alias='RuleName')
    app_protocol: Literal['HTTP', 'HTTPS', 'SCP', 'FTP', 'EMAIL'] = Field(default="", alias='ApplicationProtocol')
    severity: Literal['High', 'Medium', 'Low'] = Field(default="", alias='Severity')
    action: Literal['Deny', 'Alert', 'Allow'] = Field(default="", alias='Action')
    sort_field: str = Field('IncidentTime', max_length=32, description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: Optional[int] = Field(default=1)
    per_page: Optional[int] = Field(default=10)

    @validator('start_time')
    def start_time_validator(cls, v, values, **kwargs):
        if v.isdigit():
            return v
        else:
            raise ValueError(f'The field StartTime must be a number, given: {v}.')
        
    @validator('end_time')
    def end_time_validator(cls, v, values, **kwargs):
        if v.isdigit():
            start_time = values.get('start_time', '')
            if start_time != "" and int(v) < int(start_time):
                raise ValueError(f'EndTime must be greater than or equal to StartTime, given StartTime: {int(start_time)}, EndTime: {v}.')
            else:     
                return v
        else:
            raise ValueError(f'The field EndTime must be a number, given: {v}.')


class DlpEventLogParams(BaseModel):
    duration: Optional[int] = Field(default=7, alias='duration')
    end_time: Optional[int] = Field(default=0, alias='end_time')
    type: str = Field(..., alias='type')

