import traceback
from flask import Blueprint, request
from pydantic import ValidationError
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes
from exts import logger
from service import ftnt_device_service
from flask_module.ftnt_device_reqmodel import (
    FtntDeviceCreateReqModel,
    FtntDeviceUpdateReqModel,
    FtntDeviceDeleteReqModel,
    FtntDeviceQueryReqModel
)
from system.system_log import record_event_log, LogLevel, LogType, LogAction
from flask_module import session

ftnt_device = Blueprint('ftnt_device', __name__)


@ftnt_device.route('/', methods=['POST'])
def add_device():
    try:
        data = request.get_json()
        try:
            reqmodel = FtntDeviceCreateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"Creating Fortinet device: {reqmodel.name}")
        result, error = ftnt_device_service.create_ftnt_device(reqmodel.dict(exclude_none=True), logger)

        if error:
            if "already exists" in error:
                return error_response(Ecodes.DUPLICATE_RESOURCE, 409, error)
            return error_response(Ecodes.INTERNAL_ERROR, 500, error)

        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value,
                         message=f"Added Fortinet device {result.get('name', '')}",
                         desc='Add Fortinet device', type=LogType.SYSTEM.value,
                         action=LogAction.CREATE.value)
        notify_device_update()
        return success_response({"ErrorCode": 0, "Data": result}, 201)

    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500, str(e))


@ftnt_device.route('/', methods=['GET'])
def get_device_list():
    try:
        params = {}
        for key in ['id', 'name', 'type', 'description', 'sort_field', 'sort_method', 'page', 'per_page']:
            if key in request.args:
                params[key] = request.args.get(key)

        try:
            reqmodel = FtntDeviceQueryReqModel(**params)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        # If ID is provided, get a specific device
        if reqmodel.id:
            result, error = ftnt_device_service.get_ftnt_device_by_id(reqmodel.id, logger)
            if error:
                if error == "Device not found":
                    return error_response(Ecodes.NOT_FOUND, 404, error)
                return error_response(Ecodes.INTERNAL_ERROR, 500, error)

            return success_response(result, 200)

        # Otherwise, get devices by conditions
        devices, total = ftnt_device_service.get_ftnt_devices_by_conditions(
            reqmodel.dict(exclude_none=True),
            logger
        )

        if devices is None:
            return error_response(Ecodes.INTERNAL_ERROR, 500, "Failed to retrieve devices")

        response = {
            "list": devices,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,
            "total": total,
            "ErrorCode": 0
        }
        return success_response(response, 200)
    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500, str(e))


@ftnt_device.route('/', methods=['PUT'])
def update_device():
    try:
        data = request.get_json()
        device_id = data.get('id')

        if not device_id:
            return error_response(Ecodes.VALIDATION_ERROR, 400, "Device ID is required")

        try:
            # Remove id from data before validation
            update_data = {k: v for k, v in data.items() if k != 'id'}
            reqmodel = FtntDeviceUpdateReqModel(**update_data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"Updating Fortinet device: {device_id}")
        result, error = ftnt_device_service.update_ftnt_device(
            device_id,
            reqmodel.dict(exclude_none=True),
            logger
        )

        if error:
            if error == "Device not found":
                return error_response(Ecodes.NOT_FOUND, 404, error)
            if "already exists" in error:
                return error_response(Ecodes.DUPLICATE_RESOURCE, 409, error)
            return error_response(Ecodes.INTERNAL_ERROR, 500, error)

        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value,
                         message=f"Updated Fortinet device {result.get('name', '')}",
                         desc='Update Fortinet device', type=LogType.SYSTEM.value,
                         action=LogAction.EDIT.value)
        notify_device_update()
        return success_response({"ErrorCode": 0, "Data": result}, 200)

    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500, str(e))


@ftnt_device.route('/', methods=['DELETE'])
def delete_device():
    try:
        data = request.get_json()
        try:
            reqmodel = FtntDeviceDeleteReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"Deleting Fortinet device: {reqmodel.id}")
        result, error = ftnt_device_service.delete_ftnt_device(reqmodel.id, logger)

        if error:
            if error == "Device not found":
                return error_response(Ecodes.NOT_FOUND, 404, error)
            return error_response(Ecodes.INTERNAL_ERROR, 500, error)

        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value,
                         message=f"Deleted Fortinet device {result.get('name', '')}",
                         desc='Delete Fortinet device', type=LogType.SYSTEM.value,
                         action=LogAction.DELETE.value)
        notify_device_update()
        return success_response({"ErrorCode": 0, "Data": result}, 200)
    except Exception as e:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500, str(e))


def notify_device_update():
    from util.redis_stream import RedisStream

    message_dict = {}
    message_dict['msg_type'] = "device_update"

    redis_stream = RedisStream("dlp_http2_config_update")

    if not redis_stream.send_redis_stream_msg(message_dict, maxlen=1000, approximate=True):
        logger.error(f"Failed to send Redis stream msg: {message_dict}")
        return False

    logger.info(f"Send Redis stream msg: {message_dict}")
    return True
