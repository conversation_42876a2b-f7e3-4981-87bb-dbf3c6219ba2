
import traceback
from flask import Blueprint, jsonify, request
from pydantic import ValidationError
from util.common_log import get_logger
from util.err_codes import error_response, success_response
import util.err_codes as Ecodes

analyzer_ml = Blueprint("machine_learning", __name__)
logger = get_logger("api")


@analyzer_ml.route("/index", methods=["GET"])
def index():
    return "This is a schedule index page"

# TODO: Implement analyzer_ml API
# 1. Implement a analyzer_ml API that returns a list of scheduled tasks
# 2. Implement a analyzer_ml API that schedules a task
# 3. Implement a analyzer_ml API that unschedules a task
# 4. Implement a analyzer_ml API that updates a task schedule
# 5. .......

@analyzer_ml.route("get_main_categories", methods=['GET'])
def get_main_categories():
    from service import predefined_datatype_service

    try:
        main_categories = predefined_datatype_service.get_main_categories()
        return success_response(main_categories)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)

@analyzer_ml.route("get_sub_categories", methods=['GET'])
def get_sub_categories():
    from service import predefined_datatype_service
    from .analyzer_ml_reqmodel import CategoryQueryReqModel

    try:
        try:
            args = request.args.to_dict()
            reqmodel = CategoryQueryReqModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)
        logger.info(f"reqmodel: {reqmodel}")
        sub_categories = predefined_datatype_service.get_sub_categories(main_id=reqmodel.main_id)
        return success_response(sub_categories)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)