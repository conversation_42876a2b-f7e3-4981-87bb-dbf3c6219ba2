import uuid
from pydantic import BaseModel, validator, root_validator,conlist
from pydantic import Field, constr, conint, AnyUrl, StrictBool
from typing import List, Optional, Literal, Union, Dict
from util.enum_ import StorageType, FrequencyType, TaskStatus, ScanInterval
from service import predefined_datatype_service
import re

class ScanPolicyDeleteReqModel(BaseModel):
    # required fields
    id: List[constr(min_length=36, max_length=36)]  = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class ScanInitinfoReqModel(BaseModel):
    storage_id: constr(min_length=36, max_length=36) = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="uuid for the storage profile")

class ScanScheduleModel(BaseModel):
    frequency: FrequencyType = Field(
        ...,
        example=1,
        description="Frequency of the scan: 1 (daily), 2 (weekly), 3 (continuously)"
    )
    weekdays: Optional[List[conint(ge=1, le=7)]] = Field(
        None,
        example=[1, 2, 3, 4, 5, 6, 7],
        description="List of weekdays: 1 (Monday) to 7 (Sunday)"
    )
    hour: Optional[conint(ge=0, le=23)] = Field(
        None,
        example=12,
        description="Hour of the day for the scan: 0 to 23"
    )
    interval: Optional[conint(ge=1)] = Field(
        1,
        example=1,
        description="The interval between frequencies"
    )

    @root_validator
    def check_valid_frequency_and_fields(cls, values):
        frequency = values.get('frequency')
        weekdays = values.get('weekdays')
        hour = values.get('hour')

        if frequency == 1:  # daily
            if hour is None:
                raise ValueError('Hour must be specified for daily scans.')
        elif frequency == 2:  # weekly
            if not weekdays or hour is None:
                raise ValueError('Both weekdays and hour must be specified for weekly scans.')

        return values


class FileSizeLimitMdl(BaseModel):
    min: conint(ge=0) = Field(
        ...,
        example=1,
        description="Minimum file size limit in KB"
    )
    max: conint(ge=0, le=51200) = Field(
        ...,
        example=10240,
        description="Maximum file size limit in KB"
    )


class AnalyzeSettingMdl(BaseModel):

    ml_enabled: bool = Field(
        ...,
        example=True,
        description="Indicates whether machine learning is enabled."
    )    
    ml_certainty_level: Literal[1, 2, 3] = Field(2, description="Document classification precision level. 1: High, 2: Medium, 3: Low")
    re_precision_level: Literal[1, 2, 3] = Field(2, description="The data type detection precision level of this rule. 1:High, 2:Medium 3:Low")
    regions: List[Literal["AF", "AS", "EU", "NA", "OC", "SA"]] = Field(
        ...,
        example=["NA", "EU"],
        description="List of region identifiers, allowed values: NA (North America), EU (Europe), SA (South America), AS (Asia), AF (Africa), OC (Oceania)"
    )
    scan_category: List[str] = Field([])

    @validator('scan_category')
    def type_category_validator(cls, value):
        category_info, _ = predefined_datatype_service.get_supported_categories()
        type_categories = []
        for c in category_info["type_category"]:
            if "id" in c:
                type_categories.append(c["id"])
        for v in value:
            if v not in type_categories:
                raise ValueError(f"Invalid type category: {v}")
        return value

class ProtectionProfilesModel(BaseModel):
    copy_profiles: List[str] = Field(
    None,
    example=['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
    description="List of UUIDs for copy profiles"
    )

    quarantine_profiles: List[str] = Field(
    None,
    example=['650e8400-e29b-41d4-a716-************', '650e8400-e29b-41d4-a716-************'],
    description="List of UUIDs for quarantine profiles"
    )


class ScanPolicyCreateReqModel(BaseModel):
    name: str = Field(..., min_length=1, max_length=512, example="policy1", description="scan polichy name")
    description: str = Field("", min_length=0, max_length=256, example="This is a discover rule")
    scan_scope: int = Field(1, description="0 any, 1 included")
    scan_folders: conlist(str) = Field(..., example=["folder1", "folder2"], description="list of folders to scan")
    excluded_scan_folders: conlist(str) = Field([], example=["folder1", "folder2"], description="list of exclude folders")
    storage_type: StorageType = Field(..., description="1 : AWS S3, 2 : SharePoint(On Line) 3 : SharePoint(ON-Prem) 4:SMB")
    scan_init_info:ScanInitinfoReqModel = Field(..., description="Connection configuration of various storage systems")
    status: Optional[TaskStatus] = Field(0, description="0 : idling, 1 : Fetching, 2 : Scanning,3:Stopping ,4: Suspending,5: Preparing")
    scan_schedule: ScanScheduleModel = Field(..., description="Scan task execution")
    file_size_limit: FileSizeLimitMdl = Field(..., description="Specify the minimum and maximum file sizes")
    scan_interval: ScanInterval = Field(...,example=1,description="Scan interval: 1 (short 10ms), 2 (long 20ms), 3 (none)")
    scan_file_type: List[Literal['office documents', 'pictures', 'text files', 'source code', 'configuration files', 'certificate files']] = Field(
        ...,
        example=['office documents', 'pictures'],
        description="Types of scan files: 'office documents', 'pictures', 'text files', 'source code', 'configuration files', 'certificate files'"
    )
    analyze_setting: AnalyzeSettingMdl = Field(..., description="File analysis configurations")
    discover_policy: List[str] = Field(
        ...,
        example=['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
        description="List of UUIDs for discover policies"
    )

    custom_datatype_group: Optional[str] = Field(
        None,
        example='550e8400-e29b-41d4-a716-************',
        description="UUID of the custom datatype group, can be empty"
    )
    tag_option: Optional[list] = Field(None, description="Cloud or Local")
    protection_profiles: Optional[ProtectionProfilesModel] = Field(None, description="Specify the protection profiles")
    file_exclusion: Optional[List[str]] = Field(None, description="Paths to skip during the scan")

    class Config:
        extra = "forbid"

    @root_validator(pre=True)
    def rewrite_analyze_setting(cls, values):
        discover_policy = values.get('discover_policy', None)
        analyze_setting = values.get('analyze_setting', {}).copy()
        if not discover_policy:
            raise ValueError("In advanced mode, at least one policy must be selected.")
        analyze_setting['regions'] = ["AF", "AS", "EU", "NA", "OC", "SA"]
        analyze_setting['scan_category'] = []
        values['analyze_setting'] = analyze_setting
        return values


class ScanPolicyUpdateReqModel(BaseModel):
    # required fields
    id: str = Field(..., min_length=36, max_length=36, example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="valid UUID format string")

    # optional fields
    description: str = Field("", min_length=0, max_length=256, example="This is a discover rule")
    scan_scope: int = Field(1, description="0 any, 1 included, 2 excluded")
    scan_folders: conlist(str) = Field(..., example=["folder1", "folder2"], description="list of folders to scan")
    excluded_scan_folders: conlist(str) = Field(..., example=["folder1", "folder2"], description="list of exclude folders")
    scan_init_info:ScanInitinfoReqModel = Field(None, description="Connection configuration of various storage systems")
    scan_schedule: ScanScheduleModel = Field(None, description="Scan task execution")
    file_size_limit: FileSizeLimitMdl = Field(None, description="Specify the minimum and maximum file sizes")
    scan_interval: ScanInterval = Field(None,example=1,description="Scan interval: 1 (short 10ms), 2 (long 20ms), 3 (none)")
    scan_file_type: List[Literal['office documents', 'pictures', 'text files', 'source code', 'configuration files', 'certificate files']] = Field(
        None,
        example=['office documents', 'pictures'],
        description="Types of scan files: 'office documents', 'pictures', 'text files', 'source code', 'configuration files', 'certificate files'"
    )
    analyze_setting: AnalyzeSettingMdl = Field(None, description="File analysis configurations")
    discover_policy: List[str] = Field(
        None,
        example=['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
        description="List of UUIDs for discover policies"
    )

    custom_datatype_group: Optional[str] = Field(
        None,
        example='550e8400-e29b-41d4-a716-************',
        description="UUID of the custom datatype group, can be empty"
    )

    storage_type: StorageType = Field(None, description="1 : AWS S3, 2 : SharePoint(On Line) 3 : SharePoint(ON-Prem) 4:SMB")
    tag_option: Optional[list] = Field(None, description="Cloud or Local")
    protection_profiles: Optional[ProtectionProfilesModel] = Field(None, description="Specify the protection profiles")
    file_exclusion: Optional[List[str]] = Field(None, description="Paths to skip during the scan")

    class Config:
        extra = "forbid"


    @root_validator(pre=True)
    def rewrite_analyze_setting(cls, values):
        discover_policy = values.get('discover_policy', None)
        if discover_policy and len(discover_policy) == 0:
            raise ValueError("In advanced mode, at least one policy must be selected.")
        analyze_setting = values.get('analyze_setting', {}).copy()
        if analyze_setting:
            analyze_setting['regions'] = ["AF", "AS", "EU", "NA", "OC", "SA"]
            analyze_setting['scan_category'] = []
            values['analyze_setting'] = analyze_setting
        return values


class ScanPolicyQueryReqModel(BaseModel):
    id: constr(min_length=36, max_length=36)  = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    name: str = Field(None, min_length=1, max_length=512, example="Policy1", description="discover policy name")
    description: str = Field(None, min_length=0, max_length=512, example="This is a discover policy")
    status: TaskStatus = Field(None, description="0 : idling, 1 : Fetching, 2 : Scanning, 3 : Stopping, 4 : Suspending, 5 : Preparing")
    storage_type: StorageType = Field(None, description="1 : AWS S3, 2 : SharePoint(On Line) 3 : SharePoint(ON-Prem) 4:SMB")

    sort_field: str = Field('updated_at', max_length=32, description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: conint(ge=0) = Field(None, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class ScanPolicyCustomDatatypeCheckRefReqModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(...,
                                                     example="916aeae8-3b10-4b44-b597-b9eca337fbaf",
                                                     description="valid UUID format string")

    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class ScanPolicyDiscoverPolicyCheckRefReqModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(...,
                                                     example="916aeae8-3b10-4b44-b597-b9eca337fbaf",
                                                     description="valid UUID format string")

    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class ScanPolicyQueryHistoryReqModel(BaseModel):
    scan_policy_id: constr(min_length=36, max_length=36)  = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid UUID format string")
    limit: conint(ge=0, le=1024) = Field(10, description="The number of history records wish to show")

    class Config:
        extra = "forbid"

    @validator('scan_policy_id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class ListFoldersQueryReqModel(BaseModel):

    #storage_type: Literal[1,2,3,4,6] = Field(..., description="1 : AWS S3, 2 : SharePoint(On Line) 3 : SharePoint(ON-Prem) 4:SMB")
    storage_type: conint(ge=1, le=6) = Field(..., description="1 : AWS S3, 2 : SharePoint(On Line) 3 : SharePoint(ON-Prem) 4:SMB 6:Google")
    storage_id: constr(min_length=36, max_length=36) = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="uuid for the storage profile")
    page: conint(ge=0) = Field(None, description="page")
    per_page: conint(ge=0, le=1024) = Field(None, description="page size")


class ListFoldersViewQueryReqModel(BaseModel):
    scan_policy_id: constr(min_length=36, max_length=36) = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid id of scan policy")
    page: conint(ge=0) = Field(None, description="page")
    per_page: conint(ge=0, le=1024) = Field(None, description="page size")

    class Config:
        extra = "forbid"

    @validator('scan_policy_id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class OnlyIDReqModel(BaseModel):
    class Config:
        extra = "forbid"

    id: constr(min_length=36, max_length=36) = Field(..., description="Scan Policy ID")

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class ScanSensitiveDataQueryReqModel(BaseModel):
    file_id: Optional[uuid.UUID] = Field(None, description="File UUID")
    scan_policy_id: Optional[uuid.UUID] = Field(None, example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid id of scan policy")
    storage_id: Optional[uuid.UUID] = Field(None, description="Storage UUID")
    filename: str = Field(None, min_length=1, max_length=512, example="output.js or /tmp/index.js", description="filename")
    folder_path: str = Field(None, min_length=1, max_length=1024, example="/sites/DLP/Shared Documents/kiddtestdata/filetype or folder1", description="path of a folder")
    main_class_id: Optional[List[str]] = Field(None, example=["20000"], description="main class id")
    sub_class_id: Optional[List[str]] = Field(None, example=["20001"], description="sub class id")
    categoryid: Optional[str] = Field(None, example="10001", description="category id")
    dtype_id: Optional[List[str]] = Field(None, example=['1'], description="Standard data type id.")
    custom_dtype_id: Optional[List[str]] = Field(None, example=["c4367db8-953b-494e-aaf0-f08baf5f1da9"], description="Custom data type id.")
    custom_label: Optional[List[str]] = Field(None, example=[""], description="custom data labels")
    predefine_label: Optional[List[str]] = Field(None, example=["EU"], description="predefine data labels")
    labeled: Optional[bool] = Field(None, example=False, description="whether labeled files need to be fetched")
    sensitive: Optional[bool] = Field(None, description="Sensitive files")
    compliance: Optional[bool] = Field(None, description="Compliance files")
    storage_type: Optional[StorageType] = Field(None, description="1: AWS S3, 2: SharePoint(On Line), 3: SharePoint(ON-Prem), 4: SMB")
    scan_start: int = Field(0, description="scan time range start")
    scan_end: Optional[int] = Field(None, description="scan time range end")
    quarantine_status:int = Field(None, example=1, description="status of file quarantine.")
    copy_status: int = Field(None, example=1, description="status of file copy.")
    file_hash: str = Field(None, min_length=1, max_length=512, description="file hash")
    edm_matched: Optional[bool] = Field(None, description="edm_rule_matched")
    idm_matched: Optional[bool] = Field(None, description="idm_template_matched")
    owner: Optional[List[str]] = Field(None, description="file owner identifier(s)")
    access: Optional[List[str]] = Field(None, description="file collaborator identifier(s)")
    shared: Optional[List[Literal['any', 'pub_link', 'ext_link', 'int_link', 'ext_coll', 'int_coll']]] = Field(None,
            description="Shared. In any form, w/ public link, external link, internal link, external collaborators, internal collaborators")

    sort_field: str = Field('update_time', max_length=32, description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: conint(ge=0) = Field(None, description="page")
    per_page: conint(ge=0, le=1024) = Field(None, description="page size")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @root_validator(pre=True)
    def unwrap_lists(cls, values):
        for k in values:
            if k in {'main_class_id', 'sub_class_id',
                     'dtype_id', 'custom_dtype_id',
                     'predefine_label', 'custom_label',
                     'owner', 'access', 'shared'}:
                continue
            values[k] = values[k][0]
        return values


class ScanSensitiveDataDetailsQueryReqModel(BaseModel):
    scan_policy_id: constr(min_length=36, max_length=36) = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid id of scan policy")
    file_uuid: constr(min_length=36, max_length=36) = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid uuid of the file")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('scan_policy_id', 'file_uuid')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class ScanSensitiveDataSummaryQueryReqModel(BaseModel):
    scan_policy_id: constr(min_length=36, max_length=36) = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid id of scan policy")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('scan_policy_id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class ScanSensitiveFolderDetailsQueryReqModel(BaseModel):
    scan_policy_id: constr(min_length=36, max_length=36) = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid id of scan policy")
    folder_path: str = Field(..., min_length=1, max_length=1024, example="/sites/DLP/Shared Documents/kiddtestdata/filetype/", description="path of a folder")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('scan_policy_id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class LabelsModel(BaseModel):
    predefine: List[str] = Field(..., description="predefine tag")
    custom: List[str] = Field(..., description="custom tag")
    ml: List[str] = Field(..., description="ml tag")

    class Config:
        extra = "forbid"


class ScanSensitiveFolderDetailsUpdateReqModel(BaseModel):
    scan_policy_id: constr(min_length=36, max_length=36) = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid id of scan policy")
    folder_path: str = Field(..., min_length=1, max_length=1024, example="/sites/DLP/Shared Documents/kiddtestdata/filetype/", description="path of a folder")
    labels: LabelsModel = Field(..., description="")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('scan_policy_id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v


class ScanSensitiveDataDetailsUpdateReqModel(BaseModel):
    scan_policy_id: constr(min_length=36, max_length=36) = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid id of scan policy")
    file_uuid: constr(min_length=36, max_length=36) = Field(..., example="916aeae8-3b10-4b44-b597-b9eca337fbaf", description="valid uuid of the file")
    labels: LabelsModel = Field(..., description="")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('scan_policy_id', 'file_uuid')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class H2DiscoverPolicyCheckRefReqModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(...,
                                                     example="916aeae8-3b10-4b44-b597-b9eca337fbaf",
                                                     description="valid UUID format string")

    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class H2ScanIdsCheckRefReqModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(...,
                                                     example="916aeae8-3b10-4b44-b597-b9eca337fbaf",
                                                     description="valid UUID format string")

    class Config:
        extra = "forbid"

    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
