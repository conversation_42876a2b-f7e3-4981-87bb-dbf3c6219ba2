from util.common_log import get_logger
from pydantic import BaseModel, validator 
from pydantic import Field, constr, conint, confloat
from typing import List, Optional, Literal, Union
import uuid
import re

logger = get_logger("api")

class DefinitionModel(BaseModel):
    class KeywordModel(BaseModel):
        keyword:str = Field(...)

    class PatternModel(BaseModel):
        regex: str = Field(...)

        @validator('regex')
        def is_valid_regex(cls, v):
            try:
                pattern = re.compile(v)
                if pattern.match(''):
                    raise ValueError(f"The pattern matches an empty string. " 
                                     "Please modify it to match at least one character.")
            except re.error:
                raise ValueError(f"Invalid regex {v}")
            return v
        
    keywords: List[KeywordModel] = Field([])
    patterns: List[PatternModel] = Field(...)
    class Config:
        extra = "forbid"

class StandardDtypeCondition(BaseModel):
    # optional fields
    id: List[str] = Field(None, description="entity id")
    data_type: str = Field(None, max_length=100, description="datatype name")
    region: List[str] = Field(None, examples=["US,AU"], description="region list")
    continent: List[str] = Field(None, examples=["SA,NA"], description="continent list")
    language: List[str] = Field(None, examples=["en"], description="language list")
    type_category: List[str] = Field(None, examples=["10001,10002"], description="type category id list")

    sort_field: str = Field('data_type', max_length=32, description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('asc', description="sort method")
    page: conint(ge=0) = Field(1, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id','region','continent','language','type_category', pre=True)
    def split_tags(cls, value):
        if isinstance(value, str):
            return value.split(',')
        return value
    
class CustomDtypeGroupQueryModel(BaseModel):
    sort_field: str = Field('created_at', max_length=32, description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: conint(ge=0) = Field(1, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")

    id: constr(min_length=36, max_length=36)  = Field(None, example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="custom datatype group name")
    description: Optional[str] = Field(None, min_length=1, max_length=100, description="custom datatype group note")
    class Config:
        extra = "forbid"
    
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class CustomDtypeGroupCreateModel(BaseModel):
    # required fields
    name: str = Field(..., min_length=1, max_length=100, description="custom datatype group name")
    # optional fields
    description: str = Field("", min_length=0, max_length=256, description="description")

    class Config:
        extra = "forbid"

class CustomDtypeGroupUpdateModel(BaseModel):
    # required fields
    id: constr(min_length=36, max_length=36) = Field(..., example='916aeae8-3b10-4b44-b597-b9eca337fbaf', description="valid UUID format string")
    
    # optional fields
    name: str = Field(None, min_length=1, max_length=100, example="Group-01", description="custom dtype group name")
    description: str = Field(None, min_length=0, max_length=256, example="This is a custom dtype group")

    class Config:
        extra = "forbid"
    
    @validator('id')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class CustomDtypeGroupDeleteModel(BaseModel):
    # required fields
    id: List[constr(min_length=36, max_length=36)]  = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class CustomDtypeQueryModel(BaseModel):
    id: constr(min_length=36, max_length=36) = Field(None, example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")
    name: str = Field(None, min_length=1, max_length=100, example="Custom-dtype-01", description="custom datatype name")
    group_uuid: constr(min_length=36, max_length=36) = Field(None, example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")
    group_name: str = Field(None, min_length=1, max_length=512, example="custom-1", description="custom dtype name")
    description: Optional[str] = Field(None, min_length=1, max_length=100, description="custom datatype note")

    sort_field: str = Field('updated_at', max_length=32, description="sort field name")
    sort_method: Literal['desc', 'asc'] = Field('desc', description="sort method")
    page: conint(ge=0) = Field(1, description="page")
    per_page: conint(ge=0, le=1024) = Field(10, description="page size")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', 'group_uuid')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class CustomDtypeCreateModel(BaseModel):
    # required fields
    name: str = Field(..., min_length=1, max_length=100, example="custom-dtype-01", description="custom datatype name")
    group_uuid: constr(min_length=36, max_length=36) = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")
    definition: DefinitionModel = Field(...)
    # optional fields
    description: str = Field("", min_length=0, max_length=256, example="This is a custom datatype")
    
    # Extra inputs are not permitted
    class Config:
        extra = "forbid"
    
    @validator('group_uuid')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v

class CustomDtypeUpdateModel(BaseModel):
    id: constr(min_length=36, max_length=36)  = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")
    
    group_uuid: constr(min_length=36, max_length=36) = Field(None, example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")
    definition: DefinitionModel = Field(None)
    description: str = Field("", min_length=0, max_length=256, example="This is a custom datatype")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id','group_uuid')
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v
    
class CustomDtypeDeleteModel(BaseModel):
    # required fields
    id: List[constr(min_length=36, max_length=36)]  = Field(..., example=["916aeae8-3b10-4b44-b597-b9eca337fbaf"], description="valid UUID format string")

    # Extra inputs are not permitted
    class Config:
        extra = "forbid"

    @validator('id', each_item=True)
    def uuid_validator(cls, v):
        try:
            uuid.UUID(v)
        except ValueError as e:
            raise ValueError(f"Invalid uuid {v}")
        return v