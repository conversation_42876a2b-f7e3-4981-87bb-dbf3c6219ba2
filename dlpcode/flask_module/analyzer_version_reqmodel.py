from util.common_log import get_logger
from pydantic import BaseModel, root_validator
from pydantic import Field

from service import predefined_datatype_service

logger = get_logger("api")

class VersionModel(BaseModel):
    version: str = Field(..., examples="v1.0.0", description="version")
    name: str = Field(..., examples="classification_model", description="config name")
    
    @root_validator(pre=True)
    def inject_storage_type(cls, values):
        return predefined_datatype_service.inject_storage_type(values)
    
class PackageUpdateReqModel(BaseModel):
    use_latest: bool = Field(True, description="use latest version")
    