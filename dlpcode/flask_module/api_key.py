import secrets
import string
import traceback

from flask import Blueprint, request
from pydantic import ValidationError
from sqlalchemy.exc import IntegrityError

import util.err_codes as Ecodes
from flask_module import session
from util.err_codes import error_response, success_response
from system.system_log import record_event_log, LogLevel, LogType, LogAction
from util.common_log import get_logger

logger = get_logger("api")

api_key = Blueprint('api_key', __name__)


def generate_api_key(prefix="sk", length=32):
    """Generate a secure API key with the given prefix and length."""
    alphabet = string.ascii_letters + string.digits
    random_part = ''.join(secrets.choice(alphabet) for _ in range(length))
    return f"{prefix}_{random_part}"


@api_key.route('/', methods=['POST'])
def add_one():
    from flask_module.api_key_reqmodel import ApiKeyCreateReqModel
    from service import api_key_service

    try:
        data = request.get_json()
        try:
            reqmodel = ApiKeyCreateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        # Generate a secure API key
        api_key_value = generate_api_key()

        # Add the generated key to the model data
        model_data = reqmodel.dict(exclude_none=True)
        model_data['key'] = api_key_value

        logger.info(f"Creating API key: {reqmodel.name}")
        new_api_key = api_key_service.create_api_key(model_data, logger)

        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value,
                         message=f"Added API key {new_api_key.get('name', '')}",
                         desc='Add API key', type=LogType.SYSTEM.value,
                         action=LogAction.CREATE.value)
        notify_api_key_update()

        return success_response({"ErrorCode": 0, "Data": new_api_key}, 201)

    except IntegrityError:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.DUPLICATE_RESOURCE, 409, "An API key with this name already exists.")
    except Exception:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@api_key.route('/', methods=['GET'])
def get_list():
    from flask_module.api_key_reqmodel import ApiKeyQueryReqModel
    from service import api_key_service

    try:
        conditions = request.args.to_dict()
        try:
            reqmodel = ApiKeyQueryReqModel(**conditions)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"Query parameters: {reqmodel}")
        results, total = api_key_service.get_api_keys_by_conditions(reqmodel.dict(exclude_none=True), logger)
        resp = {
            "ErrorCode": 0,
            "list": results,
            "total": total,
            "page": reqmodel.page,
            "per_page": reqmodel.per_page,

        }
        return success_response(resp, 200)
    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@api_key.route('/', methods=['PUT'])
def update_one():
    from flask_module.api_key_reqmodel import ApiKeyUpdateReqModel
    from service import api_key_service

    try:
        data = request.get_json()
        try:
            reqmodel = ApiKeyUpdateReqModel(**data)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        model_data = reqmodel.dict(exclude={'id'}, exclude_none=True)

        if 'attributes' in model_data and model_data['attributes']:
            if 'description' in model_data['attributes']:
                description = model_data['attributes'].get('description')
                model_data['attributes'] = {'description': description}
            else:
                model_data.pop('attributes')

        logger.info(f"Updating API key: {reqmodel.id}")
        updated_api_key = api_key_service.update_api_key(reqmodel.id, model_data, logger)

        if not updated_api_key:
            return error_response(Ecodes.NOT_FOUND, 404, f"API key with ID {reqmodel.id} not found")

        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value,
                         message=f"Updated API key {updated_api_key.get('name', '')}",
                         desc='Update API key', type=LogType.SYSTEM.value,
                         action=LogAction.EDIT.value)
        notify_api_key_update()

        return success_response({"ErrorCode": 0, "Data": updated_api_key}, 200)

    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


@api_key.route('/', methods=['DELETE'])
def delete_multi():
    from flask_module.api_key_reqmodel import ApiKeyDeleteReqModel
    from service import api_key_service

    try:
        args = request.args.to_dict(flat=False)
        try:
            reqmodel = ApiKeyDeleteReqModel(**args)
        except ValidationError as e:
            return error_response(Ecodes.VALIDATION_ERROR, 400, e)

        logger.info(f"Deleting API keys: {reqmodel.id}")
        success, failed, deleted_names = api_key_service.delete_api_keys(reqmodel.id, logger)

        user = session.get_user(request)
        record_event_log(user=user, level=LogLevel.INFO.value,
                         message=f"Deleted API keys: {', '.join(deleted_names)}",
                         desc='Delete API key', type=LogType.SYSTEM.value,
                         action=LogAction.DELETE.value)
        notify_api_key_update()
        return success_response({"ErrorCode": 0, "Data": {'deleted_ids': success, 'failed_ids': failed}}, 200)

    except:
        logger.exception(traceback.format_exc())
        return error_response(Ecodes.INTERNAL_ERROR, 500)


def notify_api_key_update():
    from util.redis_stream import RedisStream

    message_dict = {}
    message_dict['msg_type'] = "api_key_update"

    redis_stream = RedisStream("dlp_http2_config_update")

    if not redis_stream.send_redis_stream_msg(message_dict, maxlen=1000, approximate=True):
        logger.error(f"Failed to send Redis stream msg: {message_dict}")
        return False

    logger.info(f"Send Redis stream msg: {message_dict}")
    return True
