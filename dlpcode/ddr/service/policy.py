import uuid
import traceback
from sqlalchemy import and_, func
from exts import Session
from ddr.model.policy import DDRPolicy
from ddr.model.task import DDRTask
from typing import List
from util.common_log import get_logger

logger = get_logger("ddr")


def get_ddr_task_ref(id:str) -> list:
    try:
        with Session() as session:
            records = session.query(DDRTask).filter(DDRTask.ddr_policy_ids.contains([id])).all()
            return [record.name for record in records]
    except Exception as e:
        logger.error(e)
        return None


def get_trigger_events_by_policy_ids(policy_ids: List[str]) -> List[str]:
    """
    depend on policy_ids search DDRPolicy, return all event_type
    Args:
        policy_ids: policy_id list
    Returns:
        return event_type list without duplicate
    """
    if not policy_ids:
        return []
    try:
        with Session() as session:
            policies = session.query(DDRPolicy).filter(DDRPolicy.id.in_(policy_ids)).all()

            unique_event_types = list(
                {policy.event_type for policy in policies if policy.event_type is not None}
            )
            return unique_event_types
    except Exception as e:
        logger.error(f"Error fetching event_types for policy_ids {policy_ids}: {e}")
        return []