from util.enum_ import Apply<PERSON>abelMode, ConditionRelationType
from util.common_log import get_logger
from readerwriterlock import rwlock
from service.data_classifier_engine_service import DataClassifierEngine
from service.data_label_service import LabelFetcher
from ddr.model.policy import get_ddr_policy

logger = get_logger("ddr")

class DDRMatchCondition:
    def __init__(self, **kwargs):
        self.attributes = kwargs

    def match(self, record):
        condition_type = self.attributes.get('type')
        if condition_type in ["collaborator", "share_link"]:
            value = self.attributes.get('value', [])
            return len(value) == 0 or record.get(condition_type) in value
        else:
            return False


class DDRPolicyMatchEngine:
    def __init__(self, policy_id:str):
        self.conditions = {}
        policy = get_ddr_policy(id=policy_id)
        self.action = policy.action
        self.risk = policy.risk
        self.update_time = policy.updated_at
        self.id = policy.id
        self.name = policy.name
        self.protection_framework = policy.protection_framework
        self.match_condition_relation = policy.match_condition_relation
        self.data_classifier_engines = []
        for id in list(policy.data_classifier_ids):
            classifier_engine = DataClassifierEngine(id)
            if classifier_engine:
                self.data_classifier_engines.append(classifier_engine)
        if not policy.enabled:
            logger.info(f"DDR policy {policy_id} is disabled, skip matching.")
            return
        for id, condition in policy.match_condition.items():
            self.conditions[id] = DDRMatchCondition(**condition)
    
    def evaluate_logic(self, expression, record):
        stack = []
        tokens = expression.split()
    
        for token in tokens:
            if token == 'AND':
                a = stack.pop()
                b = stack.pop()
                stack.append(a and b)
            elif token == 'OR':
                a = stack.pop()
                b = stack.pop()
                stack.append(a or b)
            elif token == 'NOT':
                a = stack.pop()
                stack.append(not a)
            else:
                if token in self.conditions:
                    condition = self.conditions[token]
                    condition_result = condition.match(record)
                    # logger.debug(f'id: {token}, condition: {condition.attributes}, match result: {condition_result}')
                    stack.append(condition_result)
                else:
                    logger.error(f"EvaluateLogic condition id: {token} can not find.")

        return stack.pop()
        
    def infix_to_postfix(self, expression):
        output = []
        operators = []
        tokens = expression.split()
    
        # lower number means lower precedence
        precedence = {
            'NOT': 3,
            'AND': 2,
            'OR': 1
        }
        
        associativity = {
            'NOT': 'Right',
            'AND': 'Left',
            'OR': 'Left'
        }

        for token in tokens:
            #print("token:", token)
            if token in precedence:
                while (operators and operators[-1] != '(' and
                    (precedence[operators[-1]] > precedence[token] or
                    (precedence[operators[-1]] == precedence[token] and associativity[token] == 'Left'))):
                    output.append(operators.pop())
                operators.append(token)
            elif token == '(':
                operators.append(token)
            elif token == ')':
                while operators and operators[-1] != '(':
                    output.append(operators.pop())
                operators.pop()  # Pop '('
            else:
                # Assuming token is a variable or literal
                output.append(token)
    
        # Pop all the operators left in the stack
        while operators:
            output.append(operators.pop())
    
        return ' '.join(output)
    
    def match_condition(self, record):
        if self.match_condition_relation['type'] == ConditionRelationType.ALL.value:
            for condition in self.conditions.values():
                if not condition.match(record):
                    return False
            return True
        elif self.match_condition_relation['type'] == ConditionRelationType.ONE.value:
            for condition in self.conditions.values():
                if condition.match(record):
                    return True
            return False
        elif self.match_condition_relation['type'] == ConditionRelationType.CUSTOM.value:
            custom_relations = self.match_condition_relation['custom_relations']   
            custom_relations_postfix = self.infix_to_postfix(custom_relations)
            result = self.evaluate_logic(custom_relations_postfix, record)
            # logger.debug(f'custom_relations: {custom_relations}, custom_relations_postfix: {custom_relations_postfix}, result: {result}')
            return result
        else:
            logger.error(f"ConditionRelationType {self.match_condition_relation['type']} does not support.")
            return False

    def match(self, record):
        final_sensitivity = -1
        matched_result = False
        for classifier_engine in self.data_classifier_engines:
            matched, sensitivity = classifier_engine.match(record)
            matched_result = matched or matched_result # matched_result is True if any classifier_engine matches
            final_sensitivity = max(sensitivity, final_sensitivity)
        if not matched_result:
            return False, -1
        if not self.match_condition(record):
            return False, -1
        return True, final_sensitivity

    def update_needed(self) -> bool:
        policy = get_ddr_policy(id=self.id)
        if policy.enabled and policy.updated_at > self.update_time:
            return True
        return False

class DDRPolicyEngine:
    def __init__(self):
        self.policies = dict()
        self._lock = rwlock.RWLockFairD()
        self._lock_reader = self._lock.gen_rlock()
        self._lock_writer = self._lock.gen_wlock()
    
    def get_ids(self) -> list:
        self._lock_reader.acquire()
        try:
            return self.policies.keys()
        finally:
            self._lock_reader.release()
    
    def update_needed(self, discovery_ids:list):
        if set(self.get_ids()) != set(discovery_ids):
            return True
        
        self._lock_reader.acquire()
        try:
            for _, policy in self.policies.items():
                if policy.update_needed():
                    return True
            return False
        finally:
            self._lock_reader.release()

    def create_policy(self, id):
        self._lock_writer.acquire()
        try:
            self.policies[id] = DDRPolicyMatchEngine(policy_id=id)
        finally:
            self._lock_writer.release()
    
    def remove_policy(self, id):
        self._lock_writer.acquire()
        try:
            if id in self.policies.keys():
                del self.policies[id]
        finally:
            self._lock_writer.release()
    
    def match_all(self, record:dict):
        def get_labels(label_action, result:dict, protection_framework:list) -> dict:
            label_content = {
                "predefine": [],
                "custom": LabelFetcher.get_custom_label(label_action.get("labels", {}).get("custom",[]))
            }
            ml_label_mode = label_action.get("ml_label_mode", 0)
            protection_framework_label_mode = label_action.get("protection_framework_label_mode", 0)

            if ml_label_mode == ApplyLabelMode.ENABLE.value:
                label_content["predefine"].extend(LabelFetcher.get_data_classification_label(result))

            if protection_framework_label_mode == ApplyLabelMode.ENABLE.value:
                label_content["predefine"].extend(LabelFetcher.get_protection_framework_label(protection_framework))
            
            return label_content
        
        self._lock_writer.acquire()
        
        match_result = {"policies":[]}
        try:
            remediation_actions = set()
            custom_label_set = set()
            predefined_label_set = set()
            sensitivity_level = -1
            for id, policy in self.policies.items():
                matched, sensitivity = policy.match(record)
                if matched:
                    sensitivity_level = max(sensitivity, sensitivity_level)
                    labels = get_labels(policy.action.get("label", {}), record.get("result", {}), policy.protection_framework)
                    predefined_label_set.update(labels["predefine"])
                    custom_label_set.update(labels["custom"])
                    action_list = []
                    for k, v in policy.action.get("remediation", {}).items():
                        if v:
                            action_list.append(k)
                            remediation_actions.add(k)
                    match_result["policies"].append({
                        "pid": id,
                        "pname": policy.name,
                        "risk": policy.risk,
                        "tags": labels,
                        "action": action_list,
                        "sensitivity": sensitivity if sensitivity != -1 else None,
                        "sensitivity_label_mode": policy.action.get("label", {}).get("sensitivity_label_mode", 0)
                    })
            match_result['remediation_actions'] = list(remediation_actions)
            predefined_label_set.update(LabelFetcher.get_sensitivity_label([sensitivity_level]))
            match_result['tags'] = {
                "predefine": list(predefined_label_set),
                "custom": list(custom_label_set)
            }
            match_result["sensitivity"] = sensitivity_level if sensitivity_level != -1 else None

            for policy_info in match_result.get("policies", []):
                if policy_info["sensitivity"] == sensitivity_level and policy_info["sensitivity_label_mode"] == ApplyLabelMode.ENABLE.value:
                    policy_info["tags"]["predefine"].extend(LabelFetcher.get_sensitivity_label([sensitivity_level]))
                if any(policy_info["tags"].values()):
                    policy_info["action"].append("label")

            return match_result

        finally:
            self._lock_writer.release()


def ddr_create_policy_engine(policy_ids) -> DDRPolicyEngine:
    ddr_engine = DDRPolicyEngine()
    for policy_id in policy_ids:
        try:
            ddr_engine.create_policy(policy_id)
            logger.info(f"Created policy engine for policy id: {policy_id}")
        except Exception as e:
            logger.error(f"Error processing policy id {policy_id}: {e}")
    return ddr_engine
