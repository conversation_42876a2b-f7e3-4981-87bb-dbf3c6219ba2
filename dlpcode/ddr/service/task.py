import uuid
import copy
import traceback
from exts import Session
from sqlalchemy import exists
from psycopg2.errors import UniqueViolation
from ddr.model.task import DDRTask
from typing import List, Tuple
from util.common_log import get_logger

logger = get_logger("ddr")


def check_max_ddr_task(max_ddr_task):
    with Session() as session:
        policy_count = session.query(DDRTask).count()

        if policy_count >= max_ddr_task:
            logger.info("The number of ddr task has reached the limit")
            return -1
        else:
            return 0


def get_task_by_conditions(conditions: dict) -> Tuple[int, List[DDRTask]]:
    condition_fields = {
        "id":          None,  # custom match method
        "name":        {'method': "ilike",  "type": "str"},
        "description": {'method': "ilike",  "type": "str"},
        "status":      {'method': "equal", "type": "int"},
        "storage_type":    {'method': "equal", "type": "int"},
    }
    try:
        session = Session()
        params = []
        # generate the query filters
        for key, value in conditions.items():
            if key not in condition_fields:
                continue
            if value is None:
                continue

            val_dict = condition_fields[key]
            if key == "id":
                if value:
                    if isinstance(value, list):
                        params.append(DDRTask.id.in_(value))
                    else:
                        params.append(DDRTask.id == value)
            elif val_dict:
                column = getattr(DDRTask, key)
                if val_dict["method"] == "like":
                        value = f"%{value}%"
                        params.append(column.like(value))
                elif val_dict["method"] == "ilike":
                        value = f"%{value}%"
                        params.append(column.ilike(value))
                elif val_dict["method"] == "equal":
                    params.append(column == value)
                elif val_dict["method"] == "iequal":
                    if val_dict["type"] == "str":
                        params.append(column.ilike(value))
                else:
                    logger.error(f"invalid match method {val_dict['method']}")
            else:
                logger.error(f"wrong query condition defined {key}")

        sort_field = conditions.get('sort_field') if conditions.get('sort_field') is not None  else 'updated_at'
        sort_method = conditions.get('sort_method') if conditions.get('sort_method') is not None  else 'desc'

        column = getattr(DDRTask, sort_field)
        if sort_method == 'desc':
            results = session.query(DDRTask).filter(*params).order_by(column.desc())
        else:
            results = session.query(DDRTask).filter(*params).order_by(column.asc())

        total_count = results.count()
        page = conditions.get('page')
        per_page = conditions.get('per_page', 10)
        # if no page setting, return all matched items
        if page is not None:
            page = int(page)
            per_page = int(per_page)
            slice_from = per_page * (page - 1)
            slice_to = per_page * page
            results = results.slice(slice_from, slice_to)

        policies = results.all()
        if policies:
            return total_count, [policy.to_dict() for policy in policies]
        else:
            return 0, []
    except:
        logger.exception(traceback.format_exc())
        return -1, None
    finally:
        session.close()

def query_available_index( max_scan_policy,db_index_list ):
    for i in range(1, max_scan_policy + 1):
        if i not in db_index_list:
            return i
    return -1

def create_ddr_task_with_index(data, max_ddr_task):
    from ddr.service.policy import get_trigger_events_by_policy_ids
    session = None
    try:
        session = Session()
        with session.begin():
            if session.query(exists().where(DDRTask.name == data.get('name'))).scalar():
                raise UniqueViolation
            db_indexes = session.query(DDRTask.db_index).all()
            db_index_list = [index[0] for index in db_indexes]
            available_index = query_available_index(max_ddr_task, db_index_list)

            if available_index < 0 or available_index > max_ddr_task:
                logger.error( f"can't find available indexes,the indexs: {db_indexes}" )
                return None

            data['trigger_events'] = get_trigger_events_by_policy_ids(data['ddr_policy_ids'])
            data['db_index'] = available_index

            new_task = DDRTask(**data)
            session.add(new_task)
            session.flush()
            return new_task.to_dict()
    except UniqueViolation:
        raise
    except Exception as e:
        if session:
            session.rollback()
        logger.error(e)
        return None
    finally:
        if session:
            session.close()