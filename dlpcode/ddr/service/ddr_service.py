import os
import time
import hashlib
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional
from storage.util.enum import ActivityStatus
from storage.model.activity import update_activities
from ddr.util.config import ddr_configs
from util.config import configs
from util.enum_ import StorageType
from util.common_log import get_logger
from service.task_management_service import TaskManagementService

logger = get_logger("ddr")
is_file_unified = configs.get("file_unified_queue", {}).get("enabled", False)

class DDRService:
    """Data Detection Response Service"""
    def __init__(self, task: dict):
        # Create DDR task management service to get session
        tms = TaskManagementService(task['id'], is_ddr=True)
        self.session_key = tms.session_tracker.get_session_key()
        if not self.session_key:
            # Create new session
            if tms.session_tracker.create_session():
                self.session_key = tms.session_tracker.get_session_key()
        self._storage_type = task['storage_type']
        self._storage_id = task['storage_id']
        self._analyze_setting = task['analyze_setting']
        self._ddr_task_id = task['id']
        self._scan_folders = task['scan_folders']
        self._scan_file_type = task['scan_file_type']
        self._ddr_policy_ids = task['ddr_policy_ids']
        self._ddr_task = task

    def process_events(self,
                       start_time: datetime, end_time: datetime,
                       target_operations: Optional[List[str]] = None,
                       scan_trigger_events: Optional[List[str]] = None) -> Dict[str, Any]:
        """Process platform events"""
        try:
            # 1. Get platform events
            acitvities = self._get_fetched_logs(start_time, end_time, target_operations)
            if not acitvities:
                return {"processed": 0, "scans_triggered": 0, "skipped": 0}

            scan_count = 0
            skipped_count = 0
            # 2. Process and update activities
            for activity in acitvities:
                # Check if file should be skipped
                if self._should_skip_file(activity, StorageType(self._storage_type)):
                    skipped_count += 1
                    logger.debug(f"Skipped file | Path: {activity['file_path']} | Based on configured skip rules")
                    activity["status"] = ActivityStatus.SKIPPED
                    continue

                trigger_events = self._analyze_setting.get('trigger_events', [])
                if scan_trigger_events:
                    trigger_events = scan_trigger_events

                # Check if scan should be triggered
                if self._should_trigger_scan(activity, trigger_events):
                    activity["status"] = ActivityStatus.PROCESSING
                    activity["scan_triggered"] = True
                    scan_count += 1
                    # Schedule file processing using TaskQueueService
                    self._schedule_ddr_file_processing(activity)

            # 3. Save activities to DB
            update_activities(acitvities)

            result = {
                "processed": len(acitvities),
                "scans_triggered": scan_count,
                "skipped": skipped_count
            }
            logger.info(f"DDR processing completed for {self._storage_type}: {result}")
            return result

        except Exception as e:
            logger.error(f"Error in DDR event processing: {e}")
            return {"processed": 0, "scans_triggered": 0, "skipped": 0, "error": str(e)}

    def _get_fetched_logs(self, start_time: datetime, end_time: datetime, 
                              target_operations: Optional[List[str]] = None) -> List[dict]:
        """Get platform logs from storage_activity table"""
        from storage.model.activity import get_fetched_activities
        try:
            activities = get_fetched_activities(storage_id=self._storage_id,
                                                status=ActivityStatus.NEW,
                                                event_time__gte=start_time,
                                                event_time__lte=end_time )

            if not activities:
                logger.info(f"No logs found for platform {self._storage_type}")
                return []

            return activities

        except Exception as e:
            logger.error(f"Error processing {self._storage_type} logs: {e}")
            return []

    def _schedule_ddr_file_processing(self, activity: dict):
        """
        Unified DDR file processing scheduling
        Args:
            activity: DDR activity data
        """
        try:
            from service.unified_file_service import UnifiedFileScheduler, DDRFileItem
            from celery_worker.doing_download_task import add_analyze_task, download_file

            if not self.session_key:
                logger.error(f"Failed to create session for DDR task {self._ddr_task_id}")
                activity["status"] = ActivityStatus.PENDING
                activity["scan_triggered"] = False
                return

            # Construct file info
            file_info = self._build_file_info_from_activity(activity)
            if not file_info:
                logger.error(f"Failed to build file info for activity {activity.get('id', '')}")
                activity["status"] = ActivityStatus.FAILED
                activity["scan_triggered"] = False
                return

            # Generate backlog_hash
            backlog_content = f"{self._ddr_task_id}:{activity.get('id', '')}:{file_info.get('file_name', '')}"
            backlog_hash = hashlib.md5(backlog_content.encode()).hexdigest()
            params = self._get_params(activity)

            # Add to unified file scheduler
            if is_file_unified:
                ddr_item = DDRFileItem(
                    task_uuid=self._ddr_task_id,
                    file_info=file_info,
                    params=params,
                    session_key=self.session_key,
                    backlog_hash=backlog_hash
                )
                scheduler = UnifiedFileScheduler()
                success = scheduler.add_ddr_file(ddr_item)
                if not success:
                    logger.warning(f"Failed to add DDR file to unified scheduler for activity {activity.get('id', '')}")
                    activity["status"] = ActivityStatus.PENDING
                    activity["scan_triggered"] = False
                else:
                    logger.info(f"Successfully added DDR file to unified scheduler for activity {activity.get('id', '')}")
            else:
                # Use Celery chain for DDR file processing with high priority
                from celery import chain
                download_file_task = download_file.s(
                    task_uuid=self._ddr_task_id,
                    file_info=file_info,
                    params=params,
                    session_key=self.session_key,
                    backlog_hash=backlog_hash,
                )
                analyze_task = add_analyze_task.s()

                # Create chain with DDR priority (8)
                pipeline = chain(download_file_task, analyze_task)
                pipeline.apply_async(priority=8, queue='ddr')

        except Exception as e:
            logger.error(f"Error scheduling DDR file processing: {e}")
            activity["status"] = ActivityStatus.PENDING
            activity["scan_triggered"] = False

    def _build_file_info_from_activity(self, activity: dict) -> dict:
        """build file info from DDR activity"""
        try:
            ddr_fields = activity.get("ddr_fields", {})
            display_path = ddr_fields.get("display_path", "")
            file_name = ddr_fields.get("file_name", "")
            folder = ddr_fields.get("folder", "")

            if not display_path or not file_name or not folder:
                return None

            return {
                "folder": folder,
                "file_name": file_name,
                "display_path": display_path,
                # DDR specific fields
                "event_type": activity.get("event_type", "unknown"),
                "actor_id": ddr_fields.get("actor_id", "unknown")
            }
        except Exception as e:
            logger.error(f"Error building file info from activity: {e}")
            return None

    def _should_trigger_scan(self, event: dict, scan_trigger_events: Optional[List[str]] = None) -> bool:
        """Determine whether to trigger scan"""
        if scan_trigger_events:
            return event["event_type"] in scan_trigger_events
        return False

    def _should_skip_file(self, event: dict, platform: StorageType) -> bool:
        """Determine whether to skip file"""
        try:
            platform_config = ddr_configs.get("platforms", {}).get(platform, {})
            skip_rules = platform_config.get("skip_rules", {})
            ddr_fields = event.get("ddr_fields", {})

            # Check file extension
            file_extensions_to_skip = skip_rules.get("file_extensions", [])
            if file_extensions_to_skip:
                file_extension = os.path.splitext(ddr_fields["file_name"])[1].lower()
                if file_extension in file_extensions_to_skip:
                    return True

            # Check file path
            file_paths_to_skip = skip_rules.get("file_paths", [])
            for skip_path in file_paths_to_skip:
                if skip_path in ddr_fields["display_path"]:
                    return True

            # Check file size
            max_file_size_mb = skip_rules.get("max_file_size_mb")
            if max_file_size_mb and ddr_fields["file_size"]:
                file_size_mb = ddr_fields["file_size"] / (1024 * 1024)
                if file_size_mb > max_file_size_mb:
                    return True

            return False

        except Exception as e:
            logger.error(f"Error checking skip rules: {e}")
            return False


    def _get_params(self, activity: dict) -> dict:
        """
        Get the parameters for the connector service.

        Returns:
            dict: A dictionary containing the parameters for the connector service.
            Return empty dictionary if any error occurs.
        """
        from storage.service.profiles import get_storage_profile_by_id
        try:
            params = {}
            scan_info = self._analyze_setting
            scan_info["scan_uuid"] = self._ddr_task_id
            scan_info["scan_name"] = self._ddr_task["name"]
            scan_info["scan_created_at"] = float(self._ddr_task["created_at"].timestamp())
            scan_info["scan_description"] = self._ddr_task["description"]
            scan_info["scan_file_type"] = self._scan_file_type
            scan_info["ddr_policy"] = self._ddr_policy_ids
            scan_info["activity_id"] = activity["id"]
            scan_info["is_ddr"] = True
            scan_info["celery_priority"] = 8  # DDR priority level
            if is_file_unified:
                scan_info["celery_priority"] = 8  # Keep high priority even in unified mode
            storage = get_storage_profile_by_id(self._storage_id)
            auth_info = storage['auth_info']

            if self._storage_type == StorageType.AWS:
                scan_info["target"] = str(auth_info.get("region_name", "GLOBAL"))
                params = {
                    "remote_info": {
                        "type": "aws",
                        "storage_type": 1,
                        "storage_id": self._storage_id,
                        "storage_name": storage["name"],
                        "key_id": auth_info["key_id"],
                        "access_key": auth_info["access_key"],
                        "cloudtrail_arn": auth_info.get("cloudtrail_arn", ""),
                        "Bucket": self._scan_folders,
                        "file": "",  # eg. s3//<bucket_name>/<file_name>
                        "file_name": "",  # eg. <file_name>
                    },
                    "scan_info": scan_info,
                }
            elif self._storage_type == StorageType.SHAREPOINT_OL and auth_info["usecredentials"] == False:
                scan_info["target"] = str(auth_info.get("tenantid", "UNKNOWN"))
                params = {
                    "remote_info": {
                        "type": "sharepoint",
                        "usecredentials": auth_info["usecredentials"],
                        "storage_type": 2,
                        "storage_id": self._storage_id,
                        "storage_name": storage["name"],
                        "tenantid": auth_info["tenantid"],
                        "clientid": auth_info["clientid"],
                        "clientsecret": auth_info["clientsecret"],
                        "allow_ntlm": auth_info["allow_ntlm"],
                        "file": "",
                        "file_name": "",
                    },
                    "scan_info": scan_info,
                }
            elif self._storage_type == StorageType.GOOGLE:
                scan_info["target"] = str(auth_info.get("customer_id", "UNKNOWN"))
                params = {
                    "remote_info": {
                        "type": "google",
                        "storage_type": 6,
                        "storage_id": self._storage_id,
                        "storage_name": storage["name"],
                        "customer_id": auth_info["customer_id"],
                        "delegated_admin_email": auth_info["delegated_admin_email"],
                        #"service_account_info": auth_info["service_account_info"],
                        "file": "",
                        "file_name": "",
                    },
                    "scan_info": scan_info,
                }
            return params
        except KeyError as e:
            logger.error(f"KeyError occurred: {e}")
            return {}
        except Exception as e:
            logger.error(e)
            return {}