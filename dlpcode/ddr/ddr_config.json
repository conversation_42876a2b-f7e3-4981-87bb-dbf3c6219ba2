{"enabled": true, "max_activities_per_batch": 50, "platforms": {"aws": {"enabled": true, "activity_schedule_minutes": 5, "timeout": 30, "retry_count": 3, "batch_size": 1000, "skip_rules": {"file_extensions": [".tmp", ".log", ".cache"], "file_paths": ["/temp/", "/cache/", "/.git/"], "max_file_size_mb": 100, "min_file_size_kb": 1}}, "sharepoint": {"enabled": true, "activity_schedule_minutes": 1, "timeout": 30, "batch_size": 1000, "skip_rules": {"file_extensions": [".tmp", ".lock"], "file_paths": ["/RecycleBin/", "/Forms/"], "max_file_size_mb": 50}}, "google": {"enabled": true, "activity_schedule_minutes": 15, "timeout": 30, "batch_size": 500, "skip_rules": {"file_extensions": [".tmp"], "max_file_size_mb": 100}}}, "scan_settings": {"ml_enabled": true, "re_precision_level": 2, "ml_certainty_level": 2, "max_file_size_mb": 100, "scan_timeout_seconds": 300}, "ddr_priority_queue": {"enabled": true, "file_processing_ratio": {"ddr_weight": 3, "scan_policy_weight": 7, "max_concurrent_ddr_files": 50, "max_concurrent_scan_policy_files": 100}}}