import uuid
from datetime import datetime, timezone
from exts import Base, Session
from sqlalchemy import and_, true
from sqlalchemy import Column, String, Integer, DateTime, <PERSON><PERSON><PERSON>, SmallInteger
from sqlalchemy.sql import func, exists
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm.attributes import flag_modified
from psycopg2.errors import UniqueViolation
from util.enum_ import ReservedScanPolicyId
from typing import Optional
from util.common_log import get_logger

logger = get_logger("ddr")

class DDRTask(Base):
    __tablename__ = "ddr_task"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    description = Column(String(256), nullable=True)
    enabled = Column(Boolean, default=True)
    scan_scope = Column(SmallInteger, nullable=False)
    scan_folders = Column(JSONB, nullable=False)
    excluded_scan_folders = Column(JSONB, nullable=False)
    storage_type = Column(SmallInteger, nullable=False)
    storage_id = Column(UUID(as_uuid=True), nullable=False)
    file_size_limit = Column(JSONB, nullable=False)
    scan_file_type = Column(JSONB, nullable=False)
    scan_interval = Column(SmallInteger, nullable=False)
    analyze_setting = Column(JSONB, nullable=False)
    ddr_policy_ids = Column(JSONB, nullable=False)
    db_index = Column(Integer, nullable=False)
    trigger_events = Column(JSONB, nullable=False)
    protection_profiles = Column(JSONB, nullable=False)
    last_pull_time = Column(DateTime, server_default=func.now())
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    @property
    def schedule_minutes(self):
        """Return scan interval in minutes for backward compatibility"""
        return self.scan_interval

    def __repr__(self):
        return (f"<DDRTask(id={self.id}, name='{self.name}', description='{self.description}', enabled={self.enabled}, "
                f"scan_folders={self.scan_folders}, scan_scope={self.scan_scope}, excluded_scan_folders={self.excluded_scan_folders}, "
                f"storage_type={self.storage_type}, storage_id={self.storage_id}, "
                f"file_size_limit={self.file_size_limit}, scan_file_type={self.scan_file_type}, "
                f"scan_interval={self.scan_interval}, analyze_setting={self.analyze_setting}, ddr_policy_ids={self.ddr_policy_ids}, "
                f"db_index={self.db_index}, trigger_events={self.trigger_events}, protection_profiles={self.protection_profiles}, "
                f"last_pull_time={self.last_pull_time}, created_at={self.created_at}, updated_at={self.updated_at})>")

    def to_dict(self):
        return {
            'id': str(self.id),
            'name': self.name,
            'description': self.description,
            'enabled': self.enabled,
            'scan_scope': self.scan_scope,
            'scan_folders': self.scan_folders,
            'excluded_scan_folders': self.excluded_scan_folders,
            'storage_type': self.storage_type,
            'storage_id': str(self.storage_id) if self.storage_id else None,
            'file_size_limit': self.file_size_limit,
            'scan_file_type': self.scan_file_type,
            'scan_interval': self.scan_interval,
            'analyze_setting': self.analyze_setting,
            'ddr_policy_ids': self.ddr_policy_ids,
            'db_index': self.db_index,
            'trigger_events': self.trigger_events,
            'protection_profiles': self.protection_profiles,
            'last_pull_time': self.last_pull_time.replace(tzinfo=timezone.utc).timestamp() if self.last_pull_time.tzinfo is None else self.last_pull_time.timestamp(),
            'created_at': self.created_at.replace(tzinfo=timezone.utc).timestamp() if self.created_at.tzinfo is None else self.created_at.timestamp(),
            'updated_at': self.updated_at.replace(tzinfo=timezone.utc).timestamp() if self.updated_at.tzinfo is None else self.updated_at.timestamp(),
        }

    def get_id(self):
        return self.id


def delete_ddr_tasks(ids: list) -> list:
    try:
        delete_names = []
        with Session() as session:
            tasks = session.query(DDRTask).filter(DDRTask.id.in_(ids)).all()
            for task in tasks:
                delete_names.append(task.name)
                session.delete(task)
                session.commit()
            return delete_names
    except Exception as e:
        logger.error(e)
        return []


def create_ddr_task(data: dict) -> dict:
    try:
        with Session() as session:
            if session.query(exists().where(DDRTask.name == data.get('name'))).scalar():
                raise UniqueViolation
            task = DDRTask(**data)
            session.add(task)
            session.commit()
            session.refresh(task)
            return task.to_dict()
    except UniqueViolation:
        raise
    except Exception as e:
        logger.error(e)
        return None


def update_ddr_task(id, data) -> dict:
    try:
        session = Session()
        task = session.query(DDRTask).filter(DDRTask.id == id).first()
        if not task:
            logger.debug(f"ddr task {id} not found")
            return None

        valid_columns = {column.name for column in task.__table__.columns}
        for key, value in data.items():
            if key in valid_columns and value is not None:
                setattr(task, key, value)
                flag_modified(task, key)
                logger.debug(f"update {key} to {value}")
        task.updated_at = func.now()
        session.commit()
        return task.to_dict()
    except Exception as e:
        session.rollback()
        logger.error(e)
    finally:
        session.close()


def get_ddr_dbname_by_id(sc_id, session=None):
    own_session = False
    try:
        if session is None:
            session = Session()
            own_session = True

        db_record = session.query(DDRTask).filter_by(id=sc_id).first()
        if not db_record:
            return None

        db_index = db_record.db_index
        return f"ddr_fileinfo_{db_index}"
    except Exception as e:
        logger.error(e)
        return None
    finally:
        if own_session:
            session.close()


def get_ddr_tasks_dict(**kwargs) -> list:
    """
    Retrieve a list of ddr tasks based on the provided filters.
    Args:
        **kwargs: Keyword arguments representing the filters to apply. Each keyword argument
            should correspond to a property of the DDRTask model.
    Returns:
        list: A list of ddr tasks that match the provided filters.
    Raises:
        Exception: If an error occurs while retrieving the ddr tasks.
    """
    try:
        filters = []
        for key, value in kwargs.items():
            filters.append(getattr(DDRTask, key) == value)

        with Session() as session:
            records = session.query(DDRTask).filter(and_(*filters)).all()
            return [record.to_dict() for record in records]
    except Exception as e:
        logger.error(e)
        return []


def get_ddr_task(**kwargs) -> Optional[DDRTask]:
    """
    Get DDR task by conditions
    Args:
        **kwargs: search conditions
    Returns:
        DDRTask object, None if not found
    """
    try:
        filters = []
        for key, value in kwargs.items():
            filters.append(getattr(DDRTask, key) == value)

        with Session() as session:
            record = session.query(DDRTask).filter(and_(*filters)).first()
            return record
    except Exception as e:
        logger.error(e)
        return None


def get_ddr_task_dict(**kwargs) -> Optional[dict]:
    try:
        task = get_ddr_task(**kwargs)
        return task.to_dict() if task else None
    except Exception as e:
        logger.error(e)
        return None


def update_last_pull_time(task_id: str, pull_time: datetime = None) -> bool:
    """Update task last pull time with proper datetime handling"""
    try:
        session = Session()
        task = session.query(DDRTask).filter(DDRTask.id == task_id).first()
        if task:
            task.last_pull_time = pull_time or datetime.now(timezone.utc)
            session.commit()
            return True
        return False
    except Exception as e:
        session.rollback()
        logger.error(f"Error updating last pull time: {e}")
        return False
    finally:
        session.close()
