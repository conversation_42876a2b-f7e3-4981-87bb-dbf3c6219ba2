from connector.aws_connector import AWSConnector
from connector.interface import ConnectorInterface
from connector.sharepoint_token_connector import Share<PERSON>ointTokenConnector
from connector.google_connector import GoogleConnector
from ddr.model.task import DDRTask
from typing import Union, Optional
from util.enum_ import StorageType, ScanMethod
from storage.service.profiles import get_storage_profile_by_id
from util.common_log import get_logger

logger = get_logger("ddr")


class DDRConnectorBuilder:
    """
    DDRConnectorBuilder is a class responsible for building connector objects for DDR tasks.
    
    This builder creates connectors specifically for Data Detection Response operations,
    which focus on log retrieval and event monitoring rather than file scanning.
    
    Attributes:
        _storage_type (int): The storage type (1=AWS, 2=SharePoint Online, 5=Google Drive)
        _storage_id (str): The storage profile ID
        _session_key (str): Session key for the DDR task
        _ddr_task_id (str): DDR task ID for tracking
        _storage_info (dict): Initialization info from storage profile
        _scan_scope (int): Scan scope (0=all/exclude, 1=selected)
        _scan_folders (list): Folders to scan
        _excluded_scan_folders (list): Folders to exclude
        _file_size_limit (dict): File size limits
        _scan_file_type (list): File types to scan
        _protection_profiles (dict): Protection profiles
        _scan_task_updated_at: DDR task updated timestamp
    """
    
    def __init__(self, ddr_task: DDRTask, session_key: str = ""):
        self._ddr_task_id = ddr_task.id
        self._storage_type = ddr_task.storage_type
        self._storage_id = ddr_task.storage_id
        self._session_key = session_key

        # DDR task specific attributes (similar to scan_policy)
        self._scan_scope = ddr_task.scan_scope
        self._scan_folders = ddr_task.scan_folders
        self._excluded_scan_folders = ddr_task.excluded_scan_folders
        self._file_size_limit = ddr_task.file_size_limit
        self._scan_file_type = ddr_task.scan_file_type
        self._protection_profiles = ddr_task.protection_profiles
        self._scan_task_updated_at = ddr_task.updated_at

        # Get storage_info from storage profile
        self._storage_info = self._get_storage_info()

    def _get_storage_info(self) -> dict:
        """Get storage_info from storage profile"""
        try:
            storage_profile = get_storage_profile_by_id(self._storage_id)
            if not storage_profile:
                logger.error(f"Storage profile not found: {self._storage_id}")
                return {}
            
            # Combine storage_id with auth_info to create storage_info
            storage_info = {"storage_id": self._storage_id}
            storage_info.update(storage_profile['auth_info'])
            
            return storage_info
        except Exception as e:
            logger.error(f"Error getting storage_info: {e}")
            return {}

    def get_connector(self) -> Union[ConnectorInterface, None]:
        """
        Build and return the appropriate connector for DDR operations.
        Returns:
            ConnectorInterface: The configured connector object, or None if failed.
        """
        try:
            init_params = self._storage_info
            if not init_params:
                logger.error("No storage_info available")
                return None
            init_params["scan_task_id"] = self._ddr_task_id
            init_params["is_ddr"] = True

            connector_obj = None
            if self._storage_type == StorageType.AWS:
                connector_obj = AWSConnector(init_params, self._session_key)
            elif (self._storage_type == StorageType.SHAREPOINT_OL and init_params.get("usecredentials") == False):
                connector_obj = SharePointTokenConnector(init_params, self._session_key)
            elif self._storage_type == StorageType.GOOGLE:
                connector_obj = GoogleConnector(init_params, self._session_key)

            if connector_obj:
                skip_paths, skip_files = self.get_skip_items(connector_obj, 
                                                             self._ddr_task_id, 
                                                             self._protection_profiles)
                # Set DDR-specific batch parameters
                batch_params = {
                    "scan_task_uuid": self._ddr_task_id,
                    # "operation_type": "ddr_log_retrieval",
                    "scan_scope": self._scan_scope,
                    "scan_folders": self._scan_folders,
                    "excluded_scan_folders": self._excluded_scan_folders,
                    "file_size_limit": self._file_size_limit,
                    "scan_file_type": self._scan_file_type,
                    "scan_method": ScanMethod.DDR_SCAN,
                    "scan_policy_updated_at": self._scan_task_updated_at,
                    "scan_skip_paths": skip_paths,
                    "scan_skip_files": skip_files
                }
                connector_obj.set_batch_param(batch_params)
                logger.info(f"Built DDR connector for task {self._ddr_task_id}, storage_type: {self._storage_type}")

            return connector_obj
        except Exception as e:
            logger.error(f"Error building DDR connector: {e}")
            return None

    def get_skip_items(self, connector_obj, scan_policy_id, protection_profiles):
        """Get skip paths and files for DDR operations"""
        # DDR operations typically don't skip items, but keep for compatibility
        return [], [] 
