import uuid
from datetime import timezone
from exts import Base, Session
from sqlalchemy import Column, String, Integer, DateTime, Boolean, SmallInteger, and_
from sqlalchemy.sql import func, exists
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm.attributes import flag_modified
from psycopg2.errors import UniqueViolation
from typing import Union
from util.common_log import get_logger

logger = get_logger("ddr")

class DDRPolicy(Base):
    __tablename__ = "ddr_policy"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(512), nullable=False)
    description = Column(String(512), nullable=True)
    storage_type = Column(JSONB, nullable=False)
    file_exclusion = Column(JSONB, nullable=True)
    event_type = Column(String(128), nullable=False)
    predefined_info = Column(String(100), nullable=True)
    is_predefined = Column(Boolean, nullable=False, default=False)
    enabled = Column(Boolean, nullable=False, default=True)
    data_classifier_ids = Column(JSONB, nullable=False)  # Array of data_classifier UUIDs
    match_condition = Column(JSONB, nullable=True)
    match_condition_relation = Column(JSONB, nullable=True)
    risk = Column(Integer, nullable=True)
    protection_framework = Column(JSONB, nullable=True)
    action = Column(JSONB, nullable=True)
    notification_id = Column(UUID(as_uuid=True), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return (f"<DDRPolicy(id={self.id}, name='{self.name}', description='{self.description}', "
                f"storage_type={self.storage_type}, event_type='{self.event_type}', "
                f"is_predefined={self.is_predefined}, enabled={self.enabled}, "
                f"data_classifier_ids={self.data_classifier_ids}, risk={self.risk}, "
                f"notification_id={self.notification_id}, "
                f"created_at={self.created_at}, updated_at={self.updated_at})>")

    def to_dict(self):
        return {
            'id': str(self.id),
            'name': self.name,
            'description': self.description,
            'storage_type': self.storage_type,
            'file_exclusion': self.file_exclusion,
            'event_type': self.event_type,
            'predefined_info': self.predefined_info,
            'is_predefined': self.is_predefined,
            'enabled': self.enabled,
            'data_classifier_ids': self.data_classifier_ids,
            'match_condition': self.match_condition,
            'match_condition_relation': self.match_condition_relation,
            'risk': self.risk,
            'protection_framework': self.protection_framework,
            'action': self.action,
            'notification_id': str(self.notification_id) if self.notification_id else None,
            'created_at': self.created_at.replace(tzinfo=timezone.utc).timestamp() if self.created_at.tzinfo is None else self.created_at.timestamp(),
            'updated_at': self.updated_at.replace(tzinfo=timezone.utc).timestamp() if self.updated_at.tzinfo is None else self.updated_at.timestamp(),
        }

    def get_id(self):
        return self.id

def delete_ddr_policy(ids: list) -> list:
    try:
        delete_names = []
        with Session() as session:
            policies = session.query(DDRPolicy).filter(DDRPolicy.id.in_(ids)).all()
            for policy in policies:
                delete_names.append(policy.name)
                session.delete(policy)
                session.commit()
            return delete_names
    except Exception as e:
        logger.error(e)
        return []

def create_ddr_policy(data: dict) -> dict:
    try:
        with Session() as session:
            if session.query(exists().where(DDRPolicy.name == data.get('name'))).scalar():
                raise UniqueViolation
            policy = DDRPolicy(**data)
            session.add(policy)
            session.commit()
            session.refresh(policy)
            return policy.to_dict()
    except UniqueViolation:
        raise
    except Exception as e:
        logger.error(e)
        return None

def update_ddr_policy(id, data) -> dict:
    try:
        session = Session()
        policy = session.query(DDRPolicy).filter(DDRPolicy.id == id).first()
        if not policy:
            logger.debug(f"ddr policy {id} not found")
            return None

        valid_columns = {column.name for column in policy.__table__.columns}
        for key, value in data.items():
            if key in valid_columns and value is not None:
                setattr(policy, key, value)
                flag_modified(policy, key)
                logger.debug(f"update {key} to {value}")

        policy.updated_at = func.now()
        session.commit()
        return policy.to_dict()
    except Exception as e:
        session.rollback()
        logger.error(e)
    finally:
        session.close()


def get_ddr_policies(**kwargs) -> list:
    try:
        filters = []
        for key, value in kwargs.items():
            filters.append(getattr(DDRPolicy, key) == value)

        with Session() as session:
            records = session.query(DDRPolicy).filter(and_(*filters)).all()
            return [record.to_dict() for record in records]
    except Exception as e:
        logger.error(e)
        return []


def get_ddr_policy(**kwargs) -> dict:
    try:
        filters = []
        for key, value in kwargs.items():
            filters.append(getattr(DDRPolicy, key) == value)

        with Session() as session:
            policy = session.query(DDRPolicy).filter(and_(*filters)).first()
            return policy.to_dict()
    except Exception as e:
        logger.error(e)
        return None