from enum import Enum, IntEnum
from typing import Optional, Dict, Any, List
from datetime import datetime
from util.enum_ import StorageType
from util.common_log import get_logger

logger = get_logger("ddr")

class ActorType(Enum):
    """Actor type enumeration"""
    USER = "user"
    SERVICE = "service"
    SYSTEM = "system"
    EXTERNAL = "external"

class EventVisibility(IntEnum):
    """Visibility of the event: internal, external, or public."""
    INTERNAL = 0  # Only visible to internal users
    EXTERNAL = 1  # Visible to external users (e.g., partners, guests)
    PUBLIC = 2    # Publicly visible (anyone with link or public access)


# Normalize actor_type across platforms to unified ActorType enum value
# This function maps platform-specific actor types to a unified ActorType for cross-platform consistency
# If no match, defaults to ActorType.USER value

def normalize_actor_type(platform: StorageType, raw_actor_type):
    """
    Map platform-specific actor type (string/int) to unified ActorType enum value.

    Args:
        platform (StorageType): Platform enum (e.g. SHAREPOINT_OL, GOOGLE, AWS)
        raw_actor_type (Any): The raw actor type from the event

    Returns:
        ActorType: Unified actor type enum value
    """
    mapping = {
        StorageType.SHAREPOINT_OL: {
            "0": ActorType.USER.value,
            "2": ActorType.USER.value,
            "3": ActorType.USER.value,
            "4": ActorType.SYSTEM.value,
            "5": ActorType.SERVICE.value,
            "6": ActorType.SERVICE.value,
            "7": ActorType.SYSTEM.value,
            "8": ActorType.SYSTEM.value,
            "9": ActorType.EXTERNAL.value,
            "10": ActorType.EXTERNAL.value,
        },
        StorageType.GOOGLE: {
            "user": ActorType.USER.value,
            "serviceAccount": ActorType.SERVICE.value,
            "application": ActorType.SERVICE.value,
            "anyone": ActorType.EXTERNAL.value,
        },
        StorageType.AWS: {
            "user": ActorType.USER.value,
            "root": ActorType.USER.value,
            "service": ActorType.SERVICE.value,
            "assumed-role": ActorType.SERVICE.value,
            "federated-user": ActorType.EXTERNAL.value,
            "anonymous": ActorType.EXTERNAL.value,
        }
    }

    actor_map = mapping.get(platform, {})
    raw_key = str(raw_actor_type).strip().lower()
    return actor_map.get(raw_key, ActorType.USER.value)