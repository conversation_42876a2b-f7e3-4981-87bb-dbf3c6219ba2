import json
import os
from pathlib import Path
from util.common_log import get_logger
from util.enum_ import StorageType


ddr_configs = {}
logger = get_logger("ddr")

# Map StorageType enum to platform names in ddr_config.json
PLATFORM_MAP = {
    StorageType.SHAREPOINT_OL: "sharepoint",
    StorageType.GOOGLE: "google",
    StorageType.AWS: "aws"
}

def load_ddr_config():
    global ddr_configs
    if ddr_configs:
        return ddr_configs

    try:
        config_path = Path(__file__).parent.parent / "ddr_config.json"
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"DDR config file not found at {config_path}")
        if os.path.getsize(config_path) == 0:
            raise ValueError(f"DDR config file is empty at {config_path}")

        with config_path.open() as config_file:
            ddr_configs = json.load(config_file)
        return ddr_configs
    except Exception as e:
        logger.error(f"Error loading DDR config: {e}")
        return None


def get_platform_config(platform: StorageType) -> dict:
    """Get specific platform config"""
    platform_name = PLATFORM_MAP.get(platform, "")
    config = load_ddr_config()
    return config.get("platforms", {}).get(platform_name, {})


def is_ddr_enabled() -> bool:
    """Check if DDR is enabled"""
    config = load_ddr_config()
    return config.get("enabled", True)


def get_platform_interval(platform: StorageType) -> int:
    """Get platform scan interval in minutes"""
    try:
        platform_config = get_platform_config(platform)
        return platform_config.get("activity_schedule_minutes", 10)
    except Exception as e:
        logger.error(f"Error getting platform interval: {e}")
        return 10

load_ddr_config()