import base64
from datetime import datetime, timedelta, timezone
import fnmatch
import io
import json
from mimetypes import guess_type
import os
from pathlib import Path
import string
import time
import random
from typing import Dict, List, Optional, Tuple, Union, Any
import uuid
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload, MediaFileUpload
from googleapiclient.errors import HttpError
import pytz
import requests

from connector.interface import ConnectorInterface
from domain_model.global_cache import GlobalCache
from domain_model.tracker.session_tracker import SessionTracker
from domain_model.tracker.task_tracker import TaskTracker
from exts import logger, SessionExpired, get_logger
from util.err_codes import ModErrCode
from util.enum_ import (<PERSON><PERSON>rCode, GoogleDriveType, ProtectionQuarantineStatus, ScanMethod, 
                        ProtectionConst, ScanScope, UserType, IdentityType, StorageType, 
                        ConnectorCollectionType, ScanResult, SyncVersion)
from util.config import configs, get_supported_file_type
from storage.service.identity import add_storage_identity, get_storage_identity
from storage.model.shared_drives import get_storage_shared_drives
from domain_model.scan_history import get_scan_history
from domain_model.connector_event_collection_history import (get_connector_event_collection_history, 
                                                             create_connector_event_collection_history, 
                                                             get_connector_event_collection_historys)

logger_recycle = get_logger("recycle")
skip_files = get_logger("skip_files")
fetch_storage_log = get_logger("fetch_storage_log")

SCOPES = ['https://www.googleapis.com/auth/admin.directory.user.readonly',
          'https://www.googleapis.com/auth/admin.directory.group.readonly',
          'https://www.googleapis.com/auth/admin.reports.audit.readonly',
          'https://www.googleapis.com/auth/drive.readonly',
          'https://www.googleapis.com/auth/drive']

scan_path = "/var/log/scan"
file_download_dir = Path(configs["download_queue"]["file_download_path"])

EXPORT_MAPPINGS = {
    # Google Docs
    'application/vnd.google-apps.document': (
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.docx'
    ),
    # Google Sheets
    'application/vnd.google-apps.spreadsheet': (
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.xlsx'
    ),
    # Google Slides
    'application/vnd.google-apps.presentation': (
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        '.pptx'
    ),
    # Google Drawings
    'application/vnd.google-apps.drawing': (
        'image/png',
        '.png'
    ),
    # Google Forms
    'application/vnd.google-apps.form': (
        'application/zip',
        '.zip'
    ),
    # Google Apps Script
    'application/vnd.google-apps.script': (
        'application/vnd.google-apps.script+json',
        '.json'
    ),
    # Google Jamboard
    'application/vnd.google-apps.jam': (
        'application/pdf',
        '.pdf'
    ),
    # Google Sites
    'application/vnd.google-apps.site': (
        'text/plain',
        '.txt'
    ),
    # Google Vedio
    'application/vnd.google-apps.vid': (
        'video/mp4',
        '.mp4'
    ),
    # Gmail Mail Layout
    'application/vnd.google-apps.mail-layout': (
        'text/plain',
        '.txt'
    )
}
class GoogleConnector(ConnectorInterface):
    def __init__(self, params: dict, session_key: str = "", pwd_encrypted: int = 1,  pwd_encrypted2: int = 1):
        try:
            super().__init__()
            #logger.debug(f'GoogleConnector init, service_account_info: {params["service_account_info"]}')
            if pwd_encrypted == 1:
                params["service_account_info"] = self.decrypt_pwd(params["service_account_info"])
                #logger.debug(f'GoogleConnector init after decrypt, service_account_info: {params["service_account_info"]}')
            params["service_account_info"] = base64.b64decode(params["service_account_info"]).decode('utf-8')
            #logger.debug(f'GoogleConnector init after base decode2, service_account_info: {params["service_account_info"]} type: {type(params["service_account_info"])}')
            self._session_key = session_key
            self._session_tracker = None
            self._task_tracker = None
            self._is_ddr = params.get("is_ddr", False)
            self._scan_storage_id = params.get("storage_id")
            self._scan_task_id = params.get("scan_task_id")
            self.customer_id = params["customer_id"]
            self.delegated_admin_email = params["delegated_admin_email"]
            self.credentials = self.generate_credentials(params["service_account_info"])
            self.audit_log_monitoring = params.get("audit_log_monitoring", False)
            self.max_audit_log_fetch_hours=configs["connector"]["google"]["max_audit_log_fetch_hours"]
            self.max_audit_log_once_fetch_hours=configs["connector"]["google"]["max_audit_log_once_fetch_hours"]
            self.all_files_fetch_scan_min_interval_count=configs["connector"]["google"]["all_files_fetch_scan_min_interval_count"]
            self.all_files_fetch_scan_min_interval_time=configs["connector"]["google"]["all_files_fetch_scan_min_interval_time"]
            self.all_files_fetch_recycle_min_interval_count=configs["connector"]["google"]["all_files_fetch_recycle_min_interval_count"]
            self.target_operations = configs["connector"]["google"]["target_operations"]
        except KeyError as e:
            logger.error(f"Google Connector init error, KeyError occurred: {e}")
        except Exception as e:
            logger.error(f"Google Connector init error, Exception : {e}")

    def generate_credentials(self, service_account_info):
        file_name = None
        try:
            timestamp = int(time.time())
            random_str = random.choices(string.digits, k=8)
            file_name = f"{scan_path}/service_account_info_{timestamp}_{random_str}.json"
            with open(file_name, 'w') as f:
                f.write(service_account_info)

            credentials = service_account.Credentials.from_service_account_file(
                file_name, scopes=SCOPES)
            return credentials
        except Exception as e:
            logger.error(f"Generate credentials error, Exception: {e}")
            return None
        finally:
            if file_name:
                os.remove(file_name)

    def set_batch_param(self, batch_params: Dict[str, str]) -> bool:
        """
        Set the batch parameters for the connector.

        Args:
            batch_params (Dict[str, str]): A dictionary containing the batch parameters.

        Returns:
            bool: A boolean indicating if the batch parameters are set successfully.
                True if successful, False otherwise.

        When Exception occurs:
            return False.
        """
        try:
            self._uuid = batch_params["scan_task_uuid"]
            self._scan_scope = batch_params["scan_scope"]
            self._selected_folders = batch_params["scan_folders"]
            self._excluded_folders = batch_params["excluded_scan_folders"]
            self._size_limit = batch_params["file_size_limit"]
            self._scan_file_type = batch_params["scan_file_type"]
            self._scan_method = batch_params["scan_method"]
            self._scan_policy_updated_at = batch_params["scan_policy_updated_at"]
            self._scan_skip_paths = batch_params["scan_skip_paths"]
            self._scan_skip_files = batch_params["scan_skip_files"]
            if self._session_key:
                self._session_tracker = SessionTracker(self._uuid, self._session_key)
                self._task_tracker = TaskTracker(self._uuid, self._session_key)
            return True
        except Exception as e:
            logger.error(f"Error set_batch_param: {e}")
            return False

    def test_connection(self) -> Tuple[bool, str, Optional[str]]:
        """
        Test the connection to the google drive storage.

        Returns:
            bool: True if the connection is successful, False otherwise.

        When Exception occurs:
            return False
        """
        try:
            delegated_credentials = self.credentials.with_subject(self.delegated_admin_email)
            directory_service = build('admin', 'directory_v1', credentials=delegated_credentials)
            results = directory_service.users().list(
                customer=self.customer_id,
                maxResults=1,
                orderBy='email'
            ).execute()
            users = results.get('users', [])
            logger.debug(f"Connection test: users count, {len(users)}")
            
            admin_email = self.delegated_admin_email.split('@')
            domain = admin_email[1]
            results = directory_service.groups().list(
                domain=domain,
                customer=self.customer_id,
                maxResults=1,
                orderBy='email'
            ).execute()
            groups = results.get('groups', [])
            logger.debug(f"Connection test: groups count, {len(groups)}")

            drive_service = build('drive', 'v3', credentials=delegated_credentials)
            result = drive_service.drives().list(
                pageSize=1,
                useDomainAdminAccess=True,
                fields="drives(id,name)"
            ).execute()
            dirves = result.get('drives', [])
            logger.debug(f"Connection test: dirves count, {len(dirves)}")

            if self.audit_log_monitoring == True:
                reports_service = build('admin', 'reports_v1', credentials=delegated_credentials)
                end_time = datetime.now(timezone.utc)
                start_time = end_time - timedelta(minutes=10)
                start_time_str = start_time.isoformat(timespec='seconds').replace('+00:00', 'Z')
                end_time_str = end_time.isoformat(timespec='seconds').replace('+00:00', 'Z')                
                results = reports_service.activities().list(
                    userKey='all',
                    applicationName='drive',
                    startTime=start_time_str,
                    endTime=end_time_str,
                    maxResults=1
                ).execute()                       
                audit_logs = results.get('items', [])
                logger.debug(f"Connection test: audit_logs count, {len(audit_logs)}")

            return True, ModErrCode.ErrCode02000000, None
        except HttpError as e:
            logger.error(f"Connection test failed: http error, {str(e)}")
            return False, ModErrCode.ErrCode02060001, str(e)
        except Exception as e:
            # just in case, we catch all other exceptions
            logger.error(f"Connection test failed: {str(e)}")
            return False, ModErrCode.ErrCode02000001, str(e)

    def _persist_group_info(self, group_array) -> Union[str, None]:
        """
        Persists group information to a queue file.

        Args:
            group_array (list): A list of file information dictionaries.

        Returns:
            str: The path of the queue file where the group information is persisted.

        When Exception occurs:
            return None
        """
        try:
            if self._session_key:
                file_name = f"group_info_{self._uuid}_{self._session_key}"
            else:
                file_name = f"group_info_{self._uuid}"

            queue_dir_path = Path(configs["download_queue"]["file_info_queue_path"])
            queue_dir_path.mkdir(parents=True, exist_ok=True)
            queue_path = queue_dir_path / file_name
            queue_path.unlink(missing_ok=True)

            with open(queue_path, "w") as f:
                for group_info in group_array:
                    f.write(json.dumps(group_info) + "\n")
            return str(queue_path)
        except Exception as e:
            logger.error(f"Error _persist_group_file_info: {e}")
            return None

    def get_group_info_from_catlog_folder(self, folder, logger, extended_info = True):
        try:
            group_info = {}
            folder_info = folder.split(':', 1)
            if len(folder_info) != 2:
                logger.error(f"Error get_group_info_from_catlog_folder, split drive type, invalid folder: {folder}")
                return None

            group_info['drive_type'] = folder_info[0]
            item_stripped = folder_info[1].strip()
            parts = item_stripped.rsplit('(', 1)
            if len(parts) != 2:
                logger.error(f"Error get_group_info_from_catlog_folder, split email or shared drive id, invalid folder: {folder}")
                return None

            left_part = parts[0].strip()
            right_part = parts[1].rstrip(')').strip()
            if group_info['drive_type'] == GoogleDriveType.PERSONAL.value:
                group_info['user_name'] = left_part
                group_info['user_email'] = right_part
            else:
                group_info['shared_drive_name'] = left_part
                group_info['shared_drive_id'] = right_part
                if extended_info:
                    drive_info = self.get_shared_dirve_info(right_part, get_organizer = True)
                    if drive_info is None:
                        logger.error(f"Error get_group_info_from_catlog_folder, can not get shared dirve info for drive_id: {right_part}")
                        return None
                    group_info['shared_drive_owner_email'] = drive_info['organizer_email']
            return group_info
        except Exception as e:
            logger.error(f"Error get_group_info_from_catlog_folder, Exception: {e}")
            return None

    def supplement_event_collection_history(self, collection_type):
        try:
            process_events = {}
            _now = datetime.now(timezone.utc)
            payload = {
                "scan_policy_id": self._uuid,
                "connector_type": StorageType.GOOGLE,
                "collection_type": collection_type,
                "event_count": -1,
                "events": process_events,
                "first_event_time": _now,
                "last_event_time": _now,
                "created_at": datetime.now()
            }
            create_connector_event_collection_history(payload)
        except Exception as e:
            logger.error(f"Error supplement_event_collection_history: {e}")
            return

    def is_time_to_fetch_all_files(self, logger, collection_type, min_count, min_time=600):
        try:
            event_historys = get_connector_event_collection_historys(scan_policy_id=self._uuid, connector_type=StorageType.GOOGLE, collection_type=collection_type, limit=min_count)
            if event_historys is None or len(event_historys) < min_count:
                return False
            for event_history in event_historys:
                if event_history.event_count == -1:
                    return False

            if collection_type == ConnectorCollectionType.SCAN:
                all_files_event_history = get_connector_event_collection_history(scan_policy_id=self._uuid, connector_type=StorageType.GOOGLE, collection_type=collection_type, event_count=-1)
                if all_files_event_history:
                    if all_files_event_history.created_at + timedelta(seconds=min_time) > datetime.now():
                        return False
            return True
        except Exception as e:
            logger.error(f"Error in check is_time_to_fetch_all_files: {e}")
            return True

    def fetch_audit_activities(self, service, start_time, end_time, logger, check_session=True):
        start_time_str = start_time.isoformat(timespec='seconds').replace('+00:00', 'Z')
        end_time_str = end_time.isoformat(timespec='seconds').replace('+00:00', 'Z')
        logger.info(f"Google-flow fetch audit activities start time: {start_time_str}, offset_time time: {end_time_str}")
        all_activities = []
        page_token = None

        try:
            while True:
                if check_session:
                    if self._session_tracker and not self._session_tracker.is_alive():
                        logger.info(f"SessionExpired, fetch_audit_events")
                        raise SessionExpired()
                    if self._task_tracker:
                        self._task_tracker.update_running_time()

                request = service.activities().list(
                    userKey='all',
                    applicationName='drive',
                    startTime=start_time_str,
                    endTime=end_time_str,
                    maxResults=500,
                    pageToken=page_token
                )                        
                results = request.execute()
                current_page = results.get('items', [])
                if not current_page:
                    break
                    
                all_activities.extend(current_page)
                page_token = results.get('nextPageToken')            
                if not page_token:
                    break
            
            return all_activities
        except Exception as e:
            logger.error(f"Error fetch_audit_activities: {e}")
            return []

    def fetch_audit_events(self, start_time, end_time, target_operations, logger, check_session=True):
        try:
            delegated_credentials = self.credentials.with_subject(self.delegated_admin_email)
            service = build('admin', 'reports_v1', credentials=delegated_credentials)                        
            events = []
            last_event_time = None
            first_event_time = None

            while True:
                if check_session:
                    if self._session_tracker and not self._session_tracker.is_alive():
                        logger.info(f"SessionExpired, fetch_audit_events")
                        raise SessionExpired()
                    if self._task_tracker:
                        self._task_tracker.update_running_time()

                if start_time + timedelta(hours=self.max_audit_log_once_fetch_hours) < end_time:
                    offset_time = start_time + timedelta(hours=self.max_audit_log_once_fetch_hours)
                else:
                    offset_time = end_time
                
                records = self.fetch_audit_activities(service, start_time, end_time, logger, check_session)
                for record in records:
                    record_datetime = datetime.strptime(record['id']['time'], '%Y-%m-%dT%H:%M:%S.%fZ')
                    record_dt = record_datetime.replace(tzinfo=timezone.utc)
                    if last_event_time is None or record_dt > last_event_time:
                        last_event_time = record_dt
                    if first_event_time is None or record_dt < first_event_time:
                        first_event_time = record_dt

                    for e in record.get('events', []):
                        if e['name'] in target_operations:
                            events.append(record)
                            break  

                if offset_time == end_time:
                    break
                start_time = offset_time

            return events,first_event_time,last_event_time
        except Exception as e:
            logger.error(f"Error fetch_audit_events: {e}")
            return None, None, None

    def process_aduit_log_shared_drive_item(self, file_id, shared_drive_id, group_map, cache):
        try:
            if shared_drive_id in group_map and "folder_event_exist" in group_map[shared_drive_id]:
                logger.debug(f"process_aduit_log_shared_drive_item, this group have folder event, skip file: {file_id}, shared_drive_id: {shared_drive_id}")
                return

            drive_info = self.get_shared_dirve_info(shared_drive_id, get_organizer = True)
            if drive_info is None or 'organizer_email' not in drive_info:
                logger.debug(f"process_aduit_log_shared_drive_item, get shared dirve info error, skip file: {file_id}, shared_drive_id: {shared_drive_id}")
                return None
            shared_drive_name = drive_info['name']
            delegated_user_email = drive_info['organizer_email']
            delegated_credentials = self.credentials.with_subject(delegated_user_email)
            service = build('drive', 'v3', credentials=delegated_credentials)
                                
            catlog_folder = f"{GoogleDriveType.SHARED.value}: {shared_drive_name}({shared_drive_id})"
            if self.check_folder_support(catlog_folder, logger) == False:
                logger.debug(f"process_aduit_log_shared_drive_item, no need to scan the catlog_floder: {catlog_folder}, skip file: {file_id}")
                return
            
            fields = ['id', 'name', 'mimeType', 'parents', 'driveId', 'owners']
            finfo = service.files().get(
                fileId=file_id,
                fields=','.join(fields),
                supportsAllDrives=True
            ).execute()

            if finfo.get('mimeType') == 'application/vnd.google-apps.shortcut':
                logger.debug(f"process_aduit_log_shared_drive_item, skip shortcut")
                return
            elif finfo.get('mimeType') == 'application/vnd.google-apps.folder':
                if shared_drive_id not in group_map:
                    group_map[shared_drive_id] = {
                        "drive_type": GoogleDriveType.SHARED.value,
                        "shared_drive_id": shared_drive_id,
                        "shared_drive_name": shared_drive_name,
                        "shared_drive_owner_email": delegated_user_email,
                        "folder_event_exist": 1
                    }
                else:
                    group_map[shared_drive_id]["folder_event_exist"] = 1                    
            else:
                parents = finfo.get('parents', [])
                if len(parents) < 1:
                    logger.error(f"process_aduit_log_shared_drive_item, get parents failed, skip file {finfo}")
                    return

                absolute_path = self.get_absolute_path(service, parents[0], shared_drive_id, cache)
                if absolute_path is None:
                    logger.error(f"process_aduit_log_shared_drive_item, get absolute path failed, skip file {finfo}")
                    return                

                if finfo['mimeType'] in EXPORT_MAPPINGS:
                    file_name = finfo['name']
                    _, extension = EXPORT_MAPPINGS[finfo['mimeType']]
                    base_name = os.path.splitext(file_name)[0]
                    new_file_name = f"{base_name}{extension}"
                else:
                    new_file_name = finfo['name']
                full_display_path = f"{catlog_folder}{absolute_path}/{new_file_name}"

                file = {
                    "folder": f"{delegated_user_email}/{parents[0]}/{finfo['id']}",
                    "file_name": new_file_name,
                    "display_path": full_display_path,
                    "catlog_folder": catlog_folder,
                    "skip_modify_time_check": True
                }

                if shared_drive_id not in group_map:
                    file_array = {}
                    file_array[finfo['id']] = file
                    group_map[shared_drive_id] = {
                        "drive_type": GoogleDriveType.SHARED.value,
                        "shared_drive_id": shared_drive_id,
                        "shared_drive_name": shared_drive_name,
                        "shared_drive_owner_email": delegated_user_email,
                        "file_array": file_array
                    }
                else:
                    if finfo['id'] not in group_map[shared_drive_id]["file_array"]:
                        group_map[shared_drive_id]["file_array"][finfo['id']] = file
                    else:
                        logger.debug(f"The file {file} is already in file_array, skip")
        except Exception as e:
            logger.error(f"Error process_aduit_log_shared_drive_item: {e}")
            return

    def process_aduit_log_personal_drive_item(self, file_id, owner_email, group_map, cache):
        try:
            if owner_email in group_map and "folder_event_exist" in group_map[owner_email]:
                logger.debug(f"process_aduit_log_personal_drive_item, this group have folder event, skip file: {file_id}, user email: {owner_email}")
                return

            delegated_user_email = owner_email
            delegated_credentials = self.credentials.with_subject(delegated_user_email)
            service = build('drive', 'v3', credentials=delegated_credentials)
                                
            user_info = self.get_user_info(delegated_user_email)
            if user_info is None:
                logger.error(f"process_aduit_log_personal_drive_item, can not get user info for: {delegated_user_email}")
                return
            user_name = user_info.get('name', {}).get('fullName', 'Unknow')

            catlog_folder = f"{GoogleDriveType.PERSONAL.value}: {user_name}({delegated_user_email})"
            if self.check_folder_support(catlog_folder, logger) == False:
                logger.debug(f"process_aduit_log_personal_drive_item, no need to scan the catlog_floder: {catlog_folder}, skip file: {file_id}")
                return
            
            fields = ['id', 'name', 'mimeType', 'parents', 'driveId', 'owners']
            finfo = service.files().get(
                fileId=file_id,
                fields=','.join(fields),
                supportsAllDrives=True
            ).execute()

            if finfo.get('mimeType') == 'application/vnd.google-apps.shortcut':
                logger.debug(f"process_aduit_log_personal_drive_item, skip shortcut")
                return
            elif finfo.get('mimeType') == 'application/vnd.google-apps.folder':
                if delegated_user_email not in group_map:
                    group_map[delegated_user_email] = {
                        "drive_type": GoogleDriveType.PERSONAL.value,
                        "user_name": user_name,
                        "user_email": delegated_user_email,
                        "folder_event_exist": 1
                    }
                else:
                    group_map[delegated_user_email]["folder_event_exist"] = 1                    
            else:
                parents = finfo.get('parents', [])
                if len(parents) < 1:
                    logger.error(f"process_aduit_log_personal_drive_item, get parents failed, skip file {finfo}")
                    return

                drive_id = "root"
                absolute_path = self.get_absolute_path(service, parents[0], drive_id, cache)
                if absolute_path is None:
                    logger.error(f"process_aduit_log_personal_drive_item, get absolute path failed, skip file {finfo}")
                    return

                if finfo['mimeType'] in EXPORT_MAPPINGS:
                    file_name = finfo['name']
                    _, extension = EXPORT_MAPPINGS[finfo['mimeType']]
                    base_name = os.path.splitext(file_name)[0]
                    new_file_name = f"{base_name}{extension}"
                else:
                    new_file_name = finfo['name']
                full_display_path = f"{catlog_folder}{absolute_path}/{new_file_name}"

                file = {
                    "folder": f"{delegated_user_email}/{parents[0]}/{finfo['id']}",
                    "file_name": new_file_name,
                    "display_path": full_display_path,
                    "catlog_folder": catlog_folder,
                    "skip_modify_time_check": True
                }

                if delegated_user_email not in group_map:
                    file_array = {}
                    file_array[finfo['id']] = file
                    group_map[delegated_user_email] = {
                        "drive_type": GoogleDriveType.PERSONAL.value,
                        "user_name": user_name,
                        "user_email": delegated_user_email,
                        "file_array": file_array
                    }
                else:
                    if finfo['id'] not in group_map[delegated_user_email]["file_array"]:
                        group_map[delegated_user_email]["file_array"][finfo['id']] = file
                    else:
                        logger.debug(f"The file {file} is already in file_array, skip")
        except Exception as e:
            logger.error(f"Error process_aduit_log_personal_drive_item: {e}")
            return

    def process_aduit_log_item(self, item_params, group_map, cache):
        try:
            if 'owner_is_shared_drive' not in item_params or 'doc_id' not in item_params:
                logger.error(f"process_aduit_log_item, can not find valid param, {item_params}")
                return
            file_id = item_params['doc_id']

            if item_params.get('owner_is_shared_drive') == True:
                if 'shared_drive_id' not in item_params:
                    logger.error(f"process_aduit_log_item, can not find shared_drive_id in param")
                    return
                self.process_aduit_log_shared_drive_item(file_id, item_params['shared_drive_id'], group_map, cache)               
            else:
                if 'owner' not in item_params:
                    logger.error(f"process_aduit_log_item, can not find owner in param")
                    return
                self.process_aduit_log_personal_drive_item(file_id, item_params['owner'], group_map, cache)                    
        except Exception as e:
            logger.error(f"Error process_aduit_log_item: {e}")
            return
        
    def generate_changed_groups(self, events, group_array):
        try:
            group_map = {}
            process_events = {}
            cache = {}

            for event in events:
                id = event.get('id', {})
                event_id=f"{id.get('applicationName', 'drive')}{id.get('uniqueQualifier', '0')}"
                logger.debug(f"Google-flow event id: {id}, event_id: {event_id}")

                event_finished = get_connector_event_collection_history(scan_policy_id=self._uuid, connector_type=StorageType.GOOGLE, collection_type=ConnectorCollectionType.SCAN, event_id=event_id)
                if event_finished is not None or event_id == 'drive-0':
                    logger.debug(f"Google-flow this event has been processed, skip it, event id: {event_id}")
                    continue
                
                items = event.get('events', [])
                for item in items:
                    operation = item.get("name", "NA")                                   
                    if operation in ["create", "upload", "untrash", "edit", "copy", "move", "rename", "change_user_access", "change_document_visibility", "change_document_access_scope"]:                        
                        parameters = item.get("parameters", [])
                        item_params = {}
                        for p in parameters:
                            if p.get('name', 'NA') == 'owner_is_shared_drive':
                                item_params['owner_is_shared_drive'] = p.get('boolValue')
                            elif p.get('name', 'NA') == 'shared_drive_id':
                                item_params['shared_drive_id'] = p.get('value')
                            elif p.get('name', 'NA') == 'owner':
                                item_params['owner'] = p.get('value')
                            elif p.get('name', 'NA') == 'doc_id':
                                item_params['doc_id'] = p.get('value')
                        logger.debug(f"Google-flow event operation: {operation}, params: {item_params}")
                        self.process_aduit_log_item(item_params, group_map, cache)
                    #else:
                        #logger.debug(f"Unknown operation: {operation}")

                event_info = id
                process_events[event_id] = event_info

            logger.debug(f"Google-flow: group_map: {group_map}")
            for value in group_map.values():
                if value["drive_type"] == GoogleDriveType.PERSONAL.value:
                    group_info = (
                        {
                            "drive_type": GoogleDriveType.PERSONAL.value,
                            "user_name": value["user_name"],
                            "user_email": value["user_email"]
                        }
                    )
                else:
                    group_info = (
                        {
                            "drive_type": GoogleDriveType.SHARED.value,
                            "shared_drive_id": value["shared_drive_id"],
                            "shared_drive_name": value["shared_drive_id"],
                            "shared_drive_owner_email": value["shared_drive_owner_email"],
                        }
                    )

                if "folder_event_exist" not in value:
                    file_array = []
                    files = value.get("file_array", {})
                    for file in files.values():
                        file_array.append(file)
                    group_info["file_array"] = file_array
              
                group_array.append(group_info)
            return process_events
        except Exception as e:
            logger.error(f"Error generate_changed_groups: {e}")
            return {}

    def get_group_info_by_events(self, group_array):
        try:
            scan_history = get_scan_history(scan_policy_id = self._uuid, scan_result=ScanResult.COMPLETED)
            if scan_history is not None and scan_history.start_time > self._scan_policy_updated_at.replace(tzinfo=timezone.utc):
                if self.is_time_to_fetch_all_files(logger, ConnectorCollectionType.SCAN, self.all_files_fetch_scan_min_interval_count, min_time=self.all_files_fetch_scan_min_interval_time):
                    logger.info(f"Google-flow, do all files fetch, reason is: reach min_interval_count and min_interval_time")
                    self.supplement_event_collection_history(ConnectorCollectionType.SCAN)
                    self.get_group_info(group_array)
                else:
                    logger.info(f"Google-flow, do event files fetch, scan_history.start_time: {scan_history.start_time.isoformat()}, scan_policy_updated_at: {self._scan_policy_updated_at.replace(tzinfo=timezone.utc).isoformat()}")
                    event_history = get_connector_event_collection_history(scan_policy_id=self._uuid, connector_type=StorageType.GOOGLE, collection_type=ConnectorCollectionType.SCAN)
                    start_time = None
                    if event_history is not None:
                        start_time = event_history.last_event_time
                        logger.info(f"Google-flow get start time from event history")
                    else:
                        start_time = scan_history.start_time
                        logger.info(f"Google-flow get start time from scan history")

                    _now = datetime.now(timezone.utc)
                    if start_time + timedelta(hours=self.max_audit_log_fetch_hours) > _now:
                        target_operations = ["create", "upload", "untrash", "edit", "copy", "move", "rename", "change_user_access", "change_document_visibility", "change_document_access_scope"]
                        logger.info(f"Google-flow get events start time: {start_time.isoformat()}, end time: {_now.isoformat()}")
                        events,first_event_time,last_event_time = self.fetch_audit_events(start_time, _now, target_operations, logger)
                        logger.info(f"Google-flow event counts: {len(events) if events else 0}, first event time: {first_event_time.isoformat() if first_event_time else 'None'}, last_event_time: {last_event_time.isoformat() if last_event_time else 'None'}")
                        if events is not None:
                            process_events = self.generate_changed_groups(events, group_array)
                            if first_event_time is None:
                                first_event_time = start_time
                            if last_event_time is None:
                                last_event_time = start_time
                            payload = {
                                "scan_policy_id": self._uuid,
                                "connector_type": StorageType.GOOGLE,
                                "collection_type": ConnectorCollectionType.SCAN,
                                "event_count": len(process_events),
                                "events": process_events,
                                "first_event_time": first_event_time,
                                "last_event_time": last_event_time,
                                "created_at": datetime.now()
                            }
                            create_connector_event_collection_history(payload)
                        else:
                            logger.info(f"Google-flow, do all files fetch, reason is: get event logs error")
                            self.supplement_event_collection_history(ConnectorCollectionType.SCAN)
                            self.get_group_info(group_array)
                    else:
                        logger.info(f"Google-flow, do all files fetch, reason is: start_time is too early: {scan_history.start_time.isoformat()}, can not get event logs")
                        self.supplement_event_collection_history(ConnectorCollectionType.SCAN)
                        self.get_group_info(group_array)
            else:
                if scan_history is None:
                    logger.info(f"Google-flow, do all files fetch, reason is: scan history is None")
                else:
                    logger.info(f"Google-flow, do all files fetch, reason is: scan policy have changed")

                self.supplement_event_collection_history(ConnectorCollectionType.SCAN)
                self.get_group_info(group_array)
        except Exception as e:
            logger.error(f"Error get_group_info_by_events: {e}")
            return

    def get_group_info(self, group_array):
        
        if self._scan_scope == ScanScope.ALL_FOLDERS:
            all_folders = self.get_folders()
            for folder in all_folders:
                if folder not in self._excluded_folders:
                    group_info = self.get_group_info_from_catlog_folder(folder, logger)
                    if group_info is None:
                        logger.debug(f"batch_generate_queue_group_file, ALL_FOLDERS get group info error, skip {folder}")
                        continue
                    group_array.append(group_info)
        else:
            for folder in self._selected_folders:
                group_info = self.get_group_info_from_catlog_folder(folder, logger)
                if group_info is None:
                    logger.debug(f"batch_generate_queue_group_file, SELECTED_FOLDERS get group info error, skip {folder}")
                    continue
                group_array.append(group_info)

        logger.info(f"Google-flow get_group_info, total drive count: {len(group_array)}")


    def batch_generate_queue_group_file(self) -> Union[Tuple[str, int], Tuple[None, int]]:
        """
        Get the group queue file path and queue size.

        Returns:
            Tuple[str, int]: A tuple containing the file path and size of the queue.

        When Exception occurs:
            return None

        When SessionExpired occurs:
            raise SessionExpired
        """
        try:
            logger.debug(f"Start batch_generate_queue_group_file")
            group_array = []
            if self._scan_method == ScanMethod.INCREMENTAL_SCAN and self.audit_log_monitoring == True:
                self.get_group_info_by_events(group_array)
            else:
                self.get_group_info(group_array)
            return self._persist_group_info(group_array), len(group_array)
        except SessionExpired as e:
            logger.info(f"SessionExpired: batch_generate_queue_group_file")
            raise e
        except Exception as e:
            logger.error(f"Error batch_generate_queue_group_file: {e}")
            return None, 0

    def _persist_file_info(self, file_array) -> Union[str, None]:
        """
        Persists file information to a queue file.

        Args:
            file_array (list): A list of file information dictionaries.

        Returns:
            str: The path of the queue file where the file information is persisted.

        When Exception occurs:
            return None
        """
        try:
            if self._session_key:
                file_name = f"file_info_{self._uuid}_{self._session_key}"
            else:
                file_name = f"file_info_{self._uuid}"

            queue_dir_path = Path(configs["download_queue"]["file_info_queue_path"])
            queue_dir_path.mkdir(parents=True, exist_ok=True)
            queue_path = queue_dir_path / file_name
            queue_path.unlink(missing_ok=True)

            with open(queue_path, "w") as f:
                for file_info in file_array:
                    f.write(json.dumps(file_info) + "\n")
            return str(queue_path)
        except Exception as e:
            logger.error(f"Error _persist_file_info: {e}")
            return None

    def batch_generate_queue_file(self, group_info: dict) -> Union[Tuple[str, int], Tuple[None, int]]:
        """
        Get the queue file path and queue size. If a timestamp is provided,
        the queue file should only contain data after that UTC timestamp.

        Returns:
            Tuple[str, int]: A tuple containing the file path and size of the queue.

        When Exception occurs:
            return None

        When SessionExpired occurs:
            raise SessionExpired
        """
        try:
            logger.info(f"Google-flow batch_generate_queue_file start, drive type: {group_info.get('drive_type')}, drive user name: {group_info.get('user_name')}, drive user email: {group_info.get('user_email')}, shared drive name: {group_info.get('shared_drive_name')}, shared drive id: {group_info.get('shared_drive_id')}, shared drive owner email: {group_info.get('shared_drive_owner_email')}, file array len: {len(group_info.get('file_array', []))}")
            file_array = []
            all_items = []
            catlog_folder = ""
            drive_id = ""
            delegated_user_email = ""
            cache = {}
            if "file_array" in group_info:
                file_array = group_info["file_array"]
            else:
                service = None
                page_token: Optional[str] = None
                page_size = 100
                fields="nextPageToken, files(id, name, mimeType, parents)"
                if group_info['drive_type'] == GoogleDriveType.PERSONAL.value:
                    delegated_user_email = group_info['user_email']
                    delegated_credentials = self.credentials.with_subject(delegated_user_email)
                    service = build('drive', 'v3', credentials=delegated_credentials)
                    catlog_folder = f"{GoogleDriveType.PERSONAL.value}: {group_info['user_name']}({group_info['user_email']})"
                    drive_id = "root"
                    query = "trashed = false and 'me' in owners and mimeType != 'application/vnd.google-apps.shortcut' and mimeType != 'application/vnd.google-apps.folder'"
                    while True:
                        if self._session_tracker and not self._session_tracker.is_alive():
                            logger.info(f"SessionExpired, batch_generate_queue_file")
                            raise SessionExpired()
                        if self._task_tracker:
                            self._task_tracker.update_running_time()

                        response = service.files().list(
                            q=query,
                            spaces='drive',
                            pageSize=page_size,
                            fields=fields,
                            pageToken=page_token
                        ).execute()

                        items = response.get('files', [])
                        all_items.extend(items)
                        page_token = response.get('nextPageToken')
                        if not page_token:
                            logger.info(f"Google-flow batch_generate_queue_file personal drive, all items retrieved")
                            break
                else:
                    drive_id = group_info['shared_drive_id']
                    delegated_user_email = group_info['shared_drive_owner_email']
                    delegated_credentials = self.credentials.with_subject(delegated_user_email)
                    service = build('drive', 'v3', credentials=delegated_credentials)
                    catlog_folder = f"{GoogleDriveType.SHARED.value}: {group_info['shared_drive_name']}({drive_id})"
                    query = "trashed = false and mimeType != 'application/vnd.google-apps.shortcut' and mimeType != 'application/vnd.google-apps.folder'"
                    while True:
                        if self._session_tracker and not self._session_tracker.is_alive():
                            logger.info(f"SessionExpired, batch_generate_queue_file")
                            raise SessionExpired()
                        if self._task_tracker:
                            self._task_tracker.update_running_time()

                        response = service.files().list(
                            corpora='drive',  # Search within a shared drive
                            driveId=drive_id,  # The ID of the shared drive
                            includeItemsFromAllDrives=True, # Must be True when searching Shared Drives
                            supportsAllDrives=True, # Must be True when working with Shared Drives
                            q=query,
                            pageSize=page_size,
                            fields=fields,
                            pageToken=page_token
                        ).execute()

                        items = response.get('files', [])
                        all_items.extend(items)
                        page_token = response.get('nextPageToken')
                        if not page_token:
                            logger.info(f"Google-flow batch_generate_queue_file shared drive, all items retrieved")
                            break

            for item in all_items:
                parents = item.get('parents', [])
                if len(parents) < 1:
                    logger.error(f"Google-flow batch_generate_queue_file, get parents failed, skip file {item['id']}, {item['name']}")
                    continue

                absolute_path = self.get_absolute_path(service, parents[0], drive_id, cache)
                if absolute_path is None:
                    logger.error(f"Google-flow batch_generate_queue_file, get absolute path failed, skip file {item['id']}, {item['name']}")
                    continue

                if item['mimeType'] in EXPORT_MAPPINGS:
                    file_name = item['name']
                    _, extension = EXPORT_MAPPINGS[item['mimeType']]
                    base_name = os.path.splitext(file_name)[0]
                    new_file_name = f"{base_name}{extension}"
                else:
                    new_file_name = item['name']
                full_display_path = f"{catlog_folder}{absolute_path}/{new_file_name}"

                file = {
                    "folder": f"{delegated_user_email}/{parents[0]}/{item['id']}",
                    "file_name": new_file_name,
                    "display_path": full_display_path,
                    "catlog_folder": catlog_folder
                }
                file_array.append(file)
            logger.info(f"Google-flow batch_generate_queue_file end, total-file-count: {len(file_array)}")
            return self._persist_file_info(file_array), len(file_array)
        except SessionExpired as e:
            logger.info(f"SessionExpired: batch_generate_queue_file")
            raise e
        except Exception as e:
            logger.error(f"Error batch_generate_queue_file: {e}")
            return (None, 0)

    def get_absolute_path(self, service, parent_id, drive_id, cache, path_separator: str = '/') -> str:
        path_parts = []
        current_id = parent_id
        #logger.debug(f"Get absolute path, parent_id: {parent_id}")
        try:
            while current_id and current_id != drive_id:
                if current_id in cache:
                    name, current_id = cache[current_id]
                else:
                    file = service.files().get(
                        fileId=current_id,
                        fields='name, parents',
                        supportsAllDrives=True
                    ).execute()

                    name = file['name']
                    parents = file.get('parents', [])
                    next_id = parents[0] if parents else None

                    cache[current_id] = (name, next_id)
                    current_id = next_id

                path_parts.insert(0, name)

            absolute_path = path_separator + path_separator.join(path_parts)
            #logger.debug(f"Get absolute path, absolute_path: {absolute_path}")
            return absolute_path
        except Exception as e:
            logger.error(f"Get absolute path error, current_id: {current_id} Exception: {str(e)}")
            return None

    def get_first_revision_info(self, service, file_id):
        """
        Retrieve the first revision of the specified file.
        This revision typically represents the file's creator.
        """
        try:
            response = service.revisions().list(
                fileId=file_id,
                fields=(
                    'revisions(id, modifiedTime, lastModifyingUser(emailAddress, permissionId, displayName, kind))'
                ),
                pageSize=1,
            ).execute()

            revisions = response.get('revisions', [])
            if revisions:
                return revisions[0]
            else:
                return None
        except Exception as e:
            logger.error(f"An error occurred while retrieving first revision: {e}")
            return None

    def get_permission_details(self, service, file_id, permission_id):
        """
        Retrieve permission details (email, display name) associated with a given permission ID.
        """
        try:
            permission = service.permissions().get(
                fileId=file_id,
                permissionId=permission_id,
                fields='id, emailAddress, displayName',
                supportsAllDrives=True
            ).execute()
            return permission
        except Exception as e:
            logger.error(f"An error occurred while retrieving permission details: {e}")
            return None

    def get_permission_list(self, service, file_id):
        """
        Retrieve permissions of a file.
        """
        try:
            permissions = service.permissions().list(
                fileId=file_id,
                fields='permissions(id, type, role, emailAddress, displayName)',
                supportsAllDrives=True
            ).execute()
            return permissions.get('permissions', [])
        except Exception as e:
            logger.error(f"An error occurred while retrieving permission list: {e}")
            return None

    def get_shared_drive_file_creator(self, service, file_id):
        try:
            # Get the first revision, which typically corresponds to the file's creator
            first_revision = self.get_first_revision_info(service, file_id)
            if not first_revision:
                logger.error(f"Get shared drive file creator, No revisions found for the file {file_id}.")
                return None

            logger.debug(f"First Revision Info: {first_revision}")
            # Extract the permission ID of the user who created the file
            last_modifying_user = first_revision.get('lastModifyingUser', {})
            permission_id = last_modifying_user.get('permissionId')
            if not permission_id:
                logger.error(f"Get shared drive file creator, No permission ID found for the file {file_id}.")
                return None

            logger.debug(f"Creator's Permission ID: {permission_id}")
            # Retrieve the creator's details (email and display name)
            creator = self.get_permission_details(service, file_id, permission_id)
            return creator
        except Exception as e:
            logger.error(f"Get shared drive file creator, Exception: {e}")
            return None

    def get_file_info(self, folder: str, file_name: str, task_uuid: str = '', conn = None) -> Tuple[int, Union[Dict, None]]:
        """
        Retrieves information about a file.

        Args:
            folder (str): The folder where the file is located.
            file_name (str): The name of the file.
            task_uuid (str): The UUID of the task.
            conn: It is only valid in smb connector.

        Returns:
            Tuple[int, Union[Dict, None]]: A tuple containing an integer status code and a dictionary
            with file information. If the file information is successfully retrieved, the status code
            will be 0.
            If an error occurs during the retrieval of file information, the status code will be a
            negative integer and the dictionary will be None.
        """
        file_info = {}
        try:
            folder_info = folder.split('/')
            delegated_user_email = folder_info[0]
            file_id = folder_info[2]
            delegated_credentials = self.credentials.with_subject(delegated_user_email)
            service = build('drive', 'v3', credentials=delegated_credentials)
            fields = ['id', 'name', 'mimeType', 'size', 'createdTime', 'modifiedTime', 'parents', 'webViewLink', 'driveId', 'owners']
            finfo = service.files().get(
                fileId=file_id,
                fields=','.join(fields),
                supportsAllDrives=True
            ).execute()

            file_info["folder"] = folder
            file_info["file_name"] = file_name
            file_info["size"] = finfo.get("size")
            last_modified_str = finfo.get("modifiedTime")
            if last_modified_str:
                last_modified_dt = datetime.strptime(last_modified_str, "%Y-%m-%dT%H:%M:%S.%fZ")
                formatted_date = last_modified_dt.strftime("%Y-%m-%d %H:%M:%S")
                file_info["last_modified"] = formatted_date

            created_time_str = finfo.get("createdTime")
            if created_time_str:
                created_time_dt = datetime.strptime(created_time_str, "%Y-%m-%dT%H:%M:%S.%fZ")
                formatted_date = created_time_dt.strftime("%Y-%m-%d %H:%M:%S")
                file_info["created_time"] = formatted_date

            file_info["mime_type"] = finfo.get("mimeType")

            if "owners" in finfo:
                items = []
                for owner in finfo["owners"]:
                    items.append(owner['emailAddress'])
                file_info["owners"] = items
                if len(finfo["owners"]) > 0:
                    file_info["user"] = finfo["owners"][0]["displayName"]
                    file_info["email"] = finfo["owners"][0]["emailAddress"]
            elif "driveId" in finfo:   #shared drive
                create_by = self.get_shared_drive_file_creator(service, file_id)
                if create_by and "emailAddress" in create_by:
                    file_info["create_by"] = create_by["emailAddress"]
                    file_info["user"] = create_by["displayName"]
                    file_info["email"] = create_by["emailAddress"]

            collaborators = {}
            share_link = {
                "perm_id": 1,       # perm_id = 1, means the share_link do not have 'anyone' or 'organization' permission
                "link": finfo['webViewLink'],
                "permission": "",
                "scope": "restricted",
                "coll_public": False,
                "coll_domains": []
            }
            permissions = self.get_permission_list(service, file_id)
            if permissions:
                for permission in permissions:
                    #logger.debug(f"Get file info, domain permission: {permission}")
                    if permission['type'] == "anyone":
                        share_link['scope'] = "public"
                        share_link['coll_public'] = True
                        share_link['permission'] = permission['role']
                        share_link['perm_id'] = permission['id']
                    elif permission['type'] == "domain":
                        share_link['scope'] = "organization"            
                        share_link['coll_domains'].append(permission.get('domain', permission.get('displayName', 'Unknow')))
                        share_link['permission'] = permission['role']
                        share_link['perm_id'] = permission['id']
                    elif permission['type'] == "group":
                        group_email = permission["emailAddress"]
                        groups, _ = get_storage_identity(conditions={'sid': self._scan_storage_id, 'email': group_email, 'identity_type':IdentityType.GROUP})
                        if groups and len(groups) > 0:
                            identifier = groups[0]['identifier']
                        else:
                            group = self.get_group_info_by_email(group_email)
                            if group is None:
                                logger.error(f"Error get file info, can not get group info for: {group_email}")
                                return None
                            group_info = self.format_group_info(group)
                            if group_info is None:
                                logger.error(f"Error get file info, can not format group info for: {group}")
                                return None
                            logger.debug(f"Get file info, add group: {group_info} to db")
                            add_storage_identity(self._scan_storage_id, [group_info], IdentityType.GROUP, version=SyncVersion.INIT)
                            identifier = group_info['id']

                        collaborators[identifier] = {'perm_id': permission['id'], 'permission': permission['role']}
                    elif permission['type'] == "user":
                        user_email = permission["emailAddress"]
                        users, _ = get_storage_identity(conditions={'sid': self._scan_storage_id, 'email': user_email, 'identity_type':IdentityType.PEOPLE})
                        if users and len(users) > 0:
                            identifier = users[0]['identifier']
                        else:
                            user = self.get_user_info(user_email)
                            if user is None:
                                logger.error(f"Error get file info, can not get user info for: {user_email}")
                                return None
                            user_info = self.format_user_info(user)
                            logger.debug(f"Get file info, add user: {user_info} to db")
                            add_storage_identity(self._scan_storage_id, [user_info], IdentityType.PEOPLE, version=SyncVersion.INIT)
                            identifier = user_info['id']

                        collaborators[identifier] = {'perm_id': permission['id'], 'permission': permission['role']}

            file_info['collaborators'] = collaborators
            file_info['share_link'] = [share_link]
            file_info['shared_data'] = self.generate_shared_data(collaborators, [share_link], file_info.get('email'))
            return 0, file_info
        except requests.exceptions.ConnectionError as e:
            logger.error(e)
            return ErrorCode.CONNECTION_ERROR.value, None
        except requests.exceptions.Timeout as e:
            logger.error(e)
            return ErrorCode.CONNECTION_ERROR.value, None
        except requests.exceptions.RequestException as e:
            logger.error(f"Error get_file_info: {e}")
            return  ErrorCode.CONNECTION_ERROR.value, None
        except Exception as e:
            logger.error(f"Error get_file_info: {e}")
            return ErrorCode.OTHER_ERROR.value, None

    def generate_shared_data(self, collaborators, share_link, owner_email):
        try:
            shared_data = {
                'with_public_shareable_link': False,
                'with_internal_shareable_link': False,
                'with_external_shareable_link': False,
                'with_internal_collaborators': False,
                'with_external_collaborators': False
            }

            for identifier in collaborators.keys():
                ident, _ = get_storage_identity(conditions={'sid': self._scan_storage_id, 'identifier': identifier})
                if ident and len(ident) > 0:
                    if owner_email and ident[0]['email'] == owner_email:
                        continue
                    if ident[0]['type'] == UserType.GUEST:
                        shared_data['with_external_collaborators'] = True
                    elif ident[0]['type'] == UserType.MEMBER:
                        shared_data['with_internal_collaborators'] = True

            shared_data['with_internal_shareable_link'] = shared_data['with_internal_collaborators']
            shared_data['with_external_shareable_link'] = shared_data['with_external_collaborators']
            link = share_link[0]
            if link['scope'] == 'public':
                shared_data['with_public_shareable_link'] = True
            elif link['scope'] == 'organization':
                admin_email = self.delegated_admin_email.split('@')
                domain = admin_email[1]
                for coll_domains in link['coll_domains']:
                    if coll_domains.endswith(domain):
                        shared_data['with_internal_shareable_link'] = True
                    else:
                        shared_data['with_external_shareable_link'] = True

            return shared_data
        except Exception as e:
            logger.error(f"Error generate shared data, Exception: {e}")
            return None

    def file_should_be_skipped(self, filepath, filename):
        logger.debug(f"Google-flow:  _scan_skip_files: {self._scan_skip_files}, _scan_skip_paths: {self._scan_skip_paths}")
        for skip_file in self._scan_skip_files:
             if filename == skip_file or fnmatch.fnmatch(filename, skip_file):
                logger.info(f"Google-flow: file {filepath} {filename} has been skipped due to skip file {skip_file}")
                return True

        full_path = f"{filepath}/{filename}"
        for path in self._scan_skip_paths:
            if filepath.startswith(path) or fnmatch.fnmatch(full_path, path):
                logger.info(f"Google-flow: file {filepath} {filename} has been skipped due to skip path {path}")
                return True

        return False

    def _is_after_cutoff_time(
        self, last_modified_time: str, cutoff_time: datetime
    ) -> bool:
        """
        Checks if the last modified time of a file is after the cutoff time.

        Args:
            last_modified_time (str): The last modified time of the file in the format "YYYY-MM-DD HH:MM:SS".
            cutoff_time (datetime): The cutoff time.

        Returns:
            bool: True if the last modified time is after the cutoff time, False otherwise.
        """
        try:
            last_modified = datetime.strptime(last_modified_time, "%Y-%m-%d %H:%M:%S")
            timezone = pytz.timezone("UTC")
            if last_modified.tzinfo is None:
                last_modified = timezone.localize(last_modified)
            if cutoff_time.tzinfo is None:
                cutoff_time = timezone.localize(cutoff_time)
            return last_modified >= cutoff_time
        except Exception as e:
            logger.error(f"Error _is_after_cutoff_time: {e}")
            return False

    def is_download_required(
        self, folder: str, file_name: str, cutoff_time: datetime, display_path: str = None
    ) -> Tuple[int, dict]:
        """
        Determines whether a file needs to be downloaded based on various criteria.

        Args:
            folder (str): The folder path where the file is located.
            file_name (str): The name of the file.
            cutoff_time (datetime): The cutoff time for considering a file as outdated.

        Returns:
            Tuple[int, dict]: A tuple containing:
                - First int:
                    0: If the file is not required.
                    1: If the file is required.
                    2: Connection error.
                - Second dict: file extended attribute
        """
        try:
            if display_path and self.file_should_be_skipped(display_path, file_name):
                skip_files.info(f"Task {self._uuid} {display_path}/{file_name} does not require downloading, because it matches skip_files or skip_paths")
                logger.debug(f"Is download required, file be skipped: {display_path} - {file_name}")
                return 0, None

            full_path = f"{folder}/file_name"
            cache = GlobalCache(self._uuid, is_ddr=self._is_ddr)
            records, record_count = cache.query_items({"full_path": full_path})
            supported_file_types = get_supported_file_type(self._scan_file_type)
            min_size, max_size = self.get_size_limit()
            file_ext = file_name.split(".")[-1].lower()
            code, file_info = self.get_file_info(folder, file_name)
            if code == ErrorCode.CONNECTION_ERROR:
                skip_files.error(f"Task {self._uuid} {folder}/{file_name} pass, connection errors occurred while getting file info")
                return 2, None
            elif code == ErrorCode.OTHER_ERROR:
                skip_files.error(f"Task {self._uuid} {folder}/{file_name} pass, other errors occurred while getting file info")
                return 0, None

            file_size = int(file_info["size"]) / 1024  # to KB
            if file_size < min_size or file_size > max_size:
                skip_files.info(f"Task {self._uuid} {folder}/{file_name} does not require downloading, because it does not meet the file size limit")
                return 0, None
            if file_ext not in supported_file_types["ext"]:
                skip_files.info(f"Task {self._uuid} {folder}/{file_name} does not require downloading, because extention not in support file types")
                return 0, None

            if record_count != 0 and \
                (cutoff_time and not self._is_after_cutoff_time(file_info["last_modified"], cutoff_time)) and \
                (not self.is_file_attr_changed(records[0], file_info)):
                skip_files.info(f"Task {self._uuid} {folder}/{file_name} does not require downloading, because it has not been modified after the cutoff time")
                return 0, None

            finfo = {
                "file_size":file_size,
                "last_modified": file_info["last_modified"],
                "created_time": file_info["created_time"],
                "file_type": str(file_info.get("mime_type", "")),
                "collaborators": file_info["collaborators"],
                "share_link": file_info["share_link"],
                "shared_data": file_info["shared_data"],
                "user": file_info.get("user", "UNKNOWN"),
                "email": file_info.get("email", "UNKNOWN"),
                "display_path": display_path,
                "encryption": True
            }
            if "owners" in file_info:
                finfo["owners"] = file_info["owners"]
            if "create_by" in file_info:
                finfo["create_by"] = file_info["create_by"]
            return 1, finfo
        except Exception as e:
            logger.error(f"Error is_download_required: {type(e).__name__}, {e}")
            skip_files.info(f"Task {self._uuid} Error encountered during download {folder}/{file_name}")
            return 0, None

    def is_file_attr_changed(self, record, file_info):
        """
        Check if the file analysis related attr have changed.

        Args:
            record (dict): The record of the file in cache db.
            file_info (dict): The latest file info.

        Returns:
            bool: A boolean indicating if the file analysis related attr have changed.
        """
        try:
            file_attributes = record.get('file_attributes', {})
            file_metadata = record.get('file_metadata', {})
            #logger.debug(f"Is file attr changed, _scan_method: {self._scan_method}")
            #logger.debug(f"Is file attr changed, {file_attributes.get('file_display_path', '')}, {file_info.get('display_path', '')}")
            #logger.debug(f"Is file attr changed, {file_attributes.get('file_display_path', '') == file_info.get('display_path', '')}")
            #logger.debug(f"Is file attr changed, {file_metadata.get('collaborators', {})}, {file_info.get('collaborators', {})}")
            #logger.debug(f"Is file attr changed, {file_metadata.get('collaborators', {}) == file_info.get('collaborators', {})}")
            #logger.debug(f"Is file attr changed, {file_metadata.get('share_link', [])}, {file_info.get('share_link', [])}")
            #logger.debug(f"Is file attr changed, {file_metadata.get('share_link', []) == file_info.get('share_link', [])}")
            if self._scan_method != ScanMethod.FULL_SCAN and file_metadata.get('collaborators', {}) == file_info.get('collaborators', {}) and \
                file_metadata.get('share_link', []) == file_info.get('share_link', []) and \
                file_attributes.get('file_display_path', '') == file_info.get('display_path', ''):
                logger.debug("Is file attr changed, the file analysis related attr have not changed")
                return False
            logger.debug(f"Is file attr changed, the file analysis related attr have changed")
            return True
        except Exception as e:
            logger.error(f"Is file attr changed, Exception: {e}")
            return True

    def download(self, folder: str, file_name: str, task_uuid: str, download_dir: str = None
    ) -> Union[Tuple[str, str, str], Tuple[None, None, None]]:
        """
        Downloads a file from the specified `obj_uri` using the provided `client` and saves it to the `local_file_path`.

        Args:
            folder (str): The folder path where the file is located.
            file_name (str): The name of the file.
            task_uuid (str): The UUID of the task.

        Returns:
            Tuple[str, str, str]: A tuple containing the file UUID, local file path, and file URI.

        When Exception occurs:
            return None
        """
        file_uuid = str(uuid.uuid4())
        obj_uri = f"{folder}/{file_name}"
        if download_dir is None:
            if self._is_ddr:
                download_dir = configs["download_queue"]["ddr_file_download_path"]
            else:
                download_dir = file_download_dir
        local_file_path = (
            self._create_target_dir(download_dir, task_uuid) / file_uuid
        )
        try:
            folder_info = folder.split('/')
            delegated_user_email = folder_info[0]
            file_id = folder_info[2]
            delegated_credentials = self.credentials.with_subject(delegated_user_email)
            service = build('drive', 'v3', credentials=delegated_credentials)
            file_meta = service.files().get(
                fileId=file_id,
                fields="id, name, mimeType",
                supportsAllDrives=True
            ).execute()

            mime_type = file_meta['mimeType']
            if mime_type in EXPORT_MAPPINGS:
                export_mime, _ = EXPORT_MAPPINGS[mime_type]
                request = service.files().export_media(fileId=file_id, mimeType=export_mime)
            else:
                request = service.files().get_media(fileId=file_id)

            with io.FileIO(local_file_path, 'wb') as fh:
                downloader = MediaIoBaseDownload(fh, request)
                done = False
                while not done:
                    if self._session_tracker and not self._session_tracker.is_alive():
                        logger.info(f"SessionExpired, download file")
                        raise SessionExpired()
                    if self._task_tracker:
                        self._task_tracker.update_running_time()

                    _, done = downloader.next_chunk()

            return file_uuid, str(local_file_path), obj_uri
        except requests.exceptions.ConnectionError as e:
            logger.error(f"Error download, ConnectionError: {e}")
            return -1, -1, -1
        except requests.exceptions.Timeout as e:
            logger.error(f"Error download, Timeout: {e}")
            return -1, -1, -1
        except requests.exceptions.RequestException as e:
            logger.error(f"Error download, RequestException: {e}")
            return -1, -1, -1
        except Exception as e:
            logger.error(f"Error download, {e}")
            return None, None, None

    def get_subfolder_id(self, service, parent_id, subfolder_name):
        try:
            query = (
                f"'{parent_id}' in parents and "
                f"name = '{subfolder_name}' and "
                f"mimeType = 'application/vnd.google-apps.folder' and "
                f"trashed = false"
            )
            response = service.files().list(
                q=query,
                spaces="drive",
                fields="files(id, name)",
                supportsAllDrives=True
            ).execute()

            files = response.get("files", [])
            if not files:
                logger.debug(f"Can not find subfolder: {subfolder_name}")
                return None
            return files[0]["id"]
        except Exception as e:
            logger.error(f"Error get subfolder id, Exception: {e}")
            return None

    def get_shared_dirve_info(self, drive_id, get_organizer = True):
        try:
            dirve_info = {}
            delegated_credentials = self.credentials.with_subject(self.delegated_admin_email)
            drive_service = build('drive', 'v3', credentials=delegated_credentials)
            response = drive_service.drives().get(
                driveId=drive_id,
                useDomainAdminAccess=True  # important param for admin email to get shared dirve
            ).execute()
            logger.debug(f"Drive info response: {response}")
            dirve_info['name'] = response['name']
            dirve_info['id'] = drive_id

            if get_organizer:
                response = drive_service.permissions().list(
                    fileId=drive_id,
                    fields="permissions(id,emailAddress,role,type)",
                    supportsAllDrives=True,
                    useDomainAdminAccess=True
                ).execute()

                for p in response.get('permissions', []):
                    role = p.get('role')
                    if role == "organizer":
                        dirve_info['organizer_email'] = p.get('emailAddress', p.get('domain'))
                        break
            logger.debug(f"Shared dirve info: {dirve_info}")
            return dirve_info
        except Exception as e:
            logger.error(f"Error get shared dirve info, Exception: {e}")
            return None

    def get_user_info(self, user_email):
        try:
            delegated_credentials = self.credentials.with_subject(self.delegated_admin_email)
            service = build('admin', 'directory_v1', credentials=delegated_credentials)
            user = service.users().get(
                userKey=user_email,
                fields="id,primaryEmail,name/fullName"
            ).execute()

            '''
            user_info = {
                "name": user.get("name", {}).get("fullName"),
                "primaryEmail": user.get("primaryEmail"),
                "orgUnitPath": user.get("orgUnitPath"),
                "isAdmin": user.get("isAdmin"),
                "suspended": user.get("suspended"),
                "id": user.get("id")
            }
            '''
            logger.debug(f"User info: {user}")
            return user
        except Exception as e:
            logger.error(f"Get user info, Exception: {e}")
            return None

    def get_group_info_by_email(self, group_email):
        try:
            delegated_credentials = self.credentials.with_subject(self.delegated_admin_email)
            service = build('admin', 'directory_v1', credentials=delegated_credentials)
            group = service.groups().get(
                groupKey=group_email,
                fields='id,email,name'
            ).execute()

            group_info = {
                "name": group.get("name", "Unkonw"),
                "email": group.get("email", "Unkonw"),
                "id": group.get("id", "Unkonw")
            }
            logger.debug(f"Group info: {group_info}")
            return group_info
        except Exception as e:
            logger.error(f"Get group info, Exception: {e}")
            return None

    def upload(
        self, local_file: str, target_folder: str, target_filename: str, task_uuid: str
    ) -> Union[dict, None]:
        """
        Upload a local file to the specified folder.

        Args:
            local_file (str): The local file full path.
            target_folder (str): The target path.
            target_filename (str): The target filename.
            task_uuid (str): The UUID of the task.

        Returns:
            dict: The file info of the uploaded file.

        When Exception occurs:
            return None
        """
        try:
            folder_info = target_folder.split('/')
            delegated_user_email = folder_info[0]
            delegated_credentials = self.credentials.with_subject(delegated_user_email)
            service = build('drive', 'v3', credentials=delegated_credentials)

            folder_id = folder_info[1]
            if len(folder_info) == 3 and ProtectionConst.SENSITIVE_FILES_FOLDER_PREFIX.value in folder_info[2]:
                sub_folder_id = self.get_subfolder_id(service, folder_id, folder_info[2])
                if sub_folder_id is None:
                    logger.error(f"Error upload, can not find valid folder id")
                    return None
                folder_id = sub_folder_id

            folder = service.files().get(
                fileId=folder_id,
                supportsAllDrives=True,
                fields='id,name,mimeType,driveId'
            ).execute()

            mime_type, _ = guess_type(local_file)
            mime_type = mime_type or 'application/octet-stream'
            file_metadata = {
                'name': target_filename,
                'parents': [folder_id]
            }

            is_shared_drive = 'driveId' in folder
            if is_shared_drive:
                file_metadata['supportsAllDrives'] = True
                file_metadata['driveId'] = folder['driveId']
                drive_id = folder['driveId']
            else:
                drive_id = "root"

            chunk_size = 4 * 1024 * 1024
            media = MediaFileUpload(
                local_file,
                mimetype=mime_type,
                resumable=True,
                chunksize=chunk_size
            )

            request = service.files().create(
                body=file_metadata,
                media_body=media,
                supportsAllDrives=True,
                fields='id,name,size,webViewLink,parents'
            )

            response = None
            while response is None:
                status, response = request.next_chunk()
                if status:
                    logger.debug(f"Upload percent: {int(status.progress() * 100)}%")

            absolute_path = self.get_absolute_path(service, folder_id, drive_id, {})
            if absolute_path is None:
                logger.error(f"Error upload, can not get absolute path for folder: {folder_id}")
                return None

            if is_shared_drive:
                drive_id = folder['driveId']
                drive_info = self.get_shared_dirve_info(drive_id, get_organizer = False)
                if drive_info is None:
                    logger.error(f"Error upload, can not get shared dirve info for drive_id: {drive_id}")
                    return None
                catlog_folder = f"{GoogleDriveType.SHARED.value}: {drive_info['name']}({drive_id})"
            else:
                user_info = self.get_user_info(delegated_user_email)
                if user_info is None:
                    logger.error(f"Error upload, can not get user info for: {delegated_user_email}")
                    return None
                catlog_folder = f"{GoogleDriveType.PERSONAL.value}: {user_info.get('name', {}).get('fullName', 'Unknow')}({delegated_user_email})"

            full_display_path = f"{catlog_folder}{absolute_path}/{response['name']}"
            info = {
                        "full_path": f"{delegated_user_email}/{folder_id}/{response['id']}/{response['name']}",
                        "folder": f"{delegated_user_email}/{folder_id}/{response['id']}",
                        "file_name": response['name'],
                        "file_display_path": full_display_path,
                    }
            logger.debug(f"Upload file success: {target_folder} {target_filename}")
            return info
        except Exception as e:
            logger.error(f"Error upload, Exception: {e}")
            return None

    def delete_file(
        self, folder: str, file_name: str, task_uuid: str
    ) -> bool:
        """
        Delete a remote file.

        Args:
            folder (str): The folder where the file is located.
            file_name (str): The name of the file to delete.
            task_uuid (str): The UUID of the task.

        Returns:
            True.

        When Exception occurs:
            False
        """
        try:
            folder_info = folder.split('/')
            delegated_user_email = folder_info[0]
            file_id = folder_info[2]
            delegated_credentials = self.credentials.with_subject(delegated_user_email)
            service = build('drive', 'v3', credentials=delegated_credentials)
            service.files().delete(
                fileId=file_id,
                supportsAllDrives=True,
                enforceSingleParent=True
            ).execute()
            logger.debug(f"Delete file success: {file_name}")
            return True
        except Exception as e:
            logger.error(f"Error delete file, Exception: {e}")
            return False

    def check_folder_exist(
        self, folder: str, task_uuid: str
    ) -> bool:
        """
        Check if a folder exists.

        Args:
            folder (str): The folder relative path.
            task_uuid (str): The UUID of the task.

        Returns:
            True.

        When Exception occurs:
            False
        """
        try:
            logger.info(f"Check folder exist: {folder}")
            folder_info = folder.split('/')
            delegated_user_email = folder_info[0]
            delegated_credentials = self.credentials.with_subject(delegated_user_email)
            service = build('drive', 'v3', credentials=delegated_credentials)

            folder_id = folder_info[1]
            if len(folder_info) == 3 and ProtectionConst.SENSITIVE_FILES_FOLDER_PREFIX.value in folder_info[2]:
                sub_folder_id = self.get_subfolder_id(service, folder_id, folder_info[2])
                if sub_folder_id is None:
                    logger.error(f"Error check folder exist, can not find valid folder id")
                    return None
                folder_id = sub_folder_id

            file = service.files().get(
                fileId=folder_id,
                fields='id,name,mimeType,trashed',
                supportsAllDrives=True
            ).execute()
            if file.get('trashed'):
                return False
            else:
                return True
        except Exception as e:
            logger.error(f"Error check folder exist: {e}")
            return False

    def check_write_access(
        self, folder: str, task_uuid: str
    ) -> bool:
        """
        Check if a folder has write access.

        Args:
            folder (str): The folder relative path.
            task_uuid (str): The UUID of the task.

        Returns:
            True.

        When Exception occurs:
            False
        """
        test_file_local_path = "/var/log/protection_action/notification_files/default"
        test_file_name = f"{ProtectionConst.QUARANTINE_NOTIFY_FILE_PREFIX.value}google_write_test.txt"
        try:
            upload_file_info = self.upload(test_file_local_path, folder, test_file_name, task_uuid)
            if upload_file_info is None:
                logger.error(f"Google write access test failed, upload file failed")
                return False
            if self.delete_file(upload_file_info.get('folder', ''), upload_file_info.get('file_name', ''), task_uuid) == False:
                logger.error(f"Google write access test failed, delete file failed")
                return False
            logger.error(f"Google write access test success")
            return True
        except Exception as e:
            logger.error(f"Google write access test failed, Exception: {e}")
            return False

    def create_folder(
        self, folder: str, new_folder_name: str, task_uuid: str
    ) -> Union[dict, None]:
        """
        Create a new folder.

        Args:
            folder (str): The folder relative path.
            new_folder_name (str): The name of new folder.
            task_uuid (str): The UUID of the task.

        Returns:
            dict: The info of new folder.

        When Exception occurs:
            return None
        """
        try:
            logger.info(f"Create folder: {folder}, {new_folder_name}")
            folder_info = folder.split('/')
            delegated_user_email = folder_info[0]
            delegated_credentials = self.credentials.with_subject(delegated_user_email)
            service = build('drive', 'v3', credentials=delegated_credentials)
            folder_id = folder_info[1]
            folder_metadata = {
                'name': new_folder_name,
                'mimeType': 'application/vnd.google-apps.folder',
                'parents': [folder_id],
                'description': ''
            }

            new_folder = service.files().create(
                body=folder_metadata,
                fields='id,name,webViewLink,createdTime',
                supportsAllDrives=True
            ).execute()
            return new_folder
        except Exception as e:
            logger.error(f"Error create folder, Exception: {e}")
            return False

    def check_file_exist(self, file_path: str, task_uuid: str = '', conn = None, logger = logger) -> Union[bool, None]:
        """
        Check if a file exists on the server.

        Args:
            file_path (str): The path of the file to check.
            task_uuid (str): The UUID of the task.
            conn: It is only valid in smb connector.

        Returns:
            Union[bool, None]: Returns True if the file exists, False if it doesn't exist, or None if an
            error occurred.
        """
        try:
            logger.info(f"Check file exist: {file_path}")
            file_path_info = file_path.split('/')
            if len(file_path_info) != 4:
                logger.error(f"Error check file exist, invalid file_path: {file_path}")
                return False
            delegated_user_email = file_path_info[0]
            file_id = file_path_info[2]
            delegated_credentials = self.credentials.with_subject(delegated_user_email)
            service = build('drive', 'v3', credentials=delegated_credentials)
            file = service.files().get(
                fileId=file_id,
                fields='id,name,mimeType,trashed',
                supportsAllDrives=True
            ).execute()
            if file.get('trashed'):
                return False
            else:
                return True
        except HttpError as e:
            if e.resp.status == 404:
                return False
            logger.debug(f"Check file exist, HttpError: {e}")
            return None
        except Exception as e:
            logger.debug(f"Check file exist, Exception: {e}")
            return None

    def adapt_full_path_format(self, full_path):
        #"Noting to do"
        return full_path

    def adapt_skip_path_format(self, skip_path):
        try:
            path_info = skip_path.split('/')
            delegated_user_email = path_info[0]
            delegated_credentials = self.credentials.with_subject(delegated_user_email)
            service = build('drive', 'v3', credentials=delegated_credentials)
            folder_id = path_info[1]

            folder = service.files().get(
                fileId=folder_id,
                supportsAllDrives=True,
                fields='id,name,mimeType,driveId'
            ).execute()
            is_shared_drive = 'driveId' in folder

            drive_id = ""
            if is_shared_drive:
                drive_id = folder['driveId']
                drive_info = self.get_shared_dirve_info(drive_id, get_organizer = False)
                if drive_info is None:
                    logger.error(f"Error adapt skip path format, can not get shared dirve info for drive_id: {drive_id}")
                    return skip_path
                catlog_folder = f"{GoogleDriveType.SHARED.value}: {drive_info['name']}({drive_id})"
            else:
                drive_id = "root"
                user_info = self.get_user_info(delegated_user_email)
                if user_info is None:
                    logger.error(f"Error adapt skip path format, can not get user info for: {delegated_user_email}")
                    return skip_path
                catlog_folder = f"{GoogleDriveType.PERSONAL.value}: {user_info.get('name', {}).get('fullName', 'Unknow')}({delegated_user_email})"

            absolute_path = self.get_absolute_path(service, folder_id, drive_id, {})
            if absolute_path is None:
                logger.error(f"Error adapt skip path format, can not get absolute path for folder: {folder_id}")
                return skip_path

            full_display_path = f"{catlog_folder}{absolute_path}"
            logger.debug(f"Adapt skip path format success: {skip_path} to {full_display_path}")
            return full_display_path
        except Exception as e:
            logger.error(f"Error adapt skip path format, Exception: {e}")
            return skip_path

    def process_aduit_log_shared_drive_item_recycle(self, file_id, shared_drive_id, group_map, cache):
        try:
            drive_info = self.get_shared_dirve_info(shared_drive_id, get_organizer = True)
            if drive_info is None or 'organizer_email' not in drive_info:
                logger_recycle.debug(f"process_aduit_log_shared_drive_item_recycle, get shared dirve info error, skip file: {file_id}, shared_drive_id: {shared_drive_id}")
                return None
            shared_drive_name = drive_info['name']
            delegated_user_email = drive_info['organizer_email']
            delegated_credentials = self.credentials.with_subject(delegated_user_email)
            service = build('drive', 'v3', credentials=delegated_credentials)
                                            
            fields = ['id', 'name', 'mimeType', 'parents', 'driveId', 'owners']
            finfo = service.files().get(
                fileId=file_id,
                fields=','.join(fields),
                supportsAllDrives=True
            ).execute()

            if finfo.get('mimeType') == 'application/vnd.google-apps.shortcut':
                logger_recycle.debug(f"process_aduit_log_shared_drive_item_recycle, skip shortcut")
                return
            elif finfo.get('mimeType') == 'application/vnd.google-apps.folder':
                absolute_path = self.get_absolute_path(service, finfo['id'], "root", cache)
                if absolute_path is None:
                    logger_recycle.error(f"process_aduit_log_shared_drive_item_recycle, get absolute path failed, skip file {finfo}")
                    return

                catlog_folder = f"{GoogleDriveType.SHARED.value}: {shared_drive_name}({shared_drive_id})"
                full_display_path = f"{catlog_folder}{absolute_path}"                                
                if shared_drive_id not in group_map:
                    file_array = set()
                    folder_array = set()
                    folder_array.add(full_display_path)
                    group_map[shared_drive_id] = {
                        "drive_type": GoogleDriveType.PERSONAL.value,
                        "shared_drive_id": shared_drive_id,
                        "shared_drive_name": shared_drive_name,
                        "shared_drive_owner_email": delegated_user_email,
                        "file_array": file_array,
                        "folder_array": folder_array
                    }
                else:
                    group_map[shared_drive_id]["folder_array"].add(full_display_path)
            else:
                if shared_drive_id not in group_map:
                    file_array = set()
                    folder_array = set()
                    file_array.add(file_id)
                    group_map[shared_drive_id] = {
                        "drive_type": GoogleDriveType.PERSONAL.value,
                        "shared_drive_id": shared_drive_id,
                        "shared_drive_name": shared_drive_name,
                        "shared_drive_owner_email": delegated_user_email,
                        "file_array": file_array,
                        "folder_array": folder_array
                    }
                else:
                    group_map[shared_drive_id]["file_array"].add(file_id)   
        except Exception as e:
            logger_recycle.error(f"Error process_aduit_log_shared_drive_item_recycle: {e}")
            return

    def process_aduit_log_personal_drive_item_recycle(self, file_id, owner_email, group_map, cache):
        try:
            delegated_user_email = owner_email
            delegated_credentials = self.credentials.with_subject(delegated_user_email)
            service = build('drive', 'v3', credentials=delegated_credentials)
                                
            user_info = self.get_user_info(delegated_user_email)
            if user_info is None:
                logger_recycle.error(f"process_aduit_log_personal_drive_item_recycle, can not get user info for: {delegated_user_email}")
                return
            user_name = user_info.get('name', {}).get('fullName', 'Unknow')
            
            fields = ['id', 'name', 'mimeType', 'parents', 'driveId', 'owners']
            finfo = service.files().get(
                fileId=file_id,
                fields=','.join(fields),
                supportsAllDrives=True
            ).execute()

            if finfo.get('mimeType') == 'application/vnd.google-apps.shortcut':
                logger_recycle.debug(f"process_aduit_log_personal_drive_item_recycle, skip shortcut")
                return
            elif finfo.get('mimeType') == 'application/vnd.google-apps.folder':
                absolute_path = self.get_absolute_path(service, finfo['id'], "root", cache)
                if absolute_path is None:
                    logger_recycle.error(f"process_aduit_log_personal_drive_item_recycle, get absolute path failed, skip file {finfo}")
                    return

                catlog_folder = f"{GoogleDriveType.PERSONAL.value}: {user_name}({delegated_user_email})"
                full_display_path = f"{catlog_folder}{absolute_path}"                                
                if delegated_user_email not in group_map:
                    file_array = set()
                    folder_array = set()
                    folder_array.add(full_display_path)
                    group_map[delegated_user_email] = {
                        "drive_type": GoogleDriveType.PERSONAL.value,
                        "user_name": user_name,
                        "user_email": delegated_user_email,
                        "file_array": file_array,
                        "folder_array": folder_array
                    }
                else:
                    group_map[delegated_user_email]["folder_array"].add(full_display_path)                   
            else:
                if delegated_user_email not in group_map:
                    file_array = set()
                    folder_array = set()
                    file_array.add(file_id)
                    group_map[delegated_user_email] = {
                        "drive_type": GoogleDriveType.PERSONAL.value,
                        "user_name": user_name,
                        "user_email": delegated_user_email,
                        "file_array": file_array,
                        "folder_array": folder_array
                    }
                else:
                    group_map[delegated_user_email]["file_array"].add(file_id)    
        except Exception as e:
            logger_recycle.error(f"Error process_aduit_log_personal_drive_item_recycle: {e}")
            return
    
    def process_aduit_log_item_recycle(self, item_params, group_map, cache):
        try:
            if 'owner_is_shared_drive' not in item_params or 'doc_id' not in item_params:
                logger_recycle.error(f"process_aduit_log_item_recycle, can not find valid param, {item_params}")
                return
            file_id = item_params['doc_id']

            if item_params.get('owner_is_shared_drive') == True:
                if 'shared_drive_id' not in item_params:
                    logger_recycle.error(f"process_aduit_log_item_recycle, can not find shared_drive_id in param")
                    return
                self.process_aduit_log_shared_drive_item_recycle(file_id, item_params['shared_drive_id'], group_map, cache)               
            else:
                if 'owner' not in item_params:
                    logger_recycle.error(f"process_aduit_log_item_recycle, can not find owner in param")
                    return
                self.process_aduit_log_personal_drive_item_recycle(file_id, item_params['owner'], group_map, cache)                    
        except Exception as e:
            logger_recycle.error(f"Error process_aduit_log_item_recycle: {e}")
            return

    def generate_changed_recycle_groups(self, events):
        try:
            group_map = {}
            process_events = {}
            cache = {}

            for event in events:
                id = event.get('id', {})
                event_id=f"{id.get('applicationName', 'drive')}{id.get('uniqueQualifier', '0')}"
                logger_recycle.debug(f"Google-flow event id: {id}, event_id: {event_id}")

                event_finished = get_connector_event_collection_history(scan_policy_id=self._uuid, connector_type=StorageType.GOOGLE, collection_type=ConnectorCollectionType.SCAN, event_id=event_id)
                if event_finished is not None or event_id == 'drive-0':
                    logger_recycle.debug(f"Google-flow this event has been processed, skip it, event id: {event_id}")
                    continue
                
                items = event.get('events', [])
                for item in items:
                    operation = item.get("name", "NA")                                   
                    if operation in ["trash"]:
                        parameters = item.get("parameters", [])
                        item_params = {}
                        for p in parameters:
                            if p.get('name', 'NA') == 'owner_is_shared_drive':
                                item_params['owner_is_shared_drive'] = p.get('boolValue')
                            elif p.get('name', 'NA') == 'shared_drive_id':
                                item_params['shared_drive_id'] = p.get('value')
                            elif p.get('name', 'NA') == 'owner':
                                item_params['owner'] = p.get('value')
                            elif p.get('name', 'NA') == 'doc_id':
                                item_params['doc_id'] = p.get('value')
                        self.process_aduit_log_item_recycle(item_params, group_map, cache)
                    else:
                        logger_recycle.error(f"Unknown operation: {operation}")

                event_info = id
                process_events[event_id] = event_info

            logger_recycle.debug(f"Google-flow: group_map: {group_map}")
            return group_map, process_events
        except Exception as e:
            logger_recycle.error(f"Error generate_changed_recycle_groups: {e}")
            return None, {}

    def check_file_quarantine(self, record, task_uuid):
        try:
            # Read record again to get the latest QuarantineStatus
            cache = GlobalCache(task_uuid, is_ddr=self._is_ddr)
            record = cache.get_by_file_uuid(record.get("id"))
            quarantineStatus = (record.get('reserve_json3') or {}).get('quarantine_status', ProtectionQuarantineStatus.INIT)
            if quarantineStatus in [ProtectionQuarantineStatus.DOING, ProtectionQuarantineStatus.SUCCESS]:
                logger_recycle.info(f"file have been quarantined or quarantining, no need to delete in cache, record: {record}")
                return True

            return False
        except Exception as e:
            logger_recycle.error(f"Error check file quarantine: {e}")
            return False

    def check_folder_support(self, catlog_folder, logger):
        try:
            if (self._scan_scope == ScanScope.ALL_FOLDERS and catlog_folder not in self._excluded_folders) or \
                (self._scan_scope == ScanScope.SELECTED_FOLDERS and catlog_folder in self._selected_folders):
                return True
            return False
        except Exception as e:
            logger.error(f"Error check folder support: {e}")
            return True

    def recycle(self, task_uuid: str) -> bool:
        """
        Recycles a task global cache identified by its UUID.

        Args:
            task_uuid (str): The UUID of the scan task.

        Returns:
            bool: True if the task was successfully recycled, False otherwise.

        When Exception occurs:
            return False
        """
        def process_record(record, conn=None, **kwargs) -> bool:
            try:
                full_path = record.get("full_path")
                file_uuid = record.get("id")
                file_attr = record.get("file_attributes", {})
                file_display_path = file_attr.get("file_display_path", "NA")
                file_ext = file_attr.get("file_ext")
                file_size = file_attr.get("file_size")
                catlog_folder = file_attr.get("catlog_folder")
                min_size, max_size = self.get_size_limit()
                supported_file_types = get_supported_file_type(self._scan_file_type)

                if ((file_ext and file_ext not in supported_file_types["ext"])
                    or (file_size and not (min_size <= file_size <= max_size))
                    or self.check_folder_support(catlog_folder, logger_recycle) == False):

                    if self.check_file_quarantine(record, task_uuid) == False:
                        cache.delete(file_uuid)
                        logger_recycle.debug(f"Folder or File type and size not support: {full_path} {record} deleted from cache.")
                        time.sleep(configs["recycle_backoff_time"])
                        return True

                changed_groups = kwargs.get("changed_groups", None)
                if changed_groups is not None:
                    group_info = self.get_group_info_from_catlog_folder(catlog_folder, logger_recycle, extended_info=False)
                    if group_info:
                        if group_info['drive_type'] == GoogleDriveType.PERSONAL.value:
                            primary_keyword = group_info['user_email']
                        else:
                            primary_keyword = group_info['shared_drive_id']
                        changed_group = changed_groups.get(primary_keyword, {})   
                        file_array = changed_group.get("file_array", None)
                        if file_array is not None:
                            file_path_info = full_path.split('/')
                            if len(file_path_info) == 4:
                                file_id = file_path_info[2]
                                if file_id in file_array:
                                    if self.check_file_exist(full_path, logger=logger_recycle) is False and self.check_file_quarantine(record, task_uuid) == False:
                                        cache.delete(file_uuid)
                                        time.sleep(configs["recycle_backoff_time"])
                                        logger_recycle.debug(f"File not exist: {file_display_path} {record} deleted from cache by file event.")
                                        return True
                        folder_array = changed_group.get("folder_array", None)
                        if folder_array is not None:
                            for folder in folder_array:
                                if file_display_path.startswith(folder + '/'):
                                    if self.check_file_exist(full_path, logger=logger_recycle) is False and self.check_file_quarantine(record, task_uuid) == False:
                                        cache.delete(file_uuid)
                                        time.sleep(configs["recycle_backoff_time"])
                                        logger_recycle.debug(f"File not exist: {file_display_path} {record} deleted from cache by folder event")
                                        return True
                else:
                    if self.check_file_exist(full_path, logger=logger_recycle) is False:
                        if self.check_file_quarantine(record, task_uuid) == False:
                            cache.delete(file_uuid)
                            logger_recycle.debug(f"File not exist: {full_path} {record} deleted from cache.")
                            time.sleep(configs["recycle_backoff_time"])
                return True
            except Exception as e:
                logger_recycle.error(f"Process record, Exception: {e}")
                return False

        def recycle_check_all_files():
            try:
                cache.process_records(process_record)
            except Exception as e:
                logger_recycle.error(e)
                return False

        try:
            logger_recycle.debug(f"Recycle task({task_uuid}) begin.")
            cache = GlobalCache(task_uuid, is_ddr=self._is_ddr)
            if self.audit_log_monitoring == True:
                if self.is_time_to_fetch_all_files(logger_recycle, ConnectorCollectionType.RECYCLE, self.all_files_fetch_recycle_min_interval_count):
                    logger_recycle.debug(f"Recycle, do all files recycle, reason is: reach max_recycle_by_audit_log_count")
                    self.supplement_event_collection_history(ConnectorCollectionType.RECYCLE)
                    recycle_check_all_files()
                else:
                    changed_groups = None
                    event_history = get_connector_event_collection_history(scan_policy_id=self._uuid, connector_type=StorageType.GOOGLE, collection_type=ConnectorCollectionType.RECYCLE)
                    if event_history is not None:
                        start_time = event_history.last_event_time
                        _now = datetime.now(timezone.utc)
                        target_operations = ["trash"]
                        logger_recycle.debug(f"Recycle, do event files recycle, start time: {start_time.isoformat()}, end time: {_now.isoformat()}")
                        events,first_event_time,last_event_time = self.fetch_audit_events(start_time, _now, target_operations, logger_recycle, check_session=False)
                        logger_recycle.debug(f"Recycle, fetch event counts: {len(events) if events else 0}, first event time: {first_event_time.isoformat() if first_event_time else 'None'}, last_event_time: {last_event_time.isoformat() if last_event_time else 'None'}")
                        if events is not None:
                            changed_groups, process_events = self.generate_changed_recycle_groups(events)
                            if first_event_time is None:
                                first_event_time = start_time
                            if last_event_time is None:
                                last_event_time = start_time
                            payload = {
                                "scan_policy_id": self._uuid,
                                "connector_type": StorageType.SHAREPOINT_OL,
                                "collection_type": ConnectorCollectionType.RECYCLE,
                                "event_count": len(process_events),
                                "events": process_events,
                                "first_event_time": first_event_time,
                                "last_event_time": last_event_time,
                                "created_at": datetime.now()
                            }
                            create_connector_event_collection_history(payload)
                            cache.process_records(process_record, changed_groups=changed_groups)
                        else:
                            logger_recycle.debug(f"Recycle, do all files recycle, reason is: get events log error")
                            self.supplement_event_collection_history(ConnectorCollectionType.RECYCLE)
                    else:
                        # first time recycle
                        self.supplement_event_collection_history(ConnectorCollectionType.RECYCLE)
                        #recycle_check_all_files()
            else:
                recycle_check_all_files()
            return True
        except Exception as e:
            logger_recycle.error(e)
            return False
        finally:
            logger_recycle.debug(f"Recycle task({task_uuid}) finished.")

    def get_folders(self) -> Union[List, None]:
        """
        Get the list of folders in the storage.

        Returns:
            List: A list of folders in the storage.

        When Exception occurs:
            return None
        """
        try:
            drives = []
            users, _ = get_storage_identity(conditions={'sid': self._scan_storage_id, 'identity_type':IdentityType.PEOPLE})
            for user in users:
                if 'name' in user and 'email' in user:
                    drives.append(f"{GoogleDriveType.PERSONAL.value}: {user['name']}({user['email']})")

            shared_drives, _ = get_storage_shared_drives(conditions={'sid': self._scan_storage_id})
            for shared_drive in shared_drives:
                if 'name' in shared_drive and 'identifier' in shared_drive:
                    drives.append(f"{GoogleDriveType.SHARED.value}: {shared_drive['name']}({shared_drive['identifier']})")
            return sorted(drives)
        except Exception as e:
            logger.error(f"Error get_folders: {e}")
            return None

    def move_file(
        self, folder: str, file_name: str, target_folder: str, target_filename: str, task_uuid: str, local_file: str = None
    ) -> Union[dict, None]:
        """
        Move a file to the specified folder.

        Args:
            folder (str): The folder where the file is located.
            file_name (str): The name of the file to move.
            target_folder (str): The target path.
            target_filename (str): The target filename.
            task_uuid (str): The UUID of the task.

        Returns:
            dict: The file info of the moved file.

        When Exception occurs:
            return None
        """
        pass

    def copy_file(
        self, folder: str, file_name: str, target_folder: str, target_filename: str, task_uuid: str, local_file: str = None
    ) -> Union[dict, None]:
        """
        Copy a file to the specified folder.

        Args:
            folder (str): The folder where the file is located.
            file_name (str): The name of the file to move.
            target_folder (str): The target path.
            target_filename (str): The target filename.
            task_uuid (str): The UUID of the task.

        Returns:
            dict: The file info of the copied file.

        When Exception occurs:
            return None
        """
        pass

    def update_file_tag(self, file_uri: str, tags: List[Dict[str, str]]) -> bool:
        """
        Update the tags of a file identified by its URI.

        Args:
            file_uri (str): The URI of the file to update.
            tags (List[Dict[str, str]]): The new tags to assign to the file.

        Returns:
            bool: A boolean indicating if the tags are updated successfully.
                True if successful, False otherwise.

        When Exception occurs:
            return False
        """
        pass

    def is_download_required_for_config_file(self, folder: str, file_name: str) -> Tuple[bool, int, Dict[str, str]]:
        """
        Determines whether a download is required for a given configuration file.

        Args:
            folder (str): The folder where the file is located.
            file_name (str): The name of the file.

        Returns:
            Tuple[bool, int]: A tuple containing:
                - First bool:
                    False: If the file is not required.
                    True: If the file is required.
                - Second int: file size in KB
        """
        pass

    def format_user_info(self, user):
        user_format = {
                       'id': user.get('id', 'Unknown'),
                       'mail': user.get('primaryEmail', 'Unknown'),
                       'displayName': user.get('name', {}).get('fullName', 'Unknown'),
                       'userType': "Member"
                    }
        return user_format

    def fetch_users(self):
        try:
            delegated_credentials = self.credentials.with_subject(self.delegated_admin_email)
            service = build('admin', 'directory_v1', credentials=delegated_credentials)

            users = []
            page_token: Optional[str] = None
            while True:
                response = service.users().list(
                    customer=self.customer_id,
                    maxResults=100,
                    orderBy='email',
                    pageToken=page_token
                ).execute()

                format_items = []
                items = response.get('users', [])
                for item in items:
                    f_item = self.format_user_info(item)
                    format_items.append(f_item)
                users.extend(format_items)

                page_token = response.get('nextPageToken')
                if not page_token:
                    break

            return users
        except Exception as e:
            logger.error(f'Fetch users, Exception: {e}')
            return []

    def format_group_info(self, group, service=None):
        try:
            group_info = {
                'displayName': group.get('name', 'Unknown'),
                'mail': group.get('email', 'Unknown'),
                'id': group.get('id', 'Unknown'),
                'userType': "Member"
            }

            if service is None:
                delegated_credentials = self.credentials.with_subject(self.delegated_admin_email)
                service = build('admin', 'directory_v1', credentials=delegated_credentials)

            admin_email = self.delegated_admin_email.split('@')
            domain = admin_email[1]
            members = []
            page_token = None
            while True:
                members_result = service.members().list(
                    groupKey=group['email'],
                    maxResults=100,
                    pageToken=page_token
                ).execute()
                members.extend(members_result.get('members', []))
                page_token = members_result.get('nextPageToken')
                if not page_token:
                    break

            for member in members:
                if 'email' in member and not member['email'].endswith(domain):
                    group_info['userType'] = "Guest"
                    break
            return group_info
        except Exception as e:
            logger.error(f'Format group info, Exception: {e}')
            return None

    def fetch_groups(self):
        try:
            delegated_credentials = self.credentials.with_subject(self.delegated_admin_email)
            service = build('admin', 'directory_v1', credentials=delegated_credentials)
            admin_email = self.delegated_admin_email.split('@')
            domain = admin_email[1]

            groups = []
            page_token = None
            while True:
                result = service.groups().list(
                    domain=domain,
                    customer=self.customer_id,
                    maxResults=100,
                    orderBy='email',
                    pageToken=page_token
                ).execute()

                format_items = []
                items = result.get('groups', [])
                for item in items:
                    f_item = self.format_group_info(item)
                    if f_item is None:
                        logger.error(f'Fetch groups, format group error, gourp: {item}')
                        continue
                    format_items.append(f_item)
                groups.extend(format_items)
                page_token = result.get('nextPageToken')
                if not page_token:
                    break

            return groups
        except Exception as e:
            logger.error(f'Fetch groups, Exception: {e}')
            return []

    def fetch_shared_drives(self):
        try:
            delegated_credentials = self.credentials.with_subject(self.delegated_admin_email)
            service = build('drive', 'v3', credentials=delegated_credentials)
            shared_drives = []
            page_token = None
            while True:
                result = service.drives().list(
                    pageSize=100,
                    pageToken=page_token,
                    useDomainAdminAccess=True,
                    fields="drives(id,name), nextPageToken"
                ).execute()

                shared_drives.extend(result.get('drives', []))
                page_token = result.get('nextPageToken')
                if not page_token:
                    break

            for drive in shared_drives:
                permissions = service.permissions().list(
                    fileId=drive['id'],
                    fields="permissions(emailAddress,role)",
                    supportsAllDrives=True,
                    useDomainAdminAccess=True
                ).execute()

                managers = [
                    perm['emailAddress']
                    for perm in permissions.get('permissions', [])
                    if perm.get('role') == 'organizer'
                ]
                drive['organizer_email'] = managers[0] if managers else None

            return [{
                'id': drive.get('id'),
                'name': drive.get('name'),
                'organizer_email': drive.get('organizer_email')
            } for drive in shared_drives]

        except Exception as e:
            logger.error(f'Fetch shared drives, Exception: {e}')
            return []

    def fetch_logs(self, start_time: datetime, end_time: datetime, **kwargs) -> List[dict]:
        """
        Fetch and convert Google Drive events to Storage activity dict list.
        Args:
            start_time: Start time for fetching events
            end_time: End time for fetching events
            **kwargs: Additional parameters

        Returns:
            List of Storage Activity dict ready for database storage
        """
        try:
            # Get target operations from kwargs or use all supported operations
            target_operations = kwargs.get('target_operations')
            if not target_operations:
                # Use all operations that have mappings
                target_operations = self.target_operations

            fetch_storage_log.info(f"Fetching Google Drive events for operations: {target_operations}")

            # TODO: Google Drive doesn't have built-in event logging like AWS CloudTrail
            # This would need to be implemented based on Google Drive API change tracking
            # For now, return empty list as placeholder
            # for event in events:
            #     ddr_activity = self._convert_google_record(event, event_mappings)
            #     if ddr_activity:
            #         ddr_activities.append(ddr_activity)
            fetch_storage_log.info("Google Drive DDR events not yet implemented - requires Drive API integration")
            return []

        except Exception as e:
            fetch_storage_log.error(f"Error fetching audit events from Google Drive: {e}")
            return []

    def _convert_google_record(self, audit_event: Dict[str, Any], target_operations: List[str]) -> Optional[dict]:
        """
        Convert Google Drive audit event to Storage Activity dict using strict field mapping.
        Args:
            audit_event: Raw Google Drive audit event
            target_operations: List of target operations
        Returns:
            Storage Activity dict or None if conversion fails
        """
        try:
            # TODO: Need to modify to check if the operation is monitored
            event_type = audit_event.get("events", [])[0].get("name", "")
            if event_type not in target_operations:
                fetch_storage_log.debug(f"Operation {event_type} is not monitored for Storage Activity")
                return None

            # event_time from CreationTime
            event_time = datetime.now(timezone.utc)
            event_time_str = audit_event.get("id", {}).get("time", "")
            if event_time_str:
                try:
                    event_time = datetime.fromisoformat(event_time_str.replace('Z', '+00:00'))
                except Exception:
                    pass

            activity = {
                "storage_type": StorageType.GOOGLE.value,
                "storage_id": self._scan_storage_id,
                "event_time": event_time,
                "event_type": event_type,
                "event_id": "", # TODO: Get event id from Google Drive audit event
                "scan_policy_fields": self._parse_for_scan_policy_fields(audit_event),
                "ddr_fields": self._parse_for_ddr_fields(audit_event),
                "raw_data": audit_event,
                "scan_triggered": False,
                "status": self._parse_for_status(audit_event),
                "reserve_json": {}
            }
            return activity
        except Exception as e:
            fetch_storage_log.error(f"Error converting Google Drive audit event to Storage Activity: {e}")
            return None

    def _parse_for_scan_policy_fields(self, audit_event: Dict[str, Any]) -> Optional[dict]:
        """
        Parse the audit event for scan policy fields.
        Args:
            audit_event: Raw Google Drive audit event
        Returns:
            Dict of scan policy fields or {} if parsing fails
        """
        try:
            return {}
        except Exception as e:
            fetch_storage_log.error(f"Error parsing scan policy fields from Google Drive audit event: {e}")
            return {}

    def _parse_for_ddr_fields(self, audit_event: Dict[str, Any]) -> Optional[dict]:
        """
        Parse the audit event for DDR fields.
        Args:
            audit_event: Raw Google Drive audit event
        Returns:
            Dict of DDR fields or {} if parsing fails
        """
        try:
            return {}
        except Exception as e:
            fetch_storage_log.error(f"Error parsing DDR fields from  Google Drive audit event: {e}")
            return {}

    def _parse_for_status(self, audit_event: Dict[str, Any]) -> Optional[int]:
        """
        Parse the audit event for status.
        Args:
            audit_event: Raw Google Drive audit event
        Returns:
            Status or 0 if parsing fails
        """
        try:
            return 0
        except Exception as e:
            fetch_storage_log.error(f"Error parsing status from Google Drive audit event: {e}")
            return 0
