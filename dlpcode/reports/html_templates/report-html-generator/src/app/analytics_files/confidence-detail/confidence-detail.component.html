<div class="dialog-content">
  <div class="header title-panel">
    <span>{{ 'sensitive_data.confidence_detail' | translate }}</span>
    <button mat-icon-button (click)="close()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <mat-divider></mat-divider>

  <div class="config-panel">
    <div class="info-container">
      <div class="info">
        <div class="title">
          {{ 'sensitive_data.data_type' | translate }}
        </div>
        <div class="content" *ngIf="conData?.type == 1">
          {{ conData?.sub_category }}
        </div>
        <div class="content" *ngIf="conData?.type == 2">
          {{ conData?.custom_dt }}
        </div>
      </div>

      <div class="info" *ngIf="conData?.type == 1">
        <div class="title">
          {{ 'sensitive_data.category' | translate }}
        </div>
        <div class="content">
          {{ getCategoryLabel() }}
        </div>
      </div>
    </div>

    <div class="confidence-container">
      <common-table *ngIf="tableSetting" [settings]="tableSetting" [values]="table_data"></common-table>
    </div>
  </div>

  <mat-divider></mat-divider>

  <div class="footer">
    <button mat-stroked-button (click)="close()" class="cancel-btn">
      {{ 'close' | translate | uppercase }}
    </button>
  </div>
</div>
