import { TableComponent } from '@/common/table/table/table.component';
import { Component, Inject, Input, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DataTypeService } from '../data_type.service';

@Component({
  selector: 'app-analytics-confidence-detail',
  templateUrl: './confidence-detail.component.html',
  styleUrls: ['./confidence-detail.component.scss']
})
export class ConfidenceDetailComponent {
  @Input() conData: any;
  @ViewChild(TableComponent) table!: TableComponent;

  table_data: any[];
  tableSetting: any;

  constructor(
    private dataTypeService: DataTypeService,
    public dialogRef: MatDialogRef<ConfidenceDetailComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.conData = data;
    console.log(this.conData);

    this.table_data = this.conData?.data || [];

    this.initTable();
  }

  initTable() {
    this.tableSetting = {
      tableId: 'confidence_detail_table',
      hasMenuBar: false,
      hasSelect: false,
      isClientSidePagination: true,
      columns: [
        {
          id: 'text',
          langKey: 'sensitive_data.data_preview',
          cellFormatter: (row: any) => {
            const MAX_LENGTH = 60;
            let len_limit = MAX_LENGTH;

            let text = row?.text;
            const reg = /(.*?)<#wVxJ8341#>(.*?)<#\/wVxJ8341#>/g;

            let matches = [];
            let match;

            while ((match = reg.exec(text)) != null) matches.push({ text: match[1], length: parseInt(match[2]) });

            if (matches.length) len_limit = Math.ceil(MAX_LENGTH / matches.length);

            console.log('matches:', matches, len_limit);

            // fix bug 1103565
            let strArr = [];
            matches.forEach((m) => {
              strArr.push(m.text);
              strArr.push('******');
            });

            return strArr.join('');

            /*
            let children = [];

            matches.forEach(m => {
              let length = m.length;
              if(length > len_limit)
                length = len_limit;

              let strLen = m.text.length;
              let maskLen = length - strLen;

              // check whether have split char
              if(' -_'.indexOf(m.text[0]) != -1)
                maskLen += 1;

              if(maskLen < 0)
                maskLen = 0;

              children.push(elem('div', {
                innerText: m.text
              }, {
                attributes: {
                  style: 'white-space: pre;'
                }
              }))

              if(maskLen > 0)
                children.push(elem('div', {}, {
                  attributes: {
                    style: `background-color: black; width: ${maskLen*8}px;`
                  }
                }))
            })

            let ret = elem('div', {}, {
              attributes: {
                style: 'display: flex;'
              },
              children
            })

            return ret;
            */
          }
        }
      ]
    };

    if (this.conData.type == 1)
      this.tableSetting.columns.push({
        id: 'confidence',
        langKey: 'sensitive_data.confidence_score'
      });
  }

  getCategoryLabel() {
    let category = this.conData?.category || [];

    let labels = category.map((c) => {
      return this.dataTypeService.getCategoryLabel(c);
    });

    return labels.toString();
  }

  close() {
    this.dialogRef.close();
  }
}
