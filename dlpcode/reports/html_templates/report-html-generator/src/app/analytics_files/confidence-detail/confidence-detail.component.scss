.dialog-content {
    overflow-y: hidden;
    height: 99%;
    display: flex;
    flex-direction: column;
}

.title-panel {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
    font-size: 16px;
    margin-left: 20px;
  
    span {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
    }
}

.config-panel {
    width: 100%;
    padding: 10px 20px 10px 20px;
    flex: 1;
    overflow-y: hidden;
    background: #fff;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .info-container {
        display: flex;
        gap: 15px;

        .info {
            display: flex;
            flex-direction: column;
            width: 300px;

            .title {
                font-weight: bold;
            }

            .content {
                word-wrap: break-word;
                white-space: wrap;
            }
        }
    }

    .confidence-container {
        flex: 1;
        overflow-y: auto;
    }
}

.footer {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: flex-end;
    height: 48px;
    padding-left: 20px;
    padding-right: 20px;
    gap: 20px;
}