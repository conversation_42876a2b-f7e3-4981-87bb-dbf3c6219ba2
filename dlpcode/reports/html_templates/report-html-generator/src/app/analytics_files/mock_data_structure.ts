export interface IReportInfo {
  name: string;
  /** When the report generated */
  generated_on: string;
  schedule: string;
  period: string;
  created_by: string;
  notes: string;
  scan_name: string;
  scan_storage: string;
  storage_type: number;
  /** IANA format */
  timezone: string;
}

export interface IMainCategory {
  main_class_id: string;
  main_class_name: string;
  discarded?: boolean;
}

export interface ISubClass {
  main_class_name?: string;
  sub_class_id: number | string;
  sub_class_name: string;
  discarded?: boolean;
  description: string;
}

export interface ISubCategory {
  [key: string]: {
    main_class_id: string;
    main_class_name: string;
    discarded?: boolean;
    sub_class_list: { [key: string]: ISubClass };
  };
}

export interface IScan {
  name: string;
  id: string;
  target: string;
  description: string;
  storage_type: number;
  copy: boolean;
  quarantine: boolean;
}

export interface ITypeCategory {
  id: string;
  name: string;
  description: string;
}

export interface IStandardDataType {
  continent: string[];
  create_time: number;
  data_type: string;
  description: string;
  entity: string;
  id: string;
  language: string[];
  region: string[];
  type: string;
  type_category: string[];
  update_time: number;
}

export interface ICustomDataType {
  created_at: number;
  definition: {
    keywords: [
      {
        keyword: string;
      }
    ];
    patterns: [
      {
        regex: string;
      }
    ];
  };
  description: string;
  discover_policy_cnt: number;
  dlp_policy_cnt: number;
  edm_cnt: number;
  group_uuid: string;
  id: string;
  name: string;
  updated_at: number;
}

export interface IFile {
  table_data: any[];
  /** report info */
  report_info: IReportInfo;
  /** supported standard type categories */
  supported_type_categories: { [key: string]: ITypeCategory[] };
  /** standard data types */
  standard_data_type: IStandardDataType[];
  /** custom data types */
  custom_data_type: ICustomDataType[];
  /** Main ML categories */
  main_categories: IMainCategory[];
  /** Sub ML categories */
  sub_categories: ISubCategory;
  /** used to generate PDF format report */
  is_printing: boolean;
  /** Sensitive data details */
  sensitive_data_details: any;
  /** For CSV column mapping */
  column_names: any;
  /** Storages summary */
  storage_profiles_summary: any[];
}

export enum EScanStorageType {
  aws = 1,
  cloud = 2,
  prem = 3,
  smb = 4
}

export enum CopyQuarantineStatus {
  Init,
  Success,
  Failed,
  InProgress
}

export interface IAnalyticsFileEntry {
  custom_match_count: number;
  endpoint: string;
  file_attributes: any;
  file_tag: { custom: any[]; ml: any[]; predefine: any[] };
  file_uuid: string;
  filename: string;
  full_path: string;
  id: string;
  main_class_confidence: number;
  main_class_id: string;
  standard_match_count: number;
  sub_class_confidence: number;
  sub_class_id: string;
  storage_type: number;
  storage: string;
  storage_id: string;
  update_time: number;
  quarantine_status: CopyQuarantineStatus;
  copy_status: CopyQuarantineStatus;
  scan_id: string;
  collaborators: string[];
  owner: string[];
  links: {
    public?: string[];
    internal?: string[];
    external?: string[];
  };
}
