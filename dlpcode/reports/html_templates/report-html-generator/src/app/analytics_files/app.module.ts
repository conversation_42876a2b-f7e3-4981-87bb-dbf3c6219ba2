import { NotifyModule } from '@/common/notify/notify.module';
import { CommonTableModule } from '@/common/table/table.module';
import { TranslateService } from '@/common/translate/translate.service';
import { registerNativeElements, setDiInstance } from '@/common/util/native-element';
import { MaterialModule } from '@/material.module';
import { APP_INITIALIZER, Injector, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { HighchartsChartModule } from 'highcharts-angular';
import { Observable, of } from 'rxjs';
import { AppComponent } from './app.component';
import { ConfidenceDetailComponent } from './confidence-detail/confidence-detail.component';
import { FileDetailComponent } from './file-detail/file-detail.component';
import { TRANSLATIONS_EN } from './i18n_en';

class InlineTranslateLoader implements TranslateLoader {
  getTranslation(lang: string): Observable<any> {
    switch (lang) {
      case 'en':
        return of(TRANSLATIONS_EN);
      default:
        return of({});
    }
  }
}

function appInitializerFactory(translateService: TranslateService) {
  return () => translateService.switchLanguage('en');
}

@NgModule({
  declarations: [ConfidenceDetailComponent, FileDetailComponent, AppComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    MaterialModule,
    NotifyModule,
    CommonTableModule,
    HighchartsChartModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useClass: InlineTranslateLoader
      }
    })
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService],
      multi: true
    }
  ],
  bootstrap: [AppComponent]
})
export class AppModule {
  constructor(private injector: Injector) {
    let params = { injector: this.injector };
    setDiInstance(params);
    registerNativeElements(params);
  }
}
