import { NotifyService } from '@/common/notify/service/notify.service';
import { elem } from '@/common/table/element';
import { TableSettings } from '@/common/table/table';
import { TableComponent } from '@/common/table/table/table.component';
import { TranslateService } from '@/common/translate/translate.service';
import { DateUtilService } from '@/service/date-util.service';
import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, Output, SimpleChanges, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { isNil, keys } from 'lodash';
import { ConfidenceDetailComponent } from '../confidence-detail/confidence-detail.component';
//import { FileLabelsConfigurationComponent } from '../file-labels-configuration/file-labels-configuration.component';
import { AppGlobalService } from '../app-global.service';
import { DataTypeService } from '../data_type.service';
import { CopyQuarantineStatus } from '../mock_data_structure';

@Component({
  selector: 'app-analytics-file-detail',
  templateUrl: './file-detail.component.html',
  styleUrls: ['./file-detail.component.scss']
})
export class FileDetailComponent {
  @Input() data: any;

  @Output() closeDetail = new EventEmitter<void>();
  @Output() updateData = new EventEmitter<any>();

  @ViewChild('standard_table') standard_table!: TableComponent;
  @ViewChild('custom_table') custom_table!: TableComponent;
  @ViewChild('labels_table') labels_table!: TableComponent;
  @ViewChild('collaborators_table') collaborators_table!: TableComponent;
  @ViewChild('actions_table') actions_table!: TableComponent;
  @ViewChild('idmMatch_table') idmMatch_table!: TableComponent;
  @ViewChild('edmMatch_table') edmMatch_table!: TableComponent;

  @ViewChild('detailForm')
  detailFormElement: ElementRef;

  title: string = '';

  standard_data: any[] = [];
  custom_data: any[] = [];
  labels_data: any[] = [];
  collaborators_data: any[] = [];
  actions_data: any[] = [];
  idm_data: any[] = [];
  edm_data: any[] = [];

  standard_total: number = 0;
  custom_total: number = 0;
  labels_total: number = 0;
  collaborators_total: number = 0;
  actions_total: number = 0;

  grouped_labels: any = {};

  standardTableSetting: TableSettings;
  customTableSetting: TableSettings;
  labelsTableSetting: TableSettings;
  collaboratorsTableSetting: TableSettings;
  actionsTableSetting: TableSettings;
  idmMatchTableSetting: TableSettings;
  edmMatchTableSetting: TableSettings;

  hasLabelAccess: boolean;

  loading = true;

  get isPrinting() {
    return this.globalService.isPrinting;
  }

  constructor(
    private notifyService: NotifyService,
    private translateService: TranslateService,
    private dateUtilService: DateUtilService,
    private dialog: MatDialog,
    private globalService: AppGlobalService,
    private dataTypeService: DataTypeService,
    private cdf: ChangeDetectorRef
  ) {}

  getMainClassName(value: string): string {
    return this.dataTypeService.getMainCategoryLabel(value);
  }

  getSubClassName(value: string): string {
    return this.dataTypeService.getSubCategoryLabel(value);
  }

  convertTime() {
    if (!this.data.update_time) return '';

    return this.dateUtilService.convertToGMTTimestamp(this.data.update_time, this.globalService.timezone);
  }

  getFullPath() {
    if (!this.data) return '';

    if (this.data.storage_type == 1) return `s3:/${this.data.full_path}`;

    if (!isNil(this.data?.file_attributes?.file_display_path) && this.data.file_attributes.file_display_path !== 'N/A') {
      return this.data.file_attributes?.file_display_path;
    }

    return this.data.full_path;
  }

  initTables() {
    if (!this.standardTableSetting) {
      this.standardTableSetting = {
        tableId: 'sensitive_data_detail_standard_table',
        pageSize: 10,
        pageSizeOptions: [10, 20],
        hasMenuBar: false,
        hasSelect: false,
        hasPaginator: !this.isPrinting,
        isClientSidePagination: !this.isPrinting,
        columns: [
          {
            id: '#',
            langKey: '#'
          },
          {
            id: 'sub_category',
            langKey: 'sensitive_data.sub_category',
            cellFormatter: (entry: any) => {
              return elem(
                'span',
                {
                  className: 'link-cell',
                  innerText: entry.sub_category
                },
                {
                  events: {
                    click: async (event: MouseEvent) => {
                      event.stopPropagation();
                      this.showConfidence(entry, 1);
                    }
                  }
                }
              );
            }
          },
          {
            id: 'category',
            langKey: 'sensitive_data.category',
            cellFormatter: (row: any) => {
              let values = row?.category || [];

              let labels = values.map((x) => this.dataTypeService.getCategoryLabel(x));
              return labels.toString();
            }
          },
          {
            id: 'region',
            langKey: 'sensitive_data.region'
          },
          {
            id: 'count',
            langKey: 'sensitive_data.count'
          },
          {
            id: 'max_confidence',
            langKey: 'sensitive_data.max_confidence'
          }
        ]
      };
    }

    if (!this.customTableSetting) {
      this.customTableSetting = {
        tableId: 'sensitive_data_detail_custom_table',
        pageSize: 10,
        pageSizeOptions: [10, 20],
        hasMenuBar: false,
        hasSelect: false,
        hasPaginator: !this.isPrinting,
        isClientSidePagination: !this.isPrinting,
        columns: [
          {
            id: '#',
            langKey: '#'
          },
          {
            id: 'custom_dt',
            langKey: 'sensitive_data.custom_data_type',
            cellFormatter: (entry: any) => {
              return elem(
                'span',
                {
                  className: 'link-cell',
                  innerText: entry.custom_dt
                },
                {
                  events: {
                    click: async (event: MouseEvent) => {
                      event.stopPropagation();
                      this.showConfidence(entry, 2);
                    }
                  }
                }
              );
            }
          },
          {
            id: 'custom_dt_group',
            langKey: 'sensitive_data.custom_data_type_group'
          },
          {
            id: 'count',
            langKey: 'sensitive_data.count'
          }
        ]
      };
    }

    if (!this.labelsTableSetting) {
      this.labelsTableSetting = {
        tableId: 'sensitive_data_detail_labels_table',
        pageSize: 10,
        pageSizeOptions: [10, 20],
        hasMenuBar: false,
        hasSelect: false,
        hasPaginator: !this.isPrinting,
        isClientSidePagination: !this.isPrinting,
        columns: [
          {
            id: 'label',
            langKey: 'sensitive_data.label'
          }
        ]
      };
    }

    // if (!this.collaboratorsTableSetting) {
    //   this.collaboratorsTableSetting = {
    //     tableId: 'sensitive_data_detail_collaborators_table',
    //     hasMenuBar: false,
    //     hasSelect: false,
    //     hasPaginator: !this.isPrinting,
    //     isClientSidePagination: !this.isPrinting,
    //     columns: [
    //       {
    //         id: '#',
    //         langKey: '#'
    //       },
    //       {
    //         id: 'name',
    //         langKey: 'sensitive_data.name'
    //       },
    //       {
    //         id: 'email',
    //         langKey: 'sensitive_data.email'
    //       },
    //       {
    //         id: 'role',
    //         langKey: 'sensitive_data.permission',
    //         cellFormatter: (x: any) => new TitleCasePipe().transform(x.role ?? '')
    //       }
    //     ]
    //   };
    // }
    if (!this.idmMatchTableSetting) {
      this.idmMatchTableSetting = {
        tableId: 'sensitive_data_detail_idm_match_table',
        pageSize: 10,
        pageSizeOptions: [10, 20],
        hasMenuBar: false,
        hasSelect: false,
        hasPaginator: !this.isPrinting,
        isClientSidePagination: !this.isPrinting,
        columns: [
          {
            id: 'name',
            langKey: 'sensitive_data.index_name'
          },
          {
            id: 'similarity',
            langKey: 'sensitive_data.similarity'
          }
        ]
      };
    }

    if (!this.edmMatchTableSetting) {
      this.edmMatchTableSetting = {
        tableId: 'sensitive_data_detail_edm_match_table',
        pageSize: 10,
        pageSizeOptions: [10, 20],
        hasMenuBar: false,
        hasSelect: false,
        hasPaginator: !this.isPrinting,
        isClientSidePagination: !this.isPrinting,
        columns: [
          {
            id: 'name',
            langKey: 'sensitive_data.rule_name'
          },
          {
            id: 'template_name',
            langKey: 'sensitive_data.template_name',
            breakWord: true
          }
        ]
      };
    }

    if (!this.actionsTableSetting) {
      this.actionsTableSetting = {
        tableId: 'sensitive_data_detail_actions_table',
        pageSize: 10,
        pageSizeOptions: [10, 20],
        hasMenuBar: false,
        hasSelect: false,
        hasPaginator: !this.isPrinting,
        isClientSidePagination: !this.isPrinting,
        columns: [
          {
            id: 'action',
            langKey: 'sensitive_data.action',
            cellFormatter: (row: any) => {
              if (row.path === '' && row.failed_detail_info === '') {
                return elem('fd-n-icon', {
                  matIcon: 'spinner',
                  text: row.action,
                  tooltip: this.translateService.lookup('in_progress')
                });
              }

              return !row.failed_detail_info || row.failed_detail_info === ''
                ? elem('fd-n-icon', {
                    svgIcon: 'check_circle_outline',
                    color: '#60d927',
                    text: row.action,
                    tooltip: this.translateService.lookup('successful')
                  })
                : elem('fd-n-icon', {
                    svgIcon: 'warning_outline',
                    color: '#fd7614',
                    text: row.action,
                    tooltip: this.translateService.lookup('failed')
                  });
            }
          },
          {
            id: 'path',
            langKey: 'sensitive_data.destination_path',
            breakWord: true
          },
          {
            id: 'failed_detail_info',
            langKey: 'sensitive_data.failed_detail_info',
            breakWord: true
          }
        ]
      };
    }
  }

  initTableData() {
    this.standard_data = [];
    this.custom_data = [];
    this.labels_data = [];
    this.collaborators_data = [];
    this.actions_data = [];
    this.standard_total = 0;
    this.custom_total = 0;
    this.labels_total = 0;
    this.collaborators_total = 0;
    this.actions_total = 0;
    this.idm_data = [];
    this.edm_data = [];
    this.idm_match_total = 0;
    this.edm_match_total = 0;

    const res = this.dataTypeService.reportData.sensitive_data_details[this.data.file_uuid] ?? {};
    this.loading = true;
    this.data.file_tag = res.labels;
    this.data.file_hash = res.file_hash;
    this.updateData.emit(this.data);

    if (res.hasOwnProperty('standard_list')) {
      res.standard_list.forEach((s) => {
        this.standard_total += s.count;
      });
    }

    if (res.hasOwnProperty('custom_list')) {
      res.custom_list.forEach((c) => {
        this.custom_total += c.count;
      });
    }

    if (res.hasOwnProperty('labels')) {
      this.grouped_labels = JSON.parse(JSON.stringify(res.labels));

      for (let key in res.labels) {
        res.labels[key].forEach((l) => {
          this.labels_data.push({ label: l });
        });
      }
      this.labels_total = this.labels_data.length;
    }

    // const file_collaborators = res.file_attributes?.file_collaborators;
    // if (Array.isArray(file_collaborators) && file_collaborators?.length) {
    //   res.file_attributes.file_collaborators.forEach((collaborator, idx) => {
    //     this.collaborators_data.push({ '#': idx + 1, ...collaborator });
    //   });
    //   this.collaborators_total = this.collaborators_data.length;
    // }

    const actions = [];
    if (this.data.copy_targets === CopyQuarantineStatus.InProgress) {
      // show an empty row when status is InProgress
      actions.push({ action: 'Copy', path: '', failed_detail_info: '' });
    }
    if (res.copy_targets?.length) {
      (res.copy_targets ?? []).forEach((x) => {
        actions.push({ action: 'Copy', path: x, failed_detail_info: '' });
      });
    }
    if (!['', undefined].includes(res.copy_failed_message)) {
      actions.push({ action: 'Copy', path: [], failed_detail_info: res.copy_failed_message });
    }

    if (this.data.quarantine_status === CopyQuarantineStatus.InProgress) {
      // show an empty row when status is InProgress
      actions.push({ action: 'Quarantine', path: '', failed_detail_info: '' });
    }
    if (res.quarantine_targets?.length) {
      (res.quarantine_targets ?? []).forEach((x) => {
        actions.push({
          action: 'Quarantine',
          path: x,
          failed_detail_info: ''
        });
      });
    }
    if (!['', undefined].includes(res.quarantine_failed_message)) {
      actions.push({
        action: 'Quarantine',
        path: [],
        failed_detail_info: res.quarantine_failed_message
      });
    }

    this.actions_data = actions;
    this.actions_total = actions.length;

    const idmMatch = res.idm_match_info;
    this.idm_match_total = keys(idmMatch).length;
    this.idm_data.push(...Object.values(idmMatch || {}));
    this.idmMatch_table.values = this.idm_data;
    this.idmMatch_table.refreshByQuery();

    const edmMatch = res.edm_match_info;
    this.edm_match_total = keys(edmMatch).length;
    this.edm_data.push(
      ...Object.values(edmMatch || {}).map((v: any) => ({
        name: v.name,
        template_name: v.edm_template_name
      }))
    );
    this.edmMatch_table.values = this.edm_data;
    this.edmMatch_table.refreshByQuery();

    this.standard_data = JSON.parse(JSON.stringify(res.standard_list || []));
    this.custom_data = JSON.parse(JSON.stringify(res.custom_list || []));

    this.standard_table.values = this.standard_data;
    this.standard_table.refreshByQuery();

    this.custom_table.values = this.custom_data;
    this.custom_table.refreshByQuery();

    this.labels_table.values = this.labels_data;
    this.labels_table.refreshByQuery();

    // this.collaborators_table.values = this.collaborators_data;
    // this.collaborators_table.refreshByQuery();

    this.actions_table.values = this.actions_data;
    this.actions_table.refreshByQuery();

    setTimeout(() => {
      this.loading = false;
    });
  }

  showConfidence(data, type) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.restoreFocus = false;

    let conData = JSON.parse(JSON.stringify(data));
    conData.type = type;

    dialogConfig.width = '50vw';
    dialogConfig.height = '500px';
    dialogConfig.data = conData;

    this.dialog.open(ConfidenceDetailComponent, dialogConfig);
  }

  openLabelsDialog() {
    // const dialogConfig = new MatDialogConfig();
    // dialogConfig.disableClose = true;
    // dialogConfig.autoFocus = false;
    // dialogConfig.restoreFocus = false;
    // const data = {
    //   file_name: this.data.filename,
    //   storage: this.data.storage,
    //   folder_path: this.data.folder_path,
    //   file_path: this.getFullPath(),
    //   labels: this.grouped_labels,
    //   file_uuid: this.data.file_uuid,
    //   scan_policy_id: this.data.scan_policy_id
    // };
    // dialogConfig.width = '99vw';
    // dialogConfig.height = '700px';
    // dialogConfig.data = data;
    // let dialogRef = this.dialog.open(FileLabelsConfigurationComponent, dialogConfig);
    // /*
    //   if (has(this.data, 'folder_path')) {
    //     dialogRef = this.dialog.open(FolderLabelsConfigComponent, dialogConfig);
    //   } else {
    //     dialogRef = this.dialog.open(
    //       FileLabelsConfigurationComponent,
    //       dialogConfig
    //     );
    //   }
    //     */
    // dialogRef.afterClosed().subscribe((result) => {
    //   if (result == 'success') {
    //     this.initTableData();
    //   }
    // });
  }

  // another show detail clicked, should pass in another 'data'
  ngOnChanges(changes: SimpleChanges) {
    if ('data' in changes && !changes['data'].firstChange) {
      this.initTableData();
    }
  }

  ngOnInit() {
    this.initTables();
  }

  ngAfterViewInit() {
    this.initTableData();
    this.cdf.detectChanges();
  }

  closePanel() {
    this.closeDetail.emit();
  }

  copyStr(str: string) {
    const input = document.createElement('textarea');
    input.value = str;
    document.body.appendChild(input);
    input.select();
    document.execCommand('copy');
    document.body.removeChild(input);

    this.notifyService.success('Successfully copied!');
  }

  idm_match_total = 0;
  edm_match_total = 0;
}
