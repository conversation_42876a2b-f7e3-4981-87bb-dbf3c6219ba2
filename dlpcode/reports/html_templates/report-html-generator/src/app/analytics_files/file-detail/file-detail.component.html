<div class="detail-panel">
  <div class="panel-header">
    <span class="title">{{ 'sensitive_data.sensitive_data_type_detail' | translate }}</span>
    <button mat-icon-button (click)="closePanel()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <mat-divider></mat-divider>

  <div class="detail-form" #detailForm>
    <div class="loading-panel" *ngIf="loading">
      <mat-spinner></mat-spinner>
    </div>
    <div class="form-field file-name item">
      <label>{{ 'sensitive_data.detail_file_name' | translate }}</label>
      <div class="item-with-copy">
        <div [title]="data?.filename">
          {{ data?.filename }}
        </div>
        <i *ngIf="!isPrinting" title="copy" class="material-icons" (click)="copyStr(data?.filename)">content_copy</i>
      </div>
    </div>

    <div class="form-field">
      <div class="label-line">
        <label>{{ 'sensitive_data.storage' | translate }}</label>
        <label>{{ 'sensitive_data.updated' | translate }}</label>
      </div>
      <div class="value-line">
        <span>{{ data?.storage }}</span>
        <span>{{ convertTime() }}</span>
      </div>
    </div>

    <div class="form-field">
      <div class="label-line">
        <label>
          {{ 'sensitive_data.main_class_name' | translate }}
        </label>
        <label>
          {{ 'sensitive_data.sub_class_name' | translate }}
        </label>
      </div>
      <div class="value-line">
        <span>{{ data?.main_class_name }}</span>
        <span>{{ data?.sub_class_name }}</span>
      </div>
    </div>

    <div class="form-field">
      <div class="label-line">
        <label>
          {{ 'sensitive_data.class_confidence' | translate }}
        </label>
      </div>
      <div class="value-line">
        <span>{{ data?.sub_class_confidence }}</span>
      </div>
    </div>

    <div class="form-field path item">
      <label>{{ 'sensitive_data.file_path' | translate }}</label>
      <div class="item-with-copy">
        <div [title]="getFullPath()">
          {{ getFullPath() }}
        </div>
        <i *ngIf="!isPrinting" title="copy" class="material-icons" (click)="copyStr(getFullPath())">content_copy</i>
      </div>
    </div>

    <div class="form-field item">
      <label>{{ 'sensitive_data.file_hash' | translate }}</label>
      <div class="item-with-copy">
        <div [title]="data.file_hash">
          {{ data.file_hash }}
        </div>
        <i *ngIf="!isPrinting" title="copy" class="material-icons" (click)="copyStr(data.file_hash)">content_copy</i>
      </div>
    </div>

    <div class="form-field table-field">
      <label>{{ 'sensitive_data.standard_values' | translate }} ( {{ standard_total }} )</label>
      <common-table
        #standard_table
        *ngIf="standardTableSetting"
        [settings]="standardTableSetting"
        [values]="standard_data"
        [ngClass]="{ hidden: loading }"
      ></common-table>
    </div>

    <div class="form-field table-field">
      <label>{{ 'sensitive_data.custom_values' | translate }} ( {{ custom_total }} )</label>
      <common-table
        #custom_table
        *ngIf="customTableSetting"
        [settings]="customTableSetting"
        [values]="custom_data"
        [ngClass]="{ hidden: loading }"
      ></common-table>
    </div>
    <div class="form-field profiles">
      <div class="icon-line">
        <label> {{ 'sensitive_data.idm_match_info' | translate }} ( {{ idm_match_total }} ) </label>
      </div>
      <common-table
        *ngIf="idmMatchTableSetting"
        #idmMatch_table
        [settings]="idmMatchTableSetting"
        [ngClass]="{ hidden: loading }"
        [values]="idm_data"
      ></common-table>
    </div>

    <div class="form-field profiles">
      <div class="icon-line">
        <label> {{ 'sensitive_data.edm_match_info' | translate }} ( {{ edm_match_total }} ) </label>
      </div>
      <common-table
        *ngIf="edmMatchTableSetting"
        #edmMatch_table
        [settings]="edmMatchTableSetting"
        [ngClass]="{ hidden: loading }"
        [values]="edm_data"
      ></common-table>
    </div>
    <div class="form-field table-field">
      <div class="icon-line">
        <label>{{ 'sensitive_data.assigned_labels' | translate }} ( {{ labels_total }} )</label>
        <!-- <mat-icon
          class="edit-icon"
          matTooltip="{{ 'labels.edit_file_labels' | translate }}"
          (click)="openLabelsDialog()"
          *ngIf="hasLabelAccess"
          >edit</mat-icon
        > -->
      </div>

      <common-table
        #labels_table
        *ngIf="labelsTableSetting"
        [settings]="labelsTableSetting"
        [values]="labels_data"
        [ngClass]="{ hidden: loading }"
      ></common-table>
    </div>

    <!-- <div class="form-field table-field">
      <label
        >{{ 'sensitive_data.collaborators_values' | translate }} (
        {{ collaborators_total }} )</label
      >
      <common-table
        #collaborators_table
        *ngIf="collaboratorsTableSetting"
        [settings]="collaboratorsTableSetting"
        [values]="collaborators_data"
        [ngClass]="{hidden:loading}"
      ></common-table>
    </div> -->

    <div class="form-field profiles">
      <div class="icon-line">
        <label> {{ 'sensitive_data.remediation_actions' | translate }} ( {{ actions_total }} ) </label>
      </div>
      <common-table
        #actions_table
        *ngIf="actionsTableSetting"
        [settings]="actionsTableSetting"
        [ngClass]="{ hidden: loading }"
        [values]="actions_data"
      ></common-table>
    </div>
  </div>
  <mat-divider></mat-divider>

  <div class="detail-panel-footer">
    <button mat-stroked-button [disableRipple]="true" (click)="closePanel()">
      {{ 'close' | translate | uppercase }}
    </button>
  </div>
</div>
