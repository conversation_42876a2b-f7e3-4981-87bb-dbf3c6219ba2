:host {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  position: absolute;

  .item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 8px;

    .item-with-copy {
      display: flex;
      gap: 4px;
      align-items: center;

      i {
        color: #409ee7;
        width: 14px;
        height: 14px;
        font-size: 14px;

        &:hover {
          cursor: pointer;
        }
      }

      div {
        max-width: 500px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    span {
      display: flex;
      align-items: baseline;
      gap: 4px;

      button {
        border: none;
        background: white;
        &:hover {
          cursor: pointer;
          background-color: #e6e6e6;
        }
      }

      mat-icon {
        width: 16px;
        height: 16px;
        font-size: 16px;
      }
    }
  }

  &.printing {
    position: relative;

    .detail-panel {
      position: relative;
      width: 100%;
      border-left: 1px solid #e5e5e5;
      border-right: 1px solid #e5e5e5;
    }

    .panel-header,
    .detail-panel-footer {
      display: none;
    }
  }
}

.detail-panel {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  width: 640px;
  background-color: white;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  overflow-y: hidden;
}

.panel-header {
  display: flex;
  height: 48px;
  justify-content: space-between;
  padding: 4px 20px 4px 20px;
  align-items: center;

  .title {
    font-size: 16px;
    font-weight: 700;
  }
}

.detail-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 6px 16px;
  overflow: auto;
  position: relative;

  .icon-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;

    .edit-icon {
      cursor: pointer;
    }
  }

  .form-field {
    gap: 6px;

    .label-line {
      display: flex;
      font-weight: bold;

      label {
        flex: 1;
      }
    }

    .value-line {
      display: flex;

      span {
        flex: 1;
      }
    }

    &.table-field {
      gap: 0;
    }

    &.file-name,
    &.path {
      word-break: break-all;
    }
  }
}

.detail-panel-footer {
  display: flex;
  padding: 20px;
  justify-content: flex-end;
  gap: 11px;

  button {
    width: 160px;
    height: 32px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
}

.loading-panel {
  background-color: #fff;
}
