import { Injectable } from '@angular/core';
import { IFile, ISubClass } from './mock_data_structure';

@Injectable({
  providedIn: 'root'
})
export class DataTypeService {
  sub_ml_categories: ISubClass[];

  reportData: IFile;

  constructor() {}

  initData(reportData: IFile) {
    this.reportData = reportData;
    this.initSubCategories();
  }

  getSubSearchMapping() {
    let ret = {};
    this.sub_ml_categories.forEach((s) => {
      ret[`${s.main_class_name} - ${s.sub_class_name}`] = s.sub_class_id;
    });

    return ret;
  }

  getTypeCategoryMapping() {
    let ret = {};
    (this.reportData.supported_type_categories?.['type_category'] ?? []).forEach((t) => {
      ret[t.name] = t.id;
    });

    return ret;
  }

  getCategoryLabel(type_id: string): string {
    if (!type_id) return '';

    if (type_id == '30001') return 'Custom';

    let entry = this.reportData.supported_type_categories['type_category'].find((x) => x.id == type_id);
    if (!entry) return '';

    return entry.name;
  }

  getMainCategoryLabel(main_class_id: string): string {
    if (!main_class_id) return '';

    let entry = this.reportData.main_categories.find((x) => x.main_class_id == main_class_id);
    if (!entry) return '';

    return entry.main_class_name;
  }

  getSubCategoryLabel(sub_class_id: number | string): string {
    if (!sub_class_id) return '';

    let entry = this.sub_ml_categories.find((x) => x['sub_class_id'] == sub_class_id);
    if (!entry) return '';

    return entry.sub_class_name;
  }

  private initSubCategories() {
    let types = this.reportData.sub_categories || {};
    let ret = [];

    for (let key in types) {
      if (types[key].hasOwnProperty('sub_class_list')) {
        for (let k in types[key].sub_class_list) {
          let sub = types[key].sub_class_list[k];
          sub.main_class_name = types[key].main_class_name;
          ret.push(sub);
        }
      }
    }

    this.sub_ml_categories = ret;
  }
}
