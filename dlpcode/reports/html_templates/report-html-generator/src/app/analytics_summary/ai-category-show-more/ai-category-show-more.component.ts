import { Component, Input, OnInit } from '@angular/core';
import { IAiCategory, IAiCategoryData } from '../mock_data_structure';
import { AppGlobalService } from '../app-global.service';

interface ICategoryInfo extends IAiCategoryData {
  is_sub: boolean;
}

@Component({
  selector: 'app-ai-category-show-more',
  templateUrl: './ai-category-show-more.component.html',
  styleUrls: ['../base.scss', './ai-category-show-more.component.css'],
})
export class AiCategoryShowMoreComponent implements OnInit {
  @Input() data: IAiCategory;
  categories_info: ICategoryInfo[] = [];
  total_scanned_files: number = 0;

  constructor(public globalService: AppGlobalService) {}

  ngOnInit(): void {
    this.total_scanned_files = this.data.total_scan_count;
    this.data.data.forEach((mainC) => {
      this.categories_info.push({
        ...mainC,
        is_sub: false,
      });
      mainC.sub_categories?.forEach((subC) => {
        this.categories_info.push({
          ...subC,
          is_sub: true,
        });
      });
    });
  }
}
