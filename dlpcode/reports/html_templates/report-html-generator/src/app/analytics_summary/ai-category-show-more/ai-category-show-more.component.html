<div class="main-table" [ngClass]="{ 'print-table': globalService.isPrinting }">
  <table class="info-table">
    <tr class="bold">
      <td>{{ "sensitive_data.category_and_sub_category" | translate }}</td>
      <td class="align-right">{{ "sensitive_data.files" | translate }}</td>
      <td class="align-right">%</td>
      <td>
        of {{ total_scanned_files.toLocaleString() }}
        {{ "sensitive_data.scanned_files" | translate }}
      </td>
    </tr>

    <tr
      *ngFor="let category of categories_info"
      [ngClass]="{ bold: !category.is_sub }"
    >
      <td [ngStyle]="{ 'padding-left': category.is_sub ? '20px' : '0px' }">
        {{ category.category }}
      </td>
      <td class="align-right">
        {{ category.count.toLocaleString() }}
      </td>
      <td class="align-right">{{ category.percentage.toFixed(2) }}%</td>
      <td class="percent-cell">
        <div
          class="percent-bar"
          [ngStyle]="{ width: category.percentage + '%' }"
        ></div>
      </td>
    </tr>

    <tr class="bold">
      <td>
        {{ "sensitive_data.total_files_categoried" | translate }}
      </td>
      <td class="align-right">
        {{ data.total.count.toLocaleString() }}
      </td>
      <td class="align-right">{{ data.total.percentage.toFixed(2) }}%</td>
      <td></td>
    </tr>
  </table>
</div>
