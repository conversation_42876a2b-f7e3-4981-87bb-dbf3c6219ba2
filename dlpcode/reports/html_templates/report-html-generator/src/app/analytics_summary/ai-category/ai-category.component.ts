import { Component, Input, OnInit } from '@angular/core';
import { IAiCategory, IAiCategoryData } from '../mock_data_structure';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { AiCategoryShowMoreDialogComponent } from './ai-category-show-more-dialog/ai-category-show-more-dialog.component';

@Component({
  selector: 'app-ai-category',
  templateUrl: './ai-category.component.html',
  styleUrls: ['../base.scss', './ai-category.component.css'],
})
export class AiCategoryComponent implements OnInit {
  @Input() data: IAiCategory;

  baseData: IAiCategoryData[] = [];

  constructor(private dialog: MatDialog) {}

  ngOnInit(): void {
    this.baseData.push(
      ...this.data.data.filter((v) => v.category !== 'Other'),
      ...this.data.data.filter((v) => v.category === 'Other')
    );
  }

  show_all = () => {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.restoreFocus = false;

    dialogConfig.data = {
      title: 'sensitive_data.ai_file_categories_and_sub_categories',
      data: this.data,
    };

    dialogConfig.width = '40vw';
    dialogConfig.height = '650px';

    const dialogRef = this.dialog.open(
      AiCategoryShowMoreDialogComponent,
      dialogConfig
    );
  };
}
