import { IAiCategory } from '@/analytics_summary/mock_data_structure';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-ai-category-show-more-dialog',
  templateUrl: './ai-category-show-more-dialog.component.html',
  styleUrls: ['./ai-category-show-more-dialog.component.css'],
})
export class AiCategoryShowMoreDialogComponent {
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: { title: string; data: IAiCategory }
  ) {
    data.data.data = [
      ...data.data.data.filter((v) => v.category !== 'Other'),
      ...data.data.data.filter((v) => v.category === 'Other'),
    ];
  }
}
