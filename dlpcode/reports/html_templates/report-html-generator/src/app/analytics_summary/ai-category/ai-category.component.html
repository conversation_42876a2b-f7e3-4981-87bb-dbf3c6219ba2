<div class="title">
  {{ "sensitive_data.ai_file_categories" | translate }}:
  {{ "sensitive_data.current_totals" | translate }}
</div>
<div class="body">
  <div class="content">
    <table class="info-table">
      <tr class="bold">
        <td>
          {{ "sensitive_data.category" | translate }}
        </td>
        <td class="align-right">
          {{ "sensitive_data.files" | translate }}
        </td>
        <td class="align-right">%</td>
        <td>
          of {{ data.total_scan_count }}
          {{ "sensitive_data.scanned_files" | translate }}
        </td>
      </tr>

      <tr *ngFor="let data of baseData">
        <td>
          {{ data.category }}
        </td>
        <td class="align-right">
          {{ data.count.toLocaleString() }}
        </td>
        <td class="align-right">{{ data.percentage.toFixed(2) }}%</td>
        <td class="percent-cell">
          <div
            class="percent-bar"
            [ngStyle]="{ width: data.percentage + '%' }"
          ></div>
        </td>
      </tr>

      <tr>
        <td class="bold">
          {{ "sensitive_data.total_categorized_files" | translate }}
        </td>
        <td class="align-right">
          {{ data.total.count.toLocaleString() }}
        </td>
        <td class="align-right">
          {{ data.total.percentage.toLocaleString() }}%
        </td>
        <td></td>
      </tr>

      <tr>
        <td colspan="3" class="align-right">
          <span class="link" (click)="show_all()"
            >{{ "show_more" | translate }} ></span
          >
        </td>
        <td></td>
      </tr>
    </table>
  </div>
</div>
