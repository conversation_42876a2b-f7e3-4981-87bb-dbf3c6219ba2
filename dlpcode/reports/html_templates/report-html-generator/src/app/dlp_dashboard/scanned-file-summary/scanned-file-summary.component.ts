import { Component, Input } from '@angular/core';
import { IScannedFiles } from '../mock_data_structure';

@Component({
  selector: 'app-scanned-file-summary',
  templateUrl: './scanned-file-summary.component.html',
  styleUrls: ['./scanned-file-summary.component.css'],
})
export class ScannedFileSummaryComponent {
  @Input() data: IScannedFiles;

  alltimecount: number = 0;
  onedaycount: number = 0;
  oneweekcount: number = 0;
  twoweekcount: number = 0;

  statisic_count: any[] = [];

  constructor() {}

  ngOnInit(): void {
    this.statisic_count = [
      {
        field: 'onedaycount',
        icon: 'access_time',
        count: 0,
      },
      {
        field: 'oneweekcount',
        icon: 'today',
        count: 0,
      },
      {
        field: 'twoweekcount',
        icon: 'access_alarm',
        count: 0,
      },
    ];

    ['alltimecount', 'onedaycount', 'oneweekcount', 'twoweekcount'].forEach(
      (f) => {
        this[f] = this.data?.[f] || 0;

        this.statisic_count.forEach((s) => {
          if (s.field == f) {
            s.count = this.data?.[f] || 0;
          }
        });
      }
    );
  }
}
