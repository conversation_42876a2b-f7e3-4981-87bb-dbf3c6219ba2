import { IScanIncident } from './mock_data_structure';

export const mockData: IScanIncident = {
  is_printing: false,
  //Report Info
  report_info: {
    name: 'Analytics Scan Incidents Report',
    schedule: 'Weekly Monday, at 11:20 AM PDT',
    period: 'Last 7 days',
    created_by: 'Test',
    generated_on: '2023-10-01 12:00 PM',
    notes: 'This is a test report for analytics files.',
    scan_name: 'Cloud dlp103',
    scan_storage: '10.255.255.176',
    storage_type: 4,
    timezone: 'America/Vancouver'
  },
  //Table Data
  table_data: [
    {
        "id": "33f881c9-4a7a-48a0-b691-7a18c6bacc81",
        "action": [
            "label"
        ],
        "assigned_to": null,
        "comments": "",
        "escalate_to": null,
        "false_positive": false,
        "ignored": false,
        "message": "",
        "risk": 1,
        "sensitivity": 1,
        "status": 0,
        "scan_status": true,
        "data_source": {
            "data_source_info": {
                "tenantid": "112dbc6b-af5d-4564-b13a-22909f6d053e"
            },
            "data_source_type": "SharePoint Cloud"
        },        
        "full_path": "https://dlp103.sharepoint.com/sites/DLP_QA/Shared%20Documents/2%20files/Argentina%20Driver%27s%20License%201.jpg",
        "hash": "11d8efbfc32b737a9d96506b012f998a74b0243b",
        "file_id": "9aefe963-a345-4868-bb33-ffcd54fc2724",
        "last_access": "2025-06-19 21:34:22",
        "file_name": "Argentina Driver's License 1.jpg",
        "file_owner": {
            "email": "<EMAIL>",
            "name": "test1 dlp"
        },
        "size": 106.923828125,
        "type": "image/jpeg",
        "labels": {
          "Sensitivity": [
              "Public"
          ],
          "custom": [
              "Pictures"
          ]
        },
        "policy_id": "e986476a-ff90-4584-9979-515845dbafbc",
        "policy_name": "All Templates",
        "rule_id": "7a2a4883-766e-4137-bdaf-9f7ada13c65c",
        "rule_name": "Argentina PII",
        "scan_id": "0264f7af-b346-4193-b2ed-e8267a9cac23",
        "scan_name": "Cloud dlp103",
        "scan_target": "112dbc6b-af5d-4564-b13a-22909f6d053e",
        "storage_type": 2,
        "create_time": **********.745,
        "update_time": **********.745
    },
    {
        "id": "8118ca44-c18c-4b4d-a418-1aba90405cb8",
        "action": [
            "label"
        ],
        "assigned_to": null,
        "comments": "",
        "escalate_to": null,
        "false_positive": false,
        "ignored": false,
        "message": "",
        "risk": 2,
        "sensitivity": 1,
        "status": 0,
        "scan_status": true,
        "data_source": {
            "data_source_info": {
                "tenantid": "112dbc6b-af5d-4564-b13a-22909f6d053e"
            },
            "data_source_type": "SharePoint Cloud"
        },
        "full_path": "https://dlp103.sharepoint.com/sites/DLP_QA/Shared%20Documents/%25%20Classified%20%25/b8stdxiFXmKDjcM___dir.txt",
        "hash": "e791bbeafc2683cbbfd822b8298b68b4dead8c35",
        "file_id": "57d21ead-a25c-44f5-94b9-505a0afaeea2",
        "last_access": "2025-03-26 22:57:19",
        "file_name": "b8stdxiFXmKDjcM___dir.txt",
        "file_owner": {
            "email": "<EMAIL>",
            "name": "test1 dlp"
        },
        "size": 9260.4423828125,
        "type": "text/plain",
        "labels": {
          "Sensitivity": [
              "Public"
          ],
          "custom": [
              "Pictures"
          ]
        },
        "policy_id": "e986476a-ff90-4584-9979-515845dbafbc",
        "policy_name": "All Templates",
        "rule_id": "e15041a0-cb31-4573-a28c-c34ed29f555b",
        "rule_name": "DIACAP",
        "scan_id": "0264f7af-b346-4193-b2ed-e8267a9cac23",
        "scan_name": "Cloud dlp103",
        "scan_target": "112dbc6b-af5d-4564-b13a-22909f6d053e",
        "storage_type": 2,
        "create_time": 1750898093.418,
        "update_time": 1750898093.418
    },
    {
        "id": "687eb8b1-b199-47ad-bb2a-2ed91eb0a3d0",
        "action": [
            "label"
        ],
        "assigned_to": null,
        "comments": "",
        "escalate_to": null,
        "false_positive": false,
        "ignored": true,
        "message": "",
        "risk": 3,
        "sensitivity": 1,
        "status": 1,
        "scan_status": true,
        "data_source": {
            "data_source_info": {
                "tenantid": "112dbc6b-af5d-4564-b13a-22909f6d053e"
            },
            "data_source_type": "SharePoint Cloud"
        },
        "full_path": "https://dlp103.sharepoint.com/sites/DLP_QA/Shared%20Documents/%25%20Classified%20%25/b8stdxiFXmKDjcM___dir.txt",
        "hash": "e791bbeafc2683cbbfd822b8298b68b4dead8c35",
        "file_id": "57d21ead-a25c-44f5-94b9-505a0afaeea2",
        "last_access": "2025-03-26 22:57:19",
        "file_name": "b8stdxiFXmKDjcM___dir.txt",
        "file_owner": {
            "email": "<EMAIL>",
            "name": "test1 dlp"
        },
        "size": 9260.4423828125,
        "type": "text/plain",
        "labels": {
          "Sensitivity": [
              "Public"
          ],
          "custom": [
              "Pictures"
          ]
        },
        "policy_id": "e986476a-ff90-4584-9979-515845dbafbc",
        "policy_name": "All Templates",
        "rule_id": "2b1686c8-d9c6-4376-aa3b-63b2e35dbf19",
        "rule_name": "FISMA",
        "scan_id": "0264f7af-b346-4193-b2ed-e8267a9cac23",
        "scan_name": "Cloud dlp103",
        "scan_target": "112dbc6b-af5d-4564-b13a-22909f6d053e",
        "storage_type": 2,
        "create_time": 1750898093.412,
        "update_time": 1750898093.412
    },
    {
        "id": "c11ebf7f-2915-454d-b650-7c42903db4c6",
        "action": [
            "label"
        ],        
        "assigned_to": null,
        "escalate_to": null,
        "comments": "",
        "false_positive": false,
        "ignored": false,
        "risk": 2,
        "sensitivity": 1,
        "status": 0,
        "scan_status": true,
        "data_source": {
            "data_source_info": {
                "tenantid": "112dbc6b-af5d-4564-b13a-22909f6d053e"
            },
            "data_source_type": "SharePoint Cloud"
        },        
        "full_path": "https://dlp103.sharepoint.com/sites/DLP_QA/Shared%20Documents/1%20file/f10_temp.txt",
        "hash": "b387cc5582868fb5757c165d387fbc657b527725",
        "file_id": "1cae29d4-4be6-42bd-9130-dc6c62c3dc0b",
        "last_access": "2025-04-11 21:00:45",
        "file_name": "f10_temp.txt",
        "file_owner": {
            "email": "<EMAIL>",
            "name": "test1 dlp"
        },
        "size": 0.4921875,
        "type": "text/plain",
        "labels": {
            "Sensitivity": [
                "Restricted"
            ],
            "predefine": [
                "PII",
                "Public"
            ]            
        },
        "policy_id": "e986476a-ff90-4584-9979-515845dbafbc",
        "policy_name": "All Templates",
        "rule_id": "99cd8475-4cf5-4de6-836f-94d815bca9ca",
        "rule_name": "US PII",
        "scan_id": "0264f7af-b346-4193-b2ed-e8267a9cac23",
        "scan_name": "Cloud dlp103",
        "scan_target": "112dbc6b-af5d-4564-b13a-22909f6d053e",
        "storage_type": 2,
        "create_time": 1750898088.749,
        "update_time": 1750898088.749
    },
    {
        "id": "e3edaad7-abc1-437e-ac2b-117731b54f52",
        "action": [
            "label"
        ],
        "assigned_to": null,
        "comments": "",
        "escalate_to": null,
        "false_positive": true,
        "ignored": false,
        "message": "",
        "risk": 1,
        "sensitivity": 1,
        "status": 0,
        "scan_status": true,
        "data_source": {
            "data_source_info": {
                "tenantid": "112dbc6b-af5d-4564-b13a-22909f6d053e"
            },
            "data_source_type": "SharePoint Cloud"
        },        
        "full_path": "https://dlp103.sharepoint.com/sites/DLP_QA/Shared%20Documents/%25%20Classified%20%25/ghvE4ssZ4S4R3xD___dir.txt",
        "hash": "6f2af7563efb6437979593a38625ebf8cd9289ec",
        "file_id": "0aff198d-9d51-4074-928a-8b014674c0f9",
        "last_access": "2025-03-26 22:57:19",
        "file_name": "ghvE4ssZ4S4R3xD___dir.txt",
        "file_owner": {
            "email": "<EMAIL>",
            "name": "test1 dlp"
        },
        "size": 9173.81640625,
        "type": "text/plain",
        "labels": {
            "predefine": [
                "PII",
                "HR",
                "CV",
                "Public"
            ]
        },
        "policy_id": "e986476a-ff90-4584-9979-515845dbafbc",
        "policy_name": "All Templates",
        "rule_id": "e15041a0-cb31-4573-a28c-c34ed29f555b",
        "rule_name": "DIACAP",
        "scan_id": "0264f7af-b346-4193-b2ed-e8267a9cac23",
        "scan_name": "Cloud dlp103",
        "scan_target": "112dbc6b-af5d-4564-b13a-22909f6d053e",
        "storage_type": 2,
        "create_time": 1750898087.315,
        "update_time": 1750898087.315
    },
  ],
  //column name map for CSV header fields.
  column_names: {
    id: 'ID',
    risk: 'Risk Level',
    sensitivity: 'Sensitivity',
    false_positive: 'False Positive',
    ignored: 'Ignored',
    escalate_to: 'Escalated To',
    assigned_to: 'Assigned To',
    message: 'Message',
    status: 'Status',
    scan_status: 'Scan Status',
    comments: 'Comments',
    action: 'Action',
    labels: 'Labels',
    collaboration: 'Collaboration',
    file_id: 'File ID',
    file_name: 'File Name',
    full_path: 'Full Path',
    hash: 'File Hash',
    type: 'Type',
    size: 'Size',
    file_owner: 'File Owner',
    last_access: 'Last Accessed',
    policy_id: 'Policy ID',
    policy_name: 'Policy Name',
    rule_id: 'Rule ID',
    rule_name: 'Rule Name',
    scan_id : 'Scan ID',
    scan_name: 'Scan Name',
    scan_target: 'Scan Target',
    storage_type: 'Storage Type',
    create_time: 'Create Time',
    update_time: 'Update Time'
  }  
};
