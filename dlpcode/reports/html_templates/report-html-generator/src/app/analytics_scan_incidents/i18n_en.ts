export const TRANSLATIONS_EN = {
  search_query: 'Search',
  multiplePlaceholder: 'value1,value2,...etc.',
  pleaseSelectValue: 'Please select the value',
  value: 'Value',
  search: 'Search',
  clearAllConditions: 'Clear all conditions',
  errorMessage: {
    required: 'This field is required',
    invalidExportType: 'Invalid export type',
    invalidTableQueryValue: '{{column}} can only be {{values}}',
    notAllowComma: 'Comma is not allowed',
    notAllowBackslash: 'Backslash is not allowed',
    noRowsToExport: 'First select the rows you want to export',
    noRowsToDelete: 'First select the rows you want to delete',
    noRowsToAck: 'First select the rows you want to acknowledge',
    noExistingQueries: 'There is no existing query available',
    noQuery: 'Please add a query first',
    load_file_failed: 'Could not load this file',
    min: 'The minimum value is {{min}}',
    max: 'The maximum value is {{max}}',
    range: 'Out of range: {{min}} - {{max}}',
    pattern: 'Invalid input',
    email: 'Invalid email format',
    url: 'Invalid URL',
    invalid_file_type: 'Invalid file type',
    invalid: 'This value is invalid',
    file_size: 'Maximum file size is {{max}} {{unit}}',
    cannot_read_upload_file: 'The file cannot be uploaded because its content might be changed. Please select the file again.',
    scans_file_size_limit: 'File size limit should not exceed 50 MB',
    scans_file_size_invalid: 'Invalid file size',
    scans_data_regions_empty: 'No Standard Data Type Regions selected',
    scans_data_category_empty: 'No Standard Data Type Categories selected',
    pass_not_match: 'Passwords do not match',
    no_filter_available: 'Filters cannot be duplicated, and all are currently being applied. Please edit or remove them first.',
    end_with_slash: 'Cannot end with a backslash (\\) or a forward slash (/).',
    end_with_forward_slash: 'Cannot end with a forward slash (/).',
    start_with_forward_slash: 'Input must start with a forward slash (/)',
    posiive_integer: 'Value must be a positive integer',
    invalidIPAddress: 'Invalid IP Address',
    invalidIPAddressDomain: 'Invalid IP Address or domain',
    text_length_limit: 'Input length limit: {{length}}',
    any: '{{message}}',
    maxlength: 'The maximum length is {{requiredLength}}',
    illegalChar: 'Invalid characters',
    'password-not-match': 'Passwords do not match',
    key_length: '8 characters long',
    lower_word: 'one lowercase letter',
    upper_word: 'one uppercase letter',
    number_word: 'one number',
    special_word: 'one sepcial character',
    invalidCIDR: 'Invalid CIDR format',
    non_negative_integer: 'Value must be a non-negative integer',
    scans_profiles_confirm_error: 'Please check the box to confirm that you understand the potential issues',
    number_letter_underscore: 'The value can contain only numbers, letters, and underscores',
    number_letter_underscore_hyphen: 'The value can contain only numbers, letters, underscores, and hyphens',
    number_letter_underscore_space: 'The value can contain only numbers, letters, underscores, and spaces',
    number_letter_underscore_hyphen_space: 'The value can contain only numbers, letters, underscores, hyphens, and spaces',
    trailing_spaces_error: 'Trailing spaces are not allowed. Spaces between words are fine.',
    trailingSpace: 'Trailing spaces are not allowed. Spaces between words are fine.',
    end_time_must_after_start_time: 'End time must be after start time',
    start_time_should_not_before: 'Start time must not be before {{time}}'
  },
  tableFilter: {
    equal: '=',
    like: 'Like',
    range: 'Range',
    greater: '>',
    greater_equal: '≥',
    less: '<',
    less_equal: '≤'
  },
  scans: {
    storage_type: {
      prem: 'SharePoint On Prem',
      smb: 'SMB',
      cloud: 'SharePoint Cloud',
      aws: 'AWS Bucket',
      unknown: 'Unknown'
    }
  },
  scan_incidents: {
    "incidents_header": "Scan Incidents",
    "license_warning": "Your trial license allows viewing only the latest 1,000 scan incidents. Please purchase a product license.",
    "scan_name": "Scan Name",
    "period": "Period",
    "data_src": "Data Source",
    "storage_type": "Storage Type",
    "notes": "Notes",
    "id": "ID",
    "incident_time": "Incident Time",
    "incident_status": "Status",
    "severity": "Severity",
    "risk": "Risk",
    "ignored": "Mark As Ignored",
    "false_positive": "False Positive",
    "labels": "Labels",
    "sensitivity": "Sensitivity",
    "compliance": "Compliance",
    "data_classification": "Data Classification",
    "data_residency": "Data Residency",
    "files": "Files",
    "file_name": "File Name",
    "file_owner": "Owner",
    "extension": "Extension",
    "file_size": "Size",
    "file_path": "File Path",
    "file_hash": "File Hash",
    "edm_matched": "EDM Matched",
    "idm_matched": "IDM Matched",
    "file_action": "Action",
    "scan_action": "Scan Action",
    "policy_name": "Policy Name",
    "rule_name": "Rule Name",
    "scan_id": "Scan ID",
    "policy_id": "Policy ID",
    "select_scan": "Select a Scan Name",
    "view_details": "View Details",
    "change_status": "Change Status",
    "escalate": "Escalate",
    "change_severity": "Change Severity",
    "add_to_whitelist": "Add To Whitelist",
    "mark_as_ignored": "Mark As Ignored",
    "unmark_incidents": "Unmark Ignored",
    "mark_as_false_positive": "Mark As False Positive",
    "undo_false_positive": "Unmark False Positive",
    "disable_policy": "Disable Policy",
    "disable_rule": "Disable Rule",
    "escalate_incidents": "Escalate Incident(s)",
    "assign_incidents": "Assign",
    "add_export": "ADD EXPORT",
    "export": "EXPORT",
    "delete": "Delete",
    "delete_incident": "Delete selected scan incident",
    "confirm_delete_incident": "Are you sure you want to delete the selected scan incident?",
    "delete_incidents": "Delete selected scan incidents",
    "confirm_delete_incidents": "Are you sure you want to delete the selected scan incidents?",
    "confirm_change_severity": "Do you want to change severity for selected scan incidents?",
    "confirm_change_status": "Do you want to change status for selected scan incidents?",
    "confirm_mark_as_ignored": "Are you sure you want to ignore selected scan incident(s)?",
    "confirm_unmark_incidents": "Are you sure you want to unignore selected scan incident(s)?",
    "confirm_mark_as_false_positive": "Are you sure you want to mark selected scan incident(s) as false positive?",
    "confirm_undo_false_positive": "Are you sure you want to undo the false positive marking for the selected scan incident(s)?",
    "confirm_incident_assigned": "Do you want to assign the selected incidents to {{user}}?",
    "confirm_incident_escalate": "Do you want to escalate the selected incidents to {{user}}?",
    "confirm_add_to_whitelist": "Are you sure you want to add the selected incidents to the allowlist?",
    "confirm_disable_policy": "Are you sure you want to disable policy for the selected incidents?",
    "confirm_disable_rule": "Are you sure you want to disable rule for the selected incidents?"
  },
  sensitive_data : {
    label_predefined: 'Standard Labels',
    label_sensitvity: 'Sensitivity',
    label_custom: 'Custom Labels',
    label_ml: 'Machine Learning Labels'
  },
  scan_incidents_detail: {
    "title": "Scan Incident Details",
    "time": "Time",
    "severity": "Severity",
    "file_name": "File Name",
    "data_source_type": "Data Source Type",
    "file_path": "File Path",
    "file_hash": "File Hash",
    "file_size": "File Size",
    "file_owner": "File Owner",
    "public_url": "Public URL",
    "file_collaborators": "File Collaborators",
    "inter_file_collaborators": "Internal File Collaborators",
    "external_file_collaborators": "External File Collaborators",
    "policy_details": "Policy Details",
    "scan_name": "Scan Name",
    "scan_time": "Scan Create Time",
    "discovery_policy_name": "Discovery Policy Name",
    "discovery_rule_name": "Discovery Rule Name",
    "assigned_labels": "Assigned Labels",
    "full_name": "Full Name",
    "role": "Role",
    "action_history": "Action History",
    "action": "Action",
    "user": "User"
  }
};