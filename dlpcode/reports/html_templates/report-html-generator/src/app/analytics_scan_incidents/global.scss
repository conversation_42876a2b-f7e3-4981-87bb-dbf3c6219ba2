* {
  box-sizing: border-box;
}

.title {
  font-size: 16px;
  font-weight: 700;
  color: #222222;
}

.small-title {
  font-size: 14px;
  font-weight: 700;
  color: #222222;
}

.body {
  position: relative;
  min-height: 100px;
}

.loading-panel {
  background-color: rgb(255, 255, 255) !important;
}

.pointer {
  cursor: pointer;
}

.circle {
  display: inline-block;
  height: 8px;
  width: 8px;
  border-radius: 50%;
  margin-right: 4px;
}

.align-right {
  text-align: right;
}

body {
  max-width: 1400px !important;
  margin: 0 auto !important;
}
