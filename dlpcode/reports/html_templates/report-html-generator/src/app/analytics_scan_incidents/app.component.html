<!-- <div>{{ "base_sample" | translate }}</div>
<div style="height: 400px;">
    <common-table [settings]="tableSettings"></common-table>
</div>

<highcharts-chart [Highcharts]="Highcharts" [options]="chartOptions"></highcharts-chart>

<mat-icon svgIcon="fortidata"></mat-icon> -->


<div class="basic-info">
    <div class="row">
      <mat-icon svgIcon="fortidata"></mat-icon>
      <span class="title report-title">FortiData</span>
      <span class="title report-name">{{ reportData.report_info.name }}</span>
      <span>-</span>
      <span>{{ reportData.report_info.generated_on }}</span>
      <span class="schedule">{{ reportData.report_info.schedule }}</span>
    </div>
  
    <div class="row scan-info">
      <div class="item">
        <span class="title">Scan Name:</span>
        <span>{{ reportData.report_info.scan_name }}</span>
      </div>
      <div class="item">
        <span class="title">Storage Type:</span>
        <span>{{ getStorageType() }}</span>
      </div>
      <div class="item">
        <span class="title">Storage:</span>
        <span>{{ reportData.report_info.scan_storage }}</span>
      </div>
      <div class="item">
        <span class="title">Report Note:</span>
        <span>{{ reportData.report_info.notes }}</span>
      </div>
    </div>
  
    <div class="row queries-info">
      <span class="title">Scan Incidents</span>
      <mat-divider [vertical]="true"></mat-divider>
    </div>
</div>
  
<div class="table-box" [ngClass]="{ printing: isPrinting }">
    <common-table *ngIf="tableSettings" [settings]="tableSettings" [values]="tableData"></common-table>
</div>