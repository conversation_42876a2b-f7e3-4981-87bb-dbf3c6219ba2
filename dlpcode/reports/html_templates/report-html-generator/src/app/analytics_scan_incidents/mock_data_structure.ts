export interface IReportInfo {
  name: string;
  /** When the report generated */
  generated_on: string;
  schedule: string;
  period: string;
  created_by: string;
  notes: string;
  scan_name: string;
  scan_storage: string;
  storage_type: number;
  /** IANA format */
  timezone: string;
}

export interface IScanIncident {
  table_data: any[];
  /** report info */
  report_info: IReportInfo;
  /**  Discovery Policiy */
  //discovery_policies: { [key: string]: ITypeCategory[] };
  /** used to generate PDF format report */
  is_printing: boolean;
  /** Incident data details */
  incident_data_details?: any;
  column_names: any;
}

export enum EScanStorageType {
  aws = 1,
  cloud = 2,
  prem = 3,
  smb = 4
}


export interface IScanIncidentEntry {
  id: string;
  //severity: number;
  risk: number;
  sensitivity: number;
  false_positive: boolean;
  ignored : boolean;
  escalate_to: boolean;
  assigned_to: string;
  message: string;
  status: number;
  scan_status: boolean;
  comments: string;
  action:any;
  //attributes: any;
  //attributes_ext: any;
  data_source: any;
  labels: any;
  collaboration?: any;
  file_id: string;
  file_name: string;
  full_path: string;
  hash: string;
  type: string;
  size: number;
  file_owner: any; // Owner info
  last_access: number;
  policy_id: string; // pid
  policy_name: string; // pname
  rule_id: string; // rid
  rule_name: string; // rname
  scan_id: string; // sid
  scan_name: string; // sname
  scan_target: string; // starget
  storage_type: number; // stype
  create_time: number; // ctime
  update_time: number; //uptime
}