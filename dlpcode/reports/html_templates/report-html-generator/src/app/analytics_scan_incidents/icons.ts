export const ICONS = {
    fortidata: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"> <path d="M0 0 C3.3 0 6.6 0 10 0 C10 1.98 10 3.96 10 6 C6.7 6 3.4 6 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(11,21)" /> <path d="M0 0 C3.3 0 6.6 0 10 0 C10 1.98 10 3.96 10 6 C6.7 6 3.4 6 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(11,5)" /> <path d="M0 0 C2.97 0 5.94 0 9 0 C9 1.98 9 3.96 9 6 C6.03 6 3.06 6 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(23,13)" /> <path d="M0 0 C2.97 0 5.94 0 9 0 C9 1.98 9 3.96 9 6 C6.03 6 3.06 6 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(0,13)" /> <path d="M0 0 C2.97 0 5.94 0 9 0 C8.125 4.875 8.125 4.875 7 6 C4.667 6.041 2.333 6.042 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(23,21)" /> <path d="M0 0 C2.97 0 5.94 0 9 0 C9 1.98 9 3.96 9 6 C5.625 6.125 5.625 6.125 2 6 C1.34 5.34 0.68 4.68 0 4 C0 2.68 0 1.36 0 0 Z" fill="#EE3124" transform="translate(0,21)" /> <path d="M0 0 C3.375 -0.125 3.375 -0.125 7 0 C9 2 9 2 9 6 C6.03 6 3.06 6 0 6 C0 4.02 0 2.04 0 0 Z" fill="#EE3124" transform="translate(23,5)" /> <path d="M0 0 C1.134 0.021 2.269 0.041 3.438 0.063 C3.438 2.043 3.438 4.023 3.438 6.063 C0.468 6.063 -2.503 6.063 -5.563 6.063 C-4.488 0.079 -4.488 0.079 0 0 Z" fill="#EE3124" transform="translate(5.5625,4.9375)" /> </svg>`,
    check_circle_outline: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_15283_127758)"><path d="M7.99997 1.11133C7.09531 1.11133 6.19951 1.28951 5.36371 1.63571C4.52791 1.98191 3.76849 2.48934 3.12879 3.12904C2.4891 3.76873 1.98167 4.52816 1.63547 5.36395C1.28927 6.19975 1.11108 7.09556 1.11108 8.00022C1.11108 8.90488 1.28927 9.80068 1.63547 10.6365C1.98167 11.4723 2.4891 12.2317 3.12879 12.8714C3.76849 13.5111 4.52791 14.0185 5.36371 14.3647C6.19951 14.7109 7.09531 14.8891 7.99997 14.8891C9.82702 14.8891 11.5792 14.1633 12.8712 12.8714C14.1631 11.5795 14.8889 9.82726 14.8889 8.00022C14.8889 6.17317 14.1631 4.42095 12.8712 3.12904C11.5792 1.83712 9.82702 1.11133 7.99997 1.11133ZM7.99997 2.44466C8.72964 2.44431 9.45222 2.58777 10.1264 2.86684C10.8006 3.14591 11.4132 3.55511 11.9291 4.07106C12.4451 4.58702 12.8543 5.19959 13.1334 5.87378C13.4124 6.54797 13.5559 7.27055 13.5555 8.00022C13.5559 8.72988 13.4124 9.45246 13.1334 10.1267C12.8543 10.8008 12.4451 11.4134 11.9291 11.9294C11.4132 12.4453 10.8006 12.8545 10.1264 13.1336C9.45222 13.4127 8.72964 13.5561 7.99997 13.5558C7.27031 13.5561 6.54773 13.4127 5.87354 13.1336C5.19935 12.8545 4.58677 12.4453 4.07082 11.9294C3.55487 11.4134 3.14566 10.8008 2.8666 10.1267C2.58753 9.45246 2.44407 8.72988 2.44442 8.00022C2.44407 7.27055 2.58753 6.54797 2.8666 5.87378C3.14566 5.19959 3.55487 4.58702 4.07082 4.07106C4.58677 3.55511 5.19935 3.14591 5.87354 2.86684C6.54773 2.58777 7.27031 2.44431 7.99997 2.44466ZM11.8942 6.06244L11.2684 5.43133C11.206 5.36879 11.1214 5.33357 11.033 5.3334C10.9447 5.33323 10.8599 5.36813 10.7973 5.43044L6.87108 9.32466L5.20886 7.65088C5.1466 7.58823 5.06201 7.55284 4.97367 7.55251C4.88534 7.55218 4.80049 7.58692 4.73775 7.64911L4.10664 8.27488C4.04398 8.33715 4.0086 8.42174 4.00827 8.51007C4.00793 8.59841 4.04267 8.68326 4.10486 8.746L6.62664 11.2882C6.6889 11.3509 6.77349 11.3863 6.86183 11.3866C6.95016 11.3869 7.03502 11.3522 7.09775 11.29L11.8915 6.53444C11.9542 6.47218 11.9896 6.38759 11.9899 6.29925C11.9902 6.21092 11.9555 6.12606 11.8933 6.06333L11.8942 6.06244Z" fill="currentColor"/></g><defs><clipPath id="clip0_15283_127758"><rect width="16" height="16" fill="currentColor"/></clipPath></defs></svg>`,
    check: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5.71901 13.0943L1.09679 8.47209C0.963528 8.33875 0.888672 8.15794 0.888672 7.96942C0.888672 7.78091 0.963528 7.6001 1.09679 7.46676L2.10301 6.46142C2.23635 6.32817 2.41716 6.25331 2.60568 6.25331C2.79419 6.25331 2.975 6.32817 3.10834 6.46142L6.22212 9.5752L12.8915 2.90587C13.0248 2.77261 13.2056 2.69775 13.3941 2.69775C13.5826 2.69775 13.7634 2.77261 13.8968 2.90587L14.903 3.9112C15.0363 4.04455 15.1111 4.22535 15.1111 4.41387C15.1111 4.60239 15.0363 4.78319 14.903 4.91654L6.72523 13.0943C6.65919 13.1604 6.58076 13.2129 6.49443 13.2487C6.40811 13.2845 6.31557 13.3029 6.22212 13.3029C6.12867 13.3029 6.03613 13.2845 5.94981 13.2487C5.86348 13.2129 5.78505 13.1604 5.71901 13.0943Z" fill="currentColor"/></svg>`,
    warning_outline: `<svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M14.7065 14.1395H1.29352C0.826678 14.1395 0.40781 13.8989 0.175311 13.4967C-0.057188 13.0944 -0.0590332 12.6132 0.175311 12.2109L6.88273 0.642873C7.11707 0.240618 7.53409 0 8.00094 0C8.46778 0 8.88665 0.240618 9.11915 0.642873L15.8247 12.2109C16.0572 12.6132 16.0572 13.0944 15.8247 13.4967C15.5922 13.8989 15.1734 14.1395 14.7065 14.1395ZM7.8404 1.19391L1.13483 12.762C1.0887 12.8409 1.11453 12.9107 1.13483 12.9456C1.15513 12.9805 1.20126 13.0375 1.29537 13.0375H14.7084C14.8006 13.0375 14.8486 12.9805 14.8689 12.9456C14.8892 12.9107 14.915 12.8428 14.8689 12.762L8.16147 1.19391C8.11534 1.11493 8.04153 1.10207 8.00094 1.10207C7.96034 1.10207 7.88653 1.11309 7.8404 1.19391Z" fill="currentColor"/><path d="M8.29062 9.60085H7.99723C7.93633 9.60085 7.90127 9.59167 7.88651 9.5733C7.8736 9.55493 7.86621 9.49065 7.86621 9.38044C7.86621 9.21697 7.76473 8.45286 7.55991 7.09181C7.40306 6.01729 7.32556 5.43503 7.32556 5.34503C7.32556 5.12462 7.39937 4.93543 7.54883 4.77747C7.69645 4.62134 7.90681 4.54236 8.17806 4.54236C8.44931 4.54236 8.63383 4.62685 8.77038 4.79583C8.90692 4.96482 8.9752 5.16135 8.9752 5.38544C8.9752 5.54157 8.86633 6.32771 8.64675 7.7457C8.50282 8.67511 8.42901 9.20962 8.42532 9.34921C8.42532 9.47779 8.41794 9.55126 8.40318 9.57146C8.38841 9.59167 8.34966 9.60085 8.29062 9.60085ZM8.10056 10.1078C8.32568 10.1078 8.5102 10.196 8.65413 10.3705C8.79806 10.545 8.87002 10.758 8.87002 11.0097C8.87002 11.2466 8.79621 11.4597 8.64859 11.6525C8.50097 11.8454 8.3183 11.9409 8.09871 11.9409C7.87913 11.9409 7.71491 11.8564 7.5802 11.6874C7.4455 11.5185 7.37723 11.3072 7.37723 11.0519C7.37723 10.7966 7.44181 10.5799 7.57098 10.3907C7.70014 10.2015 7.87544 10.106 8.09871 10.106L8.10056 10.1078Z" fill="currentColor"/></svg>`,
    close: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14px" height="14px"> <path d="M 13.604 13.604 C 13.076 14.132 12.204 14.132 11.676 13.604 L 7 8.929 L 2.338 13.604 C 1.809 14.132 0.938 14.132 0.409 13.604 C -0.119 13.076 -0.119 12.204 0.409 11.676 L 5.072 7 L 0.396 2.338 C -0.132 1.796 -0.132 0.938 0.396 0.396 C 0.66 0.132 1.017 0 1.36 0 C 1.704 0 2.06 0.132 2.325 0.396 L 7 5.072 L 11.662 0.396 C 12.191 -0.132 13.062 -0.132 13.591 0.396 C 13.855 0.661 13.987 1.017 13.987 1.361 C 13.987 1.704 13.855 2.061 13.591 2.325 L 8.928 7 L 13.604 11.663 C 14.132 12.204 14.132 13.063 13.604 13.604 Z"></path> </svg>`,
    more_vert: `<svg width="4" height="16" viewBox="0 0 4 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2 4.00985C3.1 4.00985 4 3.11203 4 2.01469C4 0.917352 3.1 0.0195312 2 0.0195312C0.9 0.0195312 0 0.917352 0 2.01469C0 3.11203 0.9 4.00985 2 4.00985ZM2 6.00499C0.9 6.00499 0 6.90281 0 8.00014C0 9.09748 0.9 9.9953 2 9.9953C3.1 9.9953 4 9.09748 4 8.00014C4 6.90281 3.1 6.00499 2 6.00499ZM2 11.9905C0.9 11.9905 0 12.8883 0 13.9856C0 15.083 0.9 15.9808 2 15.9808C3.1 15.9808 4 15.083 4 13.9856C4 12.8883 3.1 11.9905 2 11.9905Z" fill="currentColor"/></svg>`,
    indeterminate: `<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="M280-440h400v-80H280v80Zm-80 320q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-560H200v560Zm0-560v560-560Z"/></svg>`,
    priority_high: `<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.3436 13H1.5802L6.67867 2.23656L12.3436 13Z" stroke="#EE3333" stroke-width="2"/></svg>`
  };