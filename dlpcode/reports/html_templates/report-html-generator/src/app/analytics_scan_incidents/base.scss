:host {
  display: flex;
  flex-direction: column;
  gap: 10px;
  position: relative;
}

.info-table {
  border-spacing: 10px;
  border-collapse: true;
  font-size: 12px;
  width: 100%;

  .link {
    cursor: pointer;
    color: #2564bf;
  }

  .percent-cell {
    width: 65%;
  }

  .percent-bar {
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    background-color: #aedff8;
    height: 10px;
  }
}

.title {
  font-size: 16px;
  font-weight: 700;
  color: #222222;
}

.small-title {
  font-size: 14px;
  font-weight: 700;
  color: #222222;
}

.body {
  position: relative;
  min-height: 100px;
}

.loading-panel {
  background-color: rgb(255, 255, 255) !important;
}

.pointer {
  cursor: pointer;
}

.circle {
  display: inline-block;
  height: 8px;
  width: 8px;
  border-radius: 50%;
  margin-right: 4px;
}

.align-right {
  text-align: right;
}

.align-center {
  text-align: center;
}

.content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  justify-content: center;

  .chart-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 200px;
    width: 100%;
  }
}

.circle-icon {
  display: inline-block;
  height: 8px;
  width: 8px;
  border-radius: 50px;
}

.footer {
  display: flex;
  align-items: center;
  gap: 20px;
  padding-left: 25px;

  .small-group {
    display: flex;
    align-items: center;
    font-size: 12px;
    gap: 8px;
  }

  mat-icon {
    margin: 0 15px 0 auto;
  }
}

@media (max-width: 1110px) {
  :host {
    min-width: 800px;
  }
}

.bold {
  font-weight: bold;
}

.dialog-content {
  overflow-y: visible;
  width: 100%;
  height: 99%;
  display: flex;
  flex-direction: column;
}

.title-panel {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  font-weight: bold;
  font-size: 16px;
  padding: 14px 20px 14px 20px;

  .info {
    width: 16px;
    height: 16px;
    cursor: help;
  }

  .close {
    margin-left: auto;
    cursor: pointer;
    transition: opacity 0.15s;
  }

  span {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
  }
}

.bold {
  font-weight: bold;
}

.align-right {
  text-align: right;
}

.align-center {
  text-align: center;
}

.main-table {
  width: 98%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin: 20px 10px;
  overflow-x: hidden;
  overflow-y: auto;
}

.info-table {
  width: 98%;
  border-collapse: collapse;

  tr {
    height: 30px;
    font-size: 12px;
    border-bottom: solid 1px #cccccc;
  }

  .percent-cell {
    width: 40%;
  }

  .percent-bar {
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    background-color: #aedff8;
    height: 10px;
  }
}

.footer {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: flex-end;
  height: 48px;
  padding-left: 20px;
  padding-right: 20px;
  gap: 20px;
}
