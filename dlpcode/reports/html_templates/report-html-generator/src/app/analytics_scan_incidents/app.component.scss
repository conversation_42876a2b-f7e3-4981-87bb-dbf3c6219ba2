:host {
  display: block;
  padding: 24px;

  .a4 {
    max-width: 210mm;
  }

  .report-title {
    display: flex;
    align-items: center;
    gap: 12px;

    mat-icon {
      width: 24px;
      height: 24px;
      font-size: 24px;
    }

    .title {
      font-size: 24px;
      font-weight: 600;
    }
  }

  .report-info {
    display: grid;
    width: 850px;
    gap: 12px;
    margin: 16px 0;

    .item {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    label {
      font-size: 14px;
      font-weight: 600;
    }
  }

  .column-2 {
    grid-column: span 2;
  }

  .column-3 {
    grid-column: span 3;
  }

  .column-4 {
    grid-column: span 4;
  }

  .column-5 {
    grid-column: span 5;
  }

  .column-6 {
    grid-column: span 6;
  }

  .column-8 {
    grid-column: span 8;
  }
  .column-12 {
    grid-column: span 12;
  }

  .no-border {
    border: none !important;
  }
}


.basic-info {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .row {
    display: flex;
    gap: 8px;
    align-items: center;

    &.scan-info {
      gap: 20px;

      .title {
        font-size: 14px;
      }
    }

    &.queries-info {
      gap: 20px;

      mat-divider {
        height: 24px;
      }
    }    
  }

  mat-icon {
    width: 24px;
    height: 24px;
    font-size: 24px;
  }

  .title {
    font-weight: 700;
  }

  .report-title {
    font-size: 18px;
  }

  .report-name {
    font-size: 14px;
  }

  .schedule {
    margin-left: auto;
  }

  .item {
    display: flex;
    gap: 8px;
  }
}

.table-box {
  height: 600px;

  &.printing {
    height: 100%;

    common-table {
      table {
        width: 100%;
        max-width: 100%;
        table-layout: fixed;
        border-collapse: collapse;
      }

      th, td {
        word-wrap: break-word;
        word-break: break-word;
        white-space: normal;
      }
    }

  }
}

mat-divider {
  margin: 8px 0;
}

.detail-title {
  padding: 6px 10px;
  font-size: 16px;
  font-weight: 600;
}

.detail-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}