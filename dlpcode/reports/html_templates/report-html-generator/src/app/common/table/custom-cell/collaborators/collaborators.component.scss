:host {
  min-height: 100px;
  min-width: 200px;
}

.title {
  .text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  mat-icon {
    flex-shrink: 0;
  }
}

.content {
  display: flex;
  align-items: center;
  gap: 6px;
}

.displayed-text {
  cursor: pointer;
  transition: opacity 0.2s;
  color: #2563bf;

  &:hover {
    opacity: 0.5;
  }
}

.popover-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 460px;
  overflow: auto;
  padding: 12px;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 10px;
  line-height: 16px;

  .item {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #2563bf;
    cursor: pointer;
    transition: opacity 0.2s;

    &:hover {
      opacity: 0.5;
    }
  }
}

.section-title {
  font-weight: 700;
}
