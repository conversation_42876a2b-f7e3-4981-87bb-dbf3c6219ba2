import {
  ChangeDetectionStrategy,
  Component,
  Input,
  SecurityContext,
  ViewChild
} from '@angular/core';
import { MatMenuTrigger } from '@angular/material/menu';
import { DomSanitizer } from '@angular/platform-browser';

/**
 * This is a customized component for multiple items display
 */
@Component({
  selector: 'fd-multiple-items-popover',
  templateUrl: './multiple-items-popover.component.html',
  styleUrls: ['./multiple-items-popover.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MultipleItemsPopoverComponent {
  /**
   * Menu trigger
   */
  @ViewChild(MatMenuTrigger) trigger: MatMenuTrigger;

  /**
   * Whether the displayed item is clickable
   */
  @Input()
  clickable? = true;

  /**
   * Set classes for the displayed item
   */
  @Input()
  displayedItemClass: string;

  /**
   * The item for display
   */
  @Input()
  set displayedItem(value) {
    this._displayed.item = value;
  }

  /**
   * The item for triggering the menu
   */
  @Input()
  set triggeredItem(value) {
    this._triggered.item = value;
  }

  /**
   * Porover content
   */
  @Input()
  set popoverContent(value) {
    this._popover.item = value;
  }

  /**
   * Popover title
   */
  @Input()
  popoverTitle: string;

  /**
   * Max height of popover-content
   */
  @Input()
  maxInnerHeight: string;

  /**
   * Maximum allowed width for the title
   */
  @Input()
  titleWidth?: number;

  get displayedItem() {
    return this.getHtml('displayed');
  }

  get triggeredItem() {
    return this.getHtml('triggered');
  }

  get popoverContent() {
    return this.getHtml('popover');
  }

  private _displayed: any = {};
  private _triggered: any = {};
  private _popover: any = {};

  constructor(private domSanitizer: DomSanitizer) {}

  /**
   * Close the menu
   */
  close() {
    this.trigger.closeMenu();
  }

  /**
   * Get input data
   *
   * @param type data type
   */
  private getHtml(type: 'displayed' | 'triggered' | 'popover') {
    const obj = this[`_${type}`];

    if (obj.item === obj.previous) {
      return obj.html;
    } else {
      obj.previous = obj.item;
      obj.html = this.domSanitizer.sanitize(SecurityContext.HTML, obj.item);
      return obj.html;
    }
  }
}
