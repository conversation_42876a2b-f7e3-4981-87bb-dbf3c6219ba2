import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild
} from '@angular/core';
import { MatMenuTrigger } from '@angular/material/menu';
import { sumBy, values } from 'lodash';

/**
 * Component to display a list of collaborators.
 */
@Component({
  selector: 'fd-collaborators',
  templateUrl: './collaborators.component.html',
  styleUrls: ['./collaborators.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CollaboratorsComponent {
  /**
   * Reference to the Material menu trigger to control opening/closing the menu
   */
  @ViewChild(MatMenuTrigger) trigger: MatMenuTrigger;

  /**
   * Title
   */
  @Input()
  title: string;

  /**
   * Maximum allowed width for the title
   */
  @Input()
  titleWidth?: number;

  /**
   * Input property for the collaborators
   */
  @Input()
  set value(v: { internal: any[]; external: any[] }) {
    if (!v) {
      return;
    }

    this.count = sumBy(Object.values(v), (x) => x.length);
    this._value = v;
  }

  /**
   * Getter for the collaborators
   */
  get value() {
    return this._value;
  }

  /**
   * Total count of all collaborators
   */
  count: number;

  /**
   * Event emitter triggered when a collaborator is selected
   */
  @Output() clicked = new EventEmitter<any>();

  /**
   * Collaborators value
   */
  private _value;

  constructor() {}

  /**
   * Close the Material menu
   */
  close() {
    this.trigger.closeMenu();
  }

  /**
   * Emit the selected collaborator
   */
  select(collaborator: string) {
    this.clicked.emit(collaborator);
    this.close();
  }

  /**
   * Return the text to be displayed:
   * - If no collaborators: returns empty string.
   * - If one collaborator: returns the name.
   * - If more than one: returns the first name and a "+N" indicator.
   */
  getDisplayedText() {
    if (this.count === 0) {
      return '';
    }
    const firstValue = values(this.value).flat()[0];
    if (this.count === 1) {
      return firstValue.name;
    }

    return `${firstValue.name} <b>+${this.count - 1}</b>`;
  }
}
