import { UtilService } from '@/service/util.service';
import { ChangeDetectionStrategy, Component, Input, ViewChild } from '@angular/core';
import { MatMenuTrigger } from '@angular/material/menu';
import { sumBy } from 'lodash';

/**
 * Component to display a list of shareable links.
 */
@Component({
  selector: 'fd-shareable-links',
  templateUrl: './shareable-links.component.html',
  styleUrls: ['./shareable-links.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ShareableLinksComponent {
  /**
   * Reference to the Material menu trigger to control opening/closing the menu.
   */
  @ViewChild(MatMenuTrigger) trigger: MatMenuTrigger;

  /**
   * Title
   */
  @Input()
  title: string;

  /**
   * Maximum allowed width for the title
   */
  @Input()
  titleWidth?: number;

  /**
   * Input property for the shareable links
   */
  @Input()
  set value(v: { public: string[]; internal: string[]; external: string[] }) {
    if (!v) {
      return;
    }

    this.count = sumBy(Object.values(v), (x) => x.length);
    this._value = v;
  }

  /**
   * Getter for the shareable links
   */
  get value() {
    return this._value;
  }

  /**
   * Total count of shareable links
   */
  count: number;

  /**
   * Shareable links value
   */
  private _value;

  constructor(private util: UtilService) {}

  /**
   * Close the Material menu
   */
  close() {
    this.trigger.closeMenu();
  }

  /**
   * Copy a given string to the clipboard
   */
  copy(str: string) {
    this.util.copy(str);
  }
}
