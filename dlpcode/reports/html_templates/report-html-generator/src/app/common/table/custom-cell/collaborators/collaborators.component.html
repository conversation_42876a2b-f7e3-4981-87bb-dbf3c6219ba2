<div class="content" *ngIf="count">
  <div
    class="displayed-text"
    [matMenuTriggerFor]="popover"
    (click)="$event.stopPropagation()"
    [innerHTML]="getDisplayedText()"
  ></div>
</div>
<mat-menu #popover="matMenu" yPosition="above" class="multiple-items-popover">
  <div (click)="$event.stopPropagation()">
    <div class="title">
      <div
        class="text"
        [fdTooltip]="title"
        [fdTooltipSettings]="{ placement: 'top' }"
        [ngStyle]="{ 'max-width': titleWidth + 'px' }"
      >
        {{ title }}
      </div>
      <mat-icon (click)="close()" [svgIcon]="'close'" class="close"></mat-icon>
    </div>
    <div class="popover-content">
      <div class="section" *ngIf="value?.external?.length">
        <div class="section-title">{{ 'external' | translate }}</div>
        <div
          *ngFor="let item of value.external"
          class="item"
          title="{{ item.name }}"
          (click)="select(item)"
        >
          {{ item.name }}
        </div>
      </div>
      <div class="section" *ngIf="value?.internal?.length">
        <div class="section-title">{{ 'internal' | translate }}</div>
        <div
          *ngFor="let item of value.internal"
          class="item"
          title="{{ item.name }}"
          (click)="select(item)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
  </div>
</mat-menu>
