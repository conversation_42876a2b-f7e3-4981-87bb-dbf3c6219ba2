<div class="content" *ngIf="count">
  <div class="displayed-text" [matMenuTriggerFor]="popover" (click)="$event.stopPropagation()">
    {{ 'analytics_files.links_count' | translate : { count } }}
  </div>
</div>
<mat-menu #popover="matMenu" yPosition="above" class="multiple-items-popover">
  <div (click)="$event.stopPropagation()">
    <div class="title">
      <div
        class="text"
        [fdTooltip]="title"
        [fdTooltipSettings]="{ placement: 'top' }"
        [ngStyle]="{ 'max-width': titleWidth + 'px' }"
      >
        {{ title }}
      </div>
      <mat-icon (click)="close()" [svgIcon]="'close'" class="close"></mat-icon>
    </div>
    <div class="popover-content">
      <div class="section" *ngIf="value?.public?.length">
        <div class="section-title">{{ 'public' | translate }}</div>
        <div *ngFor="let link of value.public" class="item">
          <span class="link" title="{{ link }}">{{ link }}</span>
          <mat-icon title="{{ 'copy' | translate }}" (click)="copy(link)">content_copy</mat-icon>
        </div>
      </div>
      <div class="section" *ngIf="value?.external?.length">
        <div class="section-title">{{ 'external' | translate }}</div>
        <div *ngFor="let link of value.external" class="item">
          <span class="link" title="{{ link }}">{{ link }}</span>
          <mat-icon title="{{ 'copy' | translate }}" (click)="copy(link)">content_copy</mat-icon>
        </div>
      </div>
      <div class="section" *ngIf="value?.internal?.length">
        <div class="section-title">{{ 'internal' | translate }}</div>
        <div *ngFor="let link of value.internal" class="item">
          <span class="link" title="{{ link }}">{{ link }}</span>
          <mat-icon title="{{ 'copy' | translate }}" (click)="copy(link)">content_copy</mat-icon>
        </div>
      </div>
    </div>
  </div>
</mat-menu>
