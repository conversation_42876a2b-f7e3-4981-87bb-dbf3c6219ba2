<div class="content">
  <ng-container *ngIf="triggeredItem; else noTriggeredItem">
    <div
      [innerHtml]="displayedItem"
      class="displayed-item {{ displayedItemClass ?? '' }}"
      [ngClass]="{clickable}"
    ></div>
    <div
      [innerHtml]="triggeredItem"
      [matMenuTriggerFor]="popover"
      class="triggered-item"
      (click)="$event.stopPropagation()"
    ></div>
  </ng-container>
  <ng-template #noTriggeredItem>
    <div
      [innerHtml]="displayedItem"
      class="displayed-item triggered-item {{ displayedItemClass ?? '' }}"
      [ngClass]="{clickable}"
      [matMenuTriggerFor]="popover"
      (click)="$event.stopPropagation()"
    ></div>
  </ng-template>
</div>
<mat-menu #popover="matMenu" yPosition="above" class="multiple-items-popover">
  <div (click)="$event.stopPropagation()">
    <div class="title">
      <div
        [fdTooltip]="popoverTitle"
        [fdTooltipSettings]="{ placement: 'top' }"
        [ngStyle]="{ 'max-width': titleWidth + 'px' }"
      >
        {{ popoverTitle }}
      </div>
      <mat-icon (click)="close()" [svgIcon]="'close'" class="close"></mat-icon>
    </div>
    <div
      [innerHtml]="popoverContent"
      class="popover-content"
      [ngStyle]="{ 'max-height': maxInnerHeight }"
    ></div>
  </div>
</mat-menu>
