#!/bin/bash

# Celery worker stop script for DLP system
# This script replaces the huey worker stop script

echo "Stopping Celery workers and beat scheduler..."

# Function to stop workers by PID file
stop_by_pidfile() {
    local pidfile=$1
    local worker_name=$2
    
    if [ -f "$pidfile" ]; then
        local pid=$(cat "$pidfile")
        if kill -0 "$pid" 2>/dev/null; then
            echo "Stopping $worker_name (PID: $pid)"
            kill -TERM "$pid"
            
            # Wait for graceful shutdown
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 30 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                echo "Force killing $worker_name (PID: $pid)"
                kill -KILL "$pid"
            fi
            
            rm -f "$pidfile"
            echo "Stopped $worker_name"
        else
            echo "$worker_name PID file exists but process not running, removing stale PID file"
            rm -f "$pidfile"
        fi
    else
        echo "No PID file found for $worker_name"
    fi
}

# Stop all workers by PID files
if [ -d "log" ]; then
    stop_by_pidfile "log/analyze_worker.pid" "analyze_worker"
    stop_by_pidfile "log/ddr_worker.pid" "ddr_worker"
    stop_by_pidfile "log/download_worker.pid" "download_worker"
    stop_by_pidfile "log/default_worker.pid" "default_worker"
    stop_by_pidfile "log/report_worker.pid" "report_worker"
    stop_by_pidfile "log/protection_worker.pid" "protection_worker"
    stop_by_pidfile "log/celery_beat.pid" "celery_beat"
fi

# Fallback: kill any remaining celery processes
echo "Checking for remaining Celery processes..."
celery_pids=$(pgrep -f "celery.*worker")
if [ ! -z "$celery_pids" ]; then
    echo "Found remaining Celery worker processes, stopping them..."
    pkill -TERM -f "celery.*worker"
    sleep 5
    pkill -KILL -f "celery.*worker" 2>/dev/null
fi

beat_pids=$(pgrep -f "celery.*beat")
if [ ! -z "$beat_pids" ]; then
    echo "Found remaining Celery beat processes, stopping them..."
    pkill -TERM -f "celery.*beat"
    sleep 5
    pkill -KILL -f "celery.*beat" 2>/dev/null
fi

echo "All Celery workers and beat scheduler stopped."

# Clean up any remaining PID files
if [ -d "log" ]; then
    rm -f log/*.pid
fi

echo "Cleanup completed."
