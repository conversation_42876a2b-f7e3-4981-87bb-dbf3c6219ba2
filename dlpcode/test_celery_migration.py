#!/usr/bin/env python3
"""
Test script for Celery migration from Huey
This script tests basic functionality of the migrated Celery tasks
"""

import sys
import os
import time
from datetime import datetime, timezone, timedelta

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_celery_app():
    """Test basic Celery app configuration"""
    try:
        from celery_app import app
        print("✓ Celery app imported successfully")
        
        # Test app configuration
        print(f"✓ Broker URL: {app.conf.broker_url}")
        print(f"✓ Result backend: {app.conf.result_backend}")
        print(f"✓ Task routes configured: {len(app.conf.task_routes)} routes")
        
        return True
    except Exception as e:
        print(f"✗ Celery app test failed: {e}")
        return False

def test_task_imports():
    """Test importing Celery tasks"""
    try:
        # Test DDR task import
        from celery_worker.ddr_task import process_ddr_events, scheduled_ddr_dispatcher, emergency_ddr_events
        print("✓ DDR tasks imported successfully")
        
        # Test tag worker import
        from celery_worker.tag_worker import update_remote_tag
        print("✓ Tag worker imported successfully")
        
        # Test analyze worker import (basic import)
        from celery_worker.analyze_worker import analyze_worker, priority_analyze_worker
        print("✓ Analyze worker imported successfully")
        
        return True
    except Exception as e:
        print(f"✗ Task import test failed: {e}")
        return False

def test_queue_management():
    """Test Celery queue management service"""
    try:
        from service.celery_queue_management_service import CeleryQueueManagementService
        
        # Test basic methods
        service = CeleryQueueManagementService()
        
        # Test pending count (should not fail)
        count = service.get_pending_count()
        print(f"✓ Queue management service working, pending count: {count}")
        
        return True
    except Exception as e:
        print(f"✗ Queue management test failed: {e}")
        return False

def test_priority_levels():
    """Test priority level configuration"""
    try:
        from celery_app import PRIORITY_LEVELS, get_priority_level
        
        print("✓ Priority levels configured:")
        for name, level in PRIORITY_LEVELS.items():
            print(f"  - {name}: {level}")
        
        # Test priority function
        ddr_priority = get_priority_level('DDR')
        emergency_priority = get_priority_level('EMERGENCY')
        
        assert ddr_priority == 8, f"DDR priority should be 8, got {ddr_priority}"
        assert emergency_priority == 10, f"Emergency priority should be 10, got {emergency_priority}"
        
        print("✓ Priority levels working correctly")
        return True
    except Exception as e:
        print(f"✗ Priority levels test failed: {e}")
        return False

def test_ddr_task_creation():
    """Test creating DDR tasks"""
    try:
        from celery_worker.ddr_task import process_ddr_events
        
        # Create a test task (don't execute)
        test_task = {
            'id': 'test-task-id',
            'name': 'Test DDR Task',
            'storage_type': 1,
            'storage_id': 'test-storage-id'
        }
        
        start_time = datetime.now(timezone.utc) - timedelta(hours=1)
        end_time = datetime.now(timezone.utc)
        
        # Test task signature creation
        task_signature = process_ddr_events.s(
            test_task, 
            start_time, 
            end_time,
            ddr_policy_ids=['test-policy'],
            target_operations=['CREATE', 'UPDATE'],
            scan_trigger_events=['FILE_CREATED']
        )
        
        print("✓ DDR task signature created successfully")
        print(f"  - Task name: {task_signature.task}")
        print(f"  - Priority: DDR (8)")
        print(f"  - Queue: ddr")
        
        return True
    except Exception as e:
        print(f"✗ DDR task creation test failed: {e}")
        return False

def test_config_migration():
    """Test configuration migration from Huey to Celery"""
    try:
        from util.config import get_global_config
        
        config = get_global_config()
        
        # Check if Celery config exists
        if 'celery' in config:
            celery_config = config['celery']
            print("✓ Celery configuration found")
            print(f"  - Broker URL: {celery_config.get('broker_url', 'Not set')}")
            print(f"  - Task routes: {len(celery_config.get('task_routes', {}))}")
            print(f"  - Beat schedule: {len(celery_config.get('beat_schedule', {}))}")
        else:
            print("✗ Celery configuration not found in config.json")
            return False
        
        # Check if old Huey config is removed or deprecated
        if 'huey' in config:
            print("⚠ Warning: Old Huey configuration still present")
        
        return True
    except Exception as e:
        print(f"✗ Config migration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("Celery Migration Test Suite")
    print("=" * 60)
    
    tests = [
        ("Celery App Configuration", test_celery_app),
        ("Task Imports", test_task_imports),
        ("Queue Management Service", test_queue_management),
        ("Priority Levels", test_priority_levels),
        ("DDR Task Creation", test_ddr_task_creation),
        ("Configuration Migration", test_config_migration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Celery migration appears successful.")
        return 0
    else:
        print("❌ Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
