{"Data Classification": {"Finance": {"description": "Financial data includes records of monetary transactions, accounting, investments, tax filings, and corporate financial reports. This data is highly sensitive and subject to regulatory compliance such as SOX, GLBA, and IFRS, requiring strict security and governance controls.", "order": 100}, "Transaction Record": {"description": "Includes all documents recording details of financial transactions, such as transaction confirmations, receipts, transfer instructions, and statements. These documents are used to prove the existence, condition, and completion of a transaction.", "order": 101}, "Financial Report": {"description": "Used to display the financial status of an individual or organization, including balance sheets, income statements, cash flow statements, and shareholder equity statements. They are critical for investors, management and regulators to understand and assess financial health.", "order": 102}, "Credit and Risk Management": {"description": "Documents involved in assessing and managing credit risk, market risk, etc., such as credit reports, risk assessment reports, and risk management strategies. These documents are important for financial institutions to develop risk controls and mitigation measures.", "order": 103}, "Financial Strategy and Research": {"description": "Including market research reports, investment strategy documents and industry analysis, etc., used to guide investment decisions and financial product development. This type of document provides financial institutions with market insights based on in-depth analysis and forecasts.", "order": 104}, "Tax and Regulatory Documents": {"description": "Documents prepared for tax reporting, compliance with government regulations, and financial oversight. These are critical for ensuring legal compliance and are often submitted to tax authorities or regulatory agencies.", "order": 105}, "Expense and Reimbursement Records": {"description": "Documents that support expense tracking and reimbursement processes. These typically include supporting evidence for employee expenses, management approvals, and payment confirmation. They are frequently used in both internal audits and operational controls.", "order": 106}, "Loan and Financing Agreements": {"description": "Documents outlining the terms and structure of loans, credit facilities, or other financial arrangements. These define obligations between borrowers and lenders, repayment conditions, interest rates, and covenants, and are central to debt and capital management.", "order": 107}, "Audit and Assurance Documents": {"description": "Documents generated during internal or external audit processes to assess financial accuracy, internal controls, and regulatory compliance. These reports provide independent assurance and are critical for corporate governance and risk management.", "order": 108}, "Healthcare": {"description": "This category includes medical data such as electronic health records (EHR), patient histories, treatment plans, prescriptions, and insurance claims. Due to its sensitivity, healthcare data is subject to strict regulations like HIPAA and GDPR.", "order": 200}, "Clinical Records": {"description": "Documents that record patient health information and treatment processes, including medical records, disease course records, surgical records, discharge summaries, examination and test reports, etc. These documents are the basis for medical care decisions and are critical to patient diagnosis, treatment, and follow-up.", "order": 201}, "Medication Management": {"description": "Documentation involving drug prescriptions, medication instructions, and drug monitoring. This includes prescription orders, medication records and adverse drug reaction reports to ensure patients are using their medications safely and effectively.", "order": 202}, "Insurance and Settlement": {"description": "Documents related to medical expense reimbursement and settlement, such as insurance claim forms, expense statements, and payment records. These documents are critical to processing medical bills and ensuring the financial benefit of providers and patients.", "order": 203}, "Compliance and Risk Management": {"description": "Patient Consent: A document in which a patient gives explicit consent to a treatment plan, surgery, or other medical procedure. Privacy Policy: Policies and measures describing how a medical institution protects patients’ personal information.", "order": 204}, "Clinical Studies": {"description": "Clinical research report: records the design, implementation process and result analysis of the clinical trial.", "order": 205}, "Information Technology": {"description": "This category includes data related to IT infrastructure, software development, system administration. It covers source code, network configurations,and IT policies critical to business operations and security.", "order": 300}, "User Documentation": {"description": "For end users, it provides detailed guidance on how to use a product or system. Includes user manual, quick start guide, FAQ (Frequently Asked Questions) and use cases. User documentation helps users understand product functionality and use the product effectively.", "order": 301}, "Development Documentation": {"description": "For software developers and project team members, it provides information such as product design, architecture, development specifications, and code samples. Development documentation supports the software development process and promotes effective communication within the team.", "order": 302}, "Test Documentation": {"description": "Documentation that records software test plans, test cases, test results, and defect reports. Test documentation is critical to ensuring product quality and identifying and fixing defects.", "order": 303}, "Operational Documentation": {"description": "Provides the guidance and processes system administrators and IT professionals need to manage and maintain IT systems. Includes system administration manuals, backup and recovery guides, security protocols and maintenance plans.", "order": 304}, "Training Materials": {"description": "Training course materials for users, developers, or system administrators designed to improve technical skills and product knowledge. Includes training manuals, online courses and instructional videos.", "order": 306}, "Configuration Files": {"description": "Configuration files containing structured information used to define system settings, application behaviors, or operational parameters. Includes JSON, XML, or YAML configuration files, environment variable definitions, and similar resources designed to facilitate system customization and operation.", "order": 307}, "Log": {"description": "Log is dedicated to recording detailed, time-stamped entries related to IT activities, system events, and operational workflows. It provides a chronological history of actions taken, system behavior, or incidents encountered, which is essential for auditing, troubleshooting, and maintaining system integrity.", "order": 308}, "Legal": {"description": "This category includes legal documents such as contracts, court filings, compliance records, and attorney-client communications. These materials are typically confidential and require careful handling.", "order": 400}, "Contracts and Agreements": {"description": "Formal documents that define the legally binding terms and conditions between two or more parties involved in a transaction or service relationship. This includes agreements such as loan contracts, investment management agreements, service contracts, and insurance policies. These documents specify each party's rights, obligations, responsibilities, and remedies in case of breach.", "order": 401}, "NDA Form": {"description": "Legal document in which one or more parties agree to maintain the confidentiality of proprietary or sensitive information disclosed during a business or professional relationship. NDAs are designed to protect trade secrets, business plans, technical data, or personal information, and typically outline the scope of confidentiality, duration, and consequences of breach.", "order": 402}, "Patent": {"description": "Official legal document granted by a governmental authority that confers exclusive rights to an inventor or applicant to make, use, sell, or license a specific invention for a fixed period (usually 20 years). A patent includes technical descriptions, claims, and drawings that define the scope of legal protection. It serves as a key instrument for protecting intellectual property and promoting innovation.", "order": 403}, "License Agreement": {"description": "Legally enforceable contract that allows one party (the licensee) to use certain intellectual property owned by another party (the licensor), such as software, trademarks, patents, or copyrighted content. The agreement specifies terms such as usage scope, payment or royalties, duration, restrictions, and termination clauses, ensuring both parties' rights and responsibilities are clearly defined.", "order": 404}, "HR": {"description": "This category encompasses employee-related records, including employment contracts, performance evaluations, disciplinary actions, and termination documents. These records are essential for managing the employment relationship and ensuring organizational accountability.", "order": 500}, "Timesheet": {"description": "Timesheet is used to record and track employee working hours, attendance, and time allocation across projects or tasks. Accurate timesheet documentation supports payroll processing, project management, and compliance with labor regulations.", "order": 501}, "Performance Review": {"description": "Documents related to employee performance evaluations. It helps track individual progress, identify areas for development, and support decisions related to promotions, training, or role adjustments. Consistent performance reviews contribute to employee growth and organizational success.", "order": 502}, "CV": {"description": "Candidate CVs submitted during the recruitment process. It provides quick access to applicants’ professional backgrounds, qualifications, and work experience, supporting informed hiring decisions and streamlined candidate evaluation.", "order": 503}, "Recruitment": {"description": "Documents related to the hiring process, including job requisitions, job descriptions, candidate applications, interview records, background checks, and offer letters. These documents are used to track recruitment activities, evaluate candidate qualifications, and ensure compliance with hiring policies. Proper management of recruitment documentation supports transparency, consistency, and alignment with organizational hiring standards.", "order": 504}, "Employee Record": {"description": "Comprehensive records maintained for each employee throughout their lifecycle within the organization. Includes personal information, employment contracts, benefits enrollment, disciplinary records, training history, and separation documentation. These records ensure compliance with labor regulations and support HR operations, audits, and dispute resolution.", "order": 505}, "Source Code": {"description": "Source code is written in a programming language, which can be a high-level language (such as Python, Java, C++) or a low-level language that is closer to machine language (such as assembly language). It exists in text form, so it can be created and edited using a text editor.", "order": 600}, "C Language": {"description": "The 'C Language' here refers collectively to both C and C++ source files. C provides low-level programming capabilities, while C++ builds on C with object-oriented features. Their source files typically use extensions such as .c, .cpp, and .h, and are compiled into machine code for high-performance applications.", "order": 601}, "Golang": {"description": "Golang source files use the .go extension and start with a package declaration. They are compiled and used to build efficient, concurrent systems such as web servers, APIs, and CLI tools.", "order": 602}, "Java": {"description": "Java source files use the .java extension, typically containing a public class matching the filename. They are compiled into bytecode and run on the Java Virtual Machine (JVM) for platform independence.", "order": 603}, "JavaScript": {"description": "JavaScript files (.js, .mjs) contain code for browser or server environments. They define variables, functions, and modules, and are widely used for building interactive web applications.", "order": 604}, "Kotlin": {"description": "Kotlin source files (.kt, .kts) define classes, functions, and scripts. Interoperable with Java, Kotlin is widely used in Android and backend development for its concise syntax and modern features.", "order": 605}, "PHP": {"description": "PHP files (.php) embed code within HTML and are executed server-side. They handle logic, database access, and dynamic content generation for websites and web applications.", "order": 606}, "Python": {"description": "Python source files (.py) contain readable, indented code used in scripting, data science, web development, and automation. They are interpreted by the Python runtime environment.", "order": 607}, "Shell": {"description": "Shell scripts (.sh, .bash) contain command-line instructions, variables, and control structures. They automate tasks in Unix/Linux environments and are interpreted by shell programs like bash or zsh.", "order": 608}, "C#": {"description": "C# source files (.cs) define classes, methods, and application logic. They are compiled by the .NET runtime and are widely used in Windows applications, games (Unity), and enterprise systems.", "order": 609}}}