from presidio_analyzer.nlp_engine import SpacyNlpEngine, NerModelConfiguration, NlpEngine, NlpArtifacts
import spacy
from typing import Iterable, Tu<PERSON>, Iterator, List
from file_analyzer.util.rw_lock import RWLock
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import math
import logging

logger = logging.getLogger("fortidata_recognizer")

MODEL_TO_PRESIDIO_ENTITY_MAPPING = dict(
    PER="PERSON",
    PERSON="PERSON",
    LOC="LOCATION",
    LOCATION="LOCATION",
    GPE="LOCATION",
    ORG="ORGANIZATION",
    DATE="DATE",
    NORP="NRP",
    AGE="AGE",
    ID="ID",
    EMAIL="EMAIL",
    PATIENT="PERSON",
    STAFF="PERSON",
    HOSP="HOSPITAL",
    PATORG="ORGANIZATION",
    PHONE="PHONE_NUMBER",
    HCW="PERSON",
    HOSPITAL="HOSPITAL",
    PROBLEM="PROBLEM",
    TREATMENT="TREATMENT",
    STREET_ADDRESS="STREET_ADDRESS",
    FAC="FAC"
)



class RecognitionEngine(SpacyNlpEngine):
    def __init__(self, model_path:str, max_threads=4):
        models = [{
            "lang_code": "en",
            "model_name": model_path
        }]
        model_configuration = NerModelConfiguration(model_to_presidio_entity_mapping=MODEL_TO_PRESIDIO_ENTITY_MAPPING)
        super().__init__(models, model_configuration)
        self.load()
        self.lock = RWLock()
        self.max_threads = max(max_threads, 1)
        if self.max_threads > 1:
            self.executor = ThreadPoolExecutor(thread_name_prefix="analyzer_nlp_engine_", max_workers=self.max_threads)
            logger.info(f"RecognitionEngine create thread pool, max_worker: {self.max_threads}")
        else:
            logger.info(f"RecognitionEngine no thread pool")
            self.executor = None

    def reload_if_needed(self, threshold:int):
        import psutil, gc
        try:
            self.lock.acquire_write()
            for model in self.models:
                memory = psutil.Process().memory_info().rss
                logger.info(f"[memory_log] Current memory RSS: {memory/1024/1024} MB")
                if self.nlp[model["lang_code"]] and memory > threshold:
                    logger.info(f"[memory_log] Reload nlp model")
                    del self.nlp[model["lang_code"]]
                    gc.collect()
                    self.nlp[model["lang_code"]] = spacy.load(model["model_name"])
        except Exception as e:
            logger.error(f"reload spacy model if needed failed: {e}")
        finally:
            self.lock.release_write()

        
    def process_multiple_threads(self, text:str, language:str) -> Iterator[Tuple[int, NlpArtifacts]]:
        
        # single thread
        # when the length of the text less than 10k, the single thread has a faster speed to process text. 
        # 4 threads
        # For      10240,10.00k 0.05 seconds, 0.05 seconds
        # For      20480,20.00k 0.07 seconds, 0.09 seconds
        # For      30720,30.00k 0.08 seconds, 0.14 seconds
        # For      40960,40.00k 0.10 seconds, 0.18 seconds
        # For      51200,50.00k 0.15 seconds, 0.23 seconds
        # For     102400,100.00k 0.24 seconds, 0.44 seconds
        # For     307200,300.00k 0.63 seconds, 1.33 seconds
        # For     512000,500.00k 1.01 seconds, 2.24 seconds
        # For     819200,800.00k 1.56 seconds, 3.52 seconds
        if self.executor is None or len(text) < 10*1024:
            logger.debug(f"process text with single thread")
            return [(0, self.process_text(text, language))]
        
        # multiple threads
        chunks = self.split_text(text, math.ceil(len(text)/max(self.max_threads, 1)), self.max_threads)
        logger.debug(f"process text with multiple threads. chunks: {len(chunks)}")
        
        results = []
        future_to_sentence = {self.executor.submit(self.process_text, chunk, language): (base_offset) for (chunk, base_offset) in chunks}
        for future in as_completed(future_to_sentence):
            base_offset = future_to_sentence[future]
            try:
                nlp_artifacts = future.result()
                results.append((base_offset, nlp_artifacts))
            except Exception as exc:
                raise exc
        return results
    
    def process_text(self, text: str, language: str) -> NlpArtifacts:
        """Execute the SpaCy NLP pipeline on the given text and language."""
        try:
            self.lock.acquire_read()
            if not self.nlp:
                raise AttributeError("NLP engine is not loaded. Consider calling .load()")

            # with self.nlp[language].memory_zone():
            doc = self.nlp[language](text, disable=["parser","tagger","lemmatizer","attribute_ruler"])
            return self._doc_to_nlp_artifact(doc, language)
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"process text using nlp {language} model failed: {e}")
        finally:
            self.lock.release_read()

    def split_text(self, text, chunk_size, chunk_count):
        accept_offset = 128
        min_chunk_size = 10*1024
        chunk_size = max(chunk_size, min_chunk_size)
        logger.debug(f"chunk_size: {chunk_size}")
        chunks = []
        start = 0
        text_length = len(text)
        while start < text_length:
            if len(chunks) == max(chunk_count-1, 1) or (text_length - start <= chunk_size):
                split_pos = text_length
                logger.debug(f"start: {start}  pos: {split_pos}, {split_pos - start}")
            else:
                end = min(start + chunk_size, text_length)
                # Find the nearest '. ' within `accept_offset` characters before or after the chunk end
                split_pos = text.find('. ', max(start, end-accept_offset), end + accept_offset)
                if split_pos == -1:
                    split_pos = end
                else:
                    split_pos += 1  # Include the '.' character
                logger.debug(f"start: {start}  pos: {split_pos} end: {end}, {split_pos - start}")
            chunks.append((text[start:split_pos], start))
            start = split_pos
        return chunks
    
class EmptyNlpEngine(SpacyNlpEngine):
    def __init__(self) -> None:
        pass

    def load(self) -> None:
        pass

    def is_loaded(self) -> bool:
        return True

    def process_multiple_threads(self, text: str, language:str) -> Iterator[Tuple[int, NlpArtifacts]]:
        return None
    
    def process_text(self, text: str, language: str) -> NlpArtifacts:
        return None
    def process_batch(
        self, texts: Iterable[str], language: str, **kwargs  # noqa ANN003
    ) -> Iterator[Tuple[str, NlpArtifacts]]:
        pass

    def is_stopword(self, word: str, language: str) -> bool:
        return False

    def is_punct(self, word: str, language: str) -> bool:
        return False

    def get_supported_entities(self) -> List[str]:
        return []