from presidio_analyzer import <PERSON><PERSON>gni<PERSON><PERSON><PERSON>ult
from typing import List

def remove_duplicates(results: List[RecognizerResult]) -> List[RecognizerResult]:
        results = list(set(results))
        results = sorted(results, key=lambda x: (-x.score, x.start, -(x.end - x.start)))
        filtered_results = []

        for result in results:
            to_keep = result not in filtered_results  # equals based comparison
            if to_keep:
                flag = True
                ind = 0
                for filtered in filtered_results:
                    if (
                        result.contained_in(filtered)
                        or filtered.contained_in(result)
                    ):
                        if result.score > filtered.score:
                            filtered_results[ind] = result
                        flag = False
                        break
                    ind += 1

            if flag:
                filtered_results.append(result)

        return filtered_results

def deduplicate_results(results):
    sorted_results = sorted(results, key=lambda x: x.start)
    filtered_results = []

    for result in sorted_results:
        if not filtered_results:
            filtered_results.append(result)
        else:
            last_result = filtered_results[-1]
            if result.start < last_result.end:
                if abs(result.score - last_result.score) <= 0.05:
                    if result.end > last_result.end:
                        filtered_results[-1] = result
                    elif result.analysis_explanation and result.analysis_explanation.validation_result:
                        filtered_results[-1] = result
                else:
                    if result.score > last_result.score:
                        filtered_results[-1] = result
            else:
                filtered_results.append(result)

    return filtered_results