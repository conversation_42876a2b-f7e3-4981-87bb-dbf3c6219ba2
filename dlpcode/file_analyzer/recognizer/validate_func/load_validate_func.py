from .funcs.luhn_validate import luhn_checksum_validate
from .funcs.au_abn_validate import au_abn_validate
from .funcs.au_acn_validate import au_acn_validate
from .funcs.au_routing_validate import au_routing_validate
from .funcs.au_tfn_validate import au_tfn_validate
from .funcs.url_validate import url_validate
from .funcs.in_aadhaar_validate import in_aadhaar_validate
from .funcs.ip_address_validate import ip_address_validate
from .funcs.pl_pesel_validate import pl_pesel_validate
from .funcs.us_ssn_validate import us_ssn_validate
from .funcs.us_dea_validate import us_dea_validate
from .funcs.us_npi_validate import (
    us_npi_validate
)
from .funcs.uk_nhs_validate import uk_nhs_validate
from .funcs.sin_validate import sin_validate
from .funcs.imei_validate import imei_validate
from .funcs.iban_validate import iban_validate
from .funcs.id_validate import (
    bg_id_validate,
    cn_id_validate,
    it_id_validate,
    pl_id_validate,
    se_id_validate,
    ie_pps_validate
)
from .funcs.es_tin_validate import es_tin_validate
from .funcs.si_tin_validate import si_tin_validate
from .funcs.pt_tin_validate import pt_tin_validate
from .funcs.cy_tin_validate import cy_tin_validate
from .funcs.se_tin_validate import se_tin_validate
from .funcs.at_tin_validate import at_tin_validate
from .funcs.aba_routing_validate import aba_routing_validate
from .funcs.fr_hin_insee_validate import (
    fr_insee_validate,
    fr_hin_validate,
)
from .funcs.fi_hin_validate import fi_hin_validate

from .funcs.us_ndc_validate import (
    us_ndc_validate
)
from .funcs.us_udi_validate import (
    us_udi_validate
)
from .funcs.us_hcpcs_validate import (
    us_hcpcs_validate
)
from .funcs.icd_validate import (
    icd_validate
)
from .funcs.alphanumeric_validate import (
    no_all_alpha_validate,
    no_all_numeric_validate,
    no_uniform_alphanumeric_validate
)
from .funcs.library_validate import library_validate

def LoadValidateFunction()->dict:
    ret = dict()
    variables = globals().copy()
    for name in variables:
        if name.endswith("_validate"):
            ret[name] = variables[name]
    return ret.copy()

# def LoadValidateLoaderFunction()->dict:
#     ret = dict()
#     variables = globals().copy()
#     for name in variables:
#         if name.endswith("_load"):
#             validator_name = name[0:-5]
#             ret[validator_name] = variables[name]
#     return ret.copy()

VALIDATE_FUNC = LoadValidateFunction()
# VALIDATE_FUNC_LOADER = LoadValidateLoaderFunction()