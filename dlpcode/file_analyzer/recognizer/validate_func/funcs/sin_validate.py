from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils

def sin_validate(pattern_text: str) -> bool:
    text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""), (".", "")])
    sin_list = [int(digit) for digit in text if not digit.isspace()]

    # Set weights based on digit position
    weight = [1, 2, 1, 2, 1, 2, 1, 2, 1]

    # Perform checksums
    checksum = []
    for i in range(len(weight)):
        digit = weight[i] * sin_list[i]
        digit = int(digit/10) + int(digit%10) if digit > 9 else digit
        checksum.append(digit)
    total = sum(checksum)
    return total%10 == 0



if __name__ == '__main__':
    text = '123456782'
    text = '628 035 289'
    rst = sin_validate(text)
    print(f"rst: {rst}")
