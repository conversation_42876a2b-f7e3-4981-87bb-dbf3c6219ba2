from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils
from typing import List

def luhn_checksum_validate(pattern_text: str) -> bool:  # credit_card
    sanitized_value = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""),(".", ""),("/", "")])
    checksum = luhn_checksum(sanitized_value)

    return checksum % 10 == 0

# def luhn_checksum(sanitized_value: str) -> int:
#     def digits_of(n: str) -> List[int]:
#         return [int(dig) for dig in str(n)]

#     digits = digits_of(sanitized_value)
#     odd_digits = digits[0::2]
#     even_digits = digits[1::2]
#     checksum = sum(odd_digits)
#     for d in even_digits:
#         checksum += sum(digits_of(str(d * 2)))
#     return checksum

def luhn_checksum(card_number):
    def digits_of(n):
        return [int(d) for d in str(n)]
    digits = digits_of(card_number)
    odd_digits = digits[-1::-2]
    even_digits = digits[-2::-2]
    checksum = 0
    checksum += sum(odd_digits)
    for d in even_digits:
        checksum += sum(digits_of(d * 2))
    return checksum