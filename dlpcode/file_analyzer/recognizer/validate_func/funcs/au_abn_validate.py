from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils
def au_abn_validate(pattern_text: str) -> bool:
    """
    Validate the pattern logic e.g., by running checksum on a detected pattern.

    :param pattern_text: the text to validated.
    Only the part in text that was detected by the regex engine
    :return: A bool indicating whether the validation was successful.
    """
    # Pre-processing before validation checks
    text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""), (".", "")])
    abn_list = [int(digit) for digit in text if not digit.isspace()]

    # Set weights based on digit position
    weight = [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19]

    # Perform checksums
    abn_list[0] = 9 if abn_list[0] == 0 else abn_list[0] - 1
    sum_product = 0
    for i in range(11):
        sum_product += abn_list[i] * weight[i]
    remainder = sum_product % 89
    return remainder == 0