#US Drug Enforcement Agency Registration Number (DEA)
from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils

def us_dea_validate(pattern_text: str) -> bool:
    '''
    Step 1:
        get 6 numbers from Position 3 - 8
    Step 2:
        add the even-positioned digits together
    Step 3:
        multiply the even-positioned sum by 2
    Step 4:
        take the number got in previous step, and add the odd-positioned digits
    Step 5:
        the last digit of the added number is the final "checksum"
    '''
    pattern_text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""), (".", "")])
    if len(pattern_text) != 9:
        return False
    
    digits = [int(digit) for digit in str(pattern_text)[2:8]]
    print(digits)
    print(digits[1::2])
    even_sum = sum(digits[1::2])
    even_sum *= 2
    total_sum = even_sum + sum(digits[::2])
    checksum = total_sum % 10
    
    print(checksum)
    
    return checksum == int(pattern_text[8])