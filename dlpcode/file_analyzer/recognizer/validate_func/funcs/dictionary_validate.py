import os
import logging
from ..load_validate_data import ValidateDataConnector
import inspect

logger = logging.getLogger("fortidata_recognizer")

def dictionary_validate(pattern_text: str, validate_id:str, data_connector:ValidateDataConnector=None) -> bool:
    
    if not data_connector:
        logger.error(f"{validate_id}: missing library")
        return False
         
    if data_connector.get_validate_data(
        validate_id=validate_id, 
        value=pattern_text,
        logger=logger
    ):
        logger.debug(f"{validate_id}: True")
        return True
    else:
        return False