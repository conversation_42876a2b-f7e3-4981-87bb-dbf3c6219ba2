from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils

def cy_tin_validate(pattern_text: str) -> bool:
    text = Utils.sanitize_value(pattern_text[:-1], [("-", ""), (" ", "")])
    even_sum = sum(int(text[i]) for i in range(1, len(text), 2))

    odd_sum = sum({
        '0': 1, '1': 0, '2': 5, '3': 7, '4': 9,
        '5': 13, '6': 15, '7': 17, '8': 19, '9': 21
    }.get(text[i], 0) for i in range(0, len(text), 2))

    total_sum = even_sum + odd_sum

    remainder = total_sum % 26

    check_character = chr(remainder + 65)

    return check_character.upper() == pattern_text[-1].upper()

if __name__ == "__main__":
    """00123123T
99652156X
    """
    example = "99652156X"
    print(cy_tin_validate(example))