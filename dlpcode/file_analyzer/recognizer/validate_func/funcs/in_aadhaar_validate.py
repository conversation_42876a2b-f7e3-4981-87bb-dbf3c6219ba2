from typing import List
from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils

def in_aadhaar_validate(pattern_text: str) -> bool: #in_aadhaar
    """Determine absolute value based on calculation."""
    sanitized_value = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""), (":", "")])
    return check_aadhaar(sanitized_value)

def check_aadhaar(sanitized_value: str) -> bool:
    is_valid_aadhaar: bool = False
    if (
        len(sanitized_value) == 12
        and sanitized_value.isnumeric() is True
        and int(sanitized_value[0]) >= 2
        and Utils.is_verhoeff_number(int(sanitized_value)) is True
        and Utils.is_palindrome(sanitized_value) is False
    ):
        is_valid_aadhaar = True
    return is_valid_aadhaar