from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils
    

def uk_nhs_validate(pattern_text: str) -> bool: # UK_NHS
    """
    Validate the pattern logic e.g., by running checksum on a detected pattern.

    :param pattern_text: the text to validated.
    Only the part in text that was detected by the regex engine
    :return: A bool indicating whether the validation was successful.
    """
    text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""),(".", "")])
    total = sum(
        [int(c) * multiplier for c, multiplier in zip(text, reversed(range(11)))]
    )
    remainder = total % 11
    check_remainder = remainder == 0

    return check_remainder


if __name__ == "__main__":
    with open("nhs.txt", 'r') as f:
        lines = f.readlines()
        
    for line in lines:
        line = line.strip()
        
        r = uk_nhs_validate(line)
        print(f"{line}  {r}")
    
    # output 
    '''
************  True
************  True
************  True
************  True
************  True
************  True
************  True
************  True
************  True
************  True
    '''