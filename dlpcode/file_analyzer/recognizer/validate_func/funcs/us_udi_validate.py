import os
import logging
from ..library_connector import LibraryConnector

logger = logging.getLogger("fortidata_recognizer")


# https://www.fda.gov/media/96648/download?attachment
# https://www.fda.gov/medical-devices/unique-device-identification-system-udi-system/udi-basics
def us_udi_validate(pattern_text: str, library_id:str=None, library_connector:LibraryConnector=None) -> bool:  # udi
    logger.debug(f"call us_udi_validate to validate: {pattern_text}")
    
    if not library_connector or not library_id:
        logger.error("us_udi_validate: missing library")
        return False
    
    # UDI = DI  + PI
    # Pick out DI from pattern_text
    
    # GS1® Issuing Agency
    # (01)51022222233336(11)141231(17)150707(10)A213B1(21)1234
    if pattern_text.startswith("(01)"):
        # Field size 14  + prefix '(01)'  = 18
        if len(pattern_text) < 18:   
            return False
        di = pattern_text[4:18]  
    # Health Industry Business Communications Council® (HIBCC) Issuing Agency
    # *+H123PARTNO1234567890120/$$420020216LOT123456789012345SXYZ456789012345678/16D20130202C*
    # +H123PARTNO1234567890120/$$420020216LOT123456789012345SXYZ456789012345678/16D20130202C
    elif pattern_text.startswith("*+") or pattern_text[0]=='+':
        if pattern_text[0]=='+':
            sub_text = pattern_text[1:]
        else:
            sub_text = pattern_text[2:]
        di_end = sub_text.find('/')
        # Field size 6 - 23
        if di_end == -1:
            if len(sub_text) <= 23 :
                di = sub_text
            else:
                return False
        else:
            if di_end>=6 and di_end<=23:
                di = sub_text[:di_end]
            else:
                return False
    # International Council for Commonality in Blood Banking Automation (ICCBBA) Issuing Agency
    # =/A9999XYZ100T0944=,000025=A99971312345600=>014032=}013032&,1000000000000XYZ123
    elif pattern_text.startswith("=/"):
        # Field size 16  + prefix '=/'  = 18
        if len(pattern_text) < 18:   
            return False
        di = pattern_text[2:18]
    else:
        return False
    
    logger.debug(f"UDI-DI: {di}")
    if library_connector.get_validate_data(di, library_id):
        logger.debug("us_udi_validate: True")
        return True
    else:
        return False

# python file_analyzer/recognizer/validate_func/funcs/us_udi_validate.py
if __name__ == "__main__":
    
    
    texts = [
        "(01)**************(11)141231(17)150707(10)A213B1(21)1234",
        "*+G45315010BC0/$$420020216LOT123456789012345SXYZ456789012345678/16D20130202C*",
        "+G45315010BC0/$$420020216LOT123456789012345SXYZ456789012345678/16D20130202C",
        "=/G4532424511OLY10=,000025=A99971312345600=>014032=}013032&,1000000000000XYZ123",
    ]
    for text in texts:
        rst = us_udi_validate(text)
        print(f"{text}   {rst}")