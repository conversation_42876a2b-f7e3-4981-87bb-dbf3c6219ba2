import os
import logging
import string

from ..library_connector import LibraryConnector

logger = logging.getLogger("fortidata_recognizer")


# https://www.drugs.com/ndc.html
def us_ndc_validate(pattern_text: str, library_id:str=None, library_connector:LibraryConnector=None) -> bool:  # ndc
    logger.debug(f"call us_ndc_validate to validate: {pattern_text}")
    
    if not library_id or not library_connector:
        logger.error("us_ndc_validate: missing library")
        return False

    converted = __ndc_convert(pattern_text)
    if library_connector.get_validate_data(converted, library_id):
        logger.debug("us_ndc_validate: True")
        return True
    else:
        #logger.debug("us_ndc_validate: False")
        return False

def __ndc_convert(text):
    # convert to 5-4-2 format
    
    arr = text.split("-")
    
    if len(arr) != 3:
        return ""
    
    labeler_code_len = len(arr[0])
    if labeler_code_len == 5:
        labeler_code = arr[0]
    elif labeler_code_len < 5:
        labeler_code = '0'*(5-labeler_code_len) + arr[0]
    else:
        return ""
        
    product_code_len = len(arr[1])
    if product_code_len == 4:
        product_code = arr[1]
    elif product_code_len < 4:
        product_code = '0'*(4-product_code_len) + arr[1]
    else:
        return ""
    
    package_code_len = len(arr[2])
    if package_code_len == 2:
        package_code = arr[2]
    elif package_code_len < 2:
        package_code = '0'*(2-package_code_len) + arr[2]
    else:
        return ""
        
    return f"{labeler_code}-{product_code}-{package_code}"

# python file_analyzer/recognizer/validate_func/funcs/us_ndc_validate.py
if __name__ == "__main__":
    print("Test __ndc_convert")
    texts = [
        "0615-8371-05",
        "53329-155-23",
        "15631-0208-2",
    ]
    for text in texts:
        converted = __ndc_convert(text)
        print(f"{text} -> {converted}")
    
    print("Test us_ndc_validate")
    texts = [
        "00615-8371-05",
        "53329-0155-23",
        "15631-0208-02",
    ]
    for text in texts:
        rst = us_ndc_validate(text)
        print(f"{text}   {rst}")
    
    