from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils

def at_tin_validate(pattern_text: str) -> bool:
    text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""), (".", ""),("/","")])
    tin_list = [int(digit) for digit in text if not digit.isspace()]

    # Set weights based on digit position
    weight = [1,2,1,2,1,2,1,2]

    # Perform checksums
    checksum = []
    for i in range(len(weight)):
        digit = weight[i] * tin_list[i]
        digit = int(digit/10) + int(digit%10) if digit > 9 else digit
        checksum.append(digit)
    total = sum(checksum)
    return (10 - total%10) == tin_list[-1]

if __name__ == "__main__":
    """
    99-999/9999
        931736581
        12-370/1018
    """
    example = "931736581"
    print(at_tin_validate(example))