from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils

def pt_tin_validate(pattern_text: str) -> bool:
    text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""), (".", "")])
    weights = [9, 8, 7, 6, 5, 4, 3, 2]
    weighted_sum = sum(int(text[i]) * weights[i] for i in range(len(weights)))

    remainder = weighted_sum % 11

    check_digit = 11 - remainder
    if check_digit == 10 or check_digit == 9:
        check_digit = 0
    return check_digit == int(text[-1])

if __name__ == "__main__":
    """
    299999998
        299 999 998
        208581430
        208 581 430
        503634468
        503 634 468
        300000006
    """
    example = "300000006"
    print(pt_tin_validate(example))