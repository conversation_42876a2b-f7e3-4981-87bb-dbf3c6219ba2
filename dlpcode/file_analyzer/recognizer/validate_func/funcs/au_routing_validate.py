from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils

def au_routing_validate(pattern_text: str) -> bool:  # aba routing
        sanitized_value = Utils.sanitize_value(pattern_text, [("-", "")])
        return checksum(sanitized_value)

def checksum(sanitized_value: str) -> bool:
    s = 0
    for idx, m in enumerate([3, 7, 1, 3, 7, 1, 3, 7, 1]):
        s += int(sanitized_value[idx]) * m
    return s % 10 == 0