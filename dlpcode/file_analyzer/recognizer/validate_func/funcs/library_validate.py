import logging
from ..library_connector import LibraryConnector

logger = logging.getLogger("fortidata_recognizer")

def library_validate(pattern_text: str, library_id:str=None, library_connector:LibraryConnector=None) -> bool:  # udi
    logger.debug(f"call library_validate to validate: {pattern_text}")
    
    if not library_connector or not library_id:
        logger.error("library_validate: missing library")
        return False

    if library_connector.get_validate_data(pattern_text, library_id):
        logger.debug("pattern_text: True")
        return True
    else:
        return False