from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils
def es_tin_validate(pattern_text: str) -> bool:
    conversion_table = {
        0: 'T', 1: 'R', 2: 'W', 3: 'A', 4: 'G', 5: 'M', 6: 'Y', 7: 'F', 8: 'P', 9: 'D',
        10: 'X', 11: 'B', 12: 'N', 13: 'J', 14: 'Z', 15: 'S', 16: 'Q', 17: 'V', 18: 'H',
        19: 'L', 20: 'C', 21: 'K', 22: 'E'
    }
    first_letter_conversion = {
        "X":'1', "Y":'2', "Z": '0', "K": '0', "L": '0', "M": '0'
    }
    pattern_text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""), (".", "")])
    if pattern_text[0].upper() in first_letter_conversion.keys():
        pattern_text = first_letter_conversion[pattern_text[0].upper()] + pattern_text[1:]
    first_8_characters = pattern_text[:8]
    remainder = sum(int(char) for char in first_8_characters) % 23

    modified_remainder = (remainder + 1) % 23

    check_character = conversion_table[modified_remainder]

    return check_character == pattern_text[-1].upper()

if __name__ == "__main__":
    text = "12345678-Z"
    print(es_tin_validate(text))
