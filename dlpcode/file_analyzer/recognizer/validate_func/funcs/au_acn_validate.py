from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils
from typing import List

def au_acn_validate(pattern_text: str) -> bool:
    """
    Validate the pattern logic e.g., by running checksum on a detected pattern.

    :param pattern_text: the text to validated.
    Only the part in text that was detected by the regex engine
    :return: A bool indicating whether the validation was successful.
    """
    # Pre-processing before validation checks
    text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", "")])
    acn_list = [int(digit) for digit in text if not digit.isspace()]

    # Set weights based on digit position
    weight = [8, 7, 6, 5, 4, 3, 2, 1]

    # Perform checksums
    sum_product = 0
    for i in range(8):
        sum_product += acn_list[i] * weight[i]
    remainder = sum_product % 10
    complement = 10 - remainder
    return complement == acn_list[-1]
