
import os
import logging
import string
from .luhn_validate import luhn_checksum
from ..library_connector import LibraryConnector
#from ....memory_profiler_decorator import profile

logger = logging.getLogger("fortidata_recognizer")
# https://www.cms.gov/Regulations-and-Guidance/Administrative-Simplification/NationalProvIdentStand/Downloads/NPIcheckdigit.pdf

def us_npi_validate(pattern_text: str, library_id:str=None, library_connector:LibraryConnector=None) -> bool:  # npi
    logger.debug(f"call us_npi_validate to validate: {pattern_text}")
    
    if len(pattern_text) != 10:
        return False

    checksum = luhn_checksum(f"80840{pattern_text}")
    if checksum % 10 != 0:
        return False

    if not library_connector or not library_id:
        logger.error("us_npi_validate: missing library")
        return False
         
    if library_connector.get_validate_data(pattern_text, library_id):
        logger.debug("us_npi_validate: True")
        return True
    else:
        return False