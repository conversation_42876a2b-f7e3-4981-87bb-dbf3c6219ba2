from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils

"""
Finnish personal identity code on Kela Card

Format:
 A Finnish personal identity code is a number sequence with 11 characters. It contains the person's date of birth, individual number and check mark.
 The format is ddmmyy-nnnt:
  - dd = birthday (01..31)
  - mm = month of birth (01..12)
  - yy = year of birth (00..99)
  - The punctuation mark indicates the century of birth:
    1800s: +
    1900s: - or Y (rare), later also X W V U
    2000s: A, in future also B C D E F
  - nnn = individual number, even for women, odd for men (002..899)
  - t = check mark
 For example: 311299-9872 indicates the person was born on 31.12.99. The punctuation mark (-) indicates that the year of birth is 1999. The individual number is 987. Since the number is odd, it is a man. Check mark 2 to verify that the ID is correct.

Regex expressions:
(?i:\b(?:0[1-9]|[12][0-9]|3[01])(?:0[1-9]|1[0-2])[0-9]{2}[-+A][0-9]{3}[0-9A-FHJ-NPR-Y]\b)
  Matches a string where:
  - The [1–6] characters are date of the format DDMMYY.
  - The seventh character is '-', '+' or 'A'.
  - The [8–10] are a 3-digit number.
  - The 11th character is an alphanumeric string [0–9] or [A-Z] except 'G' 'I' 'O' 'Q'.

Examples:
  010150-1130
  280378-799C
  140490-564N
  050995-432X
  
The punctuation mark is removed from the personal identification number. The first 9 characters of the ID are interpreted as a 9-digit number, 
which is divided by the number 31. The remainder is converted into a check mark with the help of the table below:
Remainder	Check mark	Comment
0	0	
1	1	
2	2	
3	3	
4	4	
5	5	
6	6	
7	7	
8	8	
9	9	
10	A	
11	B	
12	C	
13	D	
14	E	
15	F	
–	G	G not used, easily confused with C
16	B	
–	I	I not used, easily confused with 1
17	J	
18	Q	
19	L	
20	M	
21	OF	
–	O	O not used, easily confused with 0
22	P	
–	Q	Q not in use, reason unknown
23	R	
24	S	
25	T	
26	U	
27	V	
28	W	
29	X	
30	Y	
–	Z	Z not used, easily confused with 2

Example: The personal identification number is 010101A999T. We want to find out if the check mark T is correct. 
Let's omit the punctuation mark A. Let's take the first 9 numbers, i.e. 010101999. 
Let's do the division calculation. 010101999:31 = 325870, remainder 29. 
From the table, the check mark is X. The ID is therefore incorrect.
"""

check_mark_mapping = {
    0:"0",
    1:"1",
    2:"2",
    3:"3",
    4:"4",
    5:"5",
    6:"6",
    7:"7",
    8:"8",
    9:"9",
    10:"A",
    11:"B",
    12:"C",
    13:"D",
    14:"E",
    15:"F",
    16:"B",
    17:"J",
    18:"Q",
    19:"L",
    20:"M",
    21:"F",
    22:"P",
    23:"R",
    24:"S",
    25:"T",
    26:"U",
    27:"V",
    28:"W",
    29:"X",
    30:"Y",
}

def fi_hin_validate(pattern_text: str) -> bool:  # FI_HIN
    # ddmmyy-nnnt
    if len(pattern_text) != 11:
        return False
    
    sanitized_value = Utils.sanitize_value(pattern_text, [("-", ""), ("+", ""), ("A", ""), (" ", "")])
    if len(sanitized_value) != 10:
        return False
    digit = int(sanitized_value[0:9])
    remainder = digit % 31
    if remainder not in check_mark_mapping:
        return False
    
    return pattern_text[10] == check_mark_mapping[remainder]
    
    
if __name__ == "__main__":
    texts = [
        "010101A999T",
        "010150-1130",
        "280378-799C",
        "140490-564N",
        "050995-432X",
    ]
    for t in texts:
        r = fi_hin_validate(t)
        print(f"{t}  {r}")
        
    # output
    '''
010101A999T  False
010150-1130  True
280378-799C  True
140490-564N  False
050995-432X  True
    '''