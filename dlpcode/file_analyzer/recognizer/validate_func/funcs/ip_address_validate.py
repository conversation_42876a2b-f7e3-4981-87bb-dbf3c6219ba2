import ipaddress

def ip_address_validate(pattern_text: str) -> bool: #ip_address
    """
    Check if the pattern text cannot be validated as an IP address.

    :param pattern_text: Text detected as pattern by regex
    :return: True if invalidated
    """
    try:
        ipaddress.ip_address(pattern_text)
        return True
    except ValueError:
        return False
    
if __name__ == "__main__":
    ip = "************"
    print(ip_address_validate(ip))