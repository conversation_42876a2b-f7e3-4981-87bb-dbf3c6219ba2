from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils

def se_tin_validate(pattern_text: str) -> bool:
    text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""), (".", "")])
    tin_list = [int(digit) for digit in text if not digit.isspace()]

    # Set weights based on digit position
    weight = [2, 1, 2, 1, 2, 1, 2, 1, 2]

    # Perform checksums
    checksum = []
    for i in range(len(weight)):
        digit = weight[i] * tin_list[i]
        digit = int(digit/10) + int(digit%10) if digit > 9 else digit
        checksum.append(digit)
    total = sum(checksum)
    return (10 - total%10) == tin_list[-1]

if __name__ == "__main__":
    """
    670919-9530
    811228-9874
    """
    example = "811228-9874"
    print(se_tin_validate(example))