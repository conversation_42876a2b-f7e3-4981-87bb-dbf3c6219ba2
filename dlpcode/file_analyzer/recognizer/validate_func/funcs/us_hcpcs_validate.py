import os
import logging
from ..library_connector import LibraryConnector
# from ....memory_profiler_decorator import profile

logger = logging.getLogger("fortidata_recognizer")

def us_hcpcs_validate(pattern_text: str, library_id:str=None, library_connector:LibraryConnector=None) -> bool:  # hcpcs
    logger.debug(f"call us_hcpcs_validate to validate: {pattern_text}")
    
    if not library_connector or not library_id:
        logger.error("us_hcpcs_validate: missing library")
        return False
         
    if library_connector.get_validate_data(pattern_text, library_id):
        logger.debug("us_hcpcs_validate: True")
        return True
    else:
        return False