from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils

def pl_pesel_validate(pattern_text: str) -> bool:  # PL_PESEL
    pattern_text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""), (".", "")])
    digits = [int(digit) for digit in pattern_text]
    weights = [1, 3, 7, 9, 1, 3, 7, 9, 1, 3]

    checksum = sum(digit * weight for digit, weight in zip(digits[:10], weights))
    checksum %= 10

    return checksum == digits[10]