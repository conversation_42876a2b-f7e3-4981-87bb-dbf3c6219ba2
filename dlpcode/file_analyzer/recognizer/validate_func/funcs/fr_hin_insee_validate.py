from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils

# France Health Insurance Number(HIN)
# same with France Social Security Number (INSEE)

def fr_insee_validate(pattern_text: str) -> bool:
    return fr_hin_validate(pattern_text)

def fr_hin_validate(pattern_text: str) -> bool:  # HIN
    sanitized_value = Utils.sanitize_value(pattern_text, [("-", ""), (" ", "")])
    if len(sanitized_value) != 15:
        return False

    security_key = calculate_security_key(sanitized_value)
    print(security_key, int(sanitized_value[-2:]))
    return security_key == int(sanitized_value[-2:])

# Sample Code:
def calculate_security_key(string):
    '''
    Calculation Steps:
    Step 1:
        get 13 numbers from Position 1 - 13
    Step 2:
        convert 2A and 2B to 19 and 18, respectively, if necessary
    Step 3:
        calculate security key, NIR key = 97 - ((NIR numerical value) modulo 97)
    '''
    numbers = string[:13]
    numbers = numbers.replace('2A', '19').replace('2B', '18')
    nir_value = int(numbers)
    security_key = 97 - (nir_value % 97)

    return security_key


if __name__ == '__main__':
    # Test the function
    string = "160042531111450"
    string = "1 60 04 25 311 114 50"
    string = "1 85 05 78 006 084 36"
    string = "1 85 05 78 006 084 91"
    security_key = fr_hin_validate(string)
    print("Security Key:", security_key)