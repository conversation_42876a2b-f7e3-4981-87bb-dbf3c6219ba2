from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils
from typing import List
from .luhn_validate import luhn_checksum

def imei_validate(pattern_text: str) -> bool:  # imei lv_id za_id
    sanitized_value = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""),(".", "")])
    checksum = luhn_checksum(sanitized_value)
    return (checksum) % 10 == 0

if __name__ == "__main__":
    imei = "490154203237518"
    print(imei_validate(imei))