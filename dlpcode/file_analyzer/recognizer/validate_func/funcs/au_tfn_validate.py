from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils

def au_tfn_validate(pattern_text: str) -> bool: #au_tfn
    """
    Validate the pattern logic e.g., by running checksum on a detected pattern.

    :param pattern_text: the text to validated.
    Only the part in text that was detected by the regex engine
    :return: A bool indicating whether the validation was successful.
    """
    # Pre-processing before validation checks
    text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", "")])
    tfn_list = [int(digit) for digit in text if not digit.isspace()]

    # Set weights based on digit position
    weight = [1, 4, 3, 7, 5, 8, 6, 9, 10]

    # Perform checksums
    sum_product = 0
    for i in range(9):
        sum_product += tfn_list[i] * weight[i]
    remainder = sum_product % 11
    return remainder == 0