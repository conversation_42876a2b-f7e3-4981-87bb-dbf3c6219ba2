from collections import defaultdict

def us_ssn_validate(pattern_text: str) -> bool: #US_SSN
    """
    Check if the pattern text cannot be validated as a US_SSN entity.

    :param pattern_text: Text detected as pattern by regex
    :return: True if invalidated
    """
    # if there are delimiters, make sure both delimiters are the same
    delimiter_counts = defaultdict(int)
    for c in pattern_text:
        if c in (".", "-", " "):
            delimiter_counts[c] += 1
    if len(delimiter_counts.keys()) > 1:
        # mismatched delimiters
        return False

    only_digits = "".join(c for c in pattern_text if c.isdigit())
    if all(only_digits[0] == c for c in only_digits):
        # cannot be all same digit
        return False

    if only_digits[3:5] == "00" or only_digits[5:] == "0000":
        # groups cannot be all zeros
        return False

    for sample_ssn in ("000", "666", "123456789", "98765432", "078051120"):
        if only_digits.startswith(sample_ssn):
            return False

    return None