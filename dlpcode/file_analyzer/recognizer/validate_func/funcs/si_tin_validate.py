from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils

def si_tin_validate(pattern_text: str) -> bool:
    text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""), (".", "")])
    weights = [8, 7, 6, 5, 4, 3, 2]

    weighted_sum = sum(int(text[i]) * weights[i] for i in range(len(weights)))

    remainder = weighted_sum % 11

    check_digit = 11 - remainder if remainder != 0 else 0

    return check_digit == int(text[-1])

if __name__ == "__main__":
    """
    15012557
12345678
99999999"""

    example = "15012557"
    print(si_tin_validate(example))