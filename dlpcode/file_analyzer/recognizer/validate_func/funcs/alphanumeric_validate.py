def no_all_numeric_validate(pattern_text: str) -> bool:
    if pattern_text.isdigit():
        return False
    return None

def no_all_alpha_validate(pattern_text: str) -> bool:
    if pattern_text.isalpha():
        return False
    return None

def no_uniform_alphanumeric_validate(pattern_text: str) -> bool:
    if len(pattern_text) > 0 and pattern_text == pattern_text[0] * len(pattern_text):
        return False
    return None