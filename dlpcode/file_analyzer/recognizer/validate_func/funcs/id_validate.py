from presidio_analyzer.analyzer_utils import PresidioAnalyzerUtils as Utils
from .luhn_validate import luhn_checksum

def cn_id_validate(pattern_text: str) -> bool:
    # Pre-processing before validation checks
    text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", "")])
    id_list = [int(digit) for digit in text[:-1] if not digit.isspace()]

    # Set weights based on digit position
    weight = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    checksum_map = {0: '1', 1: '0', 2: 'X', 3: '9', 4: '8', 5: '7', 6: '6', 7: '5', 8: '4', 9: '3', 10: '2'}

    # Perform checksums
    sum_product = 0
    for i in range(len(weight)):
        sum_product += id_list[i] * weight[i]
    remainder = sum_product % 11
    if remainder not in checksum_map.keys():
        return False
    return checksum_map[remainder] == text[-1]

def bg_id_validate(pattern_text: str) -> bool:
    text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", "")])
    digits = [int(digit) for digit in text if not digit.isspace()]

    weights = [2, 4, 8, 5, 10, 9, 7, 3, 6]
    
    total = sum(int(digit) * weight for digit, weight in zip(digits, weights))
    checksum = total % 11
    
    if checksum == 10:
        return 0
    else:
        return checksum
    

def se_id_validate(pattern_text: str) -> bool:
    text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""), (".", "")])
    id_list = [int(digit) for digit in text if not digit.isspace()]

    # Set weights based on digit position
    weight = [2,1,2,1,2,1,2,1,2]

    # Perform checksums
    checksum = []
    for i in range(len(weight)):
        digit = weight[i] * id_list[i]
        digit = int(digit/10) + int(digit%10) if digit > 9 else digit
        checksum.append(digit)
    total = sum(checksum)
    return (total+id_list[-1])%10 == 0

def pl_id_validate(pattern_text: str) -> bool:
    def find_first_digit(s):
        for i, char in enumerate(s):
            if char.isdigit():
                return int(char)
        return -1
    char_to_num_dict = {}
    for char, num in zip("ABCDEFGHIJKLMNOPQRSTUVWXYZ", range(10, 36)):
        char_to_num_dict[char] = num
    text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""), (".", "")])
    # Set weights based on digit position
    weights = [7, 3, 1, 0, 7, 3, 1, 7, 3]
    digits = []
    for digit in text:
        if digit.isspace():
            continue
        if digit.isalpha():
            digits.append(char_to_num_dict[digit.upper()])
        else:
            digits.append(int(digit))
    
    total = sum(int(digit) * weight for digit, weight in zip(digits[:-1], weights))
    return total % 10 == find_first_digit(text)

def it_id_validate(pattern_text: str) -> bool:
    odd_mapping = {
        "0": 1, "1": 0, "2": 5, "3": 7, "4": 9, "5": 13, "6": 15, "7": 17, "8": 19, "9": 21,
        "A": 1, "B": 0, "C": 5, "D": 7, "E": 9, "F": 13, "G": 15, "H": 17, "I": 19,
        "J": 21, "K": 2, "L": 4, "M": 18, "N": 20, "O": 11, "P": 3, "Q": 6, "R": 8,
        "S": 12, "T": 14, "U": 16, "V": 10, "W": 22, "X": 25, "Y": 24, "Z": 23
    }
    even_mapping = {
        "0": 0, "1": 1, "2": 2, "3": 3, "4": 4, "5": 5, "6": 6, "7": 7, "8": 8, "9": 9,
        "A": 0, "B": 1, "C": 2, "D": 3, "E": 4, "F": 5, "G": 6, "H": 7, "I": 8, "J": 9,
        "K": 10, "L": 11, "M": 12, "N": 13, "O": 14, "P": 15, "Q": 16, "R": 17, "S": 18,
        "T": 19, "U": 20, "V": 21, "W": 22, "X": 23, "Y": 24, "Z": 25
    }
    res_mapping = {
        0: "A", 1: "B", 2: "C", 3: "D", 4: "E", 5: "F", 6: "G", 7: "H", 8: "I", 9: "J",
        10: "K", 11: "L", 12: "M", 13: "N", 14: "O", 15: "P", 16: "Q", 17: "R", 18: "S",
        19: "T", 20: "U", 21: "V", 22: "W", 23: "X", 24: "Y", 25: "Z"
    }
    res_mapping2 = {
        0: "L", 1: "M", 2: "N", 3: "P", 4: "Q", 5: "R", 6: "S", 7: "T", 8: "U", 9: "V"
    }

    text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""), (".", "")])
    id_list = [digit for digit in text[:-1] if not digit.isspace()]
    total = 0
    #odd
    for i in id_list[0::2]:
        if i not in odd_mapping.keys():
            return False
        total += odd_mapping[i]
    for i in id_list[1::2]:
        if i not in even_mapping.keys():
            return False
        total += even_mapping[i]
    remainder = total % 26
    if res_mapping[remainder] == text[-1]:
        return True
    if remainder in res_mapping2.keys() and res_mapping2[remainder] == text[-1]:
        return True
    return False

def ie_pps_validate(pattern_text: str) -> bool:
    text = Utils.sanitize_value(pattern_text, [("-", ""), (" ", ""), (".", "")])
    id_list = [int(digit) for digit in text[:-2] if not digit.isspace()]
    id_list.append(0 if text[-1].upper() == 'W' else ord(text[-1].upper()) - ord('A') + 1)

    # Set weights based on digit position
    weights = [8, 7, 6, 5, 4, 3, 2, 9]
    total = sum(digit * weight for digit, weight in zip(id_list, weights))

    remainder = total % 23
    if remainder == 0:
        return text[-2].upper() == 'W'
    else:
        expected_letter = chr(remainder + ord('A') - 1)
        return text[-2].upper() == expected_letter