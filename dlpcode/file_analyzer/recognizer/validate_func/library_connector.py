import sqlite3
from threading import Lock
from contextlib import contextmanager

class LibraryConnector():
    def __init__(self, base_path, logger):
        self.base_path = base_path
        self.__connection = None
        self.__cursor = None
        self.__library_list = None
        self.lock = Lock()
        self.logger = logger

    @contextmanager
    def get_cursor(self):
        with self.lock:
            if not self.__connection:
                self.__connection = sqlite3.connect(self.base_path)
                self.__cursor = self.__connection.cursor()
            yield self.__cursor

    def get_validate_data(self, value:str, library_id:str) -> bool:
        try:
            if library_id not in self.get_library_list():
                return False
            
            with self.get_cursor() as cursor:
                cursor.execute(f"SELECT id FROM {library_id} WHERE id = ?", (value,))
                if cursor.fetchall():
                    return True
                return False
        except Exception as e:
            self.logger.error(f"get validate data from sqlite failed: {e}")
            return False
    
    def get_library_list(self) -> list:
        try:
            if not self.__library_list:
                with self.get_cursor() as cursor:
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    table_names = [row[0] for row in cursor.fetchall()]
                    self.__library_list = table_names
            return self.__library_list
        except Exception as e:
            self.logger.error(f"get data list from sqlite failed: {e}")
            return []

    def __del__(self):
        with self.lock:
            if self.__cursor:
                self.__cursor.close()
                self.__cursor = None
            if self.__connection:
                self.__connection.close()
                self.__connection = None
