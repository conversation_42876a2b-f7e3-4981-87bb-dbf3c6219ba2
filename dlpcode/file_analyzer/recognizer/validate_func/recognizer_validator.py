
from file_analyzer.recognizer.validate_func.library_connector import LibraryConnector
from file_analyzer.recognizer.validate_func.load_validate_func import VALIDATE_FUNC
class RecognizerValidator():
    def __init__(self, validate_id:str, library_id:str=None, library_connector:LibraryConnector=None):
        if validate_id not in VALIDATE_FUNC:
            raise ValueError(f"{validate_id} not exist")
        self.validate_id = validate_id
        self.library_id = library_id
        self.library_connector = library_connector

    def validate(self, pattern_text:str) -> bool:
        if self.library_id and self.library_connector:
            return VALIDATE_FUNC[self.validate_id](pattern_text, self.library_id, self.library_connector)
