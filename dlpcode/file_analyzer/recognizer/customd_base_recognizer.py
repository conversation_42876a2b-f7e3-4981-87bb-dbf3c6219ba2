from typing import List, Set, Tuple, Optional, Dict, Callable
from presidio_analyzer import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>co<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>tityRecog<PERSON>zer, AnalysisExplanation
from presidio_analyzer.nlp_engine import NlpArtifacts
from presidio_analyzer.predefined_recognizers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Phone<PERSON><PERSON>ognizer
from presidio_analyzer.app_tracer import AppTracer
from regex.regex import DOTALL, IGNORECASE, MULTILINE
from .validate_func import VALIDATE_FUNC
from .recognizer_config import KeywordConfig
import logging
import copy
import phonenumbers
import regex as re
from file_analyzer.recognizer.recognizer_keyword import HitKeywordsScoreEnhancer
from file_analyzer.recognizer.recognizer_match import RecognizerMatchEngine, MatchedPattern
from file_analyzer.recognizer.validate_func.recognizer_validator import RecognizerValidator

logger = logging.getLogger("fortidata_recognizer")

class CustomPatternRecognizer(PatternRecognizer):
    DROP_SCORE_THRESHOLD = 0.1

    def __init__(
            self,
            supported_entity: str,
            languages:list = ["ALL"],
            patterns: List[Pattern] = None,
            context: List[str] = None,
            allow_list:List[str] = None,
            regions:str=["All","US"],
            compliance:list = None,
            keywords:KeywordConfig = None,
            keyword_must:bool = False,
            validator:RecognizerValidator = None,
            text_categories: List[str] = None,
        ):
        '''
        Parameters:
            text_categories: Expected text category
                1. Ignore the text category if text_categories is None. It means this recognizer will scan the text with all category.
                2. Otherwise only the text which category listed in the text_categories can be scanned
        '''
        self.custom_name = f"custom_recognizer_{supported_entity}"
        self.allow_list = allow_list
        self.regions = regions
        self.languages = languages
        self.validator = validator
        self.compliance = compliance
        self.support_keywords = True
        self.keywords = keywords
        self.keyword_must = keyword_must
        self.default_regex_flags = re.DOTALL | re.MULTILINE
        self.text_categories = text_categories
        self.supported_entity = supported_entity

        logger.debug(f'''Loading PatternRecognizer with below parameters:
        ### entity: {supported_entity},
            regions: {regions},
            compliance: {compliance} ,
            keyword_must: {keyword_must},
            keywords: {keywords},
            text_categories: {text_categories},
            allow_list: {allow_list},
            ''')

        super().__init__(
            supported_entity=supported_entity,
            patterns=patterns,
            context=context
        )

    def analyze(self,
                text: str,
                hit_keywords=None,
                matched_pattern:RecognizerMatchEngine = None,
                regex_flags: DOTALL=None,
                ignore_text_category:bool = False,
                text_main_category = None,
                text_sub_category = None,
                match_num_limit = 128,
                score_threshold = 0
            ) -> List[RecognizerResult]:
        try:
            pattern_results = []
            if not ignore_text_category and not self.match_text_category(text_main_category, text_sub_category):
                return []

            if self.keyword_must and self.supported_entities[0] not in hit_keywords:
                return []
            entity_matched_pattern = matched_pattern.get(self.supported_entities[0], None)
            if self.patterns and entity_matched_pattern:
                pattern_results = self.analyze_patterns(text, regex_flags, hit_keywords, entity_matched_pattern, match_num_limit, score_threshold)
            logger.debug(f"recognizer [{self.supported_entities}] raw match: {len(pattern_results)}")

            pattern_results = EntityRecognizer.remove_duplicates(pattern_results)
            logger.debug(f"recognizer [{self.supported_entities}] match after keyword enhancement and duplicate removal: {len(pattern_results)}")

            return pattern_results

        except Exception as e:
            logger.exception(f"Analyze failed using recognizer {self.supported_entities}, message: {e}")

    def analyze_patterns(
        self, text: str, flags: int = None, hit_keywords=None, matched_pattern: List[MatchedPattern]=None, match_num_limit:int = 128, score_threshold = 0
    ) -> List[RecognizerResult]:
        """
        Evaluate all patterns in the provided text.

        Including words in the provided deny-list

        :param text: text to analyze
        :param flags: regex flags
        :return: A list of RecognizerResult
        """
        flags = flags if flags else re.MULTILINE
        results = []

        matched_country_keywords = []
        matched_province_keywords = []
        for region in self.regions:
            if hit_keywords.get("COUNTRY_KEYWORD_"+region, []):
                matched_country_keywords.extend(hit_keywords.get("COUNTRY_KEYWORD_"+region))
            if hit_keywords.get("PROVINCE_KEYWORD_"+region, []):
                matched_province_keywords.extend(hit_keywords.get("PROVINCE_KEYWORD_"+region))
        keyword_score_enhancer = None
        for pattern in matched_pattern:
            # If current pattern match number is more than 2*match_num_limit, stop processing other patterns
            if len(results) >= 2 * match_num_limit:
                    break
            pattern.text = pattern.text.decode('utf-8')
            if self.exclude_pattern(pattern):
                continue
            pattern = self.extract_matched_pattern(pattern)
            if not pattern:
                continue

            validation_result = None
            if self.validator:
                validation_result = self.validator.validate(pattern.text)
            description = self.build_regex_explanation(
                self.name,
                pattern.name,
                pattern.regex,
                pattern.score,
                validation_result,
                flags,
            )
            pattern_result = RecognizerResult(
                entity_type=self.supported_entities[0],
                start=pattern.start,
                end=pattern.end,
                score=pattern.score,
                analysis_explanation=description,
                recognition_metadata={
                    RecognizerResult.RECOGNIZER_NAME_KEY: self.name,
                    RecognizerResult.RECOGNIZER_IDENTIFIER_KEY: self.id,
                    "entity_text": pattern.text,
                    "validator_info": self.validator.validate_id if validation_result else None
                },
            )

            if validation_result is not None:
                if validation_result:
                    pattern_result.score = EntityRecognizer.MAX_SCORE
                else:
                    pattern_result.score = EntityRecognizer.MIN_SCORE

            invalidation_result = self.invalidate_result(pattern.text)
            if invalidation_result is not None and invalidation_result:
                pattern_result.score = EntityRecognizer.MIN_SCORE

            if pattern_result.score > EntityRecognizer.MIN_SCORE:
                if not keyword_score_enhancer:
                    keyword_score_enhancer = HitKeywordsScoreEnhancer(
                        hit_keywords=hit_keywords.get(self.supported_entities[0], []),
                        region_keywords=matched_country_keywords,
                        province_keywords=matched_province_keywords
                    )
                pattern_result = self.enhance_using_keyword(text=text, recognizer_result=pattern_result, keyword_score_enhancer = keyword_score_enhancer)
                if pattern_result is not None and pattern_result.score >= score_threshold:
                    results.append(pattern_result)

        return results

    def enhance_using_keyword(self, text: str, recognizer_result:RecognizerResult, keyword_score_enhancer:HitKeywordsScoreEnhancer = None):
        if not self.support_keywords:
            return recognizer_result
        # We will drop the current result,
        # if this recognizer supports keyword
        # and the keyword is not be found
        # and the score of the current result less than CustomPatternRecognizer.DROP
        if not self.keywords and keyword_score_enhancer.is_keyword_empty and \
            recognizer_result.score < CustomPatternRecognizer.DROP_SCORE_THRESHOLD:
            return None

        logger.debug(f"<{self.custom_name}> Result {recognizer_result.entity_type} score(before): {recognizer_result.score} start: {recognizer_result.start} end: {recognizer_result.end} match: {text[recognizer_result.start:recognizer_result.end]}")
        if keyword_score_enhancer:
            recognizer_result, matched = keyword_score_enhancer.enhance_by_keywords(recognizer_result)

        if self.keyword_must and not matched:
            logger.debug("no keyword matched")
            return None
        if keyword_score_enhancer:
            recognizer_result = keyword_score_enhancer.enhance_by_region_keyword(recognizer_result)

            if recognizer_result.entity_type.startswith('DL'):
                recognizer_result = keyword_score_enhancer.enhance_by_province_keyword(recognizer_result)

        return recognizer_result

    def match_text_category(self, text_main_category, text_sub_category)->bool:
        '''
        Parameters:
            text_main_category: main text category
            text_sub_category: sub text category
        Returns:
            True: the recognizer does not care about the text category (self.text_categories is None or empty) or
                the text category matched the category of this recognizer
            False: unmatched the expected text category
        '''
        if not self.text_categories:
            #logger.info(f"{self.custom_name} text category matched. recognizer's text_categories unset")
            return True

        if text_main_category in self.text_categories or text_sub_category in self.text_categories:
            #logger.info(f"{self.custom_name} text category matched")
            return True
        else:
            #logger.debug(f"{self.custom_name} text category unmatched, {text_main_category}, {text_sub_category}, {self.text_categories}")
            return False

    def extract_matched_pattern(self, matched_pattern:MatchedPattern):
        if matched_pattern.compiled_extract_regex:
            match = matched_pattern.compiled_extract_regex.search(matched_pattern.text)
            if match:
                matched_pattern.end = matched_pattern.start + match.end()
                matched_pattern.start = matched_pattern.start + match.start()
                matched_pattern.text = match.group()
            else:
                return None
        return matched_pattern

    def exclude_pattern(self, matched_pattern:MatchedPattern)->bool:
        if matched_pattern.compiled_exclude_regex:
            if matched_pattern.compiled_exclude_regex.search(matched_pattern.text):
                return True
        return False


class CustomSpacyRecognizer(SpacyRecognizer):
    ENTITIES = ["HOSPITAL", "PROBLEM", "TREATMENT", "PERSON", "STREET_ADDRESS"]

    def __init__(
        self,
        supported_entities: Optional[List[str]] = None,
        ner_strength: float = 0.85,
        default_explanation: Optional[str] = None,
        check_label_groups: Optional[List[Tuple[Set, Set]]] = None,
        context: Optional[List[str]] = None,
        allow_list: dict = None,
        keywords_mapping:Dict = None,
        text_category_mapping:Dict = None,
        ):
        '''
        Parameters:
            text_category_mapping: Expected text category mapping
        '''
        self.custom_name = f"custom_recognizer_NLP"
        self.support_keywords = True
        self.keywords_mapping = keywords_mapping
        self.text_category_mapping = text_category_mapping

        if not supported_entities:
            supported_entities = self.ENTITIES
        self.allow_list = allow_list

        logger.debug(f'''Loading NLPRecognizer with below parameters:
        ### entity: {supported_entities},
            keywords_mapping: {keywords_mapping},
            text_category_mapping: {text_category_mapping},
            allow_list: {allow_list},
            ''')

        super().__init__(
            supported_entities=supported_entities,
            ner_strength=ner_strength,
            default_explanation=default_explanation,
            check_label_groups=check_label_groups,
            context=context
        )

    def analyze(self,
                text: str,
                nlp_engine,
                language:str,
                hit_keywords=None,
                text_main_category = None,
                text_sub_category = None,
                match_num_limit = None,
                nlp_process_size:int = 1000,
                score_threshold = 0
            ):  # noqa D102
        # run the nlp pipeline over the given text, store the results in
        # a NlpArtifacts instance
        process_max_size = len(text) if len(text) <= nlp_process_size else nlp_process_size
        #nlp_artifacts = self.nlp_engine.process_text(text[:process_max_size], language)
        with nlp_engine.nlp[language].memory_zone():
            nlp_artifacts = nlp_engine.process_multiple_threads(text[:process_max_size], language)
            #self.shared_nlp_artifaces = nlp_artifacts

            return self.__analyze_plus(
                text = text,
                hit_keywords=hit_keywords,
                nlp_artifacts=nlp_artifacts,
                text_main_category=text_main_category,
                text_sub_category=text_sub_category,
                match_num_limit = match_num_limit,
                score_threshold = score_threshold
        )

    def __analyze_plus(self, text: str,
                hit_keywords=None,
                nlp_artifacts=None,
                text_main_category = None,
                text_sub_category = None,
                match_num_limit = 128,
                score_threshold = 0
            ):  # noqa D102

        results = []
        count = dict()
        if not nlp_artifacts:
            return results
        keyword_score_enhancers = dict()
        for entity in self.supported_entities:
            keyword_score_enhancers["SPACY_" + entity] = None
        if isinstance(nlp_artifacts, NlpArtifacts):
            nlp_artifacts_list = [(0, nlp_artifacts_list)]
        else:
            nlp_artifacts_list = nlp_artifacts

        for base_offset, nlp_artifacts in nlp_artifacts_list:

            ner_entities = nlp_artifacts.entities
            ner_scores = nlp_artifacts.scores

            for ner_entity, ner_score in zip(ner_entities, ner_scores):
                if (ner_entity.label_ not in self.supported_entities):
                    continue

                if ner_entity.label_ in count and count[ner_entity.label_] >= 2*match_num_limit:
                    continue

                logger.debug(f"<{self.custom_name}> found entity: {ner_entity.label_} '{ner_entity.lemma_}'")

                if text_main_category and not self.match_text_category(ner_entity.label_, text_main_category, text_sub_category):
                    logger.debug(f"<{self.custom_name}> found entity: {ner_entity.label_} '{ner_entity.lemma_}'. But it don't match text category, skip it")
                    continue

                if self.spacy_invalid(ner_entity):
                    logger.debug(f"<{self.custom_name}> found entity: {ner_entity.label_} '{ner_entity.lemma_}'. But it is a invalid value, skip it")
                    continue
                invalid_result = self.invalidate_result(ner_entity)
                if invalid_result != None and invalid_result:
                    logger.debug(f"<{self.custom_name}> found entity: {ner_entity.label_} '{ner_entity.lemma_}'. But it is a invalid result, skip it")
                    continue

                textual_explanation = self.DEFAULT_EXPLANATION.format(ner_entity.label_)
                explanation = self.build_explanation(ner_score, textual_explanation)
                spacy_result = RecognizerResult(
                    entity_type="SPACY_"+ner_entity.label_,
                    start=ner_entity.start_char + base_offset,
                    end=ner_entity.end_char + base_offset,
                    score=ner_score,
                    analysis_explanation=explanation,
                    recognition_metadata={
                        RecognizerResult.RECOGNIZER_NAME_KEY: self.name,
                        RecognizerResult.RECOGNIZER_IDENTIFIER_KEY: self.id,
                        "entity_text": text[ner_entity.start_char + base_offset:ner_entity.end_char + base_offset]
                    },
                )
                if not keyword_score_enhancers.get(spacy_result.entity_type, None):
                    keyword_score_enhancers[spacy_result.entity_type] = HitKeywordsScoreEnhancer(
                        hit_keywords=hit_keywords.get(spacy_result.entity_type, [])
                    )
                spacy_result = self.enhance_using_keyword(text, spacy_result, text_main_category, keyword_score_enhancer=keyword_score_enhancers.get(spacy_result.entity_type, None))
                if spacy_result is not None and spacy_result.score >= score_threshold:
                    count[ner_entity.label_] = 1 if ner_entity.label_ not in count else count[ner_entity.label_]+1
                    logger.debug(f"<{self.custom_name}> found entity: {ner_entity.label_} '{ner_entity.lemma_}', add it to results list")
                    results.append(spacy_result)

        return results

    def match_text_category(self, entity, text_main_category, text_sub_category)->bool:
        '''
        Parameters:
            text_main_category: main text category
            text_sub_category: sub text category
        Returns:
            True: the recognizer does not care about the text category (self.text_categories is None or empty) or
                the text category matched the category of this recognizer
            False: unmatched the expected text category
        '''
        if not self.text_category_mapping or not self.text_category_mapping.get(entity):
            #logger.info(f"{entity} text category matched. recognizer's text_categories unset")
            return True

        text_categories = self.text_category_mapping.get(entity)
        if text_main_category in text_categories or text_sub_category in text_categories:
            #logger.info(f"{entity} text category matched")
            return True
        else:
            #logger.info(f"{entity} text category unmatched")
            return False

    def spacy_invalid(self, entity) -> Optional[bool]:
        if entity.label_== "PERSON":
            if entity.end_char-entity.start_char > 20 or entity.end_char-entity.start_char < 4:
                return True
            if bool(re.search(r'[^a-zA-Z ,.\'-]', entity.text)):
                return True
            if entity.text == entity.text.lower():
                return True
        if entity.label_ == "STREET_ADDRESS":
            if entity.end_char-entity.start_char < 10:
                return True
            if "!" in entity.text or "+" in entity.text or "=" in entity.text:
                return True
        if entity.label_ == "TREATMENT" or entity.label_ == "PROBLEM":
            if entity.end_char-entity.start_char > 25:
                return True
            if bool(re.search(r'[^a-zA-Z ,.\'-]', entity.text)):
                return True
        return False

    def invalidate_result(self, entity)->Optional[bool]:
        label = "SPACY_" + entity.label_
        if not self.allow_list:
            return False
        if label not in self.allow_list.keys():
            return False
        word_list = str(entity.lemma_).split(" ")
        for word in word_list:
            if word.lower() in self.allow_list[label]:
                return True
        return False

    def enhance_using_keyword(
        self,
        text: str,
        recognizer_result:RecognizerResult,
        text_main_category:str = None,
        keyword_score_enhancer: HitKeywordsScoreEnhancer = None
    ) -> RecognizerResult:

        # logger.debug(f"Enter {self.name}.enhance_using_keyword")
        if not self.support_keywords:
            return recognizer_result

        keyword_info = self.keywords_mapping.get(recognizer_result.entity_type)
        # if recognizer_result.entity_type == "SPACY_PERSON":
        #     keyword_info['keyword_must'] = bool(text_main_category == "20000")

        logger.debug(f"<{self.custom_name}> keyword checking for {recognizer_result.entity_type} '{text[recognizer_result.start:recognizer_result.end]}'")

        if keyword_info is None:
            return recognizer_result

        else:
            if keyword_info['keyword_must'] and keyword_score_enhancer.is_keyword_empty:
                logger.debug(f"<{self.custom_name}> {recognizer_result.entity_type}'s keyword must is enabled, but no keyword found")
                return None

            logger.debug(f"<{self.custom_name}> Spacy Result {recognizer_result.entity_type} score(before): {recognizer_result.score} \
                start: {recognizer_result.start} end: {recognizer_result.end} match: {text[recognizer_result.start:recognizer_result.end]}")
            #print(f"Spacy Result score(before): {result.score} start: {result.start} end: {result.end} match: {text[result.start:result.end]}")

            recognizer_result, matched = keyword_score_enhancer.enhance_by_keywords(recognizer_result=recognizer_result)
            if keyword_info['keyword_must'] and not matched:
                logger.debug("no keyword matched")
                return None
            return recognizer_result

class CustomedPhoneRecognizer(PatternRecognizer):
    SCORE = 0.4
    def __init__(
            self,
            supported_entity: str,
            languages:list = ["ALL"],
            patterns: List[Pattern] = None,
            context: List[str] = None,
            allow_list:List[str] = None,
            regions:str=["All"],
            compliance:list = None,
            keywords:KeywordConfig = None,
            keyword_must:bool = False,
            validator: RecognizerValidator = None,
            text_categories: List[str] = None,
        ):
        '''
        Parameters:
            text_categories: Expected text category
                1. Ignore the text category if text_categories is None. It means this recognizer will scan the text with all category.
                2. Otherwise only the text which category listed in the text_categories can be scanned
        '''
        self.custom_name = f"custom_recognizer_{supported_entity}"
        self.allow_list = allow_list
        self.regions = regions
        self.languages = languages
        self.compliance = compliance
        self.support_keywords = True
        self.keywords = keywords
        self.keyword_must = keyword_must
        self.default_regex_flags = re.DOTALL | re.MULTILINE
        self.text_categories = text_categories
        self.supported_entity = supported_entity
        self.validator = validator

        logger.debug(f'''Loading PatternRecognizer with below parameters:
        ### entity: {supported_entity},
            regions: {regions},
            languages: {languages},
            compliance: {compliance} ,
            keyword_must: {keyword_must},
            keywords: {keywords},
            text_categories: {text_categories},
            allow_list: {allow_list},
            ''')

        super().__init__(
            supported_entity=supported_entity,
            patterns=patterns,
            context=context,
        )

    def analyze(self,
                text: str,
                regions:list = ['US'],
                hit_keywords=None,
                matched_pattern=None,
                regex_flags: DOTALL=None,
                phone_matcher_max_retry = 1000,
                match_num_limit = 128,
                score_threshold = 0
            ) -> List[RecognizerResult]:
        try:
            if self.keyword_must and self.supported_entities[0] not in hit_keywords:
                return []

            entity_matched_pattern = matched_pattern.get("PHONE_NUMBER", [])
            pattern_results = self.analyze_patterns(text, regex_flags, regions, hit_keywords, entity_matched_pattern, phone_matcher_max_retry, match_num_limit, score_threshold)

            logger.debug(f"recognizer [{self.supported_entities}] raw match: {len(pattern_results)}")

            pattern_results = EntityRecognizer.remove_duplicates(pattern_results)
            logger.debug(f"recognizer [{self.supported_entities}] match after keyword enhancement and duplicate removal: {len(pattern_results)}")

            return pattern_results
        except Exception as e:
            logger.exception(f"Analyze failed using recognizer {self.supported_entities}, message: {e}")

    def analyze_patterns(
        self, text: str, flags: int = None, regions:list = ['US'], hit_keywords = None, matched_pattern:List[MatchedPattern] = None, phone_matcher_max_retry:int = 1000, match_num_limit:int = 128, score_threshold = 0
    ) -> List[RecognizerResult]:
        """
        Evaluate all patterns in the provided text.

        Including words in the provided deny-list

        :param text: text to analyze
        :param flags: regex flags
        :return: A list of RecognizerResult
        """
        flags = flags if flags else re.MULTILINE
        results = []
        keyword_enhancers = {
            "PHONE_NUMBER": HitKeywordsScoreEnhancer(hit_keywords=hit_keywords.get("PHONE_NUMBER", []))
        }

        if matched_pattern:
            for pattern in matched_pattern:
                # If current pattern match number is more than 2*match_num_limit, stop processing other patterns
                if len(results) >= 2 * match_num_limit:
                    break

                # If the pattern does not belong to config.regions, skip
                if not any(item in pattern.name.split(',') for item in regions):
                    continue

                current_match = current_match = pattern.text.decode('utf-8')

                # Skip empty results
                if current_match == "":
                    continue

                validation_result = None
                if self.validator:
                    validation_result = self.validator.validate(current_match)
                description = self.build_regex_explanation(
                    self.name,
                    pattern.name,
                    pattern.regex,
                    pattern.score,
                    validation_result,
                    flags,
                )
                pattern_result = RecognizerResult(
                    entity_type=self.supported_entities[0],
                    start=pattern.start,
                    end=pattern.end,
                    score=pattern.score,
                    analysis_explanation=description,
                    recognition_metadata={
                        RecognizerResult.RECOGNIZER_NAME_KEY: self.name,
                        RecognizerResult.RECOGNIZER_IDENTIFIER_KEY: self.id,
                        "entity_text":current_match
                    },
                )

                if validation_result is not None:
                    if validation_result:
                        pattern_result.score = EntityRecognizer.MAX_SCORE
                    else:
                        pattern_result.score = EntityRecognizer.MIN_SCORE

                invalidation_result = self.invalidate_result(current_match)
                if invalidation_result is not None and invalidation_result:
                    pattern_result.score = EntityRecognizer.MIN_SCORE

                if f"PHONE_NUMBER_{pattern.name}" in keyword_enhancers:
                    keyword_score_enhancer = keyword_enhancers.get(f"PHONE_NUMBER_{pattern.name}")
                else:
                    keyword_score_enhancer = HitKeywordsScoreEnhancer(hit_keywords=hit_keywords.get(f"PHONE_NUMBER_{pattern.name}", []))
                    keyword_enhancers[f"PHONE_NUMBER_{pattern.name}"] = keyword_score_enhancer
                if pattern_result.score > EntityRecognizer.MIN_SCORE:
                    pattern_result = self.enhance_using_keyword(text=text, recognizer_result=pattern_result, keyword_score_enhancer=keyword_score_enhancer)
                    if pattern_result is not None and pattern_result.score >= score_threshold:
                        results.append(pattern_result)

        for match in phonenumbers.PhoneNumberMatcher(text=text, region=None, leniency=1, max_tries=phone_matcher_max_retry):
            result = self.enhance_using_keyword(
                text=text,
                recognizer_result=self._get_recognizer_result(match, flags),
                keyword_score_enhancer=keyword_enhancers.get("PHONE_NUMBER", None)
            )
            if result is not None:
                results.append(result)
                if len(results) >= 2 * match_num_limit:
                    break
        return results

    def _get_recognizer_result(self, match, flags):
        description = self.build_regex_explanation(
            self.name,
            "phonenumbers.PhoneNumberMatcher",
            "library",
            self.SCORE,
            True,
            flags,
        )
        result = RecognizerResult(
            entity_type="PHONE_NUMBER",
            start=match.start,
            end=match.end,
            score=self.SCORE,
            analysis_explanation=description,
            recognition_metadata={
                RecognizerResult.RECOGNIZER_NAME_KEY: self.name,
                RecognizerResult.RECOGNIZER_IDENTIFIER_KEY: self.id,
                "entity_text":match.raw_string
            },
        )

        return result

    def enhance_using_keyword(
        self,
        text: str,
        recognizer_result: RecognizerResult,
        keyword_score_enhancer: HitKeywordsScoreEnhancer=None,
    ) -> RecognizerResult:

        if not self.support_keywords:
            return recognizer_result
        # We will drop the current result,
        # if this recognizer supports keyword
        # and the keyword is not be found
        # and the score of the current result less than CustomPatternRecognizer.DROP
        if not self.keywords and keyword_score_enhancer.is_keyword_empty and \
            recognizer_result.score < CustomPatternRecognizer.DROP_SCORE_THRESHOLD:
            return None

        logger.debug(f"<{self.custom_name}> Result {recognizer_result.entity_type} score(before): {recognizer_result.score} start: {recognizer_result.start} end: {recognizer_result.end} match: {text[recognizer_result.start:recognizer_result.end]}")
        if keyword_score_enhancer:
            recognizer_result, matched = keyword_score_enhancer.enhance_by_keywords(recognizer_result)

        if self.keyword_must and not matched:
            logger.debug("no keyword matched")
            return None

        return recognizer_result