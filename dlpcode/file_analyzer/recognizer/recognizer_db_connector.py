from file_analyzer.util.rw_lock import RWLock
from file_analyzer.analyzer_exception import VersionError
from file_analyzer.analyzer_version import AnalyzerVersionWrapper
from file_analyzer.util.enum import AnalyzerPackageType
import os
import atexit
import sqlite3
class RecognizerDBConnector():
    def __init__(self) -> None:
        pass
    
    def connect(self, db_path:str):
        return RecognizerDBManager(db_path=db_path)

class RecognizerDBManager:
        def __init__(self, db_path:str) -> None:
            self.db_path = db_path
    
        def __enter__(self):
            self.conn = sqlite3.connect(self.db_path)
            return self.conn
        
        def __exit__(self, exc_type, exc_value, traceback):
            if self.conn:
                self.conn.close()
