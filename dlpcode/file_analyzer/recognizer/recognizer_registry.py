import os
import json
from .customd_base_recognizer import Custom<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CustomSpacyRec<PERSON><PERSON><PERSON>, CustomedPhoneRecognizer
from .recognizer_config import RecognizerMetadata, DataTypeMetadata, PatternConfig, KeywordConfig
from file_analyzer.recognizer.validate_func.recognizer_validator import <PERSON>cogni<PERSON><PERSON>alidator
from presidio_analyzer import RecognizerRegistry, <PERSON><PERSON>
from typing import List
import logging
import string
from typing import Optional

logger = logging.getLogger("fortidata_recognizer")

class CustomPatternRecognizerRegistry():
    # SPACY_SUPPORTED_ENTITIES = {
    #     'PII': ['PERSON', 'STREET_ADDRESS'],
    #     'PHI': ['HOSPITAL', 'PROBLEM', 'TREATMENT'],
    # }

    def __init__(self, global_path, cc_mapping=None, library_connector=None) -> None:
        '''
        Parameters:
            cc_mapping: Regulation/compliance/standards - classification mapping
        '''
        self.recognizers = dict()

        self.cc_mapping = cc_mapping

        self.library_connector = library_connector

        recognizer_metadata = RecognizerMetadata.from_db(global_path)
        self.recognizer_metadata = recognizer_metadata
        self.initDefaultRecognizer()

        for identifier in recognizer_metadata.get_supported_entities():
            if str(identifier).__contains__("PHONE_NUMBER"):
                continue
            data_type_config = recognizer_metadata.get_data_type_config(identifier)
            logging.info(f"loading custom recognizer: {identifier} ...")
            if identifier in self.recognizers.keys():
                logger.warn(f"{identifier} defined duplicately, check again, skip this time")
                continue

            patterns = []
            conf_patterns = data_type_config.patterns
            if not conf_patterns:
                logger.warn(f"{identifier} without patterns")
                continue

            for pattern in conf_patterns:
                patterns.append(
                    Pattern(name=pattern.name, regex=r'' + pattern.regex.replace('\\\\', '\\'), score=pattern.score)
                )

            text_categories = []
            if self.cc_mapping:
                for ctype in data_type_config.compliance_type:
                    if ctype in self.cc_mapping.keys():
                        text_categories.extend(self.cc_mapping[ctype])
                    else:
                        text_categories.extend(data_type_config.text_category)
            else:
                text_categories.extend(data_type_config.text_category)

            text_categories = list(set(text_categories))
            text_categories.sort()
            #logger.debug(f"'{identifier}' - {data_type_config.compliance_type} - {text_categories}")

            try:
                if data_type_config.validate_id:
                    validator = RecognizerValidator(
                        validate_id=data_type_config.validate_id,
                        library_id=data_type_config.library_id,
                        library_connector=self.library_connector if data_type_config.library_id else None
                    )
                else:
                    validator = None
                recognizer = CustomPatternRecognizer(
                    supported_entity=identifier,
                    languages=data_type_config.languages,
                    patterns=patterns,
                    allow_list=data_type_config.allow_list,
                    regions=data_type_config.regions,
                    compliance=data_type_config.compliance_type,
                    keywords=data_type_config.keywords,
                    keyword_must=data_type_config.keyword_must,
                    validator=validator,
                    text_categories = text_categories,
                )
                self.recognizers[identifier] = recognizer
            except Exception as e:
                logger.exception(f"Load recognizer {identifier} from conf failed: {e}")

    def getRecognizerMetadata(self):
        return self.recognizer_metadata

    def initDefaultRecognizer(self):
        try:
            identifier = "PHONE_NUMBER"
            if identifier in self.recognizer_metadata.get_supported_entities():
                patterns = []
                for ent in self.recognizer_metadata.config:
                    if str(ent).startswith("PHONE_NUMBER_"):
                        for p in self.recognizer_metadata.config[ent].patterns:
                            patterns.append(Pattern(name=",".join(self.recognizer_metadata.config[ent].regions), regex=p.regex, score=p.score))
                data_type_config = self.recognizer_metadata.get_data_type_config(identifier)
                self.recognizers[identifier] = CustomedPhoneRecognizer(
                            languages=data_type_config.languages,
                            patterns=patterns,
                            supported_entity=identifier,
                            regions=data_type_config.regions,
                            compliance=data_type_config.compliance_type,
                            allow_list=data_type_config.allow_list,
                            keywords=data_type_config.keywords,
                            keyword_must=data_type_config.keyword_must,
                            text_categories=self.cc_mapping.get("PII") if self.cc_mapping else None,
                    )
        except Exception as e:
            logger.exception(f"Load recognizer {identifier} from conf failed: {e}")


    def LoadPatternRecognizerByName(self, registry:RecognizerRegistry, name:str) -> bool:
        if name not in self.recognizers:
            logger.warn(f'recognizer {name} does not exist in config file, please check again')
            return False
        try:
            registry.add_recognizer(self.recognizers[name])
            return True
        except Exception as e:
            logger.exception(f'Add recognizer failed: {e}')
            return False

    def LoadPatternRecognizerByCompliance(self, registry:RecognizerRegistry, compliance=None, regions:list=[], languages:list = ["en"], entities: list=[]):
        if "PHONE_NUMBER" in self.recognizers.keys():
            self.recognizers["PHONE_NUMBER"].supported_regions = regions
        registry_entities = []
        for name in self.recognizers:
            if not entities or name in entities:
                recognizer = self.recognizers[name]
                if compliance in recognizer.compliance and recognizer.supported_language in languages:
                    for region in regions:
                        if region in recognizer.regions:
                            registry.add_recognizer(recognizer=recognizer)
                            registry_entities.append(name)
                            break
        logger.info(f"the following entities have been added into registry of compliance {compliance}, {registry_entities} ")
        if len(registry_entities) == 0:
            return False
        return True

    def LoadPatternRecognizer(self, registry:RecognizerRegistry, regions:Optional[list]=None, languages:Optional[list] = None, entities:Optional[list] = None):
        if "PHONE_NUMBER" in self.recognizers.keys():
            self.recognizers["PHONE_NUMBER"].supported_regions = regions
        registry_entities = []
        for name in self.recognizers:
            recognizer = self.recognizers[name]
            #filter by entities
            if entities:
                if name not in entities:
                    continue
            # filter by languages: if the recognizer language is 'ALL', then it means that this recognizer is available for all languages
            if languages:
                if not bool(set(languages) & set(recognizer.languages)) and "ALL" not in recognizer.languages:
                    continue
            # filter by regions: if the recognizer regions is 'ALL', then it means that this recognizer is available for all regions
            if regions:
                if not bool(set(regions) & set(recognizer.regions)) and "ALL" not in recognizer.regions:
                    continue
            registry.add_recognizer(recognizer=recognizer)
            registry_entities.append(name)
        logger.info(f"the following entities have been added into PatternRecognizer registry {len(registry_entities)}, {registry_entities} ")
        return registry_entities

    def LoadNlpRecognizer(self, registry:RecognizerRegistry, support_entities:list=None, language:str = "en"):
        keywords_mapping = {}
        allow_list_dict = {}
        text_category_mapping = {}

        # the internal entity name doesn't has the prefix: SAPCY_
        support_entities_internal = []

        for identifier in CustomSpacyRecognizer.ENTITIES:
            label = "SPACY_"+identifier

            # Load keyword mapping
            if self.recognizer_metadata and label in self.recognizer_metadata.get_supported_entities() and \
                (not support_entities or label in support_entities):
                support_entities_internal.append(identifier)

                data_type_config = self.recognizer_metadata.get_data_type_config(label)
                keywords_conf = data_type_config.keywords
                keywords = []
                if keywords_conf is not None:
                    for kwc in keywords_conf:
                        keyword = {"keyword": kwc.keyword}
                        if kwc.left is not None: keyword["left"] = kwc.left
                        if kwc.right is not None: keyword["right"] = kwc.right
                        if kwc.score is not None: keyword["score"] = kwc.score
                        keywords.append(keyword)
                keyword_must = data_type_config.keyword_must
                keywords_mapping[label] = {
                    "keywords": keywords,
                    "keyword_must": keyword_must if len(keywords) > 0 else False,
                }
                if data_type_config.allow_list is not None:
                    allow_list_dict[label] = data_type_config.allow_list

                text_category_mapping[identifier] = []
                if self.cc_mapping:
                    for ctype in data_type_config.compliance_type:
                        if ctype in self.cc_mapping.keys():
                            text_category_mapping[identifier].extend(self.cc_mapping[ctype])
                        else:
                            text_category_mapping[identifier].extend(data_type_config.text_category)
                else:
                    text_category_mapping[identifier].extend(data_type_config.text_category)

        if support_entities_internal:
            logger.debug(f"keywords_mapping for NLPRecognizer ({len(keywords_mapping)}): {keywords_mapping}")
            logger.debug(f"text_category_mapping for NLPRecognizer ({len(text_category_mapping)}): {text_category_mapping}")

            recognizer = CustomSpacyRecognizer(
                                            supported_entities=support_entities_internal,
                                            keywords_mapping=keywords_mapping,
                                            allow_list=allow_list_dict,
                                            text_category_mapping=text_category_mapping,
                                            )
            registry.add_recognizer(recognizer=recognizer)

        logger.info(f"the following entities have been added into NLPRecognizer registry {len(support_entities_internal)}, {support_entities_internal} ")
        return support_entities_internal

class UserDefinedRecognizerRegistry():
    def __init__(self, config:RecognizerMetadata) -> None:
        self.recognizer_metadata = config

    def getRecognizerMetadata(self):
        return self.recognizer_metadata

    def LoadRecognizers(
            self,
            registry:RecognizerRegistry,
            regions:Optional[list]=None,
            languages:Optional[list]=None,
            entities:Optional[list]=None
        ) -> bool:
        succ_num = 0
        for entity in self.recognizer_metadata.get_supported_entities():
            data_type = self.recognizer_metadata.get_data_type_config(entity)

            # filter by regions: if the recognizer regions is 'ALL', then it means that this recognizer is available for all regions
            if regions:
                if not bool(set(regions) & set(data_type.regions)) and "ALL" not in data_type.regions:
                    continue
            # filter by languages: if the recognizer language is 'ALL', then it means that this recognizer is available for all languages
            if languages:
                if not bool(set(languages) & set(data_type.languages)) and "ALL" not in data_type.languages:
                    continue
            #filter by entities
            if entities:
                if data_type.entity not in entities:
                    continue
            try:
                patterns = []
                if data_type.patterns:
                    for pattern in data_type.patterns:
                        patterns.append(Pattern(
                            name=pattern.name,
                            regex=pattern.regex,
                            score=pattern.score
                        ))
                recognizer = CustomPatternRecognizer(
                    supported_entity=entity,
                    languages=data_type.languages,
                    regions=data_type.regions,
                    keyword_must=data_type.keyword_must,
                    patterns=patterns,
                    keywords=data_type.keywords
                )
                registry.add_recognizer(recognizer=recognizer)
                succ_num += 1
            except Exception as e:
                logger.error(f"Load user customed recognizer entity:[{entity}] failed: {e}")
                continue
        if succ_num == 0:
            return False
        logger.info(f"Load {succ_num} user customized recognizers")
        return True

