import os, sys, json
import datetime
import logging
from typing import List, Optional, Dict
import copy
import hyperscan
import traceback
import time

from presidio_analyzer import (
    LocalRecognizer,
    <PERSON><PERSON>,
    RecognizerResult,
    EntityRecognizer,
    AnalysisExplanation,
)
from presidio_analyzer.nlp_engine import NlpArtifacts
from presidio_analyzer import AnalyzerEngine

from memory_profiler import memory_usage

#logger = logging.getLogger("fortidata_recognizerr")
logger = logging.getLogger("HyperscanRecognizer")
logger.setLevel(logging.DEBUG)

# TEST_CONFIGS = [
#     {
#         "entity": "TEXT",
#         "context": ["keyword"],
#         "patterns": [
#             {"name": "", "score": 0.1, "regex": ""},
#         ]
#     },
#     {
#         "entity": "TEXT2",
#         "context": ["keyword"],
#         "recognition": "",
#         "patterns": [
#             {"name": "", "flags": 0, "score": 0.1, "regex": ""},
#         ]
#     },
# ]



# https://python-hyperscan.readthedocs.io/en/latest/usage/
class HyperscanRecognizer(LocalRecognizer):
    """
    PII entity recognizer using regular expressions or deny-lists.

    :param patterns: A list of patterns to detect
    :param deny_list: A list of words to detect,
    in case our recognizer uses a predefined list of words (deny list)
    :param context: list of context words
    :param deny_list_score: confidence score for a term
    identified using a deny-list
    :param global_hs_flags: hyperscan flags to be used in regex matching,
    including deny-lists.
    """
    DEFAULT_EXPLANATION = "Identified as {} by Hyperscan's Named Entity Recognition"
    HYPERSCAN_RECOGNIZER_MATCH_ID="hyperscan_recognizer_match_id"
    MAX_PATTERN_NUM_PER_ENTITY = 10000000

    def __init__(
        self,
        supported_entities: Optional[List[str]] = None,
        name: str = None,
        config: dict = None,
        supported_language: str = "en",
        global_hs_flags: Optional[int] = hyperscan.HS_FLAG_CASELESS|hyperscan.HS_FLAG_SOM_LEFTMOST,
        version: str = "0.0.1",
        context_prefix_count: int = 5,
        context_suffix_count: int = 0,
        context_similarity_factor: float = 0.2,
        min_score_with_context_similarity: float = 0.4,
    ):

        if not config:
            raise ValueError(
                "Hyperscan recognizer should be initialized with config"
            )

        self.db = None
        self.config = config
        self.global_hs_flags = global_hs_flags
        self.context_prefix_count = context_prefix_count
        self.context_suffix_count = context_suffix_count
        self.context_similarity_factor = context_similarity_factor
        self.min_score_with_context_similarity = min_score_with_context_similarity

        super().__init__(
            supported_entities=self._get_supported_entities(),
            supported_language=supported_language,
            name=name,
            version=version,
        )

    def _get_supported_entities(self):
        supported_entities = []
        for conf in self.config:
            supported_entities.append(conf['entity'])

        return list(set(supported_entities))

    def _load_config(self):
        entities = {}
        expressions = []
        flags_list = []
        ids = []
        for eidx, conf in enumerate(self.config):
            eid = eidx+1
            if conf['entity'] not in self.supported_entities:
                logger.debug(f"Entity {conf['entity']} not supported")
                continue

            entity = {
                "id": eid,
                "name":conf['entity'],
                "context": conf['context'],
                "recognition": conf.get("recognition"),
                "context_similarity_factor": conf.get("context_similarity_factor", self.context_similarity_factor),
                "min_score_with_context_similarity": conf.get("min_score_with_context_similarity", self.min_score_with_context_similarity),
            }
            #print(entity)

            patterns = []
            pidx = 0
            for _, item in enumerate(conf['patterns']):

                #print(item)
                flags = item['flags'] if 'flags' in item and item['flags'] != 0 else self.global_hs_flags
                regex = item.get('regex')
                keywords = item.get('keywords')
                if regex:
                    pid = eid*HyperscanRecognizer.MAX_PATTERN_NUM_PER_ENTITY + (pidx+1)
                    ids.append(pid)
                    expressions.append(regex if type(regex) == bytes else regex.encode('utf-8'))
                    flags_list.append(flags)

                    pattern = {
                        'pid': pid,
                        "name": item['name'],
                        'regex': regex,
                        "flags": flags,
                        "score": item['score'],
                        "context_prefix_count": item.get('context_prefix_count', self.context_prefix_count),
                        "context_suffix_count": item.get('context_suffix_count', self.context_suffix_count),
                    }
                    patterns.append(pattern)
                    pidx += 1
                    if pidx > HyperscanRecognizer.MAX_PATTERN_NUM_PER_ENTITY:
                        raise ValueError(f"The number of pattern has been exceeded, {HyperscanRecognizer.MAX_PATTERN_NUM_PER_ENTITY}")

                elif keywords:
                    #keywords = [s if type(s)==bytes else s.encode("utf-8") for s in keywords]
                    for keyword in keywords:
                        pid = eid*HyperscanRecognizer.MAX_PATTERN_NUM_PER_ENTITY + (pidx+1)
                        ids.append(pid)
                        expressions.append( keyword if type(keyword) == bytes else keyword.encode('utf-8'))
                        flags_list.append(flags)

                        pattern = {
                            'pid': pid,
                            "name": item['name'],
                            'regex': regex,
                            "flags": flags,
                            "score": item['score'],
                            "context_prefix_count": item.get('context_prefix_count', self.context_prefix_count),
                            "context_suffix_count": item.get('context_suffix_count', self.context_suffix_count),
                        }
                        patterns.append(pattern)
                        pidx += 1
                        if pidx > HyperscanRecognizer.MAX_PATTERN_NUM_PER_ENTITY:
                            raise ValueError(f"The number of pattern has been exceeded, {HyperscanRecognizer.MAX_PATTERN_NUM_PER_ENTITY}")
                else:
                    raise ValueError(f"Invalid pattern, pattern : {item}")

            entity['patterns'] = patterns
            entities[eid] = entity

        return entities, expressions, flags_list, ids

    def load(self):
        try:
            entities, expressions, flags, ids = self._load_config()
            self.loaded_entities = entities
            self.db = hyperscan.Database()
            # print(expressions)
            # print(ids)
            print(f"Entities Count: {len(entities)}, expressions Count: {len(expressions)}")

            # compile_start_time = datetime.datetime.now()
            # self.db.compile(expressions=expressions, ids=ids, flags=flags)
            # compile_time = datetime.datetime.now() - compile_start_time
            # print(f"--- compile_time: {compile_time.seconds}.{compile_time.microseconds} seconds")
            # serialized_db = hyperscan.dumpb(self.db)
            # print(f"Hyperscan db info, {self.db.info().decode()} Size: {len(serialized_db)}")


            total=200000
            expressions = expressions[0:total]
            ids=ids[0:total]
            flags=flags[:total]
            print(f"expressions Count: {len(expressions)} ids: {len(ids)} flags: {len(flags)}")

            compile_start_time = datetime.datetime.now()
            #self.db.compile(expressions=expressions, ids=ids, flags=flags)
            mem_usage = memory_usage((self.db.compile, (), {"expressions":expressions, "ids":ids, "flags":flags}))
            #print(f"mem_usage: {mem_usage}")
            print(f"Compile, Peak memory usage: {max(mem_usage)} MiB")
            compile_time = datetime.datetime.now() - compile_start_time
            print(f"--- compile_time: {compile_time.seconds}.{compile_time.microseconds} seconds")
            serialized_db = hyperscan.dumpb(self.db)
            print(f"Hyperscan db info, {self.db.info().decode()} Size: {len(serialized_db)}")

            ## for test
            # total=700000
            # batch_size = 100000
            # for i in range(len(expressions[:total])//batch_size + 1):
            #     print(f"Compiled 0-{(i+1)*batch_size} expressions")
            #     compile_start_time = datetime.datetime.now()
            #     self.db.compile(expressions=expressions[0:(i+1)*batch_size], ids=ids[0:(i+1)*batch_size], flags=flags[0:(i+1)*batch_size])
            #     compile_time = datetime.datetime.now() - compile_start_time

            #     print(f"--- compile_time: {compile_time.seconds}.{compile_time.microseconds} seconds")
            #     serialized_db = hyperscan.dumpb(self.db)
            #     print(f"Hyperscan db info, {self.db.info().decode()} Size: {len(serialized_db)}")
            #time.sleep(50)
        except:
            print(traceback.format_exc())
            raise ValueError(
                f"hyperscan db init failed"
            )
    def _parse_match_id(self, id):
        eid = id // HyperscanRecognizer.MAX_PATTERN_NUM_PER_ENTITY
        pid = id % HyperscanRecognizer.MAX_PATTERN_NUM_PER_ENTITY - 1
        return eid, pid

    def analyze(
        self,
        text: str,
        entities: List[str],
        nlp_artifacts: Optional[NlpArtifacts] = None
    ) -> List[RecognizerResult]:

        def match_handler(id, start, end, flags, context):
            match = {
                "id": id,
                "start": start,
                "end": end,
                "flags": flags,
                "context": context,
            }
            match_results.append(match)
            #print(match)
            return

        #print(self.global_hs_flags, self.config)
        results = []
        match_results = []
        if self.db:
            #print(f"text: {text}")
            #self.db.scan(text.encode('utf-8'), match_handler)

            mem_usage = memory_usage((self.db.scan, (text.encode('utf-8'), match_handler), {}))
            #print(f"mem_usage: {mem_usage}")
            print(f"Scan, Peak memory usage: {max(mem_usage)} MiB")
        else:
            logger.error("Hyerscan db is None")

        for _, match in enumerate(match_results):
            #print(match)
            id = match["id"]
            start = match['start']
            end = match['end']
            flags = match['flags']
            context = match['context']

            eid, pid = self._parse_match_id(id)
            #print(eid, pid)

            current_entity = self.loaded_entities.get(eid)
            if not current_entity:
                logger.error(f"entity id {eid} not found")
                continue

            if entities and current_entity['name'] not in entities:
                logger.debug(
                        f"Skipping entity {current_entity['name']} "
                        f"as it is not in the analyze entities list"
                )
                continue

            patterns = current_entity['patterns']
            if pid >= len(patterns):
                logger.error(f"pattern id {pid} not found")
                continue

            pattern = patterns[pid]
            current_match = text[start:end]
            score = pattern["score"]

            validation_result = self.validate_result(current_entity, current_match)
            textual_explanation = self.DEFAULT_EXPLANATION.format(current_entity['name'])

            description = self.build_regex_explanation(
                self.name,
                pattern['name'],
                pattern['regex'],
                score,
                validation_result,
                flags,
                textual_explanation=textual_explanation,
            )
            pattern_result = RecognizerResult(
                entity_type=current_entity['name'],
                start=start,
                end=end,
                score=score,
                analysis_explanation=description,
                recognition_metadata={
                    RecognizerResult.RECOGNIZER_NAME_KEY: self.name,
                    RecognizerResult.RECOGNIZER_IDENTIFIER_KEY: self.id,
                    HyperscanRecognizer.HYPERSCAN_RECOGNIZER_MATCH_ID: id,
                },
            )

            if validation_result is not None:
                if validation_result:
                    pattern_result.score = EntityRecognizer.MAX_SCORE
                else:
                    pattern_result.score = EntityRecognizer.MIN_SCORE

            invalidation_result = self.invalidate_result(current_match)
            if invalidation_result is not None and invalidation_result:
                pattern_result.score = EntityRecognizer.MIN_SCORE

            if pattern_result.score > EntityRecognizer.MIN_SCORE:
                results.append(pattern_result)

            # Update analysis explanation score following validation or invalidation
            description.score = pattern_result.score

            results = EntityRecognizer.remove_duplicates(results)
            # print(pattern_result)

        print(f"analyze results: {results}")
        return results

    def validate_result(self, current_entity, pattern_text: str) -> Optional[bool]:
        """
        Validate the pattern logic e.g., by running checksum on a detected pattern.

        :param pattern_text: the text to validated.
        Only the part in text that was detected by the regex engine
        :return: A bool indicating whether the validation was successful.
        """
        return None

    def invalidate_result(self, pattern_text: str) -> Optional[bool]:
        """
        Logic to check for result invalidation by running pruning logic.

        For example, each SSN number group should not consist of all the same digits.

        :param pattern_text: the text to validated.
        Only the part in text that was detected by the regex engine
        :return: A bool indicating whether the result is invalidated
        """
        return None

    @staticmethod
    def build_regex_explanation(
        recognizer_name: str,
        pattern_name: str,
        pattern: str,
        original_score: float,
        validation_result: bool,
        regex_flags: int,
        textual_explanation: str,
    ) -> AnalysisExplanation:
        """
        Construct an explanation for why this entity was detected.

        :param recognizer_name: Name of recognizer detecting the entity
        :param pattern_name: Regex pattern name which detected the entity
        :param pattern: Regex pattern logic
        :param original_score: Score given by the recognizer
        :param validation_result: Whether validation was used and its result
        :param regex_flags: Regex flags used in the regex matching
        :return: Analysis explanation
        """
        explanation = AnalysisExplanation(
            recognizer=recognizer_name,
            original_score=original_score,
            pattern_name=pattern_name,
            pattern=pattern,
            validation_result=validation_result,
            regex_flags=regex_flags,
            textual_explanation=textual_explanation,
        )
        return explanation

    def enhance_using_context(
        self,
        text: str,
        raw_recognizer_results: List[RecognizerResult],
        other_raw_recognizer_results: List[RecognizerResult],
        nlp_artifacts: NlpArtifacts,
        context: Optional[List[str]] = None,
    ) -> List[RecognizerResult]:
        """Enhance confidence score using context of the entity.

        Override this method in derived class in case a custom logic
        is needed, otherwise return value will be equal to
        raw_results.

        in case a result score is boosted, derived class need to update
        result.recognition_metadata[RecognizerResult.IS_SCORE_ENHANCED_BY_CONTEXT_KEY]

        :param text: The actual text that was analyzed
        :param raw_recognizer_results: This recognizer's results, to be updated
        based on recognizer specific context.
        :param other_raw_recognizer_results: Other recognizer results matched in
        the given text to allow related entity context enhancement
        :param nlp_artifacts: The nlp artifacts contains elements
                              such as lemmatized tokens for better
                              accuracy of the context enhancement process
        :param context: list of context words
        """
        # logger.info(f"Enter {self.name}.enhance_using_context")
        print(f"Enter {self.name}.enhance_using_context")

        results = copy.deepcopy(raw_recognizer_results)

        # Create empty list in None or lowercase all context words in the list
        if not context:
            context = []
        else:
            context = [word.lower() for word in context]

        # Sanity
        if nlp_artifacts is None:
            logger.warning("NLP artifacts were not provided")
            return results

        for result in results:
            match_id = None
            # get recognizer matching the result, if found.
            if (
                result.recognition_metadata
                and HyperscanRecognizer.HYPERSCAN_RECOGNIZER_MATCH_ID
                in result.recognition_metadata.keys()
            ):

                match_id = result.recognition_metadata[
                    HyperscanRecognizer.HYPERSCAN_RECOGNIZER_MATCH_ID
                ]

            if not match_id:
                logger.debug(
                    "Match id not found as part of the "
                    "recognition_metadata dict in the RecognizerResult. "
                )
                continue

            eid, pid = self._parse_match_id(match_id)

            current_entity = self.loaded_entities.get(eid)
            if not current_entity:
                logger.error(f"entity id {eid} not found")
                continue
            # print("\n"+"*"*50)
            # print(f"current_entity: {current_entity}\n match text: {text[result.start:result.end]}")

            entity_context = current_entity.get('context')
            context_similarity_factor = current_entity.get('context_similarity_factor')
            min_score_with_context_similarity = current_entity.get('min_score_with_context_similarity')
            # skip recognizer result if the recognizer doesn't support
            # context enhancement
            if not entity_context:
                logger.debug(
                    "recognizer '%s' does not support context enhancement",
                    self.name,
                )
                continue

            current_pattern = current_entity.get('patterns')[pid]
            context_prefix_count = current_pattern.get('context_prefix_count')
            context_suffix_count = current_pattern.get('context_suffix_count')
            # extract lemmatized context from the surrounding of the match
            word = text[result.start : result.end]
            #print(f"word: {word}")

            surrounding_words = self._extract_surrounding_words(
                nlp_artifacts=nlp_artifacts, word=word, start=result.start,
                context_prefix_count=context_prefix_count,
                context_suffix_count=context_suffix_count
            )

            #print(f"surrounding_words A: {surrounding_words}")
            # combine other sources of context with surrounding words
            surrounding_words.extend(context)
            #print(f"entity_context: {entity_context}")

            supportive_context_word = self._find_supportive_word_in_context(
                surrounding_words, entity_context
            )
            #print(f"supportive_context_word: {supportive_context_word}")

            if len(supportive_context_word) > 0:
                result.score += (context_similarity_factor) * len(supportive_context_word)
                result.score = max(result.score, min_score_with_context_similarity)
                result.score = min(result.score, EntityRecognizer.MAX_SCORE)

                # Update the explainability object with context information
                # helped to improve the score
                result.analysis_explanation.set_supportive_context_word(
                    supportive_context_word
                )
                result.analysis_explanation.set_improved_score(result.score)


        return results

    @staticmethod
    def _find_supportive_word_in_context(
        context_list: List[str], recognizer_context_list: List[str]
    ) -> List[str]:
        word = []
        # If the context list is empty, no need to continue
        if context_list is None or recognizer_context_list is None:
            return word

        for predefined_context_word in recognizer_context_list:
            result = next(
                (
                    True
                    for keyword in context_list
                    if predefined_context_word == keyword
                ),
                False,
            )
            if result:
                logger.debug("Found context keyword '%s'", predefined_context_word)
                word.append(predefined_context_word)

        return word

    def _extract_surrounding_words(
        self, nlp_artifacts: NlpArtifacts, word: str, start: int,
        context_prefix_count, context_suffix_count,
    ) -> List[str]:
        """Extract words surrounding another given word.

        The text from which the context is extracted is given in the nlp
        doc.

        :param nlp_artifacts: An abstraction layer which holds different
                              items which are the result of a NLP pipeline
                              execution on a given text
        :param word: The word to look for context around
        :param start: The start index of the word in the original text
        """
        if not nlp_artifacts.tokens:
            logger.info("Skipping context extraction due to lack of NLP artifacts")
            # if there are no nlp artifacts, this is ok, we can
            # extract context and we return a valid, yet empty
            # context
            return [""]

        # Get the already prepared words in the given text, in their
        # LEMMATIZED version
        lemmatized_keywords = nlp_artifacts.keywords

        # print(f"nlp_artifacts.lemmas: {nlp_artifacts.lemmas}")
        # print(f"lemmatized_keywords: {lemmatized_keywords}")

        # since the list of tokens is not necessarily aligned
        # with the actual index of the match, we look for the
        # token index which corresponds to the match
        token_index = self._find_index_of_match_token(
            word, start, nlp_artifacts.tokens, nlp_artifacts.tokens_indices
        )

        # print(f"token_index: {token_index}, token: {nlp_artifacts.tokens[token_index]} tokens_indices: {nlp_artifacts.tokens_indices[token_index]}")

        # index i belongs to the PII entity, take the preceding n words
        # and the succeeding m words into a context list

        backward_context = self._add_n_words_backward(
            token_index,
            context_prefix_count,
            nlp_artifacts.lemmas,
            lemmatized_keywords,
        )
        forward_context = self._add_n_words_forward(
            token_index,
            context_suffix_count,
            nlp_artifacts.lemmas,
            lemmatized_keywords,
        )

        # print(f"backward_context: {backward_context}, forward_context: {forward_context} context_prefix_count: {context_prefix_count} context_suffix_count: {context_suffix_count}")

        context_list = []
        context_list.extend(backward_context)
        context_list.extend(forward_context)
        context_list = list(set(context_list))
        logger.debug("Context list is: %s", " ".join(context_list))
        return context_list

    @staticmethod
    def _find_index_of_match_token(
        word: str, start: int, tokens, tokens_indices: List[int]  # noqa ANN001
    ) -> int:
        found = False
        # we use the known start index of the original word to find the actual
        # token at that index, we are not checking for equivalence since the
        # token might be just a substring of that word (e.g. for phone number
        # 555-124564 the first token might be just '555' or for a match like '
        # rocket' the actual token will just be 'rocket' hence the misalignment
        # of indices)
        # Note: we are iterating over the original tokens (not the lemmatized)
        i = -1
        for i, token in enumerate(tokens, 0):
            # Either we found a token with the exact location, or
            # we take a token which its characters indices covers
            # the index we are looking for.
            if (tokens_indices[i] == start) or (start < tokens_indices[i] + len(token)):
                # found the interesting token, the one that around it
                # we take n words, we save the matching lemma
                found = True
                break

        if not found:
            raise ValueError(
                "Did not find word '" + word + "' "
                "in the list of tokens although it "
                "is expected to be found"
            )
        return i

    @staticmethod
    def _add_n_words(
        index: int,
        n_words: int,
        lemmas: List[str],
        lemmatized_filtered_keywords: List[str],
        is_backward: bool,
    ) -> List[str]:
        """
        Prepare a string of context words.

        Return a list of words which surrounds a lemma at a given index.
        The words will be collected only if exist in the filtered array

        :param index: index of the lemma that its surrounding words we want
        :param n_words: number of words to take
        :param lemmas: array of lemmas
        :param lemmatized_filtered_keywords: the array of filtered
               lemmas from the original sentence,
        :param is_backward: if true take the preceding words, if false,
                            take the succeeding words
        """
        i = index
        context_words = []
        # The entity itself is no interest to us...however we want to
        # consider it anyway for cases were it is attached with no spaces
        # to an interesting context word, so we allow it and add 1 to
        # the number of collected words

        # collect at most n words (in lower case)
        remaining = n_words + 1
        while 0 <= i < len(lemmas) and remaining > 0:
            lower_lemma = lemmas[i].lower()
            if lower_lemma in lemmatized_filtered_keywords:
                context_words.append(lower_lemma)
                remaining -= 1
            i = i - 1 if is_backward else i + 1
        return context_words

    def _add_n_words_forward(
        self,
        index: int,
        n_words: int,
        lemmas: List[str],
        lemmatized_filtered_keywords: List[str],
    ) -> List[str]:
        return self._add_n_words(
            index, n_words, lemmas, lemmatized_filtered_keywords, False
        )

    def _add_n_words_backward(
        self,
        index: int,
        n_words: int,
        lemmas: List[str],
        lemmatized_filtered_keywords: List[str],
    ) -> List[str]:
        return self._add_n_words(
            index, n_words, lemmas, lemmatized_filtered_keywords, True
        )

def test():
    TEST_CONFIGS = [
        {
            "entity": "TEST1",
            "context": ["id"],
            "patterns": [
                {"name": "", "score": 0.1, "regex": br"\b\d{5}\b"},
            ]
        },
        {
            "entity": "TEST2",
            "context": ["name", "student"],
            "recognition": None,
            "patterns": [
                {"name": "", "flags": 0, "score": 0.1, "regex": br"\b[a-zA-Z]{3,5}\b", "context_prefix_count":4, "context_suffix_count":0},
            ]
        },
        {
            "entity": "TEST3",
            "context": ["name"],
            "recognition": [],
            "patterns": [
                {"name": "", "flags": 0, "score": 0.1, "regex": br"TOM"},
            ]
        },
        {
            "entity": "TEST4",
            "context": ["student"],
            "recognition": [],
            "patterns": [
                {"name": "", "flags": 0, "score": 0.1, "keywords": ["Tom", "Tony", "Anna"]},
            ]
        },
    ]
    #hs_recognizer = HyperscanRecognizer(supported_entities=["TEST1", "TEST2"], config=TEST_CONFIGS)
    hs_recognizer = HyperscanRecognizer(config=TEST_CONFIGS)
    #hs_recognizer.load()

    analyzer = AnalyzerEngine()
    analyzer.registry.add_recognizer(hs_recognizer)

    text = "the student's name is Tom, his student id is 12345"
    #text = "This is a test text abcde"
    #results = analyzer.analyze(text=text, language='en', entities=["TEST1", "TEST2"])
    #results = analyzer.analyze(text=text, language='en', entities=["TEST2"])
    results = analyzer.analyze(text=text, language='en')
    for res in results:
        print(f"type: {res.entity_type}, score: {res.score}, match: {text[res.start:res.end]}")

def gen_keywords_config(entity, keyword_path=None, keywords=None, name=None, context=None, score=0.5):
    if keywords is None:
        if not os.path.exists(keyword_path):
            raise ValueError(f"{keyword_path} not found")

        keywords = []
        with open(keyword_path, 'r') as f:
            lines = f.readlines()

            for line in lines:
                line = line.strip()
                if not line:
                    print("empty line, skipping")
                    continue
                keywords.append(line)

    if keywords:
        patterns = []
        if False and len(keywords) > 1000:
            batch_size = 10
            batch_count = (len(keywords)//batch_size) + 1
            for i in range(batch_count):
                sub_keywords = keywords[i*batch_size: (i+1)*batch_size]
                combined_keywords = "|".join(sub_keywords)

                pattern = {
                    "name": name if name is not None else f"{entity}_keyword_b{i}",
                    'regex': combined_keywords,
                    "score": score,
                }
                patterns.append(pattern)

        else:
            patterns.append({
                "name": name if name is not None else f"{entity}_keyword",
                "score": score,
                "keywords": keywords,
            })

        config = {
            "entity": entity,
            "context": context,
            "patterns": patterns
        }
        print(f"Entity: {entity} patterns: {len(config['patterns'])} keyword: {len(keywords)}")
        return config
    else:
        print("failed to generate keywords configuration")
        return None

def test_keywords():
    config_all = []
    config = gen_keywords_config("TEST", keywords=["Tom", "Tony", "Anna"], context=['student'], score=0.5)
    if config:
        #print(f"config: {config}")
        config_all.append(config)

    # config = gen_keywords_config("ICD", keyword_path="../data/healthcare/international_classification_of_diseases(ICD).txt", context=['student'], score=0.5)
    # if config:
    #     #print(f"config: {config}")
    #     config_all.append(config)

    # config = gen_keywords_config("HCPCS", keyword_path="../data/healthcare/us_healthcare_common_procedure_coding_system(HCPCS).txt", context=['student'], score=0.5)
    # if config:
    #     #print(f"config: {config}")
    #     config_all.append(config)

    # config = gen_keywords_config("NDC", keyword_path="../data/healthcare/us_national_drug_code(NDC).txt", context=['student'], score=0.5)
    # if config:
    #     #print(f"config: {config}")
    #     config_all.append(config)

    config = gen_keywords_config("NPI", keyword_path="../data/healthcare/us_national_provider_identifier(NPI).txt", context=['student'], score=0.5)
    if config:
        #print(f"config: {config}")
        config_all.append(config)

    # config = gen_keywords_config("UDI", keyword_path="../data/healthcare/us_unique_device_identifier(UDI).txt", context=['student'], score=0.5)
    # if config:
    #     #print(f"config: {config}")
    #     config_all.append(config)

    if not config_all:
        print("No configuration, exit")
        return

    hs_recognizer = HyperscanRecognizer(config=config_all)
    analyzer = AnalyzerEngine()
    analyzer.registry.add_recognizer(hs_recognizer)

    text = "the student's name is Tom, his student id is 12345, NPI: **********, **********"
    results = analyzer.analyze(text=text, language='en')
    for res in results:
        print(f"type: {res.entity_type}, score: {res.score}, match: {text[res.start:res.end]}")

def performance_test(keyword_path):
    keywords = []
    with open(keyword_path, 'r') as f:
        lines = f.readlines()

        for line in lines:
            line = line.strip()
            if not line:
                print("empty line, skipping")
                continue
            keywords.append(line)

    print(f"keywords: {len(keywords)}")
    ids = [i for i in range(len(keywords))]
    flags = [hyperscan.HS_FLAG_CASELESS|hyperscan.HS_FLAG_SOM_LEFTMOST for i in range(len(keywords))]
    expressions = [keyword if type(keyword) == bytes else keyword.encode('utf-8') for keyword in keywords]

    text = b"the student's name is Tom, his student id is 12345, NPI: **********, **********"
    with open('performance_result.txt', 'w') as f:
        f.write(f"Pattern-Count Compile-Mem Scan-Mem Compile-time\n")

    #for i in range(10, 50):
    for i in range(0, 3):
        print("*"* 100)
        total=100000 * (i+1)
        print(f"{total}")

        sub_expressions = expressions[0:total]
        sub_ids=ids[0:total]
        sub_flags=flags[:total]

        print(f"Expressions Count: {len(sub_expressions)} ids: {len(sub_ids)} flags: {len(sub_flags)}")

        db = hyperscan.Database()
        compile_start_time = datetime.datetime.now()
        #self.db.compile(expressions=expressions, ids=ids, flags=flags)
        mem_usage = memory_usage((db.compile, (), {"expressions":sub_expressions, "ids":sub_ids, "flags":sub_flags}))
        #print(f"mem_usage: {mem_usage}")
        compile_mem_max = max(mem_usage)
        print(f"Compile, Peak memory usage: {compile_mem_max} MiB")
        compile_time = datetime.datetime.now() - compile_start_time
        print(f"--- compile_time: {compile_time.seconds}.{compile_time.microseconds} seconds")
        serialized_db = hyperscan.dumpb(db)
        print(f"Hyperscan db info, {db.info().decode()} Size: {len(serialized_db)}")

        with open(f"hs_db_{total}.db", 'wb') as f:
            f.write(serialized_db)

        mem_usage = memory_usage((db.scan, (text, None), {}))
        #print(f"Scan, Peak memory usage: {max(mem_usage)} MiB")
        scan_mem_max = max(mem_usage)
        print(f"Compile, Peak memory usage: {scan_mem_max} MiB")

        with open('performance_result.txt', 'a') as f:
            f.write(f"{total} {compile_mem_max:9}MiB {scan_mem_max:9}MiB {compile_time.seconds}.{compile_time.microseconds} seconds\n")

def match_handler(id, start, end, flags, context):
    match = {
        "id": id,
        "start": start,
        "end": end,
        "flags": flags,
        "context": context,
    }
    # match_results.append(match)
    print(match)
    return

def performance_test_scan():
    text = b"the student's name is Tom, his student id is 12345, NPI: **********, **********"
    for i in range(0,3):
        print("*"* 100)
        total=100000 * (i+1)
        filename = f"hs_db_{total}.db"
        if not os.path.exists(filename):
            print(f"{filename} not found")
            continue

        with open(filename, 'rb') as f:
            serialized = f.read()

        db = hyperscan.loadb(serialized, hyperscan.HS_MODE_BLOCK)

        print(f"Hyperscan db info, {db.info().decode()}")
        db.scan(text, match_handler)

        mem_usage = memory_usage((db.scan, (text,), {}))
        print(f"Scan, Peak memory usage: {max(mem_usage)} MiB")


if __name__ == "__main__":
    from presidio_analyzer import AnalyzerEngine
    #test_keywords()
    #performance_test("../data/healthcare/us_national_provider_identifier(NPI).txt")

    performance_test_scan()
