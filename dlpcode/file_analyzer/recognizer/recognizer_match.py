import os, sys
import traceback
import json
import datetime
import threading
import hyperscan
import logging
from .recognizer_config import RecognizerMetadata
from typing import List
from presidio_analyzer.recognizer_result import RecognizerResult
import regex as re
logger = logging.getLogger("fortidata_recognizer")

class MatchedPattern():
    def __init__(self, id, start, end, score, type, name, regex, text, compiled_extract_regex, compiled_exclude_regex):
        self.id = id
        self.start = start
        self.end = end
        self.score = score
        self.type = type
        self.name = name
        self.regex = regex
        self.text = text
        self.compiled_extract_regex = compiled_extract_regex
        self.compiled_exclude_regex = compiled_exclude_regex

class RecognizerMatchEngine():
    def __init__(self, recognizer_metadata:RecognizerMetadata, entities:list=None) -> None:
        self.thread_local = threading.local()
        self.db = None
        try:
            self.load_config(recognizer_metadata=recognizer_metadata, entities=entities)
            self.compile()
        except Exception as e:
            self.db = None
            self.patterns = []
            logger.exception(traceback.format_exc())
            raise e

    def compile(self):
        expressions, ids, flags_list = self.__convert_kws()
        if not expressions:
            logger.warning(f"no expressions found")
            return 
        self.db = hyperscan.Database()

        # db = hyperscan.Database()
        # for i in reversed(range(len(expressions))):
        #     try:
        #         db.compile(expressions=[expressions[i]], ids=[ids[i]], flags=[flags_list[i]])
        #     except hyperscan.error as e:
        #         del expressions[i]
        #         del ids[i]
        #         del flags_list[i]
        #         print(f"{self.patterns[i]['type']}:{self.patterns[i]['name']} compile error: {e}")

        logger.info(f"hyperscan recognizer expressions count: {len(expressions)}")
        compile_start_time = datetime.datetime.now()
        self.db.compile(expressions=expressions, ids=ids, flags=flags_list)
        compile_time = datetime.datetime.now() - compile_start_time
        logger.info(f"hyperscan recognizer compile_time: {compile_time.seconds}.{compile_time.microseconds} seconds")

    def __convert_kws(self):
        expressions = []
        flags_list = []
        ids = []
        
        for id, pattern in enumerate(self.patterns):
            regex = pattern['regex']  
            expressions.append(regex if type(regex) == bytes else regex.encode('utf-8'))
            ids.append(id)
            if pattern["leftmost_flag"]:
                flags_list.append(hyperscan.HS_FLAG_MULTILINE|hyperscan.HS_FLAG_SOM_LEFTMOST)
            else:
                flags_list.append(hyperscan.HS_FLAG_MULTILINE)
        
        return expressions, ids, flags_list

    def load_config(self, recognizer_metadata:RecognizerMetadata, entities:list=None):
        if not recognizer_metadata:
            logger.error("empty recognizer metadata")

        patterns = []
        for entity in recognizer_metadata.get_supported_entities():
            if entities and entity not in entities and not (entity.startswith('PHONE_NUMBER_') and "PHONE_NUMBER" in entities):
                continue
            item = recognizer_metadata.get_data_type_config(entity)
            if item.patterns is not None:
                for pattern in item.patterns:
                    patterns.append({
                        "regex": pattern.regex,
                        "score": pattern.score,
                        "name": ','.join(item.regions) if entity.startswith('PHONE_NUMBER_') else pattern.name,
                        "type": "PHONE_NUMBER" if entity.startswith('PHONE_NUMBER_') else entity,
                        "keyword_must": item.keyword_must,
                        "compiled_extract_regex": pattern.compiled_extract_regex,
                        "compiled_exclude_regex": pattern.compiled_exclude_regex,
                        "leftmost_flag": pattern.leftmost_flag
                    })
        self.patterns = patterns
        return
    
    def scan(self, text:str, hit_keywords:dict):
        text = text.encode('utf-8')
        match_results = {}
        longest_match = {}
        def match_handler(id, start, end, flags, context):
            pattern = self.patterns[id]
            if pattern["keyword_must"] and pattern["type"] not in hit_keywords:
                return
            entity_type = pattern["type"]
            key = f"{start}:{entity_type}"
            current_longest = longest_match.get(key)
            if current_longest is None or end - start > current_longest.end - current_longest.start:
                longest_match[key] = MatchedPattern(
                    id=id,
                    start=start,
                    end=end,
                    score=pattern["score"],
                    type=pattern["type"],
                    name=pattern["name"],
                    regex=pattern["regex"],
                    text=text[start:end],
                    compiled_extract_regex=pattern["compiled_extract_regex"],
                    compiled_exclude_regex=pattern["compiled_exclude_regex"]
                )

        if self.db:
            scratch = getattr(self.thread_local, "scratch", None)
            if not scratch:
                scratch = hyperscan.Scratch()
                scratch.set_database(self.db)
                self.thread_local.scratch = scratch
            
            self.db.scan(text, match_handler, scratch = scratch)
        else:
            logger.error("Hyerscan db is None")
        
        for _, match in longest_match.items():
            match_results.setdefault(match.type, []).append(match)
        return match_results
            


