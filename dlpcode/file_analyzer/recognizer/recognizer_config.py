import os
import json
import logging
#from collections import namedtuple
from typing import List, Dict, Optional
import regex as re

logger = logging.getLogger("fortidata_recognizer")

class RecognizerJsonEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, BasicMetadata):
            return obj.__dict__
        return super().default(obj)

class BasicMetadata():
    DEFAULT_ATTRIBUTES = {}
    
    @classmethod
    def from_dict(cls, data:Dict):
        attributes = cls.DEFAULT_ATTRIBUTES.copy()
        for key, value in data.items():
            if key in attributes.keys():
                attributes[key] = value
        return cls(**attributes)
    
    def __repr__(self):
        #attributes = ', '.join(f"{key}={value!r}" for key, value in self.__dict__.items())
        #return f"{self.__class__.__name__}({attributes})"
        return json.dumps(self.__dict__, cls=RecognizerJsonEncoder)
    
class KeywordConfig(BasicMetadata):
    DEFAULT_ATTRIBUTES = {
        "keyword": "",
        "left": None,
        "right": None,
        "score": None,
        "cflag": True,
        "iflag": False
    }
    def __init__(self, keyword:str = "", left:int = None, right:int = None, score: float = None, cflag:bool = True, iflag:bool = False) -> None:
        self.keyword = keyword
        self.left = left
        self.right = right
        self.score = score
        self.cflag = cflag
        self.iflag = iflag

class PatternConfig(BasicMetadata):
    DEFAULT_ATTRIBUTES = {
        "name": "",
        "regex": "",
        "score": 0.5,
        "extract_regex": "",
        "exclude_regex": "",
        "leftmost_flag": True
    }
    def __init__(self, name: str, regex: str, extract_regex: str, exclude_regex: str, leftmost_flag: bool, score: float = 0.5) -> None:
        self.name = name
        self.regex = regex
        self.compiled_extract_regex = None
        self.compiled_exclude_regex = None
        self.score = score
        if extract_regex and extract_regex != "":
            self.compiled_extract_regex = re.compile(extract_regex)
        if exclude_regex and exclude_regex != "":
            self.compiled_exclude_regex = re.compile(exclude_regex)
        self.leftmost_flag = leftmost_flag

class DataTypeMetadata(BasicMetadata):
    DEFAULT_ATTRIBUTES = {
        "id": "unset",
        "entity": "",
        "data_type": "", # data type name
        "entity_category": "",
        "entity_category_id": "",
        "text_category": [],
        "context": None,
        "description": "",
        "regions": ["ALL"],
        "languages": ["ALL"],
        "allow_list": [],
        "compliance_type": "",
        "compliance_type_ids": [],
        "patterns": None,
        "keyword_must": False,
        "keywords": None,
        "validate_id": None,
        "library_id": None,
        "group_id": "",
    }
    def __init__(
            self, 
            id:str, 
            patterns:List[PatternConfig] = None, 
            entity:str = "", 
            data_type: str = "",
            entity_category: str = "",
            entity_category_id: str = "",
            context: list = None,
            description: str = "",
            regions: list = ["ALL"],
            languages: list = ["ALL"],
            allow_list: list = [],
            compliance_type: List[str] = [],
            compliance_type_ids: List[str] = [],
            keywords: List[KeywordConfig] = None,
            keyword_must: bool = False,
            validate_id: Optional[str] = None,
            library_id: Optional[str] = None,
            text_category: List[str]=[],
            group_id: str = "",
        ) -> None:
        self.id = id
        self.patterns = patterns
        self.entity = entity
        self.data_type = data_type
        self.entity_category = entity_category
        self.entity_category_id = entity_category_id
        self.context = context
        self.description = description
        self.regions = regions
        self.languages = languages
        self.allow_list = allow_list
        self.compliance_type = compliance_type
        self.compliance_type_ids = compliance_type_ids
        self.keywords = keywords
        self.keyword_must = keyword_must
        self.validate_id = validate_id
        self.library_id = library_id
        self.text_category = text_category
        self.group_id = group_id
        
    @classmethod
    def from_dict(cls, data:Dict):
        # print(data)
        attributes = cls.DEFAULT_ATTRIBUTES.copy()
        for key, value in data.items():
            if key in attributes.keys():
                if key == "keywords" and value:
                    keywords = []
                    for kw in value:
                        keywords.append(KeywordConfig.from_dict(kw))
                    attributes[key] = keywords
                elif key == "patterns" and value:
                    patterns = []
                    for kw in value:
                        patterns.append(PatternConfig.from_dict(kw))
                    attributes[key] = patterns
                else:
                    attributes[key] = value
        if not attributes['entity']:
            attributes['entity'] = f"Entity-{attributes['id']}"
        # print("*"*100)
        # print(attributes)
        return cls(**attributes)
    
class RecognizerMetadata():
    def __init__(self, data_type_configs: List[DataTypeMetadata]) -> None:
        self.config = dict()
        '''
        [
            "NATIONAL_ID_CANADA_PRN": {
                "id": 184, 
                "patterns": [{"name": "ca_prn", "regex": "\\b[A-Z]{2}\\d{7}(\\d{3})?\\b", "score": 0.2}], 
                "entity": "NATIONAL_ID_CANADA_PRN", 
                "data_type": "Canada Permanent Resident Number", 
                "entity_category": 20001, 
                "context": [], 
                "description": "", 
                "regions": ["CA"], 
                "languages": ["en"], 
                "allow_list": [], 
                "compliance_type": ["PII"], 
                "keyword_must": true,
                "keywords":[],
                "validate_id": null, 
                "library_id": null,
                "text_category": [""]
            }
        ]
        '''
        for conf in data_type_configs:
            if conf.entity in self.config.keys():
                logger.warn(f"entity: {conf.entity} is duplicated, skip")
                continue
            self.config[conf.entity] = conf

    def __repr__(self):
        return json.dumps(self.config, cls=RecognizerJsonEncoder)
    
    @classmethod
    def from_dict(cls, datatypes:List):
        '''
        Parameters:
            data: List of data types
            [
                {
                    "id": "39364a60-952e-4f99-b042-60c84de1b4c6",
                    "data_type": "Custom data type- 01",
                    "group_id": "",
                    "regions": ["ALL"],
                    "languages": ["en"],
                    "patterns": [
                        {"regex": "\\b(\\d{4}-\\d{4}-\\d{2}|\\d{5}-\\d{4}\\-[\\da-z]|\\d{5}\\-[A-Z\\d]{3}\\-[A-Z\\d]{2}|\\d{5}\\-\\d{4}\\-\\d{2})\\b"},
                    ],
                    "keyword_must": False,
                    "keywords": [
                        {"keyword": "NDC"}
                    ],
                }
            ]
        '''
        if not datatypes:
            return None
        
        datatype_configs = []
        for dt in datatypes:
            datatype_configs.append(DataTypeMetadata.from_dict(dt))
            
        if datatype_configs:
            return cls(data_type_configs=datatype_configs)
        else:
            return None
    
    @classmethod
    def from_db(cls, global_path):
        def dict_factory(cursor, row):
            d = {}
            for idx, col in enumerate(cursor.description):
                d[col[0]] = row[idx]
            return d
        data_types = []
        try:
            with global_path.connect_recognizer() as conn:
                conn.row_factory = dict_factory
                cur = conn.cursor()
                # get all compliance list
                compliance_map = {}
                cur.execute("SELECT * FROM compliance_type")
                compliances = cur.fetchall()
                for c in compliances:
                    compliance_map[str(c["id"])] = c["compliance_type"]
                
                # get all entity category list
                entity_category_map = {}
                cur.execute("SELECT * FROM entity_category")
                entity_categories = cur.fetchall()
                for c in entity_categories:
                    #entity_category_map[c["entity_category"]] = c["id"]
                    entity_category_map[str(c["id"])] = c["entity_category"]
                    
                cur.execute("SELECT * FROM datatype")
                rows = cur.fetchall()
                
                for row in rows:
                    row_dict = dict(row)
                    entity = row_dict.get('entity', "")
                    try:
                        keyword_list = str(row['keywords']).split('\n') if 'keywords' in row and row['keywords'] != '' else []
                        keywords = []
                        kw = ""
                        cflag = True
                        iflag = False
                        for item in keyword_list:
                            left, right, score = None, None, None
                            item_list = str(item).split(',')
                            if len(item_list) > 0:
                                kw = item_list[0]
                            if len(item_list) > 1:
                                left = int(item_list[1]) if item_list[1] != "" else None
                            if len(item_list) > 2:
                                right = int(item_list[2]) if item_list[2] != "" else None
                            if len(item_list) > 3:
                                score = float(item_list[3]) if item_list[3] != "" else None
                            if len(item_list) > 4:
                                cflag = False if item_list[4] == "0" else True
                            if len(item_list) > 5:
                                iflag = True if item_list[5] == "1" else False
                            keywords.append(KeywordConfig(kw, left, right, score, cflag, iflag))
                            
                        pattern_ids = str(row['pattern_id']).split(',')
                        # query = f"SELECT {', '.join(fields)} FROM pattern WHERE id IN ({', '.join(['?'] * len(pattern_ids))})"
                        query = f"SELECT * FROM pattern WHERE id IN ({', '.join(['?'] * len(pattern_ids))})"
                        cur.execute(query, pattern_ids)
                        patterns = cur.fetchall()
                        pattern_list = []
                        for pattern in patterns:
                            pattern_list.append(
                                PatternConfig(
                                    name=pattern['pattern_name'], 
                                    regex=pattern['regex'], 
                                    score=pattern['score'], 
                                    extract_regex=pattern.get('extract_regex', None),
                                    exclude_regex=pattern.get('exclude_regex', None),
                                    leftmost_flag=True if pattern.get('som_leftmost_flag', 1) == 1 else False
                                )
                            )
                        #compliance_ids = list(map(int, str(row_dict.get('compliance_type', "")).rstrip(',').split(',')))
                        compliance_ids = str(row_dict.get('compliance_type', "")).rstrip(',').split(',')
                        if 'OTHER' in compliance_ids or '10004' in compliance_ids:
                            compliance_ids = [] 
                        
                        entity_category = str(row_dict.get('entity_category', "")).strip()
                        text_category = row_dict.get('text_category', "").strip()
                        text_category = text_category.split(',') if text_category else []

                        data_types.append(
                            DataTypeMetadata(
                                id = f"{row_dict.get('id', '0')}",
                                entity=row_dict.get('entity', ""),
                                patterns=pattern_list if len(pattern_list) > 0 else None,
                                data_type = row_dict.get('data_type', ""),
                                text_category = text_category,
                                entity_category = entity_category_map.get(entity_category, ""),
                                entity_category_id = entity_category,
                                description = row_dict.get('dscription', ""),
                                regions = str(row_dict.get('region', "ALL")).split(','),
                                languages = str(row_dict.get('language', "ALL")).split(','),
                                compliance_type = [compliance_map[i] for i in compliance_ids],
                                compliance_type_ids = compliance_ids,
                                context = str(row['context']).split(',') if 'context' in row and row['context'] != "" else [],
                                allow_list = str(row['allow_list']).split(',') if 'allow_list' in row and row['allow_list'] != "" else [],
                                keyword_must = True if 'keyword_must' in row and row['keyword_must'] == 1 else False,
                                validate_id = row['validate_id'] if 'validate_id' in row and row['validate_id'] != '' else None,
                                library_id = row['library_id'] if 'library_id' in row and row['library_id'] != '' else None,
                                keywords=keywords if len(keywords) > 0 else None
                            )
                        )
                    except Exception as e:
                        logger.exception(f"read entity {entity} conf failed: {e}")
                        continue
            return RecognizerMetadata(data_types)
        except Exception as e:
            return None
        
    def get_data_type_config(self, entity:str) -> Optional[DataTypeMetadata]:
        if entity not in self.config.keys():
            return None
        return self.config[entity]
    
    def get_supported_entities(self) -> List[str]:
        return self.config.keys()

    def map_entity_to_id(self, entity:str) -> str:
        if entity not in self.config.keys():
            return -1
        return self.config[entity].id
    
    def map_entity_to_category(self, entity:str) -> str:
        if entity not in self.config.keys():
            return ""
        entity = self.config[entity]
        cid = entity.compliance_type_ids if entity.compliance_type_ids  else entity.entity_category_id
        if cid:
            return cid[0] if isinstance(cid, list) else cid
        return ""
    
    def map_entity_to_groupid(self, entity:str) -> str:
        if entity not in self.config.keys():
            return "not found"
        return self.config[entity].group_id
    
    def map_ids_to_entities(self, ids)-> List[str]:
        entities = []
        
        for key, attr in self.config.items():
            if attr.id in ids:
                entities.append(attr.entity)
                
        return entities
    
    def get_entities_by_scan_id(self, sids: List[str]) -> List[str]:
        entities = []
        
        for key, attr in self.config.items():
            # check compliance type
            for cid in attr.compliance_type_ids:
                if cid in sids:
                    entities.append(attr.entity)
                    break
            
            # check entity category. Use the entity category if the entity's compliance type is empty.
            if not attr.compliance_type_ids and attr.entity_category_id in sids:
            # if attr.entity_category_id in sids:
                #print(f"{key} attr.compliance_type_ids {attr.compliance_type_ids}")
                entities.append(attr.entity)
                
        return list(set(entities))
    
class RegionInfo():
    @classmethod
    def from_db(cls, global_path):
        def dict_factory(cursor, row) -> dict:
            d = {}
            for idx, col in enumerate(cursor.description):
                d[col[0]] = row[idx]
            return d
        country_info = dict()
        province_info = dict()
        try:
            with global_path.connect_recognizer() as conn:
                conn.row_factory = dict_factory
                cur = conn.cursor()
                cur.execute("SELECT * FROM region")
                regions = cur.fetchall()
                for r in regions:
                    country_info[r["country_code"]] = r["country_name"]
                cur.execute("SELECT * FROM country")
                countries = cur.fetchall()
                for c in countries:
                    if c['country_code'] not in province_info:
                        province_info[c['country_code']] = [c['province_name']]
                    else:
                        province_info[c['country_code']].append(c['province_name'])
            return {"country":country_info, "province": province_info}
        except Exception as e:
            logger.exception(f"get region info failed: {e}")
            return None
