import os, sys
import traceback
import re
import json
import datetime
import threading
import hyperscan
import logging
from .recognizer_config import RecognizerMetadata
from typing import List
from presidio_analyzer.recognizer_result import RecognizerResult
logger = logging.getLogger("fortidata_recognizer")


class MatchedKeyword():
    def __init__(self, id, keyword, start, end, score, left, right, iflag):
        self.id = id
        self.keyword = keyword
        self.start = start
        self.end = end
        self.score = score
        self.left = left
        self.right = right
        self.iflag = iflag
        
    def __str__(self) -> str:
        f = '{{"id": "{0}", "keyword": "{1}", "start": "{2}", "end": "{3}", "score": "{4:.2f}", "left": "{5}", "right": "{6}", "included_flag": "{7}"}}'
        return f.format(self.id, self.keyword, self.start, self.end, self.score, self.left, self.right, self.included_flag)

    def __dict__(self):
        return {
            'id': self.id,
            'keyword': self.keyword,
            'start': self.start,
            'end': self.end,
            'score': self.score,
            'left': self.left,
            'right': self.right,
            'iflag': self.iflag
        }

class MatchedKeywordEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, MatchedKeyword):
            return obj.__dict__()
        return super().default(obj)
    
class RecognizerKeywordEngine():
    def __init__(self, recognizer_metadata:RecognizerMetadata, region_info:dict, entities:list=None, default_left=0, default_right=16, default_score=0.4, default_convert_flag=True):
        logger.debug("Keyword engine initialization")
        self.thread_local = threading.local()
        
        self.kws = []
        self.db = None
        # self.kws_path = path
        self.kws_default_score = default_score
        self.kws_default_left = default_left
        self.kws_default_right = default_right
        self.kws_default_convert_flag = default_convert_flag
        try:
            self.load_from_compliance_conf(recognizer_metadata, entities=entities)
            self.load_from_region_info(region_info=region_info)
            self.compile()
        except Exception as e:
            self.db = None
            self.kws = []
            logger.exception(f"Init keyword engine failed: {e}")
            raise e
        
    def load_from_region_info(self, region_info:dict = None):
        if not region_info:
            logger.info("No region information")
            return
        region_kws = []
        for country in region_info["country"]:
            if country == "":
                continue
            region_kws.extend([
                {
                    "keyword": country,
                    "left": 64,
                    "right": 64,
                    "score": 0.1,
                    "types": ["COUNTRY_KEYWORD_" + country],
                    "cflag": True,
                    "iflag": False
                },
                {
                    "keyword": region_info["country"][country],
                    "left": 128,
                    "right": 128,
                    "score": 0.1,
                    "types": ["COUNTRY_KEYWORD_" + country],
                    "cflag": True,
                    "iflag": False
                }
            ])

        for country in region_info["province"]:
            if country == "":
                continue
            for province in region_info["province"][country]:
                region_kws.append({
                    "keyword": province,
                    "left": 0,
                    "right": 50,
                    "score": 0.1,
                    "types": ["PROVINCE_KEYWORD_" + country],
                    "cflag": True,
                    "iflag": False
                })
                
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"loaded region keywords: {json.dumps(region_kws)}")
        logger.info(f"loaded region keywords, keywords num: {len(region_kws)}")
        self.kws.extend(region_kws)
        
    def load_from_compliance_conf(self, recognizer_metadata:RecognizerMetadata = None, entities:list=None):
        '''
        range: 
            left: 0-N
            right: 0-N
            global: -1
            default: -2 or empty
        '''
        # logger.info(f"loading keywords from compliance file: {path}")
        logger.info(f"kws_default_left: {self.kws_default_left}, kws_default_right: {self.kws_default_right}")
        if not recognizer_metadata:
            logger.error("empty recognizer metadata")
        kws = []
        for entity in recognizer_metadata.get_supported_entities():
            if entities and (
                entity not in entities and not (
                    entity.startswith("PHONE_NUMBER_") and "PHONE_NUMBER" in entities
                )
            ):
                continue
            item = recognizer_metadata.get_data_type_config(entity)
            sub_kws = []
            if item.keywords is not None:
                for kw_obj in item.keywords:
                    kw = kw_obj.keyword
                    if len(kw) == 0:
                        logger.error(f"Wrong keyword '{kw}' of type: '{entity}'")
                        continue
                    left = kw_obj.left if (kw_obj.left is not None and kw_obj.left > -2) else self.kws_default_left
                    right = kw_obj.right if (kw_obj.right is not None and kw_obj.right > -2) else self.kws_default_right
                    score = kw_obj.score if (kw_obj.score is not None and kw_obj.score > 0) else self.kws_default_score
                    cflag = kw_obj.cflag
                    iflag = kw_obj.iflag
                    
                    found = False
                    for kwo in kws:
                        if kwo['keyword'] == kw and kwo['left'] == left and kwo['right'] == right and \
                            kwo['score'] == score and kwo['cflag'] == cflag and kwo['iflag'] == iflag:
                            kwo['types'].append(entity)
                            found = True
                    if not found:
                        sub_kws.append({
                            "keyword": kw,
                            "left": left,
                            "right": right,
                            "score": score,
                            "types": [entity],
                            "cflag": cflag,
                            "iflag": iflag
                        })
            if sub_kws:
                kws.extend(sub_kws)
        
        self.kws = kws

        # if logger.isEnabledFor(logging.DEBUG):
        #     logger.debug(f"loaded keywords: {json.dumps(self.kws)}")
        logger.info(f"loaded keywords, keywords num: {len(self.kws)}")
        
        return self.kws
    
    def __convert_kws(self):
        expressions = []
        flags_list = []
        ids = []
        
        for id, kw in enumerate(self.kws):
            regex = kw['keyword']
            regex = re.escape(regex)
            if kw['cflag']:
                if regex[0].isascii() and (regex[0].isdigit() or regex[0].isalpha()):
                    regex = "(\\b|_)"+regex
                if regex[-1].isascii() and (regex[-1].isdigit() or regex[-1].isalpha()):
                    regex += "(\\b|_)"           
        
            #regex = kw['keyword'] if not kw['cflag'] else f"\\b{kw['keyword']}\\b" 
            #regex = kw['keyword']
            expressions.append(regex if type(regex) == bytes else regex.encode('utf-8'))
            ids.append(id)
            flags_list.append(hyperscan.HS_FLAG_CASELESS|hyperscan.HS_FLAG_SOM_LEFTMOST)
        
        return expressions, ids, flags_list
    
    def compile(self):
        expressions, ids, flags_list = self.__convert_kws()
        if not expressions:
            logger.warning(f"no expressions found")
            return 
        
        self.db = hyperscan.Database()
        #print(expressions)
        logger.info(f"expressions count: {len(expressions)}")
        # print(ids)
        compile_start_time = datetime.datetime.now()
        self.db.compile(expressions=expressions, ids=ids, flags=flags_list)
        compile_time = datetime.datetime.now() - compile_start_time
        logger.info(f"compile_time: {compile_time.seconds}.{compile_time.microseconds} seconds")

    def scan(self, text):
        match_results = {}
        def match_handler(id, start, end, flags, context):
            kw_info = self.kws[id]
            matched_keyword = MatchedKeyword(
                id=id,
                start=start,
                end=end,
                keyword=kw_info['keyword'],
                score=kw_info['score'],
                left=kw_info['left'],
                right=kw_info['right'],
                iflag=kw_info['iflag']
            )
            
            for t in kw_info['types']:
                match_results.setdefault(t, []).append(matched_keyword)
         
        #logger.debug(f"==> keyword search: {text}")
        if self.db:
            # print(f"text: {text}")
            scratch = getattr(self.thread_local, "scratch", None)
            if not scratch:
                scratch = hyperscan.Scratch()
                scratch.set_database(self.db)
                self.thread_local.scratch = scratch
            
            #self.db.scan(text.encode('utf-8'), match_handler)
            self.db.scan(text.encode('utf-8'), match_handler, scratch = scratch)
        else:
            logger.error("Hyerscan db is None")
            
        #match_results = {t:MatchedKeyword.remove_contained_kws(kws) for t,kws in match_results.items()}
        
        # if logger.isEnabledFor(logging.DEBUG):
        #     logger.debug(f"hit keywords: {json.dumps(match_results, cls=MatchedKeywordEncoder)}")
        return match_results

class HitKeywordsScoreEnhancer():
    '''
        Check whether the keyword is valid
        Judgement Condition:  
            [start-result-end]......[keyword.left]...[start-keyword-end]...[keyword.right]......[start-result-end]
    '''
    def __init__(self, hit_keywords:List[MatchedKeyword] = None, region_keywords:List[MatchedKeyword] = None, province_keywords:List[MatchedKeyword] = None) -> None:
        #self.keywords = hit_keywords if hit_keywords else []
        self.region_keywords = [(kw.start-kw.left, kw.end+kw.right, kw.score) for kw in region_keywords] if region_keywords else []
        self.province_keywords = [(kw.start-kw.left, kw.end+kw.right, kw.score) for kw in province_keywords] if province_keywords else []
        
        from collections import defaultdict
            
        # Group kws by (score, right, left)
        score_groups = defaultdict(list)
        for kw in hit_keywords:
            score_groups[(kw.score, kw.right, kw.left)].append(kw)
        
        self.keywords = []
        for _, group in score_groups.items():
            # Sort each group by start in ascending order, and by end in descending order if start is the same
            group.sort(key=lambda x: (x.start, -x.end))
            
            last_end = -float('inf')

            for kw in group:
                if kw.end > last_end:  # Only keep keywords that are not contained within the previous keyword
                    if kw.iflag:
                        self.keywords.append((kw.start-kw.left, kw.end+kw.right, kw.score))
                    else:
                        self.keywords.append((kw.start-kw.left, kw.end+kw.right, kw.score, kw.start, kw.end))
                    last_end = kw.end
        self.keywords.sort(key=lambda x: x[0])
        self.is_keyword_empty = True if len(self.keywords) == 0 else False
        self.region_keywords.sort(key=lambda x: x[0])
        self.province_keywords.sort(key=lambda x: x[0])

    def enhance_by_keywords(self, recognizer_result:RecognizerResult)->RecognizerResult:
        extra_score = 0
        for keyword in self.keywords:
            if keyword[0] > recognizer_result.end:
                break
            if recognizer_result.start <= keyword[1]:
                if (len(keyword) < 5 
                    or (recognizer_result.start < keyword[3]
                        or recognizer_result.end > keyword[4])):     
                    extra_score += keyword[2]
                    if recognizer_result.score + extra_score >= 1:
                        break
        if extra_score > 0:
            recognizer_result.score += extra_score
            recognizer_result.score = min(recognizer_result.score, 1)
            recognizer_result.analysis_explanation.set_improved_score(recognizer_result.score)

        return recognizer_result, extra_score > 0

    def enhance_by_region_keyword(self, recognizer_result:RecognizerResult)->RecognizerResult:
        for keyword in self.region_keywords:
            if keyword[0] > recognizer_result.end:
                break
            if recognizer_result.start <= keyword[1]:
                recognizer_result.score += keyword[2]
                recognizer_result.analysis_explanation.set_improved_score(recognizer_result.score)
                break
        return recognizer_result
    
    def enhance_by_province_keyword(self, recognizer_result:RecognizerResult)->RecognizerResult:
        for keyword in self.province_keywords:
            if keyword[0] > recognizer_result.end:
                break
            if recognizer_result.start <= keyword[1]:
                recognizer_result.score += keyword[2]
                recognizer_result.analysis_explanation.set_improved_score(recognizer_result.score)
                break
        return recognizer_result
