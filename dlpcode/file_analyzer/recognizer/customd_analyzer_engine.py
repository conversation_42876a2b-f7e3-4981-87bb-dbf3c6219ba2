import json
import logging
from typing import List, Optional

from presidio_analyzer import (
    RecognizerRegistry,
    RecognizerResult,
    EntityRecognizer,
)
from presidio_analyzer.app_tracer import AppTracer
from presidio_analyzer.context_aware_enhancers import (
    ContextAwareEnhancer,
    LemmaContextAwareEnhancer,
)
from .nlp_engine_registry import EmptyNlpEngine
from presidio_analyzer.nlp_engine import NlpEngine, NlpEngineProvider, NlpArtifacts
from presidio_analyzer import AnalyzerEngine, RecognizerRegistry

from .recognizer_keyword import Recognizer<PERSON>eywordEngine
from .recognizer_config import RecognizerMetadata, DataTypeMetadata, PatternConfig, KeywordConfig
from .customd_base_recognizer import CustomPatternRecognizer, CustomSpacyRecognizer, CustomedPhoneRecognizer
from concurrent.futures import ThreadPoolExecutor, as_completed
from file_analyzer.recognizer.recognizer_match import RecognizerMatchEngine

logger = logging.getLogger("fortidata_recognizer")

AN_MIN_SCORE = 0.4
AN_MAX_SOCRE = 1

class CustomAnalyzerEngine(AnalyzerEngine):
    def __init__(
        self,
        registry: RecognizerRegistry = None,
        entities: list = None,
        nlp_engine: NlpEngine = None,
        app_tracer: AppTracer = None,
        log_decision_process: bool = False,
        default_score_threshold: float = 0,
        supported_languages: Optional[list] = None,
        context_aware_enhancer: Optional[ContextAwareEnhancer] = None,
        recognizer_metadata: RecognizerMetadata = None,
        region_info:dict = None,
        regions: Optional[list] = None,
        default_keyword_distance = (0, 16),  # left, right
        spacy_enable: bool = False,
        nlp_process_size:int = 1000,
        nlp_recognizer_pool: ThreadPoolExecutor = None,
        phone_matcher_max_retry: int = 1000
    ):
        self.spacy_enable = spacy_enable
        self.nlp_process_size = nlp_process_size
        self.nlp_recognizer_pool = nlp_recognizer_pool
        self.regions = regions
        self.phone_matcher_max_retry = phone_matcher_max_retry
        if not self.spacy_enable:
            nlp_engine = EmptyNlpEngine()
        super().__init__(
            registry=registry,
            nlp_engine=nlp_engine,
            app_tracer=app_tracer,
            log_decision_process=log_decision_process,
            default_score_threshold=default_score_threshold,
            supported_languages=supported_languages,
            context_aware_enhancer=context_aware_enhancer,
        )
        
        if recognizer_metadata:
            self.default_keyword_distance = default_keyword_distance
            self.recognizer_kw_engine = RecognizerKeywordEngine(
                recognizer_metadata, 
                region_info=region_info,
                entities=entities,
                default_left=default_keyword_distance[0],
                default_right=default_keyword_distance[1],
                )
            self.recognizer_match_engine = RecognizerMatchEngine(recognizer_metadata, entities)
    
    def analyze(
        self,
        text: str,
        languages: list = ["en"],
        correlation_id: Optional[str] = None,
        score_threshold: Optional[float] = None,
        allow_list: Optional[List[str]] = None,
        ignore_text_category: bool = False,
        text_main_category = None,
        text_sub_category = None,
        max_num_return_entity: int = 16,
        max_times_per_entity: int = 32,
        expected_entities = [],
    ) -> List[RecognizerResult]:
        
        self.shared_nlp_artifaces = None
        if score_threshold is None:
            score_threshold = self.default_score_threshold

        recognizers = self.registry.recognizers
        nlp_recognizers = []
        for recognizer in recognizers:
            if isinstance(recognizer, CustomSpacyRecognizer):
                nlp_recognizers.append(recognizer)
                
        hit_keywords = self.recognizer_kw_engine.scan(text)

        futures = []
        if self.spacy_enable:
            for language in languages:
                for nlp_recognizer in nlp_recognizers:
                    futures.append(
                        self.nlp_recognizer_pool.submit(
                            nlp_recognizer.analyze, 
                            text,
                            self.nlp_engine,
                            language,
                            hit_keywords,
                            text_main_category,
                            text_sub_category,
                            max_times_per_entity,
                            self.nlp_process_size,
                            score_threshold
                        )
                    )
        matched_patterns = self.recognizer_match_engine.scan(text, hit_keywords)
        entity_count = 0
        results = []
        for recognizer in recognizers:
            if expected_entities and not isinstance(recognizer, CustomSpacyRecognizer) and recognizer.supported_entity not in expected_entities:
                continue
            
            current_results = None
            if isinstance(recognizer, CustomedPhoneRecognizer):
                current_results = recognizer.analyze(
                    text=text, 
                    regions=self.regions,
                    hit_keywords=hit_keywords, 
                    matched_pattern=matched_patterns,
                    phone_matcher_max_retry = self.phone_matcher_max_retry,
                    match_num_limit=max_times_per_entity,
                    score_threshold=score_threshold
                )
            elif isinstance(recognizer, CustomPatternRecognizer):
                current_results = recognizer.analyze(
                        text=text,
                        hit_keywords=hit_keywords,
                        matched_pattern=matched_patterns,
                        ignore_text_category = ignore_text_category,
                        text_main_category=text_main_category,
                        text_sub_category=text_sub_category,
                        match_num_limit=max_times_per_entity,
                        score_threshold=score_threshold
                    )
            if current_results:
                # add recognizer name to recognition metadata inside results
                # if not exists
                self.__add_recognizer_id_if_not_exists(current_results, recognizer)
                results.extend(current_results)
                entity_count += 1
                # if current entity number is more than 2*max_num_return_entity, skip the subsequent recognizer analysis tasks. 
                if entity_count >= 2 * max_num_return_entity:
                    break

        for future in as_completed(futures):
            current_results = future.result()
            results.extend(current_results)

        if self.log_decision_process:
            self.app_tracer.trace(
                correlation_id,
                json.dumps([str(result.to_dict()) for result in results]),
            )

        if allow_list:
            results = self._remove_allow_list(results, allow_list, text)

        # if not return_decision_process:
        #     results = self.__remove_decision_process(results)

        return results
    
    @staticmethod
    def __add_recognizer_id_if_not_exists(
        results: List[RecognizerResult], recognizer: EntityRecognizer
    ) -> None:
        """Ensure recognition metadata with recognizer id existence.

        Ensure recognizer result list contains recognizer id inside recognition
        metadata dictionary, and if not create it. recognizer_id is needed
        for context aware enhancement.

        :param results: List of RecognizerResult
        :param recognizer: Entity recognizer
        """
        for result in results:
            if not result.recognition_metadata:
                result.recognition_metadata = dict()
            if (
                RecognizerResult.RECOGNIZER_IDENTIFIER_KEY
                not in result.recognition_metadata
            ):
                result.recognition_metadata[
                    RecognizerResult.RECOGNIZER_IDENTIFIER_KEY
                ] = recognizer.id
            if RecognizerResult.RECOGNIZER_NAME_KEY not in result.recognition_metadata:
                result.recognition_metadata[
                    RecognizerResult.RECOGNIZER_NAME_KEY
                ] = recognizer.name