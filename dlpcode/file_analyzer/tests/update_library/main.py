import sys, os
import shutil
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))

from update_library.downloaders.NPI import NPIDownloader
from update_library.downloaders.NDC import NDCDownloader
from update_library.downloaders.UDI import UDIDownloader
from update_library.downloaders.HCPCS import HCPCSDownloader
from update_library.downloaders.ICD import ICD_Downloader

def clear_folder(folder_path):
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        try:
            if os.path.isfile(file_path) or os.path.islink(file_path):
                os.unlink(file_path)
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)
        except Exception as e:
            print(f'Failed to delete {file_path}. Reason: {e}')

if __name__ == "__main__":
    ICD_Downloader().load()
    NPIDownloader().load()
    NDCDownloader().load()
    UDIDownloader().load()
    HCPCSDownloader().load()

    clear_folder("tmp")
