import requests
import os
import re
from bs4 import BeautifulSoup
from tqdm import tqdm
import zipfile
import pandas as pd
from update_library.downloaders.base_downloader import BaseDownloader

class HCPCSDownloader(BaseDownloader):
    def __init__(self):
        super().__init__(label = "HCPCS", library_name = "us_healthcare_common_procedure_coding_system(HCPCS).txt")

    def get_download_url(self) -> list:
        response = requests.get("https://www.cms.gov/medicare/regulations-guidance/physician-self-referral/list-cpt-hcpcs-codes")
        if response.status_code != 200:
            raise Exception(f"Failed to retrieve webpage: {response.status_code}")
        
        webpage_content = response.text
        soup = BeautifulSoup(webpage_content, 'html.parser')
        content_div = soup.find('div', class_='rxbodyfield')
        if content_div is None:
            raise Exception("Div with class 'content' not found")

        content_html = str(content_div)

        pattern = re.compile(r"/apps/ama/license.asp\?file=/files/zip/list-codes-effective-.*?\.zip")
        matches = pattern.findall(content_html)

        if not matches:
            raise Exception("No matching links found")
        
        url_path = matches[0].split('=')[-1]
        return ["https://www.cms.gov" + url_path + "?agree=yes&next=Accept"]
        
    
    def get_data_href(self, url)->list:
        res = []
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'}
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            html_content = response.text
            soup = BeautifulSoup(html_content, 'html.parser')
            clickable_rows = soup.find_all('tr', class_='clickable-row')
            for row in clickable_rows:
                data_href = row.get('data-href')
                res.append(data_href.split('/')[-1])

        return res

    def extract(self):
        file = open(self.library_path, 'a')
        try:
            xlsx_files = [f for f in os.listdir(self.unzip_dir) if f.endswith('.xlsx')]
            if not xlsx_files:
                raise Exception(f"xlsx files not found in {self.unzip_dir}")
            target_file = os.path.join(self.unzip_dir, xlsx_files[0])
            xlsx = pd.ExcelFile(target_file)

            for sheet_name in xlsx.sheet_names:
                df = pd.read_excel(target_file, sheet_name=sheet_name)
                
                for row in df.itertuples(index=False, name=None):
                    for hcpcs in row:
                        if isinstance(hcpcs, str) and len(hcpcs) == 5:
                            file.write("{}\n".format(hcpcs))
            
            for code in tqdm(self.get_data_href("https://www.hcpcsdata.com/Codes")):
                sub_url = "https://www.hcpcsdata.com/Codes" + '/'+ code
                for hcpcs in self.get_data_href(sub_url):
                    file.write("{}\n".format(hcpcs))

        except Exception as e:
            raise(e)
        finally:
            file.close()


