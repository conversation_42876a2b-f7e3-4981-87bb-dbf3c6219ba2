import requests
import os
import re
import pandas as pd
from update_library.downloaders.base_downloader import BaseDownloader

class ICD_Downloader(BaseDownloader):

    def __init__(self):
        super().__init__(label = "ICD", library_name = "international_classification_of_diseases(ICD).txt")

    def get_download_url(self) -> list:
        response = requests.get("https://www.cdc.gov/nchs/icd/icd-10-cm/files.html")
        if response.status_code != 200:
            raise Exception(f"Failed to retrieve webpage: {response.status_code}")
        
        webpage_content = response.text

        pattern = re.compile(r'https://ftp.cdc.gov/pub/Health_Statistics/NCHS/Publications/ICD10CM/\d{4}/')
        matches = pattern.findall(webpage_content)

        if not matches:
            raise Exception("No matching links found")
        
        link = matches[0]
        response = requests.get(link)
        if response.status_code != 200:
            raise Exception(f"Failed to retrieve webpage: {response.status_code}")
        
        webpage_content = response.text

        pattern = re.compile(r'ICD10-CM%20Code%20Descriptions%20\d{4}.zip')
        matches = pattern.findall(webpage_content)

        if not matches:
            raise Exception("No matching links found")

        return [
            "https://icdcdn.who.int/static/releasefiles/2024-01/MortalityTabulationList_en.zip",
            "https://icdcdn.who.int/static/releasefiles/2024-01/MorbidityTabulationList_en.zip",
            link+matches[0]
        ]

    def extract(self):
        file = open(self.library_path, 'a')
        try:
            pattern = re.compile(r'icd10cm-codes-\d{4}.txt')
            target_files = [f for f in os.listdir(self.unzip_dir) if pattern.match(f)]
            if target_files:
                target_file = os.path.join(self.unzip_dir, target_files[0])
                with open(target_file, 'r') as text_file:
                    for line in text_file:
                        icd = line.split()[0]
                        file.write("{}\n".format(icd))
            
            target_files = [f for f in os.listdir(self.unzip_dir) if f in ["MortalityTabulationList.xlsx","MorbidityTabulationList.xlsx"]]
            if target_files:
                for f in target_files:
                    target_file = os.path.join(self.unzip_dir, f)
                    xlsx = pd.ExcelFile(target_file)

                    for sheet_name in xlsx.sheet_names:
                        df = pd.read_excel(target_file, sheet_name=sheet_name)
                        
                        if 'Expanded codes' not in df.columns:
                            raise ValueError(f"Column Expanded codes not found in sheet '{sheet_name}'.")
                        for value in df['Expanded codes']:
                            for icd in value.split(','):
                                file.write("{}\n".format(icd))
        except Exception as e:
            raise(e)
        finally:
            file.close()

