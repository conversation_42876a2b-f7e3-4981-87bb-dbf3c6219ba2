import os
import requests
import zipfile
class BaseDownloader():
    def __init__(self, label, library_name):
        self.tmp_dir = "tmp"
        self.label = label
        self.unzip_dir = os.path.join(self.tmp_dir, self.label)
        self.library_path = os.path.join("libraries", library_name)

    def get_download_url(self)->list:
        return []

    def download_unzip(self, url):
        # Download the file
        print(f"Downloading from {url} ...")
        file_name = self.label + ".zip"
        zip_path = os.path.join(self.tmp_dir, file_name)
        file_response = requests.get(url, stream=True)
        if file_response.status_code == 200:
            with open(zip_path, 'wb') as f:
                for chunk in file_response.iter_content(chunk_size=1024):
                    if chunk:
                        f.write(chunk)
            print(f"Downloaded: {zip_path}")
        else:
            raise Exception(f"Failed to download {url}: {file_response.status_code}")

        #unzip file
        if not os.path.exists(self.unzip_dir):
            os.makedirs(self.unzip_dir)
        
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(self.unzip_dir)
            print(f"Extracted all contents of {zip_path} to {self.unzip_dir}")

    def extract(self):
        pass

    def load(self):
        print("*"*50)
        print(f"start loading {self.label} library")
        if os.path.exists(self.library_path):
            os.remove(self.library_path)
        download_list = self.get_download_url()
        for url in download_list:
            self.download_unzip(url)
            self.extract()
        print(f'loading {self.label} library success, {self.library_path}\n\n')