import requests
import re
import os
import zipfile
import xml.etree.ElementTree as ET
from tqdm import tqdm
from update_library.downloaders.base_downloader import BaseDownloader

class UDIDownloader(BaseDownloader):
    def __init__(self):
        super().__init__(label="UDI", library_name="us_unique_device_identifier(UDI).txt")

    def get_download_url(self) -> list:
        response = requests.get("https://accessgudid.nlm.nih.gov/download")
        if response.status_code != 200:
            raise Exception(f"Failed to retrieve webpage: {response.status_code}")
        
        webpage_content = response.text

        pattern = re.compile(r"release_files/download/gudid_full_release_\d{8}\.zip")
        matches = pattern.findall(webpage_content)

        if not matches:
            raise Exception("No matching links found")
        
        return ["https://accessgudid.nlm.nih.gov/"+matches[0]]

    def __extract_device_ids_from_file(self, file_path):
        tree = ET.parse(file_path)
        root = tree.getroot()

        namespaces = {'ns': 'http://www.fda.gov/cdrh/gudid'}
        
        device_ids = []
        for device in root.findall('.//ns:device', namespaces):
            for identifier in device.findall('.//ns:identifiers/ns:identifier', namespaces):
                device_id = identifier.find('ns:deviceId', namespaces)
                if device_id is not None:
                    device_ids.append(device_id.text)
        return device_ids

    def extract(self):
        file = open(self.library_path, 'a')
        try:
            target_files = [f for f in os.listdir(self.unzip_dir) if f.endswith('.xml')]
            for f in tqdm(target_files):
                file_path = os.path.join(self.unzip_dir, f)
                device_ids = self.__extract_device_ids_from_file(file_path)
                for udi in device_ids:
                    file.write("{}\n".format(udi))
        except Exception as e:
            raise(e)
        finally:
            file.close()