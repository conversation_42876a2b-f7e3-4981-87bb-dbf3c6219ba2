import requests
import re
import os
import pandas
from update_library.downloaders.base_downloader import BaseDownloader

class NPIDownloader(BaseDownloader):
    def __init__(self):
        super().__init__(label = "NPI", library_name = "us_national_provider_identifier(NPI).txt")

    def get_download_url(self) -> list:
        response = requests.get("https://download.cms.gov/nppes/NPI_Files.html")
        if response.status_code != 200:
            raise Exception(f"Failed to retrieve webpage: {response.status_code}")
        
        webpage_content = response.text

        pattern = re.compile(r"NPPES_Data_Dissemination_[^\"']*\.zip")
        matches = pattern.findall(webpage_content)

        if not matches:
            raise Exception("No matching links found")
        
        return ["https://download.cms.gov/nppes/"+matches[0]]

    def extract(self):
        file = open(self.library_path, 'a')
        try:
            pattern = re.compile(r'(npidata|endpoint|pl|othername)_pfile_\d{8}-\d{8}\.csv')
            for filename in os.listdir(self.unzip_dir):
                if pattern.match(filename):
                    target_file = os.path.join(self.unzip_dir, filename)
                df = pandas.read_csv(target_file, usecols=['NPI'])
                for npi in df['NPI']:
                    file.write("{}\n".format(npi))
        except Exception as e:
            raise(e)
        finally:
            file.close()
