import requests
import os
import zipfile
from update_library.downloaders.base_downloader import BaseDownloader

class NDCDownloader(BaseDownloader):
    def __init__(self):
        super().__init__(label = "NDC", library_name = "us_national_drug_code(NDC).txt")
    
    def get_download_url(self) -> list:
        return ["https://www.accessdata.fda.gov/cder/ndctext.zip"]

    def extract(self):
        with open(os.path.join(self.unzip_dir, "package.txt"), 'r') as file:
            lines = file.readlines() 
        with open(self.library_path, 'a') as file:
            for line in lines:
                columns = line.split('\t')
                if len(columns) > 2 and columns[2] != "NDCPACKAGECODE":
                    file.write("{}\n".format(columns[2]))