#!/bin/bash


GROUP_URL="http://localhost:5000/custom_dtype_group"
GROUP_POST_DATA='{
    "name": "custom-group-01",
    "description": "just for test"
}'


GROUP_RESPONSE=$(curl -s -X POST "$GROUP_URL" -w "%{http_code}" -H "Content-Type: application/json" -d "$GROUP_POST_DATA")
GROUP_HTTP_CODE="${GROUP_RESPONSE: -3}"
GROUP_RESPONSE_BODY="${GROUP_RESPONSE:0:${#GROUP_RESPONSE}-3}"

if [ "$GROUP_HTTP_CODE" -ne 200 ]; then
  echo "Curl request failed with status code: $GROUP_HTTP_CODE"
  exit 1
fi

echo "Full group response: $GROUP_RESPONSE_BODY"

GROUP_UUID=$(echo "$GROUP_RESPONSE_BODY" | sed -n 's/.*"id": "\(.*\)",/\1/p')


URL="http://localhost:5000/custom_datatype"
POST_DATA='{
    "name": "custom-01",
    "description": "just for test",
    "patterns":[
        {"regex": "\\b(\\d{4}-\\d{4}-\\d{2}|\\d{5}-\\d{4}-[\\da-z]|\\d{5}-[A-Z\\d]{3}-[A-Z\\d]{2}|\\d{5}-\\d{4}-\\d{2})\\b"}
    ],
    "keywords": [
      {"keyword": "NDC"}
    ],
    "group_uuid": "'"$GROUP_UUID"'"
}'

RESPONSE=$(curl -s -X POST "$URL" -w "%{http_code}"  -H "Content-Type: application/json" -d "$POST_DATA")
HTTP_CODE="${RESPONSE: -3}"
RESPONSE_BODY="${RESPONSE:0:${#RESPONSE}-3}"

if [ $? -ne 0 ]; then
  echo "Curl request failed"
  exit 1
fi

echo "Full response: $RESPONSE_BODY"