import re
import pandas as pd
import json
import argparse
import sys

def process_text(text):
    pattern = re.compile(r'\b(keyword: .+?|example: .+?)\s*')
    example_match = re.search(r'example: (.+)', text)
    example_content = example_match.group(1) if example_match else ''

    cleaned_text = pattern.sub('', text).strip()

    return cleaned_text, example_content

def convert(input, output, sheet_name:list):
    
    test_case_dict = {}
    for sheet in sheet_name:
        first_row = pd.read_excel(input, sheet_name=sheet, nrows=1)
        if 'Label' in first_row:
            data = pd.read_excel(input,sheet_name=sheet)
        else:
            data = pd.read_excel(input,sheet_name=sheet, skiprows=1)
            
        for index, row in data.iterrows():

            label = str(row["Entity"]).strip()
            test_case = row["Test Case"]
            if pd.isna(test_case):
                continue
            text, content = process_text(test_case)

            test_case_dict[label] = {
                "text":text,
                "content": content
            }
    
    with open(output, 'w') as json_file:
        json.dump(test_case_dict, json_file)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog=sys.argv[0], description="Conver excel into config file", formatter_class=argparse.RawDescriptionHelpFormatter)
    parser.add_argument('-i', type=str, default="data_types.xlsx", help='Specify the input file')
    parser.add_argument('-o', type=str, default="test.json", help='Specify the output file')

    sheet_name = ["PresidioPattern-PII", "PresidioPattern-PCI", "PresidioPattern-PHI", "Others"]
    args = parser.parse_args()

    convert(args.i, args.o, sheet_name)
