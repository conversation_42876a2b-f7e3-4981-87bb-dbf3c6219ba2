{"PERSONAL_AGE": {"text": "The average age:19, of the participants was within the expected range.\n\nge\n9", "content": "19"}, "PERSONAL_DOB": {"text": "Her birthday, celebrated on 12/25/2020, was a joyful occasion for the whole family.\n\nirthday\n2/25/2020", "content": "12/25/2020"}, "TAX_ID_AUSTRALIA": {"text": "Please provide your TFN (Tax File Number), which should be entered as TFN ********* on the form.\n\nax File Number\n23456782", "content": "*********"}, "EMAIL": {"text": "Please provide your email (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@superrito.com) for further communication.\n\nmail\nerzy<PERSON><PERSON><PERSON><PERSON>@superrito.com", "content": "Jerzy<PERSON><PERSON><PERSON><PERSON>@superrito.com"}, "NATIONAL_ID_INDIA": {"text": "Please provide your <PERSON><PERSON><PERSON><PERSON> (************) for identification purposes.\n\nadhaar\n97788000234", "content": "************"}, "TAX_ID_INDIA_PAN": {"text": "To proceed with the application, please ensure that your Permanent Account Number (**********) is accurately entered.\n\nermanent Account Number\nAAPL1234A", "content": "**********"}, "URL": {"text": "Make sure to visit our website at the following url: https://OrthoTraffic.ch/ for more information.\n\nrl\nttps://OrthoTraffic.ch/", "content": "https://OrthoTraffic.ch/"}, "IP_ADDRESS": {"text": "The network administrator detected unusual activity from the IP address ************, indicating a possible security breach.\n\np\n06.31.73.20", "content": "************"}, "PHONE_NUMBER": {"text": "After receiving a missed call on his phone (71) 4233-6306, he checked his voicemail for any messages.\n\nhone\n71) 4233-6306", "content": "(71) 4233-6306"}, "TAX_ID_US_ITIN": {"text": "After submitting the necessary documents, she received her ITIN, which was *********, from the IRS.\n\ntin\n27705828", "content": "*********"}, "PASSPORT_US": {"text": "Before boarding the flight, <PERSON> double-checked that her passport, with the number R92586045, was safely tucked into her carry-on bag.\n\nassport\n92586045", "content": "R92586045"}, "SSN_US": {"text": "After completing the employment form, he provided his SSN, which was ***********, as part of the required information.\n\nsn\n50-53-3582", "content": "***********"}, "ZIP_US": {"text": "The postal code for the address is 12201-7050, which ensures accurate mail delivery.\n\nostal\n2201-7050", "content": "12201-7050"}, "ZIP_CANADA": {"text": "The zip code, V5C6C6, corresponds to the address of the recipient.\n\nip\n5C6C6", "content": "V5C6C6"}, "USERNAME": {"text": "To access the application, please enter your user account: test.\n\nser account: test", "content": "user account: test"}, "PERSONAL_TITLE": {"text": "The house belonged to Mrs., the widow of a local businessman.\n\nrs.", "content": "Mrs."}, "EDUCATIONAL_STUDENT_ID": {"text": "The university's database contained records for all currently enrolled students. One of the entries read: Student ID is 123456.\n\n23456", "content": "123456"}, "SOCIAL_MEDIA_LINK": {"text": "The developer shared their GitHub profile URL in the team chat, stating \"My code repositories can be found at https://www.github.com/username.\"\n\nttps://www.github.com/username", "content": "https://www.github.com/username"}, "SSN_CANADA_SIN": {"text": "The social insurance number (sin) provided, 725 381 164, is necessary for completing the application.\n\nin\n25 381 164", "content": "725 381 164"}, "MAC_ADDRESS": {"text": "The MAC address (mac), 7c:c2:55:56:04:c4, is used to uniquely identify the device on the network.\n\nac\nc:c2:55:56:04:c4", "content": "7c:c2:55:56:04:c4"}, "IMEI": {"text": "The International Mobile Equipment Identity (IMEI) number, 490154203237518, is a unique identifier for mobile devices.\n\nmei\n90154203237518", "content": "490154203237518"}, "NATIONAL_ID_ELECTORAL_ROLL_NUMBER": {"text": "The ERN, NR2345, is a unique identifier assigned to each voter.\n\nRN\nR2345", "content": "NR2345"}, "TAX_ID_UK_UTR": {"text": "The taxpayer's identification number, **********, is crucial for tax-related transactions.\n\naxpayer\n234567890", "content": "**********"}, "SSN_UK_NINO": {"text": "The National Insurance Number (NINO), DQ123456C, is used for identification purposes.\n\nino\nQ123456C", "content": "DQ123456C"}, "NATIONAL_ID_FRANCE_CNI": {"text": "The customer provided their CNI number, which is X4RTBPFW4.\n\nni\n4RTBPFW4", "content": "X4RTBPFW4"}, "NATIONAL_ID_JAPAN_MY": {"text": "The form requires your Resident Registration Number (0**********1) for verification.\n\nesident Registration Number\n**********1", "content": "0**********1"}, "NATIONAL_ID_JAPAN_RESIDENCE": {"text": "The immigration officer requested my Residence card number, FG56234567RT, for documentation purposes.\n\nesidence card number\nG56234567RT", "content": "FG56234567RT"}, "NATIONAL_ID_BRAZIL_RG": {"text": "Please provide your identification, including your RG (30.123.456-7), for registration.\n\ng\n0.123.456-7", "content": "30.123.456-7"}, "NATIONAL_ID_CHINA": {"text": "Please present your Resident Identity Card, ******************, for verification at the registration desk.\n\nesident Identity Card\n12901197608212008", "content": "******************"}, "NATIONAL_ID_SINGAPORE_NRIC": {"text": "Please provide your NRIC (*********) for identification purposes.\n\nric\n0000001I", "content": "*********"}, "NATIONAL_ID_ARGENTINA": {"text": "Make sure to bring your DNI (25.249.210) for identification when visiting the embassy.\n\nni\n5.249.210", "content": "25.249.210"}, "NATIONAL_ID_BRAZIL_CPF": {"text": "Ensure you provide your CPF (025.762.835-51) when completing the registration form.\n\nPF\n25.762.835-51", "content": "025.762.835-51"}, "NATIONAL_ID_CHILE": {"text": "Please ensure your identity (12.749.625-K) is verified before accessing the secure area.\n\ndentity\n2.749.625-K", "content": "12.749.625-K"}, "NATIONAL_ID_CROATIA": {"text": "Ensure you carry your identity card, with the unique number 100060409, at all times.\n\ndentity\n00060409", "content": "100060409"}, "NATIONAL_ID_CZECH": {"text": "Make sure to carry your identity card, with the unique number 981115/0570, at all times.\n\ndentity\n81115/0570", "content": "981115/0570"}, "NATIONAL_ID_DENMARK": {"text": "Please provide your identification number (020955-2017) when completing the registration form.\n\ndentification number\n20955-2017", "content": "020955-2017"}, "NATIONAL_ID_FINLAND": {"text": "Please bring your ID (240147-632T) for verification when you visit the office.\n\nd\n40147-632T", "content": "240147-632T"}, "NATIONAL_ID_GREECE": {"text": "Make sure to carry your Greek ID (Y-783584) when traveling abroad.\n\nreek id\n-783584", "content": "Y-783584"}, "NATIONAL_ID_HK_HKID": {"text": "Ensure you have your HKID (Z683365(5)) ready for inspection at the immigration counter.\n\nkid\n683365(5)", "content": "Z683365(5)"}, "NATIONAL_ID_IRELAND_PPS": {"text": "Make sure to include your PPS number (1234567TW) on the form for processing.\n\nps number\n234567TW", "content": "1234567TW"}, "NATIONAL_ID_ISRAEL": {"text": "Ensure your identification, with the number *********, is up-to-date.\n\ndentification\n19999991", "content": "*********"}, "NATIONAL_ID_MALAYSIA": {"text": "Make sure to bring your Identification Card (691206-10-5330) when visiting the government office.\n\ndentification Card\n91206-10-5330", "content": "691206-10-5330"}, "NATIONAL_ID_NETHERLANDS_BSN": {"text": "Please provide your BSN (*********) for verification when submitting the application.\n\nsn\n23456782", "content": "*********"}, "NATIONAL_ID_NORWAY": {"text": "The customer provided their personal identification number *********** when making the purchase. \n\ndentification\n2067612345", "content": "***********"}, "NATIONAL_ID_PHILIPPINES": {"text": "The customer presented their Unified Multi-Purpose ID card with the number 0028-1215160-9 to gain access to the secure facility. \n\nnified Multi-Purpose ID\n028-1215160-9", "content": "0028-1215160-9"}, "NATIONAL_ID_POLAND_IC": {"text": "The customer provided the identification number ABA300000 when setting up their new bank account. \n\ndentification\nBA300000", "content": "ABA300000"}, "NATIONAL_ID_POLAND_PESEL": {"text": "The customer was required to provide their pesel number *********** when applying for the government assistance program. \n\nesel\n6083006995", "content": "***********"}, "TAX_ID_POLAND_NIP": {"text": "The customer submitted their tax identification number ********** when filing their annual income tax return.\n\nax identification number\n234567895", "content": "**********"}, "NATIONAL_ID_SAUDI": {"text": "The customer service representative requested the user's ID. She entered **********.\n\nD\n553451234", "content": "**********"}, "NATIONAL_ID_SOUTH_AFRICA": {"text": "Please provide a copy of your government-issued ID, such as a driver's license or passport, ensuring your identification number (*************) is clearly visible.\n\ndentification\n001015009087", "content": "*************"}, "NATIONAL_ID_SK_RRN": {"text": "The document labeled rrn 820701-2409184 was found in the archives.\n\nrn\n20701-2409184", "content": "820701-2409184"}, "SSN_SPAIN": {"text": "<PERSON>'s SSN, ***********, was required for the job application.\n\nSN\n1**********", "content": "11**********"}, "NATIONAL_ID_SWEDEN": {"text": "She showed her id, 820821-2384, to the security guard before entering the building.\n\nd\n20821-2384", "content": "820821-2384"}, "NATIONAL_ID_BULGARIA_EGN": {"text": "The identification number, egn **********, was required to access the online portal.\n\ngn\n523169263", "content": "**********"}, "NATIONAL_ID_BELGIUM": {"text": "The national number 85.02.01-002.00 was printed on the back of her driver's license.\n\national number\n5.02.01-002.00", "content": "85.02.01-002.00"}, "NATIONAL_ID_CYPRUS": {"text": "The identity card number ********** was required to verify her identity before she could access her bank account.\n\ndentity card number\n234567890", "content": "**********"}, "NATIONAL_ID_ESTONIA": {"text": "Ensure your identification, with the number 38*********, is valid and up-to-date.\n\ndentification\n8*********", "content": "38*********"}, "NATIONAL_ID_GERMANY": {"text": "He showed his identity card, *********, to the security guard before being allowed to enter the restricted area.\n\ndentity card\n01008921", "content": "*********"}, "NATIONAL_ID_HUNGARY": {"text": "The employee submitted their identification number *********** to access the secure company database.\n\ndentification\n8709189997", "content": "***********"}, "NATIONAL_ID_ITALY_CF": {"text": "The customer provided their fiscal code **************** when submitting tax documents. \n\niscal code\nLLSNT82P65Z404U", "content": "****************"}, "NATIONAL_ID_LATVIA": {"text": "For security reasons, please enter your valid personal code (121282-11210) to proceed.\n\nersonal code\n21282-11210", "content": "121282-11210"}, "NATIONAL_ID_LITHUANIA": {"text": "The user entered their personal code *********** to access the secure online portal. \n\nersonal code\n8409152012", "content": "***********"}, "NATIONAL_ID_LUXEMBOURG": {"text": "He provided his identification, *************, to the bank teller before being able to withdraw money from his account.\n\ndentification\n985010512345", "content": "*************"}, "NATIONAL_ID_MALTA": {"text": "When checking in for the flight, the passenger provided their identity card number 0346979M as a form of identification. \n\ndentity card number\n346979M", "content": "0346979M"}, "NATIONAL_ID_PORTUGAL": {"text": "The resident presented their citizen card number 851111114RM9 to prove their identity when applying for government benefits.\n\nitizen card\n51111114RM9", "content": "851111114RM9"}, "NATIONAL_ID_SPAIN_DNI": {"text": "When signing up for the service, the customer provided their dni number 00000051T as a form of identification.\n\nni\n0000051T", "content": "00000051T"}, "NATIONAL_ID_ROMANIA_CNP": {"text": "The employee submitted their cnp number ************* when filling out the HR paperwork. \n\nnp\n800101221144", "content": "*************"}, "NATIONAL_ID_SLOVAKIA": {"text": "She was asked to provide her personal identification number, 600101/8760, to verify her identity before accessing her medical records.\n\nersonal\n00101/8760", "content": "600101/8760"}, "NATIONAL_ID_SLOVENIA": {"text": "When applying for government benefits, the applicant provided their unique master citizen number 280698505145 as a form of identification. \n\nnique master citizen number\n80698505145", "content": "280698505145"}, "NATIONAL_ID_TAIWAN": {"text": "missing keyword", "content": ""}, "DL_CA": {"text": "The customer presented their driver's license number ********** to verify their identity when making a large purchase. \n\nriver's license\n34711-320", "content": "**********"}, "DL_US": {"text": "When renting the car, the customer provided their driver's license number 6940579 to complete the transaction. \n\nriver's license\n940579", "content": "6940579"}, "DL_AUSTRALIA": {"text": "I had to provide my driver's license number, *********, when filling out the rental car paperwork.\n\nriver's license\n11805335", "content": "*********"}, "DL_BELGIUM": {"text": "He showed his driver's license, **********, to the police officer when he was pulled over for speeding.\n\nriver's license\n123456789", "content": "**********"}, "DL_BULGARIA": {"text": "My license number is ********* and I need to renew my driver's license.\n\nriver's license\n00103683", "content": "*********"}, "DL_CROATIA": {"text": "They confirmed my identity with the number on my driver's license, 10000901, after I showed it at the DMV.\n\nriver's license\n0000901", "content": "10000901"}, "DL_CYPRUS": {"text": "I recently obtained my driver's license and the number is 277778000000, which is my lucky number.\n\nriver's license\n77778000000", "content": "277778000000"}, "DL_CZECH": {"text": "I presented my driver's license, which bore the number EH 000000, to the traffic officer during the routine check.\n\nriver's license\nH 000000", "content": "EH 000000"}, "DL_DENMARK": {"text": "When I applied for a new driver's license,  with number 90000917, which was a milestone in my life.\n\nriver's license\n0000917", "content": "90000917"}, "DL_ESTONIA": {"text": "My new driver's license, bearing the number ET999901, just arrived in the mail.\n\nriver's license\nT999901", "content": "ET999901"}, "DL_FINLAND": {"text": "When applying for a replacement driver's license, I provided my code 090185-8886, so they confirmed my identity.\n\nriver's license\n90185-8886", "content": "090185-8886"}, "DL_FRANCE": {"text": "When I renewed my driver's license online, a number **********12 was assigned to me.\n\nriver's license\n23456789012", "content": "**********12"}, "DL_UK": {"text": "My friend's driver's license, with the code MORGA657054SM9IJ35, was recently renewed after passing the driving test.\n\nriver's license\nORGA657054SM9IJ35", "content": "MORGA657054SM9IJ35"}, "DL_GERMANY": {"text": "I was asked to provide my driver's license number, which is B072RRE2I55, when filling out the rental car paperwork.\n\nriver's license\n072RRE2I55", "content": "B072RRE2I55"}, "DL_GREECE": {"text": "I applied for my driver's license with the number 690009412, a milestone in my journey towards independence.\n\nriver's license\n90009412", "content": "690009412"}, "DL_HUNGARY": {"text": "I carried my driver's license, with the code CM001267, as I embarked on my first solo road trip, feeling a sense of freedom and responsibility.\n\nriver's license\nM001267", "content": "CM001267"}, "DL_IRELAND": {"text": "I had to provide my driver's license number, 020012ABCD, as part of the application process for car insurance.\n\nriver's license\n20012ABCD", "content": "020012ABCD"}, "DL_LATVIA": {"text": "I presented my driver's license, bearing the distinctive code NAA123456, as proof of identity when registering for the driving course.\n\nriver's license\nAA123456", "content": "NAA123456"}, "DL_LITHUANIA": {"text": "I updated my driver's license, ensuring that the number 51000007 remained accurate for official documentation.\n\nriver's license\n1000007", "content": "51000007"}, "DL_LUXEMBOURG": {"text": "I provided my driver's license number, 778843, as part of the application process for a new job requiring valid identification.\n\nriver's license\n78843", "content": "778843"}, "DL_MALTA": {"text": "I displayed my driver's license, which bore the number AB123456, as I passed through airport security for my flight.\n\nriver's license\nB123456", "content": "AB123456"}, "DL_NETHERLANDS": {"text": "I was required to provide my driver's license number, **********, when completing the rental car reservation online.\n\nriver's license\n094962111", "content": "**********"}, "DL_POLAND": {"text": "I handed my driver's license with the number 42222/13/1465108 before processing my application for a new car registration.\n\nriver's license\n2222/13/1465108", "content": "42222/13/1465108"}, "DL_PORTUGAL": {"text": "I presented my driver's license, displaying the identifier ********** 0, as I entered the nightclub for the first time.\n\nriver's license\n-12345678 0", "content": "********** 0"}, "nan": {"text": "The regulatory agency requires all medical devices to bear a unique device identifier (UDI) like 1000000000000XYZ123, enabling precise tracking and regulatory compliance throughout the product's lifecycle.\n\nDI\n000000000000XYZ123", "content": "1000000000000XYZ123"}, "DL_SLOVAKIA": {"text": "I placed my driver's license, marked with the code ********, back into my wallet after successfully passing my driving test.\n\nriver's license\n0016789", "content": "********"}, "DL_SLOVENIA": {"text": "I had to provide my driver's license number, 999900403, as part of the application process for my new job, which required valid identification.\n\nriver's license\n99900403", "content": "999900403"}, "DL_SPAIN": {"text": "I was relieved when my driver's license, featuring the number 01234567-X, arrived in the mail just in time for my upcoming road trip.\n\nriver's license\n1234567-X", "content": "01234567-X"}, "DL_SWEDEN": {"text": "I finally received my driver's license with the identifier 630821-2817, marking the end of months of rigorous driving lessons and tests.\n\nriver's license\n30821-2817", "content": "630821-2817"}, "DL_JAPAN": {"text": "My new driver's license arrived with the number 600800101440, allowing me to continue driving legally.\n\nriver's license\n00800101440", "content": "600800101440"}, "DL_ITALY": {"text": "I handed my driver's license, marked with the code **********, to the clerk as proof of age when purchasing alcohol at the liquor store.\n\nriver's license\nV1234567A", "content": "**********"}, "PERSONAL_ETHNICITY": {"text": "The discussion on race must include Latino individuals, as they navigate issues of identity, discrimination, and social justice within the broader context of society.\n\nace\natino", "content": "latino"}, "PERSONAL_GENDER": {"text": "Exploring the complexities of gender is essential for both male and female individuals in society.\n\nender\nale", "content": "male"}, "PASSPORT_UK": {"text": "I grabbed my passport, which bore the number *********, before heading to the airport for my international flight.\n\nassport\n07182890", "content": "*********"}, "PASSPORT_SWEDEN": {"text": "I grabbed my passport, which bore the passport number 25534512, before heading to the airport for my international flight.\n\nassport\n5534512", "content": "25534512"}, "PASSPORT_SLOVENIA": {"text": "Before boarding the plane for my vacation, I double-checked that I had my passport, marked with the code *********, ensuring smooth travels ahead.\n\nassport\nB9000309", "content": "*********"}, "PASSPORT_SLOVAKIA": {"text": "I handed over my passport, marked with the code *********, to the customs officer upon arrival at the international airport.\n\nassport\nB7891817", "content": "*********"}, "PASSPORT_ROMANIA": {"text": "I made sure to pack my passport, with the identifier *********, before embarking on my overseas adventure to ensure smooth passage through customs.\n\nassport\n40014869", "content": "*********"}, "PASSPORT_PORTUGAL": {"text": "I was relieved to find my passport, marked with the code I101473, in my travel bag just before heading to the airport for my international flight.\n\nassport\n101473", "content": "I101473"}, "PASSPORT_MALTA": {"text": "I carefully stored my passport, bearing the num 0981489, in my carry-on bag before heading to the airport for my trip abroad.\n\nassport\n981489", "content": "0981489"}, "PASSPORT_LITHUANIA": {"text": "As I prepared for my journey overseas, I made sure to double-check my passport, which featured the passport code 21810408, to ensure smooth travels at the airport.\n\nassport\n1810408", "content": "21810408"}, "PASSPORT_LATVIA": {"text": "My passport number is *********, before passing through customs at the international airport.\n\nassport\nV9000367", "content": "*********"}, "PASSPORT_ITALY": {"text": "I quickly located my passport, bearing the num *********, as I prepared for my upcoming international trip.\n\nassport\nK6000533", "content": "*********"}, "PASSPORT_IRELAND": {"text": "Before boarding the flight, I made sure to secure my passport, with the number *********, in my travel pouch for easy access during the journey.\n\nassport\nN5004216", "content": "*********"}, "PASSPORT_HUNGARY": {"text": "As I packed for my trip, I placed my passport, with the number *********, in a safe and accessible pocket of my carry-on bag.\n\nassport\nD0002028", "content": "*********"}, "PASSPORT_GREECE": {"text": "As I packed for my trip, I placed my passport, with the number *********, in a safe and accessible pocket of my carry-on bag.\nassport\nT1497524", "content": "*********"}, "PASSPORT_FINLAND": {"text": "Before embarking on my journey abroad, I double-checked that my passport, with the number *********, was safely stowed in my travel wallet.\n\nassport\nP8271602", "content": "*********"}, "PASSPORT_ESTONIA": {"text": "As I prepared for my international trip, I meticulously packed my passport, bearing the num K3295837, ensuring I had all necessary documents for smooth travels.\n\nassport\n3295837", "content": "K3295837"}, "PASSPORT_DENMARK": {"text": "I breathed a sigh of relief when I found my passport with the number ********* nestled securely in my travel bag just before heading to the airport for my flight.\n\nassport\n10670932", "content": "*********"}, "PASSPORT_CZECH": {"text": "I quickly checked my travel documents, ensuring my passport with the number 99003853 was securely tucked into my carry-on before heading to the airport.\n\nassport\n9003853", "content": "99003853"}, "PASSPORT_CYPRUS": {"text": "As I prepared for my overseas adventure, I double-checked that my passport with the number K00000413 was safely stored in my travel pouch.\n\nassport\n00000413", "content": "K00000413"}, "PASSPORT_CROATIA": {"text": "Before embarking on my journey, I meticulously packed my passport, bearing the num *********, ensuring I had all the necessary documents for a smooth travel experience.\n\nassport\n00000115", "content": "*********"}, "PASSPORT_BULGARIA": {"text": "As I approached the immigration desk, I reached for my passport with number *********, ready to present it to the officer for clearance.\n\nassport\n08028837", "content": "*********"}, "PASSPORT_BELGIUM": {"text": "Before boarding the international flight, I made sure to securely place my passport with  code EH100396 in the designated pocket of my travel bag.\n\nassport\nH100396", "content": "EH100396"}, "PASSPORT_AUSTRALIA": {"text": "As I prepared for my trip, I made sure to double-check my passport, with the number *********, to ensure I had all the necessary documentation for smooth travels.\n\nassport\nA0940443", "content": "*********"}, "PASSPORT_POLAND": {"text": "I breathed a sigh of relief when I found my passport with number ********* safely tucked into my travel bag just before heading to the airport.\n\nassport\nJ1033101", "content": "*********"}, "PASSPORT_SPAIN": {"text": "I always keep my passport with number AJ000162 in a secure and easily accessible place whenever I travel internationally.\n\nassport\nJ000162", "content": "AJ000162"}, "PASSPORT_NETHERLANDS": {"text": "As I prepared for my international journey, I double-checked that my passport, with number XJ1F10162, was safely stowed in my carry-on bag.\n\nassport\nJ1F10162", "content": "XJ1F10162"}, "PASSPORT_GERMANY": {"text": "I made sure to securely store my passport, with the code C01XYCCG9, in my travel wallet before embarking on my trip abroad.\n\nassport\n01XYCCG9", "content": "C01XYCCG9"}, "PASSPORT_SOUTH_KOREA": {"text": "Before leaving for the airport, I verified that my passport, featuring the code M70689098, was safely packed in my carry-on bag.\n\nassport\n70689098", "content": "M70689098"}, "PASSPORT_CHINA": {"text": "I reached into my bag to retrieve my passport, with number ********* stamped on it, ensuring smooth travels ahead.\n\nassport\nF1260892", "content": "*********"}, "PASSPORT_JAPAN": {"text": "I made sure to secure my passport with the identifier ********* in a safe compartment of my backpack before heading to the airport for my flight.\n\nassport\nF5643123", "content": "*********"}, "PASSPORT_FRANCE": {"text": "I handed over my passport with number 78TH67845 to the customs officer as I arrived at the international airport, ready to embark on my adventure.\n\nassport\n8TH67845", "content": "78TH67845"}, "PASSPORT_CANADA": {"text": "As I approached the immigration counter, I reached into my bag to retrieve my passport, with code ER890765 was visible for the officer to inspect.\n\nassport\nR890765", "content": "ER890765"}, "PHONE_NUMBER_CZ": {"text": "I provided my phone number, which is +420 123 456 789, to the customer service representative to ensure they could reach me for any updates regarding my order.\n\nhone number\n420 123 456 789", "content": "+420 123 456 789"}, "PHONE_NUMBER_FI": {"text": "I shared my new phone number +358 1 234 5678 with my friends so they could reach me easily after my move to Finland.\n\nhone number\n358 1 234 5678", "content": "+358 1 234 5678"}, "PHONE_NUMBER_PL": {"text": "I wrote down my friend's phone number +48 62 323 88 76 in case I needed to reach them during my visit to Poland.\n\nhone number\n48 62 323 88 76", "content": "+48 62 323 88 76"}, "PHONE_NUMBER_RO": {"text": "Feel free to reach out to our customer support phone number at +40213 564 864 if you need assistance with your order.\n\nhone number\n40213 564 864", "content": "+40213 564 864"}, "PHONE_NUMBER_AT": {"text": "Please ensure to include your valid phone number, such as +43 660 4812282, when completing the registration form.\n\nhone number\n43 660 4812282", "content": "+43 660 4812282"}, "PHONE_NUMBER_IE": {"text": "Please write down our phone number, reach out to us at +353 ********** for assistance if you encounter any issues with your account.\n\nhone number\n353 **********", "content": "+353 **********"}, "PHONE_NUMBER_IN": {"text": "It's essential to provide a reachable phone number like +91 (020) ******** so we can easily contact you regarding your inquiry.\n\nhone number\n91 (020) ********", "content": "+91 (020) ********"}, "BANK_ACCOUNT_NUMBER_US": {"text": "The bank's security team flagged an attempted transaction using the account number **************** as suspicious activity.\n\nccount\n512321987654320", "content": "****************"}, "BANK_ACCOUNT_NUMBER_CA": {"text": "The investigation revealed that the account number 5131362 had been used in multiple attempted breaches across various financial institutions.\n\nccount\n131362", "content": "5131362"}, "BANK_ACCOUNT_NUMBER_AU": {"text": "Banks are required to verify the legitimacy of account numbers and flag those like ********* as part of their compliance procedures.\n\nccount\n59100001", "content": "*********"}, "BANK_ACCOUNT_NUMBER_UK": {"text": "Financial regulators work closely with banks to develop strategies to combat the use of fake account numbers such as ******** in fraudulent schemes.\n\nccount\n1926819", "content": "********"}, "BANK_ACCOUNT_NUMBER_JP": {"text": "I need to provide my account number, which is 8976-789, for the wire transfer.\n\nccount\n976-789", "content": "8976-789"}, "BANK_ACCOUNT_NUMBER_IL": {"text": "I accidentally mistyped my account number as 49-856-********, resulting in a failed transaction.\n\nccount\n9-856-********", "content": "49-856-********"}, "HEALTHCARE_ID_US_MBI": {"text": "Medical beneficiaries often need to provide their Medicare numbers, such as 1EG4TE5MK73, when accessing healthcare services or filling out paperwork.\n\nedicare\nEG4TE5MK73", "content": "1EG4TE5MK73"}, "SSN_FRANCE_INSEE": {"text": "To update your Carte Vitale information, please provide 1 85 05 78 006 084 36 in the renewed documents.\n\nitale\n85 05 78 006 084 36", "content": "1 85 05 78 006 084 36"}, "HEALTH_ID_FINLAND": {"text": "Kela requires your code, such as 280378-799C, to process your healthcare benefits application.\n\nela\n80378-799C", "content": "280378-799C"}, "HEALTH_ID_US_HICN": {"text": "Due to recent changes, HICNs like ***********-B6 are being replaced with Medicare Beneficiary Identifiers (MBIs) to enhance security and privacy in healthcare systems.\n\nICN\n89-67-2131-B6", "content": "***********-B6"}, "HEALTH_ID_EU_EHIC": {"text": "When traveling within the European Union, it's essential to carry your EHIC card with the identifier 00578000110001020424 to ensure access to necessary medical care.\n\nHIC\n0578000110001020424", "content": "00578000110001020424"}, "HEALTH_ID_UK_NHS": {"text": "The representative requested Mr<PERSON> <PERSON>'s NHS number, which is ************, to update his medical records.\n\nHS\n85 777 3457", "content": "************"}, "HEALTH_ID_AUSTRALIAN_MEDICARE": {"text": "When enrolling in the system, individuals are assigned a unique medicare number, like ************, which is used to access healthcare benefits and services.\n\nedicare\n395 65357 1", "content": "************"}, "TAX_ID_BRAZIL_CNPJ": {"text": "To conduct business transactions in Brazil, companies are required to provide their CNPJ, such as 00.623.904/0001-73, for tax and regulatory purposes.\n\nNPJ\n0.623.904/0001-73", "content": "00.623.904/0001-73"}, "PASSPORT_LUXEMBURG": {"text": "To apply for a visa, holders of a Luxembourg passport, with the code JD5V2J7N, must submit their application to the appropriate embassy or consulate.\n\nuxembourg passport\nD5V2J7N", "content": "JD5V2J7N"}, "PASSPORT_ARGENTINA": {"text": "To facilitate international travel, please ensure your passport, with code ZZZ000110, is valid for at least six months beyond your intended stay.\n\nassport\nZZ000110", "content": "ZZZ000110"}, "PASSPORT_INDIA": {"text": "When traveling abroad, it's essential to keep your passport, bearing the number ********, secure at all times to avoid any complications during customs inspections.\n\nassport\n9137927", "content": "********"}, "PASSPORT_NEW_ZEALAND": {"text": "To proceed with the visa application, please provide a scanned copy of your passport, displaying the number RA000554, for verification purposes.\n\nassport\nA000554", "content": "RA000554"}, "DL_ROMANIA": {"text": "You'll need to present a valid driver's license like the one with the number ********* to rent a car at the rental agency counter.\n\nriver's license\n10724188", "content": "*********"}, "DL_ARGENTINA": {"text": "Please go to the local DMV office, where they will renew your driver's license using the number 35400840 and necessary documents.\n\nriver's license\n5400840", "content": "35400840"}, "DL_NEW_ZEALAND": {"text": "When applying for car insurance, you'll be asked to provide your driver's license, including the identifier BQ739482, to determine your eligibility and coverage options.\n\nriver's license\nQ739482", "content": "BQ739482"}, "DL_INDIA": {"text": "To register for the driving test, candidates must present their driver's license, which includes the number DL-142011001234, as proof of eligibility.\n\nriver's license\nL-142011001234", "content": "DL-142011001234"}, "DL_SOUTH_KOREA": {"text": "To rent a vehicle, you'll need to present a valid driver's license like the one with the code 12-084384-30 to the rental company's representative.\n\nriver's license\n2-084384-30", "content": "12-084384-30"}, "TAX_ID_FRANCE": {"text": "To file your annual tax return, you'll need to include your tax number, which is a unique code like 3023217600053, to ensure the timely processing.\n\nax number\n023217600053", "content": "3023217600053"}, "PASSPORT_AUSTRIA": {"text": "To complete the hotel reservation, please provide your passport number, which is P 4366918, along with your personal details.\n\nassport number\n4366918", "content": "P 4366918"}, "DL_AUSTRIA": {"text": "To complete the registration process, please provide a copy of your driver's license, which features the number 81944939, for verification purposes.\n\nriver's license\n1944939", "content": "81944939"}, "NATIONAL_ID_AUSTRIA": {"text": "The online registration form requires your identity number, which is encoded as MDEyMzQ1Njc4OWFiY2RlZg==, for security purposes and to verify your identity accurately.\n\ndentity number\nDEyMzQ1Njc4OWFiY2RlZg==", "content": "MDEyMzQ1Njc4OWFiY2RlZg=="}, "SSN_AUSTRIA": {"text": "When contacting customer support, please provide your insurance code 1751 080851 for efficient assistance with your policy inquiries.\n\nnsurance code\n751 080851", "content": "1751 080851"}, "TAX_ID_AUSTRIA": {"text": "When submitting your business expenses for reimbursement, please remember to include your tax number, such as 12-370/1018, to expedite the processing of your claim.\n\nax number\n2-370/1018", "content": "12-370/1018"}, "SSN_PORTUGAL": {"text": "It's important to safeguard your personal information, including your social security number like ***********, to prevent identity theft and unauthorized access to sensitive data.\n\nocial security number\n2168411613", "content": "***********"}, "SSN_LUXEMBOURG": {"text": "When applying for a loan, it's standard procedure for lenders to request your social security number, such as *************, to assess your creditworthiness and verify your identity.\n\nocial security number\n978031212393", "content": "*************"}, "SSN_HUNGARY": {"text": "The bank representative verified the applicant's identity by confirming their social security number, which was provided as *********, before completing the application.\n\nocial security number\n87657479", "content": "*********"}, "SSN_GREECE": {"text": "Your social security number *********** serves as a unique identifier used by government agencies for purposes such as tax reporting and retirement benefits.\n\nocial security number\n3121199999", "content": "***********"}, "TAX_ID_SLOVENIA": {"text": "To ensure accurate processing of your tax return, please include your tax number, ********, on all relevant documentation submitted to the tax authorities.\n\nax number\n5012557", "content": "********"}, "TAX_ID_PORTUGAL": {"text": "When completing your tax forms, ensure to include your tax number, such as ***********, to accurately report your income and deductions to the tax authorities.\n\nax number\n08 581 430", "content": "***********"}, "TAX_ID_CYPRUS": {"text": "To access your online tax portal, you'll need to enter your tax number like 99652156X along with your password and security credentials for authentication.\n\nax number\n9652156X", "content": "99652156X"}, "TAX_ID_SWEDEN": {"text": "To inquire about your tax refund status, please provide your tax number, 610321-3499, along with your full name and contact information to our customer service representative.\n\nax number\n10321-3499", "content": "610321-3499"}, "TAX_ID_GREECE": {"text": "Upon completion of your tax assessment, please ensure that your tax number ********* is accurately recorded on all official documents for proper documentation and filing with the tax authorities.\n\nax number\n13305853", "content": "*********"}, "TAX_ID_GERMANY": {"text": "For instance, if your tax number is 26954371827, any errors or discrepancies in this crucial information could potentially lead to delays in tax refunds or even trigger audits by the tax authorities.\n\nax number\n6954371827", "content": "26954371827"}, "TAX_ID_MALTA": {"text": "During the tax audit, the auditor requested the taxpayer to confirm their tax number, which is 0034581M, to verify the accuracy of the reported income and deductions.\n\nax number\n034581M", "content": "0034581M"}, "TAX_ID_SPAIN": {"text": "When completing your tax return online, ensure to input your tax number M0200049H accurately to avoid any potential processing delays.\n\nax number\n0200049H", "content": "M0200049H"}, "NATIONAL_ID_CANADA_PRN": {"text": "The immigration officer scanned the PR card with the number PA0123456 to verify the traveler's permanent resident status.\n\nR\nA0123456", "content": "PA0123456"}, "CREDIT_CARD_AE": {"text": "The customer provided their credit card number, 3421-123456-12345, to complete the online purchase of the items in their shopping cart.\n\nredit card\n421-123456-12345", "content": "3421-123456-12345"}, "CREDIT_CARD_UNION": {"text": "For security reasons, never share your credit card number, such as ****************, with anyone over the phone or email.\n\nredit card\n250941006528599", "content": "****************"}, "CREDIT_CARD_DISCOVER": {"text": "If your credit card with the number 6011-0001-8033-1112 is lost or stolen, immediately report it to your bank to prevent unauthorized transactions.\n\nredit card\n011-0001-8033-1112", "content": "6011-0001-8033-1112"}, "CREDIT_CARD_JCB": {"text": "Financial fraudsters often target unsuspecting individuals by phishing for sensitive information, including credit card numbers like 3566 0000 2000 0410, through fake emails or websites.\n\nredit card\n566 0000 2000 0410", "content": "3566 0000 2000 0410"}, "CREDIT_CARD_MASTER": {"text": "The traveler used their credit card with the number 5425-2334-3010-9903 to book a hotel room for their upcoming vacation.\n\nredit card\n425-2334-3010-9903", "content": "5425-2334-3010-9903"}, "CREDIT_CARD_VISA": {"text": "\"The shopper provided their credit card status, including the code ****************, to finalize the online purchase of a new laptop.\n\nredit card\n485536666666663\"", "content": "****************\""}, "CREDIT_CARD_MAESTRO": {"text": "\"The shopper provided their credit card status, including the code ************, to finalize the online purchase of a new laptop.\n\nredit card\n30427373398\"", "content": "************\""}, "PIN": {"text": "The customer verified their identity by entering their PIN, which was set to 1234, before completing the transaction at the ATM.\n\nIN\n234", "content": "1234"}, "HEALTHCARE_ID_NPI": {"text": "Dr<PERSON> <PERSON>, with NPI number **********, ensures that accurate patient information is maintained and shared securely among healthcare providers for coordinated care.\n\nPI\n003000399", "content": "**********"}, "HEALTHCARE_ID_ICD": {"text": "Within the International Classification of Diseases (ICD) framework, the code 1E30 represents a specific diagnosis, serving as a standardized identifier that enables accurate documentation, communication, and analysis of the patient's health condition.\n\nCD\nE30", "content": "1E30"}, "HEALTHCARE_ID_US_NDC": {"text": "The pharmaceutical manufacturer labeled the medication with the NDC code 0002-4462-01 to facilitate accurate identification and tracking throughout the distribution process.\n\nDC\n002-4462-01", "content": "0002-4462-01"}, "HEALTHCARE_ID_US_DEA": {"text": "As part of this oversight, healthcare providers must obtain a DEA registration number, such as H93456781, which serves as a unique identifier allowing authorities to track and monitor the distribution and usage of controlled substances.\n\nEA\n93456781", "content": "H93456781"}, "HEALTHCARE_ID_US_UDI": {"text": "The regulatory agency mandates that medical devices bear a unique device identifier (UDI), such as (01)**********1234(17)140102(11)100102(10)A1234(21)1234, to enhance traceability and facilitate post-market surveillance.\n\nDI\n01)**********1234(17)140102(11)100102(10)A1234(21)1234", "content": "(01)**********1234(17)140102(11)100102(10)A1234(21)1234"}, "HEALTHCARE_ID_US_HCPCS": {"text": "The physician's office submitted a claim with HCPCS code 0019M to the insurance company for reimbursement of the advanced imaging test performed on the patient.\n\nCPCS\n019M", "content": "0019M"}, "FINANCIAL_COMPENSATION": {"text": "The compensation for the project amounts to $123.45 per hour, reflecting the expertise and dedication required for the job.\n\nompensation\n23.45", "content": "123.45"}, "PASSWORD": {"text": "When creating a new account, ensure your password, such as \"co0lcat23!\", meets the security requirements and is memorable for easy access.\n\nassword\no0lcat23!", "content": "co0lcat23!"}, "EDUCATIONAL_INFO_STUDENT_GPA": {"text": "She was delighted to see her GPA rise to 3.25, a testament to her hard work and perseverance throughout the semester.\n\nPA\n.25", "content": "3.25"}, "FINANCIAL_CUSIP": {"text": "The CUSIP number ********* is essential for investors to accurately identify and trade shares of the corresponding stock in the financial markets.\n\nUSIP\n37833100", "content": "*********"}, "BUSINESS_ID_AU_ABN": {"text": "ABC Pty Ltd, a construction company based in Sydney, Australia, provided its ABN, **************, to subcontractors and suppliers when invoicing for services rendered and materials purchased. \n\nBN\n3 ***********", "content": "**************"}, "BUSINESS_ID_AU_ACN": {"text": "During the registration process with the Australian Securities and Investments Commission (ASIC), XYZ Pty Ltd was assigned the ACN ***********, which serves as its unique identifier in the Australian business registry. \n\nCN\n04 085 616", "content": "***********"}, "BUSINESS_ID_BR_CNPJ": {"text": "The CNPJ number 00.623.904/0001-73 is a unique identifier assigned to the company, facilitating its registration and recognition within the Brazilian business landscape.\n\nNPJ\n0.623.904/0001-73", "content": "00.623.904/0001-73"}, "FI_ABA": {"text": "When setting up automatic payments for his mortgage, <PERSON> provided his bank's ABA routing number, *********, to ensure the transactions were processed accurately and efficiently.\n\nBA routing number\n22105155", "content": "*********"}, "FI_IBAN": {"text": "When making a payment to his supplier in France, <PERSON> carefully entered the IBAN *************************** to ensure the funds were directed to the correct bank account without any issues.\n\nBAN\nR763000600001**********189", "content": "***************************"}}