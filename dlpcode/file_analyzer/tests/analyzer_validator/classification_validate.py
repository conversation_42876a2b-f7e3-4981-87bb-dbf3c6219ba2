import json
import sys
import os
from file_analyzer.analyzer_global import Ana<PERSON>zerGlobal
from file_analyzer.file_analyzer import FileAnalyzer
from file_analyzer.analyzer_config import AnalyzerConfig
from file_analyzer.analyzer_target import AnalyzerTarget
from tqdm import tqdm

class ClassificationValidator():
    def __init__(self, file_dir:str, local) -> None:
        self.data = []
        for filename in os.listdir(file_dir):
            file_path = os.path.join(file_dir, filename)
            with open(file_path, 'r') as json_file:
                data = json.load(json_file)
                self.data.extend(data)
        if local:
            base_path = sys.path[0] + "/file_analyzer/config-mini"
            session = None
        else:
            from exts import Session
            from util.config import get_global_config
            configs = get_global_config()
            base_path = configs.get("file_analyzer").get("config_path")
            session = Session

        self.global_data = AnalyzerGlobal(
            base_path = base_path,
            session = session
        )

        self.analyzer = FileAnalyzer(
            config=AnalyzerConfig(
                data_classification_enabled=True, 
                languages=[],
                regions=[],
                entities=['NULL'],
            ),
            global_data=self.global_data
        )
    
    def validate(self, output:str):
        stat_dict = {
            "main_category": dict(),
            "sub_category": dict()
        }
        for test_case in tqdm(self.data):
            category = test_case['MainCategory']
            sub_category = test_case['SubCategory']
            text = test_case['Text']

            res = self.analyzer.analyze(AnalyzerTarget(
                text=text,
                source="test.json",
                source_type="json"
            ))

            # record main category statistic
            if category not in stat_dict['main_category']:
                stat_dict['main_category'][category] = {
                    "correct": 1 if res is not None and res.main_class_name is not None and category == res.main_class_name.lower() else 0,
                    "total": 1
                }
            else:
                stat_dict['main_category'][category]["correct"] += 1 if res is not None and res.main_class_name is not None and category == res.main_class_name.lower() else 0
                stat_dict['main_category'][category]["total"] += 1

            if sub_category != "":
                # record sub category statistic
                if sub_category not in stat_dict['sub_category']:
                    stat_dict['sub_category'][sub_category] = {
                        "correct": 1 if res is not None and res.sub_class_name is not None and sub_category == res.sub_class_name.lower() else 0,
                        "total": 1
                    }
                else:
                    stat_dict['sub_category'][sub_category]["correct"] += 1 if res is not None and res.sub_class_name is not None and sub_category == res.sub_class_name.lower() else 0
                    stat_dict['sub_category'][sub_category]["total"] += 1

        main_correct_nums = 0
        main_total_nums = 0
        for category in stat_dict['main_category']:
            stat_dict['main_category'][category]['accuracy'] = stat_dict['main_category'][category]["correct"]/stat_dict['main_category'][category]["total"]*100
            main_correct_nums += stat_dict['main_category'][category]["correct"]
            main_total_nums += stat_dict['main_category'][category]["total"]
            print(f"The accuracy of main category [{category}] is:" + str(stat_dict['main_category'][category]['accuracy']))
        
        sub_correct_nums = 0
        sub_total_nums = 0
        for category in stat_dict['sub_category']:
            stat_dict['sub_category'][category]['accuracy'] = stat_dict['sub_category'][category]["correct"]/stat_dict['sub_category'][category]["total"]*100
            sub_correct_nums += stat_dict['sub_category'][category]["correct"]
            sub_total_nums += stat_dict['sub_category'][category]["total"]
            print(f"The accuracy of sub category [{category}] is:" + str(stat_dict['sub_category'][category]['accuracy']))

        stat_dict['main_category_accuracy'] = main_correct_nums/main_total_nums*100
        stat_dict['sub_category_accuracy'] = sub_correct_nums/sub_total_nums*100
        with open(output, 'w') as json_file:
            json.dump(stat_dict, json_file)

        return stat_dict['main_category_accuracy'], stat_dict['sub_category_accuracy']

