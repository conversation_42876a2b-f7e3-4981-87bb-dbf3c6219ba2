import sys, os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

import json
import requests
import sys
from file_analyzer.analyzer_global import AnalyzerGlobal
from file_analyzer.file_analyzer import FileAnalyzer
from file_analyzer.analyzer_config import AnalyzerConfig
from file_analyzer.analyzer_target import AnalyzerTarget
from tqdm import tqdm

class NlpValidate():
    def __init__(self, file_dir, local) -> None:
        self.data = []
        for filename in os.listdir(file_dir):
            file_path = os.path.join(file_dir, filename)
            with open(file_path, 'r') as json_file:
                data = json.load(json_file)
                self.data.extend(data)
        if local:
            base_path = sys.path[0] + "/file_analyzer/config-mini"
            session = None
        else:
            from exts import Session
            from util.config import get_global_config
            configs = get_global_config()
            base_path = configs.get("file_analyzer").get("config_path")
            session = Session

        self.global_data = AnalyzerGlobal(
            base_path = base_path,
            session = session
        )

        self.analyzer = FileAnalyzer(
            config=AnalyzerConfig(
                data_classification_enabled=True, 
                languages=[],
                regions=[],
                entities=['SPACY_PERSON', 'SPACY_HOSPITAL','SPACY_STREET_ADDRESS','SPACY_PROBLEM','SPACY_TREATMENT']
            ),
            global_data=self.global_data
        )


    def validate(self, output):
        stat_dict = {
            "FPR":{},
            "RR":{}
        }
        for d in tqdm(self.data):
            text = d['Text']
            spans = d['spans']
            res = self.analyzer.analyze(AnalyzerTarget(text=text, source="test.json", source_type="json"))

            #FPR_validate
            for entity in res.compliances["ENTITIES"]:
                label = entity['entity'].split('SPACY_')[-1]
                for ent in entity['list']:
                    flag = False
                    for span in spans:
                        if label == span["entity_type"] and (span['entity_value'] in ent['text'] or ent['text'] in span['entity_value']):
                            flag = True
                            break
                    if label not in stat_dict["FPR"]:
                        stat_dict["FPR"][label] = {
                            "total": 1,
                            "wrong": 0
                        }
                    else:
                        stat_dict["FPR"][label]['total'] += 1
                    if not flag:
                        stat_dict["FPR"][label]['wrong'] += 1

            #RR_validate
            for span in spans:
                list = []
                for entity in res.compliances["ENTITIES"]:
                    label = entity['entity'].split('SPACY_')[-1]
                    if span["entity_type"] == label or "SPACY_"+span["entity_type"] == label:
                        list = entity['list']
                        break

                flag = False
                for ent in list:
                    if span['entity_value'] in ent['text'] or ent['text'] in span['entity_value']:
                        flag = True
                        break
                if span['entity_type'] not in stat_dict["RR"]:
                    stat_dict["RR"][span['entity_type']] = {
                        "total": 1,
                        "correct": 0
                    }
                else:
                    stat_dict["RR"][span['entity_type']]['total'] += 1
                if flag:
                    stat_dict["RR"][span['entity_type']]['correct'] += 1

        # calculate FPR
        wrong_nums = 0
        total_nums = 0
        for label in stat_dict["FPR"]:
            stat_dict["FPR"][label]['false_positive_rate'] = stat_dict["FPR"][label]['wrong']/stat_dict["FPR"][label]['total']*100 if stat_dict["FPR"][label]['total'] != 0 else 0
            wrong_nums += stat_dict["FPR"][label]['wrong']
            total_nums += stat_dict["FPR"][label]['total']
            print(f"The false positive rate of {label} is: " + str(stat_dict["FPR"][label]['false_positive_rate']))
        stat_dict["FPR"]['false_positive_rate'] = wrong_nums/total_nums*100

        #calculate RR
        correct_nums = 0
        total_nums = 0
        for label in stat_dict["RR"]:
            stat_dict["RR"][label]['recognition_rate'] = stat_dict["RR"][label]['correct']/stat_dict["RR"][label]['total']*100 if stat_dict["RR"][label]['total'] != 0 else 0
            correct_nums += stat_dict["RR"][label]['correct']
            total_nums += stat_dict["RR"][label]['total']
            print(f"The recognition rate of {label} is: " + str(stat_dict["RR"][label]['recognition_rate']))
        stat_dict["RR"]['recogntion_rate'] = correct_nums/total_nums*100
        with open(output, 'w') as output_file:
            json.dump(stat_dict, output_file)
        return stat_dict["RR"]['recogntion_rate'], stat_dict["FPR"]['false_positive_rate']

    # def FPR_validate(self, output):
    #     stat_dict = {}
    #     for d in tqdm(self.data[:10]):
    #         text = d['Text']
    #         res = self.analyzer.analyze(AnalyzerTarget(text=text, source="test.json", source_type="json"))
    #         for label in res.compliances['ENTITIES']:
    #             for ent in res.compliances['ENTITIES'][label]['list']:
    #                 label = str(label).split("SPACY_")[-1]
    #                 request_data = self._build_request_data(text[max(0, ent['start']-100):min(len(text),ent['end']+100)], label, ent['text'])
    #                 ans = self._request_180(request_data=request_data)
    #                 if ans is not None:
    #                     if label not in stat_dict:
    #                         stat_dict[label] = {
    #                             "total": 1,
    #                             "wrong": 0
    #                         }
    #                     else:
    #                         stat_dict[label]['total'] += 1
    #                     if ans is False:
    #                         stat_dict[label]['wrong'] += 1
    #     wrong_nums = 0
    #     total_nums = 0
    #     for label in stat_dict:
    #         wrong_nums += stat_dict[label]['wrong']
    #         total_nums += stat_dict[label]['total']
    #         stat_dict[label]['false positive rate'] = stat_dict[label]['wrong']/stat_dict[label]['total']*100 if stat_dict[label]['total'] != 0 else 0
    #         print(f"The false positive rate of {label} is: " + str(stat_dict[label]['false positive rate']))
    #     stat_dict['false positive rate'] = wrong_nums/total_nums*100
    #     with open(output, 'w') as output_file:
    #         json.dump(stat_dict, output_file)
    #     return stat_dict['false positive rate']

    @classmethod
    def build_request_data(cls, text, label, entity):
        if label == "PERSON":
            prompt = f"In the text given below, does {entity} belong to a name. Just give me an answer YES or NO"
        if label == "STREET_ADDRESS":
            prompt = f"In the text given below, does {entity} belong to address or a location. Just give me an answer YES or NO"
        if label == "HOSPITAL":
            prompt = f"In the text given below, does {entity} belong to a hospital or a medical institution. Just give me an answer YES or NO"
        if label == "TREATMENT":
            prompt = f"In the text given below, does {entity} belong to a medicine or a treatment method. Just give me an answer YES or NO"
        if label == "PROBLEM":
            prompt = f"In the text given below, does {entity} belong to a syndrome or a medical reaction or a physical problem or a disease or a medical condition. Just give me an answer YES or NO"
        request_data = {
            'data': text,
            'prompt': prompt,
            'type': "Q&A", # Q&A, text_classification
            'model': 'llama3'
        }
        return request_data
    
    @classmethod
    def request_180(cls, request_data) -> bool|None:
        url = 'http://*************:5000'
        headers = {
            'Content-Type': 'application/json',
        }
        try:
            response = requests.post(url, json=request_data, headers=headers, timeout=6)

            if response.status_code == 200:
                resp = response.json()
                result = (resp['category'])
                if str(result).upper() == "NO":
                    return False
                else:
                    return True
            else:
                print(f"Request failed: {response.status_code}")
                return None
        except Exception as e:
            print(f"Exception: {e}")
            return None
