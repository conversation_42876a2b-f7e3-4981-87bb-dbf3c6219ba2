import argparse
import sys, os
import logging


logging.basicConfig(level=logging.ERROR)
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))
from file_analyzer.tests.analyzer_validator.s3_tool import <PERSON><PERSON><PERSON><PERSON>

def pattern_validate(input:str = "", output:str = "", local:bool = False):
    from file_analyzer.tests.analyzer_validator.pattern_validate import PatternValidator
    accuracy = PatternValidator(file_dir=input, local=local).validate_all(output_path=output)
    print(f"The accuracy of compliance recognizer pattern is {accuracy}")

# def nlp_FPR_validate(input:str = "", output:str = ""):
#     from file_analyzer.tests.analyzer_validator.nlp_validate import NlpValidate
#     FPR_rate = NlpValidate(file_path=input).FPR_validate(output=output)
#     print(f"The false positive rate of spacy model is {FPR_rate}")

def nlp_validate(input:str = "", output:str = "", local:bool = False):
    from file_analyzer.tests.analyzer_validator.nlp_validate import NlpValidate
    RR_rate, FPR_rate = NlpValidate(file_dir=input, local=local).validate(output=output)
    print(f"The recognize rate of spacy model is {RR_rate}")
    print(f"The false positve rate of spacy model is {FPR_rate}")

def classification_validate(input:str = "", output:str = "", local:bool = False):
    from file_analyzer.tests.analyzer_validator.classification_validate import ClassificationValidator
    main_category_accuracy, sub_category_accuracy = ClassificationValidator(file_dir=input, local=local).validate(output=output)
    print(f"The main category accuracy of classification model is {main_category_accuracy}")
    print(f"The sub category accuracy of classification model is {sub_category_accuracy}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog=sys.argv[0], formatter_class=argparse.RawDescriptionHelpFormatter)
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("-N_FPR", action="store_true", help="calculate false positive rate of nlp model using llma3")
    group.add_argument("-N", action="store_true", help="calculate recognition & false positive rate of nlp model")
    group.add_argument("-P", action="store_true", help="calculate the accuracy of recognizer patterns")
    group.add_argument("-C", action="store_true", help="calculate the accuracy of classification model")
    config_group = parser.add_mutually_exclusive_group(required=True)
    config_group.add_argument("-local", action="store_true", help="local_test")
    config_group.add_argument("-remote", action="store_true", help="remote_test")
    parser.add_argument('-i', type=str, default="", help='Specify the input file')
    parser.add_argument('-o', type=str, default="", help='Specify the output file')
    parser.add_argument('-key_id', type=str, default="", help='specify the key_id of aws account')
    parser.add_argument('-access_key', type=str, default="", help='specify the access_key of aws account')
    parser.add_argument('-bucket', type=str, default="", help='specify the bucket name of S3')
    args = parser.parse_args()

    handler = None
    input_path = args.i
    output_path = args.o

    if args.bucket != "" and args.access_key != "" and args.key_id != "":
        handler = FileHandler(key_id=args.key_id, access_key = args.access_key)
        input_path = handler.download_file(bucket=args.bucket, file_dir=args.i)
        output_path = "test_result/performance.json"

    local = False
    if args.local:
        local = True
    if args.P:
        pattern_validate(input_path, output_path, local)

    if args.N:
        nlp_validate(input_path, output_path, local)

    if args.C:
        classification_validate(input_path, output_path, local)

    if handler:
        handler.upload_file(bucket=args.bucket, file_path=args.o)
