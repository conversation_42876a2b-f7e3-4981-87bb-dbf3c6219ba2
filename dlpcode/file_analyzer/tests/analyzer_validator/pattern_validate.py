import json, sys, os
from file_analyzer.analyzer_global import Ana<PERSON>zerG<PERSON>bal
from file_analyzer.file_analyzer import <PERSON><PERSON>naly<PERSON>
from file_analyzer.analyzer_config import AnalyzerConfig
from file_analyzer.analyzer_target import AnalyzerTarget
from tqdm import tqdm

class PatternValidator():
    def __init__(self, file_dir, local) -> None:
        self.data = []
        for filename in os.listdir(file_dir):
            file_path = os.path.join(file_dir, filename)
            with open(file_path, 'r') as json_file:
                data = json.load(json_file)
                self.data.extend(data)
        if local:
            base_path = sys.path[0] + "/file_analyzer/config-mini"
            session = None
        else:
            from exts import Session
            from util.config import get_global_config
            configs = get_global_config()
            base_path = configs.get("file_analyzer").get("config_path")
            session = Session

        self.global_data = AnalyzerGlobal(
            base_path = base_path,
            session = session
        )
    
    def validate(self, entity:str) -> list:
        #create file analyzer
        analyzer = FileAnalyzer(
            config=AnalyzerConfig(
                data_classification_enabled=False,
                regions=[],
                languages=[],
                entities=[entity]
            ),
            global_data=self.global_data
        )
        failed_ids = []
        test_case = self.data[entity]
        for test_case in self.data[entity]:
            text = test_case['Text']
            id = test_case['id']
            res = analyzer.analyze(AnalyzerTarget(
                text=text,
                source="test",
                source_type="text"
            ))
            flag = False
            if entity in res.compliances['ENTITIES']:
                for ent in res.compliances['ENTITIES'][entity]['list']:
                    if ent['start'] == test_case['start_position'] and ent['end'] == test_case['end_position']:
                        flag = True
                        break
            if not flag:
                failed_ids.append(id)
        return failed_ids
    
    def validate_all(self, output_path:str):
        res = {
            "accuracy": 0,
            "failed_nums": 0,
            "total_nums": 0,
            "failed_ids": dict()
        }
        for entity in tqdm(self.data):
            failed_ids = self.validate(entity=entity)
            res["failed_nums"] += len(failed_ids)
            res["total_nums"] += len(self.data[entity])
            if len(failed_ids) > 0:
                res["failed_ids"][entity] = failed_ids
        
        #calculate average accuracy
        res["accuracy"] = (1-res["failed_nums"]/res["total_nums"])*100

        with open(output_path, 'w') as json_file:
            json.dump(res, json_file)

        return res["accuracy"]
        
            
