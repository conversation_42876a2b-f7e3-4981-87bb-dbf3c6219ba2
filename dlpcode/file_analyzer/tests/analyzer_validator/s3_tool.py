import boto3
import shutil
import os

class FileHandler():
    def __init__(self, key_id, access_key) -> None:
        session = boto3.Session(
            aws_access_key_id=key_id,
            aws_secret_access_key=access_key
            # aws_session_token='your_session_token'  # optional
        )
        self.s3_client = session.client('s3')

    def download_file(self, bucket:str, file_dir:str):
        def clear_folder(folder_path):
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                except Exception as e:
                    print(f'Failed to delete {file_path}. Reason: {e}')
        try:
            test_dir = 'test_case'
            clear_folder(test_dir)
            paginator = self.s3_client.get_paginator('list_objects_v2')
            for page in paginator.paginate(Bucket=bucket, Prefix=file_dir):
                for obj in page.get('Contents', []):
                    s3_key = obj['Key']
                    local_file_path = os.path.join(test_dir, os.path.relpath(s3_key, file_dir))
                    
                    if not os.path.exists(os.path.dirname(local_file_path)):
                        os.makedirs(os.path.dirname(local_file_path))
                    
                    print(f"Downloading {s3_key} to {local_file_path}...")
                    self.s3_client.download_file(bucket, s3_key, local_file_path)
            return test_dir
        except Exception as e:
            print(f"An error occurred: {e}")
    
    def upload_file(self, bucket:str, file_path:str):
        try:
            local_file = 'test_result/performance.json'
            self.s3_client.upload_file(local_file, bucket, file_path)
            print(f"Uploaded {local_file} to {file_path}")
        except Exception as e:
            print(f"An error occurred: {e}")
