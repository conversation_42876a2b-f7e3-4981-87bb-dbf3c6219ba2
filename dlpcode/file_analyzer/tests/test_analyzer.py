import sys, os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

from file_analyzer import FileAnalyzer, AnalyzerConfig, AnalyzerTarget, AnalyzerResult, AnalyzerGlobal

import unittest
import logging
import argparse

global_data = None

def init(config_path):
    global global_data
    global_data = AnalyzerGlobal(base_path=config_path, use_gpu=False)

class TestPredefinedDataType(unittest.TestCase):
    def test_predefined_data_type(self):
        self.maxDiff = None
        except_result = {
            'main_class_id': '20000', 
            'main_class_name': 'Healthcare', 
            'sub_class_id': '20002', 
            'sub_class_name': 'Medication Management', 
            'compliances': {
                'ENTITIES': {
                    'HEALTHCARE_ID_US_NDC': {'count': 1, 'list': [{'score': 1, 'start': 73, 'end': 85, 'text': '0002-0137-00'}], 'id': '232'}, 
                    'URL': {'count': 1, 'list': [{'score': 1, 'start': 195, 'end': 217, 'text': 'http://www.example.com'}], 'id': '214'}}, 
                'CUSTOM': {}
                }
            }
        text = "The pharmaceutical manufacturer labeled the medication with the NDC code 0002-0137-00 to facilitate accurate identification and tracking throughout the distribution process. This is the website: http://www.example.com."
        config = AnalyzerConfig(regions = ["US"], data_classification_enabled =True)
        analyzer = FileAnalyzer(config, global_data=global_data, debug=False, user_defined_datatypes=[])
        result = analyzer.analyze(AnalyzerTarget(
            text=text, 
            source="test.json", 
            source_type='json'))
        self.assertDictEqual(result.to_dict(), except_result)
        
    def test_predefined_data_type_without_ML(self):
        self.maxDiff = None
        except_result = {
            'main_class_id': None, 
            'main_class_name': '', 
            'sub_class_id': None, 
            'sub_class_name': '', 
            'compliances': {
                'ENTITIES': {
                    'HEALTHCARE_ID_US_NDC': {'count': 1, 'list': [{'score': 1, 'start': 73, 'end': 85, 'text': '0002-0137-00'}], 'id': '232'}, 
                    'URL': {'count': 1, 'list': [{'score': 1, 'start': 195, 'end': 217, 'text': 'http://www.example.com'}], 'id': '214'}}, 
                'CUSTOM': {}
                }
            }
        text = "The pharmaceutical manufacturer labeled the medication with the NDC code 0002-0137-00 to facilitate accurate identification and tracking throughout the distribution process. This is the website: http://www.example.com."
        config = AnalyzerConfig(regions = ["US"], data_classification_enabled =False)
        analyzer = FileAnalyzer(config, global_data=global_data, debug=False, user_defined_datatypes=[])
        result = analyzer.analyze(AnalyzerTarget(
            text=text, 
            source="test.json", 
            source_type='json'))
        self.assertDictEqual(result.to_dict(), except_result)
        
    def test_userdefind_data_type(self):
        self.maxDiff = None
        except_result = {
            'main_class_id': None, 
            'main_class_name': '', 
            'sub_class_id': None, 
            'sub_class_name': '', 
            'compliances': {
                'ENTITIES': {
                    'HEALTHCARE_ID_US_NDC': {'count': 1, 'list': [{'score': 1, 'start': 73, 'end': 85, 'text': '0002-0137-00'}], 'id': '232'}, 
                    'URL': {'count': 1, 'list': [{'score': 1, 'start': 195, 'end': 217, 'text': 'http://www.example.com'}], 'id': '214'}
                }, 
                'CUSTOM': {
                    'Entity-39364a60-952e-4f99-b042-60c84de1b4c6': {'count': 1, 'list': [{'score': 0.9, 'start': 73, 'end': 85, 'text': '0002-0137-00'}], 'id': '39364a60-952e-4f99-b042-60c84de1b4c6'},
                }
            }
        }
        datatypes = [
                {
                    "id": "39364a60-952e-4f99-b042-60c84de1b4c6",
                    "data_type": "Custom data type- 01", 
                    "regions": ["ALL"],
                    "language": "en",
                    "patterns": [
                        {"regex": r'\b(\d{4}-\d{4}-\d{2}|\d{5}-\d{4}\-[\da-z]|\d{5}\-[A-Z\d]{3}\-[A-Z\d]{2}|\d{5}\-\d{4}\-\d{2})\b'},
                    ],
                    "keyword_must": False,
                    "keywords": [
                        {"keyword": "NDC"}
                    ],
                }
            ]
        text = "The pharmaceutical manufacturer labeled the medication with the NDC code 0002-0137-00 to facilitate accurate identification and tracking throughout the distribution process. This is the website: http://www.example.com."
        config = AnalyzerConfig(regions = ["US"], data_classification_enabled =False)
        analyzer = FileAnalyzer(config, global_data=global_data, debug=False, user_defined_datatypes=datatypes)
        result = analyzer.analyze(AnalyzerTarget(
            text=text, 
            source="test.json", 
            source_type='json'))
        #print(result.to_dict()) 
        self.assertDictEqual(result.to_dict(), except_result)
        self.assertEqual(result.to_dict(), except_result)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--config', type=str, default="/var/log/file_analyzer/config", help="First number")
    args, unknown = parser.parse_known_args()
    
    init(args.config)
    
    unittest.main(argv=['first-arg-is-ignored'] + unknown)
    #unittest.main()