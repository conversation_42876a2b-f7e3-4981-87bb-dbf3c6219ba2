import base64
import os
import sys
from pathlib import Path

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))
from file_analyzer import FileA<PERSON><PERSON>zer, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>zerGlobal
from file_analyzer.util.memory_profiler_decorator import profile, set_memory_profiling
from file_analyzer.analyzer_log import setup_debug_logging
import json
import argparse
import uuid

setup_debug_logging()


def simple_enc_text(text: str) -> str:
    return base64.b64encode(text.encode("utf-8")).decode("utf-8")

def get_global_config() -> dict:
    try:
        config_path = Path(__file__).parent.parent.parent / "config.json"
        with config_path.open() as config_file:
            configs = json.load(config_file)

        return configs
    except Exception as e:
        return {}

@profile
def test_plain_text(input, config_path, data=None, limit=0):
    if not data and not os.path.exists(input):
        print(f"input file/directory {input} not found")
        sys.exit(1)
        
    if not os.path.exists(config_path):
        print(f"config directory {config_path} not found")
        sys.exit(2)
        
        
    # read test data
    all_texts = []
    if data:
        all_texts.append({
                "text": data,
                "from": "-d",
                "uuid": uuid.uuid4(),
            })
    elif os.path.isfile(input):
        with open(input, "r") as f:
            d=f.read()
            
            all_texts.append({
                "text": d,
                "from": input,
                "uuid": uuid.uuid4(),
            })
    else:
        for path, dirnames, filenames in os.walk(input):
            for filename in filenames:
                file_path = os.path.join(path, filename)
                print(file_path)
                with open(file_path, "r") as f:
                    data=f.read()
                    
                    all_texts.append({
                        "text": data,
                        "from": file_path,
                        "uuid": uuid.uuid4(),
                    })
    if not all_texts:
        print("No test data")
        return 
    
    datatypes = [
                {
                    "id": "39364a60-952e-4f99-b042-60c84de1b4c6",
                    "group_id": "88884a60-952e-4f99-b042-60c84de1b4c7",
                    "data_type": "Custom data type- 01", 
                    "regions": ["ALL"],
                    "language": "en",
                    "patterns": [
                        {"regex": r'\b(\d{4}-\d{4}-\d{2}|\d{5}-\d{4}\-[\da-z]|\d{5}\-[A-Z\d]{3}\-[A-Z\d]{2}|\d{5}\-\d{4}\-\d{2})\b'},
                    ],
                    "keyword_must": False,
                    "keywords": [
                        {"keyword": "NDC"}
                    ],
                },
                {
                    "id": "39364a60-952e-4f99-b042-60c84de1b4c7",
                    "group_id": "88884a60-952e-4f99-b042-60c84de1b4c7",
                    "data_type": "Custom data type- 02", 
                    "regions": ["ALL"],
                    "language": "en",
                    "patterns": [
                        {"regex": r'\b(\d{4}-\d{4}-\d{2}|\d{5}-\d{4}\-[\da-z]|\d{5}\-[A-Z\d]{3}\-[A-Z\d]{2}|\d{5}\-\d{4}\-\d{2})\b'},
                    ],
                    "keyword_must": False,
                    "keywords": [
                        {"keyword": "NDC"}
                    ],
                },
            ]
    global_conf = get_global_config()
    encrypt_sensitive_data = global_conf.get('file_analyzer', {}).get("encrypt_sensitive_data", True)

    config_ext={
                "regions": ['ALL'], 
                "data_classification_enabled": True,
                "encrypt_sensitive_data":encrypt_sensitive_data
            }
    
    config = AnalyzerConfig(**config_ext)
    print(f"Analyzer configuration: {config}")
    global_data = AnalyzerGlobal(
            base_path=config_path, 
            use_gpu=False, 
            classifier_thread_num=2,
            classifier_token_limit=512,
            nlp_thread_num= 2,
        )
    analyzer = FileAnalyzer(
            config, 
            global_data=global_data, 
            debug=False, 
            user_defined_datatypes=datatypes,
        )
    # with open("result.txt", "w") as f:
    #     f.write(f"# {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n")
        
    for idx, item in enumerate(all_texts):
        if limit>0 and idx >= limit:
            break
        text = item['text']
        result = analyzer.analyze(AnalyzerTarget(
                text=text, 
                source=item["from"], 
                source_type='json',
                source_uuid=item['uuid'],
            ), encrypt_function=simple_enc_text

        )
        
        print(f"## Index:{idx}\n## from: {item['from']}\n## uuid: {item['uuid']}")
        
        if result:
            print(json.dumps(result.to_dict()))
        else:
            print("No result")
        
        with open("result.txt", "a") as f:
            f.write(f"## Index:{idx}\n## from: {item['from']}\n")
            if result:
                f.write(f"{json.dumps(result.to_dict())}\n")
            else:
                f.write("No result")      

def equal(item, tag) -> bool:
    LABEL_MAPPING = {
        "ID":"MEDICALRECORD",
        "LOCATION":"STREET_ADDRESS",
        "DOCTOR":"PERSON",
        "PATIENT":"PERSON"
    }
    if (tag["type"] in LABEL_MAPPING.keys() and LABEL_MAPPING[tag["type"]] == item["type"]) or item["type"] == tag["type"]:
        if tag["start"] >= item["start"] and tag["end"] <= item["end"]:
            return True
        if tag["start"] <= item["start"] and tag["end"] >= item["end"]:
            return True
    return False

def calculate_precision(res:AnalyzerResult, tags:list):
    new_res = []
    types = []
    for compliance in res.compliances.keys():
        for label in res.compliances[compliance].keys():
            if label in types:
                continue
            types.append(label)
            for item in res.compliances[compliance][label]["list"]:
                new_res.append({
                    "text": item["text"],
                    "type":label,
                    "start":item["start"],
                    "end":item["end"],
                    "score": item["score"]
                })
    false_negative = []
    false_positive = []
    for item in new_res:
        flag = False
        for tag in tags:
            if equal(item=item, tag=tag):
                flag = True
                break
        if not flag:
            false_negative.append("[{}] - [{}] - {}:{} - {}".format(item["type"], item["text"], item["start"], item["end"], item["score"]))
    for tag in tags:
        flag = False
        for item in new_res:
            if equal(item=item, tag=tag):
                flag = True
                break
        if not flag:
            false_positive.append("[{}] - [{}] - {}:{}".format(tag["type"], tag["text"], tag["start"], tag["end"]))
    false_rate = len(false_negative)/len(new_res)
    miss_rate = len(false_positive)/len(tags)
    return false_negative, false_positive, false_rate, miss_rate

@profile 
def test_dataset(input, config_path, data=None, limit=0, no_stat=False):
    if not data and not os.path.exists(input):
        print(f"input file/directory {input} not found")
        sys.exit(1)
        
    if not os.path.exists(config_path):
        print(f"config directory {config_path} not found")
        sys.exit(2)
        
        
    # read test data
    all_texts = []
    if data:
        all_texts.append({
                "text": data,
                "from": "-d",
            })
    elif os.path.isfile(input):
        with open(input, "r") as f:
            items = json.load(f)
            for item in items:
                if 'from' in item:
                    item['origin_from'] = item['from']
                item['from'] = input
                all_texts.append(item)
    else:
        for path, dirnames, filenames in os.walk(input):
            for filename in filenames:
                file_path = os.path.join(path, filename)
                print(file_path)
                with open(file_path, "r") as f:
                    items = json.load(f)
                    for item in items:
                        if 'from' in item:
                            item['origin_from'] = item['from']
                        item['from'] = file_path
                        all_texts.append(item)
                    
    if not all_texts:
        print("No test data")
        return 
    
    config = AnalyzerConfig(**{"regions": ["US"]})
    print(f"Analyzer configuration: {config}")
    
    global_data = AnalyzerGlobal(base_path=config_path, use_gpu=False)
    analyzer = FileAnalyzer(config, global_data=global_data)
    
    # with open("result.txt", "w") as f:
    #     f.write(f"# {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n")
        
    precision_res = []
    total_false = 0
    total_miss = 0
    for idx, item in enumerate(all_texts):
        if limit>0 and idx >= limit:
            break
        #text = item['text']
        text = item.get('text', item.get('Text'))
        if text is None:
            print("Text not found")
            continue

        result = analyzer.analyze(AnalyzerTarget(
            text=text, 
            source=item.get('from'), 
            source_type='json')
        )
        
        if not no_stat:
            false_negative, false_positive, false_rate, miss_rate = calculate_precision(result, item["tags"])
            precision_res.append({
                "Text": text,
                "False": false_negative,
                "Miss": false_positive
            })
            total_false += false_rate
            total_miss += miss_rate
            
        print(f"## Index:{idx}\n## from: {item['from']}")
        #print(json.dumps(result.to_dict()))
        if result:
            print(json.dumps(result.to_dict()))
        else:
            print("No result")
            
        with open("result.txt", "a") as f:
            f.write(f"## Index:{idx}\n## from: {item['from']}\n")
            if 'origin_from' in item:
                f.write(f"## origin_from: {item['origin_from']}\n")
            #f.write(f"{json.dumps(result.to_dict())}\n")
            if result:
                f.write(f"{json.dumps(result.to_dict())}\n")
            else:
                f.write("No result")
    
    if not no_stat:   
        false_rate = total_false/min(limit, len(all_texts))
        miss_rate = total_miss/min(limit, len(all_texts))
        with open('precision.json','w') as precision_json:
            precision_json.write(f"## Index:{idx}\n## from: {item['from']}\n")
            if 'origin_from' in item:
                precision_json.write(f"## origin_from: {item['origin_from']}\n")
            precision_json.write(f"## total_num: {min(limit, len(all_texts))}\n")
            precision_json.write(f"## miss_rate: {miss_rate}\n")
            precision_json.write(f"## false_rate: {false_rate}\n")
            if precision_json:
                precision_json.write(f"{json.dumps(precision_res)}\n")
            else:
                precision_json.write("No result")
    
if __name__ == '__main__':
    print("Welcome to file analyzer")
    
    epilog = '''
eg.
    python {0} -plain -f test 
    python {0} -plain -d "abcdefghijklmnopqrstuvwxy"
    python {0} -dateset -f datasets/test_datasets/n2c2_06.json -l 1
    python {0} -dateset -f datasets/test_datasets/n2c2_06.json -no_stat -l 1
    python {0} -plain -d "The pharmaceutical manufacturer labeled the medication with the NDC code 0002-0137-00 to facilitate accurate identification and tracking throughout the distribution process. This is the website: http://www.example.com. Address: 4260 Still Creek St, Burnaby, BC"
'''.format(sys.argv[0])
    parser = argparse.ArgumentParser(prog=sys.argv[0], epilog=epilog, description="Data Reader", formatter_class=argparse.RawDescriptionHelpFormatter)
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("-plain", action="store_true", help="the test target is a plain text")
    group.add_argument("-dataset", action="store_true", help="the test target is from a test dataset (formatted text)")
    
    parser.add_argument('-c', type=str, help='Specify the configuration directory. The default config path: config, ../config-mini, ../config or /var/log/file_analyzer/config')
    parser.add_argument('-f', type=str, default="datasets/test_datasets/n2c2_06.json", help='Specify the input file/directory')
    parser.add_argument('-d', type=str, help='Specify the input text')
    parser.add_argument('-limit', type=int, default=1, help='limit')
    parser.add_argument("-no_stat", action="store_true", help="no stat")
    parser.add_argument("-memory_profile", action="store_true", help="memory profile")

    try:
        args = parser.parse_args()
        
        if args.memory_profile:
            set_memory_profiling(True)
        
        
        if not args.c:
            default_config = "config"
            default_config1 = "../config-mini"
            default_config2 = "../config"
            default_config3 = "/var/log/file_analyzer/config"
            if os.path.exists(default_config):
                args.c = default_config
            elif os.path.exists(default_config1):
                args.c = default_config1
            elif os.path.exists(default_config2):
                args.c = default_config2
            elif os.path.exists(default_config3):
                args.c = default_config3
            else:
                print("Please specify a config path")
                sys.exit()
            print(f"Use the default config path: '{args.c}'")
        print(args)     
                
        if args.plain:
            test_plain_text(args.f, args.c, args.d, args.limit)
        elif args.dataset:
            test_dataset(args.f, args.c, args.d, args.limit, no_stat=args.no_stat)
    except (KeyboardInterrupt, SystemExit):
        print("Analyzer Test Exiting...")
    