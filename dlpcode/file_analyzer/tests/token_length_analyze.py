import wikipediaapi
from transformers import BertTokenizer
import numpy as np
import time

tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')

user_agent = 'MyUserAgent/1.0 (https://example.com/; <EMAIL>)'
wiki_wiki = wikipediaapi.Wikipedia(language='en', user_agent=user_agent)

def read_titles_from_file(filename):
    with open(filename, 'r') as file:
        titles = file.read().splitlines()
    return titles

def get_wikipedia_text(title):
    page = wiki_wiki.page(title)
    return page.text if page.exists() else ""

titles = read_titles_from_file('titles.txt')

texts = [get_wikipedia_text(title) for title in titles]

original_lengths = []
tokenized_lengths = []
times = []
times_2 = []
valid_text_count = 0

for text in texts:
    if not text:
        continue

    valid_text_count += 1

    original_length = len(text)
    original_lengths.append(original_length)

    # ------------------------------------------
    start_time = time.time()
    
    for i in range(10):
        tokens = tokenizer(text[:3*1024], max_length=512, truncation=True)
    
    end_time = time.time()
    
    elapsed_time = end_time - start_time
    times.append(elapsed_time)
    
    # ---------------------------------------------
    start_time = time.time()
    
    for i in range(10):
        tokens = tokenizer(text, max_length=512, truncation=True)
    
    end_time = time.time()
    
    elapsed_time = end_time - start_time
    times_2.append(elapsed_time)
    
    # ------------------------------------------

    input_ids = tokens['input_ids']

    decoded_text = tokenizer.decode(input_ids, skip_special_tokens=True)

    decoded_length = len(decoded_text)

    tokenized_lengths.append(decoded_length)


average_original_length = np.mean(original_lengths)
max_original_length = np.max(original_lengths)
min_original_length = np.min(original_lengths)
median_original_length = np.median(original_lengths)

average_tokenized_length = np.mean(tokenized_lengths)
max_tokenized_length = np.max(tokenized_lengths)
min_tokenized_length = np.min(tokenized_lengths)
median_tokenized_length = np.median(tokenized_lengths)

average_time = np.mean(times)
average_time_2 = np.mean(times_2)

print(f"Number of valid texts: {valid_text_count}")

print(f"Average original length of text: {average_original_length:.2f} characters")
print(f"Maximum original length of text: {max_original_length} characters")
print(f"Minimum original length of text: {min_original_length} characters")
print(f"Median original length of text: {median_original_length} characters")

print(f"Average length of tokenized text for 512 tokens: {average_tokenized_length:.2f} characters")
print(f"Maximum length of tokenized text for 512 tokens: {max_tokenized_length} characters")
print(f"Minimum length of tokenized text for 512 tokens: {min_tokenized_length} characters")
print(f"Median length of tokenized text for 512 tokens: {median_tokenized_length} characters")

print(f"Average serialization time for truncated tokenizing text: {average_time:.4f} seconds")
print(f"Average serialization time for tokenizing text: {average_time_2:.4f} seconds")
