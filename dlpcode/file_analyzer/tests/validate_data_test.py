import os, sys

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

from file_analyzer.recognizer.validate_func import ValidateDataContainer
from dlpcode.file_analyzer.util.memory_profiler_decorator import profile, set_memory_profiling

if __name__ == '__main__':
    set_memory_profiling(True)
    container = ValidateDataContainer("../config-mini/compliance_recognizer/config_v0.0.1", unload=True, validate=True)
    container.load_test()
    print(container.size())