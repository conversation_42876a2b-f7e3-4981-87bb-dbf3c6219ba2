import os, shutil

def clear_directory(directory):
    if os.path.exists(directory):
        for filename in os.listdir(directory):
            file_path = os.path.join(directory, filename)
            try:
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    os.unlink(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            except Exception as e:
                print(f'Failed to delete {file_path}. Reason: {e}')

def delete_folder_recursive(folder_path):
    if os.path.exists(folder_path):
        if os.path.isfile(folder_path):
            os.remove(folder_path)
        elif os.path.islink(folder_path):
            os.unlink(folder_path)
        elif os.path.isdir(folder_path):
            shutil.rmtree(folder_path)

def find_files_with_prefix(directory, prefix):
    matching_files = []
    
    if os.path.exists(directory):
        for filename in os.listdir(directory):
            file_path = os.path.join(directory, filename)
            if os.path.isfile(file_path) and filename.lower().startswith(prefix) and filename.lower().endswith('.tar.gz'):
                matching_files.append(filename)
    
    return matching_files

def list_files_in_directory(directory):
    files = []
    for filename in os.listdir(directory):
        filepath = os.path.join(directory, filename)
        files.append(filepath)
    return files

def check_file_exists(func):
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        if not os.path.exists(result):
            raise FileNotFoundError(f"'{result}' does not exist.")
        return result
    return wrapper