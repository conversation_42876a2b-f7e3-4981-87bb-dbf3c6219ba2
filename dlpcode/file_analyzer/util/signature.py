
import os
import base64
import tarfile
import logging
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding, rsa
from cryptography.hazmat.primitives.asymmetric.utils import Prehashed

logger = logging.getLogger("fortidata_file_analyzer")

# Load the public key
def load_public_key(public_key_path):
    with open(public_key_path, "rb") as key_file:
        public_key = serialization.load_pem_public_key(
            key_file.read()
        )
    return public_key

def verify_signature(root_path, public_key, signature):
    
    if not os.path.exists(root_path):
        err_msg = f"{root_path} not found"
        logger.error(err_msg)
        return False, err_msg
        
    hash_func = hashes.Hash(hashes.SHA256())
    if os.path.isfile(root_path):
        with open(root_path, 'rb') as f:
            while chunk := f.read(4096):
                hash_func.update(chunk)
    else:
        for root, dirs, files in os.walk(root_path):
            print(root, files)
            for file in files:
                file_path = os.path.join(root, file)
                with open(file_path, 'rb') as f:
                    while chunk := f.read(4096):
                        hash_func.update(chunk)
    
    digest = hash_func.finalize()
    
    try:
        public_key.verify(
            signature,
            digest,
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            Prehashed(hashes.SHA256())
        )
        logger.info(f"File/Directory {root_path} is with a valid signature")
        return True, None
    except Exception as e:
        err_msg = f"Signature verification failed."
        logger.error(err_msg)
        return False, err_msg

def verify_tar_signature(tar_path, public_key_path):
    # Load the public key
    public_key = load_public_key(public_key_path)

    hash_func = hashes.Hash(hashes.SHA256())

    # Open the tar file and iterate over its members
    with tarfile.open(tar_path, "r:gz") as tar:
        signature = None
        for member in tar.getmembers():
            if member.isfile():
                file = tar.extractfile(member)
                if file is not None:
                    if os.path.basename(member.name) == "signature.sig":
                        #signature = base64.b64decode(file.read())
                        signature_encode = file.read()
                        print(f"Get signature: {signature_encode}")
                        signature = base64.b64decode(signature_encode)
                    else:
                        while chunk := file.read(4096):
                            hash_func.update(chunk)
    # for test
    #hash_func.update(b"123")
    
    if signature is None:
        err_msg = f"Signature file not found in the tarball. {tar_path}"
        logger.error(err_msg)
        return False, err_msg

    digest = hash_func.finalize()

    try:
        public_key.verify(
            signature,
            digest,
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            Prehashed(hashes.SHA256())
        )
        logger.info("Signature is valid.")
        return True, None
    except Exception as e:
        err_msg = f"Signature verification failed. {tar_path}"
        logger.error(err_msg)
        return False, err_msg

