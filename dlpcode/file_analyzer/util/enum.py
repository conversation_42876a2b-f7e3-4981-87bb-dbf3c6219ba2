from enum import Enum

class AnalyzerPackageType(Enum):
    CLASSIFICATION_MODEL = "classification_model"
    COMPLIANCE_MODEL = "compliance_model"
    COMPLIANCE_RECOGNIZER = "compliance_recognizer"

PACKAGE_LEVEL_MAPPING = {
    "Classification": AnalyzerPackageType.CLASSIFICATION_MODEL.value,
    "NLP": AnalyzerPackageType.COMPLIANCE_MODEL.value,
    "DateType": AnalyzerPackageType.COMPLIANCE_RECOGNIZER.value 
}

PACKAGE_NAME_MAPPING = {
    AnalyzerPackageType.CLASSIFICATION_MODEL.value: "ML Classification Model",
    AnalyzerPackageType.COMPLIANCE_MODEL.value: "NLP Model",
    AnalyzerPackageType.COMPLIANCE_RECOGNIZER.value: "Data Type Database"
}