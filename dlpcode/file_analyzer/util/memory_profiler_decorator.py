import os
from memory_profiler import profile as mem_profile
import functools


ENABLE_MEMORY_PROFILING = False

# store the profile log in the working directory
LOG_FILE = os.path.join(os.getcwd(), 'memory_profile.log')

def set_memory_profiling(enabled):
    global ENABLE_MEMORY_PROFILING
    ENABLE_MEMORY_PROFILING = enabled
    open(LOG_FILE, 'w').close()

def profile(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        if ENABLE_MEMORY_PROFILING:
            with open(LOG_FILE, 'a') as log_file:
                return mem_profile(func, stream=log_file)(*args, **kwargs)
        else:
            return func(*args, **kwargs)
    return wrapper
