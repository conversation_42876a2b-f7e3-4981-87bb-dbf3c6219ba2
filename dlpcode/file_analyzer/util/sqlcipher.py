import hashlib
from pysqlcipher3 import dbapi2 as sqlcipher
import tempfile
import random
import string
import os
from pathlib import Path
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes

def decrypt_db_mode_1(db_path, decrypted_db_path, logger):
    if check_db_decrypted(db_path=db_path):
        with open(db_path, 'rb') as file:
            file.seek(110)
            key = file.read(32)
            encrypted_data = file.read(32)
        password = aes_decrypt_ecb(encrypted_data, key)
        with open(db_path, 'rb') as file:
            file.seek(174)
            file_content = file.read()
        encrypted_file_name = os.path.join("/tmp",generate_random_string())
        with open(encrypted_file_name, 'wb') as encrypted_file:
            encrypted_file.write(file_content)
        decrypt_sqlcipher_db(encrypted_db_path=encrypted_file_name, decrypted_db_path=decrypted_db_path, password=password, logger=logger)
        os.remove(encrypted_file_name)
    else:
        src_path = Path(db_path)
        dst_path = Path(decrypted_db_path)
        dst_path.write_bytes(src_path.read_bytes())


def decrypt_db_mode_2(db_path, logger):
    if check_db_decrypted(db_path=db_path):
        with open(db_path, 'rb') as file:
            file.seek(110)
            key = file.read(32)
            encrypted_data = file.read(32)
        password = aes_decrypt_ecb(encrypted_data, key)
        with open(db_path, 'rb') as file:
            file.seek(174)
            file_content = file.read()
        encrypted_file_name = os.path.join("/tmp",generate_random_string())
        decrypted_file_name = os.path.join("/tmp","compliance_recognizer_"+generate_random_string())
        
        with tempfile.NamedTemporaryFile(delete=False, mode='wb') as temp_file:
            temp_file.write(file_content)
        
        decrypt_sqlcipher_db(encrypted_file_name, decrypted_file_name, password, logger=logger)
        os.remove(encrypted_file_name)
        return decrypted_file_name, True
    else:
        return db_path, False


def aes_decrypt_ecb(encrypted_data, key):
    cipher = AES.new(key, AES.MODE_ECB)
    decrypted_data = cipher.decrypt(encrypted_data)
    unpadded_data = unpad(decrypted_data, AES.block_size)
    return unpadded_data.decode('utf-8')


def generate_random_string(length=10):
    characters = string.ascii_letters + string.digits
    random_string = ''.join(random.choice(characters) for _ in range(length))
    return random_string

def check_db_decrypted(db_path:str) -> bool:
    with open(db_path, 'rb') as file:
        format = file.read(13).hex()
        if format == "53514c69746520666f726d6174":
            return False
        else:
            return True

def decrypt_sqlcipher_db(encrypted_db_path, decrypted_db_path, password, logger):
    try:
        encrypted_conn = sqlcipher.connect(encrypted_db_path)
        encrypted_conn.execute(f"PRAGMA key = '{password}'")
        
        encrypted_conn.execute("SELECT count(*) FROM sqlite_master;")
        
        encrypted_conn.execute(f"ATTACH DATABASE '{decrypted_db_path}' AS decrypted KEY '';")
        
        encrypted_conn.execute("SELECT sqlcipher_export('decrypted');")
        encrypted_conn.execute("DETACH DATABASE decrypted;")
        
        logger.info(f"Decryption completed successfully. Decrypted database is at '{decrypted_db_path}'")
    except Exception as e:
        logger.exception(f"decrypt sqlcipher failed: {e}")
    finally:
        if encrypted_conn:
            encrypted_conn.close()