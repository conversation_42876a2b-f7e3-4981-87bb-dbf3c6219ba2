from exts import Base, Session
from sqlalchemy import (
    Column,
    String,
    UUID,
    LargeBinary,
    TIMESTAMP,
    Integer,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func


class CeleryTaskBacklog(Base):
    __tablename__ = "celery_task_backlog"

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.uuid_generate_v4())
    scan_policy_id = Column(UUID(as_uuid=True), nullable=False)
    session_key = Column(UUID(as_uuid=True), nullable=False)
    file_info = Column(JSONB, nullable=False)
    backlog_hash = Column(String(40), nullable=False)
    params = Column(LargeBinary, nullable=False)
    dispatch_count = Column(Integer, nullable=False)
    created_at = Column(TIMESTAMP(timezone=True), nullable=False)


def get_backlogs(scan_policy_id: str, session_key: str) -> list[CeleryTaskBacklog]:
    """
    Retrieve CeleryTaskBacklog records for a specific scan policy and session key.

    Args:
        scan_policy_id (str): The UUID of the scan policy to filter by.
        session_key (str): The UUID of the session key to filter by.

    Returns:
        list[CeleryTaskBacklog]: A list of CeleryTaskBacklog objects that match the
        given criteria. Returns an empty list if no matching records are found.
    """
    with Session() as session:
        records = session.query(CeleryTaskBacklog).filter_by(scan_policy_id=scan_policy_id, session_key=session_key).all()
        if not records:
            return []
        return records
