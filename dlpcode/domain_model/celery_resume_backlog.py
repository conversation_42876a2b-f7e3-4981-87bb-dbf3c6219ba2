from exts import Base
from sqlalchemy import (
    Column,
    String,
    UUID,
    LargeBinary,
    TIMESTAMP,
    Integer,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func


class CeleryResumeBacklog(Base):
    __tablename__ = "celery_resume_backlog"

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.uuid_generate_v4())
    scan_policy_id = Column(UUID(as_uuid=True), nullable=False)
    session_key = Column(UUID(as_uuid=True), nullable=False)
    file_info = Column(JSONB, nullable=False)
    backlog_hash = Column(String(40), nullable=False)
    params = Column(LargeBinary, nullable=False)
    dispatch_count = Column(Integer, nullable=False)
    created_at = Column(TIMESTAMP(timezone=True), nullable=False)
