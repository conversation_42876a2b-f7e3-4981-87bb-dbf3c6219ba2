{"kind": "admin#reports#activity", "id": {"time": "2025-08-08T09:34:35.791Z", "uniqueQualifier": "7434810424890129209", "applicationName": "drive", "customerId": "C01birj3q"}, "etag": "\"s1ft4A7g325PQYoK4kPw8-aoLZMKf8MvgO2neaLp3Ds/MKaFlWFOfUjU_VgrbgfK2cMds9k\"", "actor": {"email": "<EMAIL>", "profileId": "101837953802581287956"}, "ipAddress": "***************", "networkInfo": {"ipAsn": [4657], "regionCode": "SG", "subdivisionCode": ""}, "events": [{"type": "access", "name": "edit", "parameters": [{"name": "primary_event", "boolValue": false}, {"name": "billable", "boolValue": true}, {"name": "owner_is_shared_drive", "boolValue": false}, {"name": "owner", "value": "<EMAIL>"}, {"name": "doc_id", "value": "1nvyNa-NHnpoccI-LjRxyNMa7uvyMqNcM"}, {"name": "doc_type", "value": "unknown"}, {"name": "is_encrypted", "boolValue": false}, {"name": "doc_title", "value": "5512345_04773f0e_d2f4742d_2_meta.csv"}, {"name": "visibility", "value": "people_with_link"}, {"name": "originating_app_id", "value": "************"}, {"name": "actor_is_collaborator_account", "boolValue": false}, {"name": "owner_is_team_drive", "boolValue": false}], "resourceIds": ["1nvyNa-NHnpoccI-LjRxyNMa7uvyMqNcM"]}, {"type": "acl_change", "name": "change_user_access", "parameters": [{"name": "primary_event", "boolValue": true}, {"name": "billable", "boolValue": true}, {"name": "visibility_change", "value": "none"}, {"name": "target_user", "value": "<EMAIL>"}, {"name": "old_value", "multiValue": ["none"]}, {"name": "new_value", "multiValue": ["can_edit"]}, {"name": "old_visibility", "value": "people_with_link"}, {"name": "owner_is_shared_drive", "boolValue": false}, {"name": "owner", "value": "<EMAIL>"}, {"name": "doc_id", "value": "1nvyNa-NHnpoccI-LjRxyNMa7uvyMqNcM"}, {"name": "doc_type", "value": "unknown"}, {"name": "is_encrypted", "boolValue": false}, {"name": "doc_title", "value": "5512345_04773f0e_d2f4742d_2_meta.csv"}, {"name": "visibility", "value": "people_with_link"}, {"name": "originating_app_id", "value": "************"}, {"name": "actor_is_collaborator_account", "boolValue": false}, {"name": "owner_is_team_drive", "boolValue": false}], "resourceIds": ["1nvyNa-NHnpoccI-LjRxyNMa7uvyMqNcM"]}, {"type": "acl_change", "name": "change_user_access", "parameters": [{"name": "primary_event", "boolValue": true}, {"name": "billable", "boolValue": true}, {"name": "visibility_change", "value": "none"}, {"name": "target_user", "value": "<EMAIL>"}, {"name": "old_value", "multiValue": ["none"]}, {"name": "new_value", "multiValue": ["can_edit"]}, {"name": "old_visibility", "value": "people_with_link"}, {"name": "owner_is_shared_drive", "boolValue": false}, {"name": "owner", "value": "<EMAIL>"}, {"name": "doc_id", "value": "1nvyNa-NHnpoccI-LjRxyNMa7uvyMqNcM"}, {"name": "doc_type", "value": "unknown"}, {"name": "is_encrypted", "boolValue": false}, {"name": "doc_title", "value": "5512345_04773f0e_d2f4742d_2_meta.csv"}, {"name": "visibility", "value": "people_with_link"}, {"name": "originating_app_id", "value": "************"}, {"name": "actor_is_collaborator_account", "boolValue": false}, {"name": "owner_is_team_drive", "boolValue": false}], "resourceIds": ["1nvyNa-NHnpoccI-LjRxyNMa7uvyMqNcM"]}, {"type": "acl_change", "name": "change_user_access", "parameters": [{"name": "primary_event", "boolValue": true}, {"name": "billable", "boolValue": true}, {"name": "visibility_change", "value": "none"}, {"name": "target_user", "value": "<EMAIL>"}, {"name": "old_value", "multiValue": ["none"]}, {"name": "new_value", "multiValue": ["can_edit"]}, {"name": "old_visibility", "value": "people_with_link"}, {"name": "owner_is_shared_drive", "boolValue": false}, {"name": "owner", "value": "<EMAIL>"}, {"name": "doc_id", "value": "1nvyNa-NHnpoccI-LjRxyNMa7uvyMqNcM"}, {"name": "doc_type", "value": "unknown"}, {"name": "is_encrypted", "boolValue": false}, {"name": "doc_title", "value": "5512345_04773f0e_d2f4742d_2_meta.csv"}, {"name": "visibility", "value": "people_with_link"}, {"name": "originating_app_id", "value": "************"}, {"name": "actor_is_collaborator_account", "boolValue": false}, {"name": "owner_is_team_drive", "boolValue": false}], "resourceIds": ["1nvyNa-NHnpoccI-LjRxyNMa7uvyMqNcM"]}], "resourceDetails": [{"id": "1nvyNa-NHnpoccI-LjRxyNMa7uvyMqNcM", "title": "5512345_04773f0e_d2f4742d_2_meta.csv", "type": "DRIVE_ITEM", "relation": "DRIVE_PRIMARY"}]}