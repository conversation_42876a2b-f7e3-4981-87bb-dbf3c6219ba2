{"kind": "admin#reports#activity", "id": {"time": "2025-08-08T08:46:06.484Z", "uniqueQualifier": "-3057200259021121908", "applicationName": "drive", "customerId": "C01birj3q"}, "etag": "\"s1ft4A7g325PQYoK4kPw8-aoLZMKf8MvgO2neaLp3Ds/dHGKR15wqaoCisBIm9G3vE2ypWM\"", "actor": {"email": "<EMAIL>", "profileId": "101837953802581287956"}, "ipAddress": "***************", "networkInfo": {"ipAsn": [4657], "regionCode": "SG", "subdivisionCode": ""}, "events": [{"type": "acl_change", "name": "change_document_visibility", "parameters": [{"name": "primary_event", "boolValue": false}, {"name": "billable", "boolValue": true}, {"name": "visibility_change", "value": "internal"}, {"name": "target_domain", "value": "all"}, {"name": "old_value", "multiValue": ["people_with_link"]}, {"name": "new_value", "multiValue": ["private"]}, {"name": "old_visibility", "value": "people_with_link"}, {"name": "owner_is_shared_drive", "boolValue": false}, {"name": "owner", "value": "<EMAIL>"}, {"name": "doc_id", "value": "13mAmaGOcH858eEgASOgh5tTQ-FQSobox"}, {"name": "doc_type", "value": "txt"}, {"name": "is_encrypted", "boolValue": false}, {"name": "doc_title", "value": "1. PAYMENT INSTRUCTIONS.txt"}, {"name": "visibility", "value": "private"}, {"name": "originating_app_id", "value": "************"}, {"name": "actor_is_collaborator_account", "boolValue": false}, {"name": "owner_is_team_drive", "boolValue": false}], "resourceIds": ["13mAmaGOcH858eEgASOgh5tTQ-FQSobox"]}, {"type": "acl_change", "name": "change_document_access_scope", "parameters": [{"name": "primary_event", "boolValue": false}, {"name": "billable", "boolValue": true}, {"name": "visibility_change", "value": "internal"}, {"name": "target_domain", "value": "all"}, {"name": "old_value", "multiValue": ["can_view"]}, {"name": "new_value", "multiValue": ["none"]}, {"name": "old_visibility", "value": "people_with_link"}, {"name": "owner_is_shared_drive", "boolValue": false}, {"name": "owner", "value": "<EMAIL>"}, {"name": "doc_id", "value": "13mAmaGOcH858eEgASOgh5tTQ-FQSobox"}, {"name": "doc_type", "value": "txt"}, {"name": "is_encrypted", "boolValue": false}, {"name": "doc_title", "value": "1. PAYMENT INSTRUCTIONS.txt"}, {"name": "visibility", "value": "private"}, {"name": "originating_app_id", "value": "************"}, {"name": "actor_is_collaborator_account", "boolValue": false}, {"name": "owner_is_team_drive", "boolValue": false}], "resourceIds": ["13mAmaGOcH858eEgASOgh5tTQ-FQSobox"]}, {"type": "access", "name": "move", "parameters": [{"name": "primary_event", "boolValue": true}, {"name": "billable", "boolValue": true}, {"name": "source_folder_title", "multiValue": ["test"]}, {"name": "source_folder_id", "multiValue": ["1mXG_rerPW7ejtcQmXaqbjNKdR-Fol__a"]}, {"name": "destination_folder_title", "multiValue": ["test-quarantine"]}, {"name": "destination_folder_id", "multiValue": ["1cgKwuNEbLnYthVMMptiWWPfyBZNJZXnX"]}, {"name": "owner_is_shared_drive", "boolValue": false}, {"name": "owner", "value": "<EMAIL>"}, {"name": "doc_id", "value": "13mAmaGOcH858eEgASOgh5tTQ-FQSobox"}, {"name": "doc_type", "value": "txt"}, {"name": "is_encrypted", "boolValue": false}, {"name": "doc_title", "value": "1. PAYMENT INSTRUCTIONS.txt"}, {"name": "visibility", "value": "private"}, {"name": "originating_app_id", "value": "************"}, {"name": "actor_is_collaborator_account", "boolValue": false}, {"name": "owner_is_team_drive", "boolValue": false}], "resourceIds": ["13mAmaGOcH858eEgASOgh5tTQ-FQSobox"]}], "resourceDetails": [{"id": "13mAmaGOcH858eEgASOgh5tTQ-FQSobox", "title": "1. PAYMENT INSTRUCTIONS.txt", "type": "DRIVE_ITEM", "relation": "DRIVE_PRIMARY"}]}