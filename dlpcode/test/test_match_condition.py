import unittest
import sys
import os


sys.path.append(os.path.abspath(os.path.join(
    os.path.dirname(__file__), 
    '../../cooked/python3/lib/python3.10/site-packages')))

from service.data_classifier_engine_service import MatchCondition


class TestFilePathMatch(unittest.TestCase):
    def make_record(self, file_name: str):
        return {
            "file_info": {
                "file_name": file_name,
                "mime_type": "text/plain",
            },
            "detection_result": {
                "result": {},
                "idm": set(),
                "edm": set(),
            }
        }


    def test_simple_txt_match(self):
        cond = MatchCondition(type="file_path", file_path_pattern="*.txt")
        record = self.make_record("notes.txt")
        self.assertTrue(cond.match(record))

    def test_no_match_for_csv(self):
        cond = MatchCondition(type="file_path", file_path_pattern="*.csv")
        record = self.make_record("notes.txt")
        self.assertFalse(cond.match(record))

    def test_recursive_match(self):
        cond = MatchCondition(type="file_path", file_path_pattern="**/secrets/*.log")
        record = self.make_record("var/data/secrets/hidden.log")
        self.assertTrue(cond.match(record))

    def test_negation_included(self):
        cond = MatchCondition(type="file_path", file_path_pattern=["**/secrets/*.log", "!**/secrets/ignore.log"])
        record = self.make_record("var/data/secrets/important.log")
        self.assertTrue(cond.match(record))

    def test_negation_excluded(self):
        cond = MatchCondition(type="file_path", file_path_pattern=["**/secrets/*.log", "!**/secrets/ignore.log"])
        record = self.make_record("var/data/secrets/ignore.log")
        self.assertFalse(cond.match(record))

    def test_multiple_patterns_match_one(self):
        cond = MatchCondition(type="file_path", file_path_pattern=["*.log", "*.txt"])
        record = self.make_record("readme.txt")
        self.assertTrue(cond.match(record))

    def test_missing_pattern_key(self):
        cond = MatchCondition(type="file_path")
        record = self.make_record("readme.txt")
        self.assertFalse(cond.match(record))

    def test_pattern_as_string(self):
        cond = MatchCondition(type="file_path", file_path_pattern="*.log")
        record = self.make_record("system.log")
        self.assertTrue(cond.match(record))

    def test_invalid_pattern(self):
        cond = MatchCondition(type="file_path", file_path_pattern=["[invalid[pattern"])
        record = self.make_record("test.txt")
        self.assertFalse(cond.match(record))

    def test_dotfile_match(self):
        cond = MatchCondition(type="file_path", file_path_pattern=".*")
        record = self.make_record(".env")
        self.assertTrue(cond.match(record))

    def test_deep_nested_match(self):
        cond = MatchCondition(type="file_path", file_path_pattern="**/logs/*.log")
        record = self.make_record("x/y/z/logs/app.log")
        self.assertTrue(cond.match(record))

    def test_empty_pattern_string(self):
        cond = MatchCondition(type="file_path", file_path_pattern="")
        record = self.make_record("data.csv")
        self.assertFalse(cond.match(record))

    def test_directory_pattern_match(self):
        cond = MatchCondition(type="file_path", file_path_pattern="**/logs/")
        record = self.make_record("a/b/logs/")
        self.assertTrue(cond.match(record))

    def test_long_nested_path_match(self):
        cond = MatchCondition(type="file_path", file_path_pattern="**/2023/**/logs/*.log")
        record = self.make_record("backup/2023/sep/server/logs/access.log")
        self.assertTrue(cond.match(record))

    def test_special_characters_in_filename(self):
        cond = MatchCondition(type="file_path", file_path_pattern="**/tmp/*[!@#$%^&*()].log")
        record = self.make_record("archive/tmp/debug!@#.log")
        self.assertFalse(cond.match(record))  # should fail due to special chars not being matched

    def test_unicode_filename_match(self):
        cond = MatchCondition(type="file_path", file_path_pattern="**/*.日誌")
        record = self.make_record("系统/2023/運行日誌.日誌")
        self.assertTrue(cond.match(record))

    def test_dot_directory_handling(self):
        cond = MatchCondition(type="file_path", file_path_pattern="**/.config/*.json")
        record = self.make_record("home/user/.config/settings.json")
        self.assertTrue(cond.match(record))

    def test_exclude_multiple_patterns(self):
        cond = MatchCondition(type="file_path", file_path_pattern=[
            "**/*.csv",
            "!**/archive/*.csv",
            "!**/ignore/*.csv"
        ])
        record1 = self.make_record("data/results.csv")
        record2 = self.make_record("data/archive/results.csv")
        record3 = self.make_record("data/ignore/results.csv")
        self.assertTrue(cond.match(record1))
        self.assertFalse(cond.match(record2))
        self.assertFalse(cond.match(record3))

    def test_absolute_path_match(self):
        cond = MatchCondition(type="file_path", file_path_pattern="/var/log/**/*.log")
        record = self.make_record("/var/log/system/security/error.log")
        self.assertTrue(cond.match(record))

    def test_empty_file_name(self):
        cond = MatchCondition(type="file_path", file_path_pattern="**/*.log")
        record = self.make_record("")
        self.assertFalse(cond.match(record))

    def test_directory_trailing_slash(self):
        cond = MatchCondition(type="file_path", file_path_pattern="**/images/")
        record = self.make_record("static/assets/images/")
        self.assertTrue(cond.match(record))

    def test_filename_with_spaces(self):
        cond = MatchCondition(type="file_path", file_path_pattern="**/legal docs/*.pdf")
        record = self.make_record("clients/legal docs/agreement v2.pdf")
        self.assertTrue(cond.match(record))

if __name__ == '__main__':
    unittest.main(verbosity=2)