"""
Celery application configuration for DLP system
"""
import os
from celery import Celery
from util.config import configs
from util.common_log import get_logger

logger = get_logger("celery")
celery_config = configs.get("celery", {})

# Create Celery application
app = Celery(
    main='dspm_celery',
    broker = celery_config.get("broker_url", "redis://localhost:6379/0"),
    backend = celery_config.get("result_backend", "redis://localhost:6379/0"),
)

routes = {
    **celery_config.get("task_routes", {})
}

schedules = {
    **celery_config.get("beat_schedule", {})
}

app.conf.update(
    task_acks_late=celery_config.get("task_acks_late", True),
    accept_content=['json'],
    task_routes=routes,
    # Beat schedule for periodic tasks
    beat_schedule=schedules,
    # Result expiration
    result_expires=3600,

    task_default_queue=celery_config.get("task_default_queue", "default"),

    # Worker configuration
    worker_prefetch_multiplier=celery_config.get("worker_prefetch_multiplier", 1),
    worker_disable_rate_limits=celery_config.get("worker_disable_rate_limits", False),
    task_reject_on_worker_lost=celery_config.get("task_reject_on_worker_lost", True),

    # Task timeouts
    task_soft_time_limit=celery_config.get("task_soft_time_limit", 300),
    task_time_limit=celery_config.get("task_time_limit", 600),
    worker_max_tasks_per_child=celery_config.get("worker_max_tasks_per_child", 1000),

    # Priority configuration
    task_inherit_parent_priority=True,
    task_default_priority=5,
    worker_direct=True,

    # Task compression
    task_compression='gzip',
    result_compression='gzip',
)

# Auto-discover tasks from celery_worker modules
app.autodiscover_tasks([
    'celery_worker.analyze_worker',
    'celery_worker.ddr_task',
    'celery_worker.dispatch_download_task',
    'celery_worker.doing_download_task',
    'celery_worker.fetch_storage',
    'celery_worker.protection_action_task',
    'celery_worker.report_generation_task',
    'celery_worker.tag_worker',
    'celery_worker.unified_file_process'
])

# Task result ignore configuration
app.conf.task_ignore_result = False

# Configure logging
app.conf.worker_log_format = '[%(asctime)s: %(levelname)s/%(processName)s] %(message)s'
app.conf.worker_task_log_format = '[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s'

logger.info("Celery application configured successfully")

if __name__ == '__main__':
    app.start()
