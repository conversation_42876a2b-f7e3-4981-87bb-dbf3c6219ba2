"""
Download Worker - Processes download tasks from queue
Only handles queue data processing, no task submission interfaces
"""
import os
import json
import hashlib
import threading
from pathlib import Path
from datetime import datetime

from celery_worker.celery_app import app
from domain_model.celery_task_backlog import CeleryTaskBacklog
from domain_model.task_queue import ChangeResultEvent, ChangeStatusEvent
from exts import logger, Session, get_logger
from service.task_management_service import TaskManagementService, SessionExpired
from service.task_queue_service import TaskQueueService
from util.config import configs, get_supported_file_type
from util.enum_ import ScanResult, TaskStatus
from util.utils import get_utc_timestamp

skip_files = get_logger("skip_files")
logger = get_logger("download_worker")
is_file_unified = configs.get("file_unified_queue", {}).get("enabled", False)


@app.task(bind=True, priority=5, queue='download')
def dispatch_download_task(self, task_uuid: str, group_file_path: str, 
                          file_path: str, params: dict, session_key: str):
    """
    Dispatch download task - processes file dispatch from queue
    
    Args:
        task_uuid: Task UUID
        group_file_path: Group file path
        file_path: File path
        params: Task parameters
        session_key: Session key
    """
    try:
        logger.info(f"Processing dispatch download task: {task_uuid}")
        
        # Process the dispatch logic
        result = do_dispatch_download(task_uuid, group_file_path, file_path, params, session_key)
        
        logger.info(f"Dispatch download task completed: {task_uuid}")
        return result
        
    except Exception as e:
        logger.error(f"Error in dispatch download task {task_uuid}: {e}")
        raise


@app.task(bind=True, priority=5, queue='download')
def download_file_task(self, task_uuid: str, file_info: dict, params: dict,
                      session_key: str, backlog_hash: str):
    """
    Download file task - processes file download from queue
    
    Args:
        task_uuid: Task UUID
        file_info: File information
        params: Task parameters
        session_key: Session key
        backlog_hash: Backlog hash
    """
    try:
        logger.info(f"Processing download file task: {task_uuid}, file: {file_info.get('file_name', 'unknown')}")
        
        # Process the download logic
        result = do_download_file(task_uuid, file_info, params, session_key, backlog_hash)
        
        logger.info(f"Download file task completed: {task_uuid}")
        return result
        
    except Exception as e:
        logger.error(f"Error in download file task {task_uuid}: {e}")
        raise


@app.task(bind=True)
def ddr_download_file_task(self, task_uuid: str, file_info: dict, params: dict,
                          session_key: str, backlog_hash: str):
    """
    DDR download file task - processes DDR file download with high priority
    
    Args:
        task_uuid: Task UUID
        file_info: File information
        params: Task parameters
        session_key: Session key
        backlog_hash: Backlog hash
    """
    try:
        logger.info(f"Processing DDR download file task: {task_uuid}, file: {file_info.get('file_name', 'unknown')}")
        
        # Process the download logic with DDR priority
        result = do_download_file(task_uuid, file_info, params, session_key, backlog_hash, is_ddr=True)
        
        logger.info(f"DDR download file task completed: {task_uuid}")
        return result
        
    except Exception as e:
        logger.error(f"Error in DDR download file task {task_uuid}: {e}")
        raise


def do_dispatch_download(task_uuid: str, group_file_path: str, file_path: str, params: dict, session_key: str):
    """
    Process dispatch download logic
    
    Args:
        task_uuid: Task UUID
        group_file_path: Group file path
        file_path: File path
        params: Task parameters
        session_key: Session key
    """
    try:
        # Validate session
        tms = TaskManagementService(task_uuid, session_key)
        if not tms.is_scanning():
            logger.info(f"Dispatch download end, task: {task_uuid} is not in SCANNING status.")
            return {"status": "skipped", "reason": "not_scanning"}

        # Process file dispatch logic
        # This would include the actual dispatch implementation
        logger.debug(f"Dispatching file: {file_path} for task: {task_uuid}")
        
        # Create backlog entry
        backlog_hash = hashlib.md5(f"{task_uuid}:{file_path}".encode()).hexdigest()
        
        with Session() as session:
            backlog = CeleryTaskBacklog(
                scan_policy_id=task_uuid,
                session_key=session_key,
                file_info={"file_path": file_path, "group_file_path": group_file_path},
                backlog_hash=backlog_hash,
                params=json.dumps(params).encode('utf-8'),
                dispatch_count=0,
                created_at=get_utc_timestamp()
            )
            session.add(backlog)
            session.commit()
        
        return {"status": "dispatched", "backlog_hash": backlog_hash}
        
    except SessionExpired:
        logger.error(f"Dispatch download end, task: {task_uuid} session expired!")
        raise
    except Exception as e:
        logger.error(f"Error in dispatch download: {e}")
        raise


def do_download_file(task_uuid: str, file_info: dict, params: dict, 
                    session_key: str, backlog_hash: str, is_ddr: bool = False):
    """
    Process file download logic
    
    Args:
        task_uuid: Task UUID
        file_info: File information
        params: Task parameters
        session_key: Session key
        backlog_hash: Backlog hash
        is_ddr: Whether this is a DDR task
    """
    try:
        # Validate session
        tms = TaskManagementService(task_uuid, session_key)
        if not tms.is_scanning():
            logger.info(f"Download file end, task: {task_uuid} is not in SCANNING status.")
            return {"status": "skipped", "reason": "not_scanning"}

        # Process file download logic
        file_name = file_info.get('file_name', 'unknown')
        logger.debug(f"Downloading file: {file_name} for task: {task_uuid}")
        
        # Create download directory
        download_dir = Path(configs["download_queue"]["file_download_path"]) / task_uuid
        if is_ddr:
            download_dir = Path(configs["download_queue"]["ddr_file_download_path"]) / task_uuid
        
        download_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate local file path
        file_uuid = file_info.get('file_uuid', hashlib.md5(file_name.encode()).hexdigest())
        local_file_path = download_dir / f"{file_uuid}_{file_name}"
        
        # Simulate download process (actual implementation would download from remote)
        # For now, just create a placeholder file
        local_file_path.touch()
        
        # Submit to analyze queue after download
        if is_file_unified:
            # Use unified file scheduler
            from service.unified_file_service import UnifiedFileScheduler, DDRFileItem, ScanPolicyFileItem
            scheduler = UnifiedFileScheduler()
            
            if is_ddr:
                ddr_item = DDRFileItem(
                    task_uuid=task_uuid,
                    file_info=file_info,
                    params=params,
                    session_key=session_key,
                    backlog_hash=backlog_hash,
                    priority=8  # DDR priority
                )
                scheduler.add_ddr_file(ddr_item)
            else:
                scan_item = ScanPolicyFileItem(
                    task_uuid=task_uuid,
                    file_info=file_info,
                    params=params,
                    session_key=session_key,
                    backlog_hash=backlog_hash,
                    priority=5  # Normal priority
                )
                scheduler.add_scan_policy_file(scan_item)
        else:
            # Submit directly to analyze queue
            from tasks.analyze_tasks import submit_analyze_task, submit_priority_analyze_task
            
            scan_info = params.get('scan_info', {})
            scan_info['is_ddr'] = is_ddr
            
            if is_ddr:
                submit_priority_analyze_task(
                    local_file=str(local_file_path),
                    remote_info=file_info,
                    file_uuid=file_uuid,
                    scan_info=scan_info,
                    task_uuid=task_uuid,
                    session_key=session_key,
                    backlog_hash=backlog_hash
                )
            else:
                submit_analyze_task(
                    local_file=str(local_file_path),
                    remote_info=file_info,
                    file_uuid=file_uuid,
                    scan_info=scan_info,
                    task_uuid=task_uuid,
                    session_key=session_key,
                    backlog_hash=backlog_hash,
                    priority='NORMAL'
                )
        
        return {
            "status": "downloaded",
            "local_file": str(local_file_path),
            "file_uuid": file_uuid,
            "submitted_to_analyze": True
        }
        
    except SessionExpired:
        logger.error(f"Download file end, task: {task_uuid} session expired!")
        raise
    except Exception as e:
        logger.error(f"Error in download file: {e}")
        raise
