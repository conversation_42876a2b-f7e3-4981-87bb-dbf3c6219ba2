"""
Miscellaneous Workers - Processes various tasks from queues
Only handles queue data processing, no task submission interfaces
"""
from celery_worker.celery_app import app
from connector.aws_connector import AWSConnector
from connector.sharepoint_connector import SharePointConnector
from connector.sharepoint_token_connector import SharePointTokenConnector
from util.enum_ import StorageType
from util.config import get_global_config
from exts import logger
from reports import generate_report
from util.common_log import get_logger

configs = get_global_config()
tag_logger = get_logger("tag_worker")
report_logger = get_logger("report_worker")
protection_logger = get_logger("protection_worker")


@app.task(bind=True, priority=5, queue='default')
def update_remote_tag_task(self, remote_info, tags):
    """Update remote file tags - processes tag update from queue"""
    try:
        tag_logger.info(f"Processing tag update for file: {remote_info.get('file', 'unknown')}")
        
        if remote_info["type"] == "aws":
            aws_connector = AWSConnector(remote_info)
            aws_connector.update_file_tag(remote_info['file'], tags)
            tag_logger.info(f"Updated AWS tag for file {remote_info['file']} to {tags}")
        else:
            if remote_info["storage_type"] == StorageType.SHAREPOINT_OL:
                sp_connector = SharePointTokenConnector(remote_info)
            elif remote_info["storage_type"] == StorageType.SHAREPOINT_OP:
                sp_connector = SharePointConnector(remote_info)
            if remote_info["storage_type"] == StorageType.SHAREPOINT_OL and remote_info["usecredentials"] == True:
                sp_connector = SharePointConnector(remote_info)
            sp_connector.update_file_tag(remote_info['file'], tags)
            tag_logger.info(f"Updated SharePoint tag for file {remote_info['file']} to {tags}")
            
        return {"status": "success", "file": remote_info['file'], "tags": tags}
        
    except KeyError as e:
        tag_logger.error(f"Missing key in remote_info: {str(e)}")
        raise
    except Exception as e:
        tag_logger.error(f"Error updating file tag: {str(e)}")
        raise


@app.task(bind=True, priority=5, queue='report')
def generate_report_task(self, report_id: str) -> bool:
    """
    Generate report task - processes report generation from queue
    """
    try:
        report_logger.info(f"Processing report generation for report_id: {report_id}")
        
        # Call the report generation API
        result = generate_report.generate_new_report(report_id)
        
        if result:
            report_logger.info(f"Report generated successfully: {result}")
            return {"status": "success", "report_id": report_id, "result": result}
        else:
            report_logger.error(f"Report generation failed: {result}")
            return {"status": "failed", "report_id": report_id, "result": result}

    except Exception as e:
        report_logger.error(f"Error generating report {report_id}: {e}", exc_info=True)
        raise


@app.task(bind=True, priority=7, queue='protection')
def protection_action_task(self, task_uuid: str, file_uuid: str, local_file_copy: str,
                          file_info: dict, scan_info: dict, remote_info: dict,
                          matched_result: dict):
    """
    Protection action task - processes protection actions from queue
    """
    try:
        protection_logger.info(f"Processing protection action for task: {task_uuid}, file: {file_uuid}")
        
        # Process protection actions
        result = do_protection_actions(
            task_uuid, file_uuid, local_file_copy, 
            file_info, scan_info, remote_info, matched_result
        )
        
        protection_logger.info(f"Protection action completed for task: {task_uuid}")
        return result
        
    except Exception as e:
        protection_logger.error(f"Error in protection action task {task_uuid}: {e}")
        raise


@app.task(bind=True, priority=5, queue='default')
def fetch_storage_task(self, storage_config: dict):
    """
    Fetch storage task - processes storage fetching from queue
    """
    try:
        storage_logger = get_logger("fetch_storage")
        storage_logger.info(f"Processing fetch storage task for storage: {storage_config.get('name', 'unknown')}")
        
        # Process storage fetching logic
        result = do_fetch_storage(storage_config)
        
        storage_logger.info(f"Fetch storage task completed")
        return result
        
    except Exception as e:
        storage_logger = get_logger("fetch_storage")
        storage_logger.error(f"Error in fetch storage task: {e}")
        raise


@app.task(bind=True, priority=5, queue='default')
def unified_file_process_task(self):
    """
    Unified file processing task - processes unified file queue
    """
    try:
        unified_logger = get_logger("unified_file")
        unified_logger.info("Processing unified file processing task")
        
        # Process unified file logic
        result = do_unified_file_processing()
        
        unified_logger.info("Unified file processing task completed")
        return result
        
    except Exception as e:
        unified_logger = get_logger("unified_file")
        unified_logger.error(f"Error in unified file processing task: {e}")
        raise


def do_protection_actions(task_uuid: str, file_uuid: str, local_file_copy: str,
                         file_info: dict, scan_info: dict, remote_info: dict,
                         matched_result: dict):
    """
    Process protection actions logic
    """
    try:
        remediation_actions = matched_result.get('remediation_actions', [])
        results = []
        
        for action in remediation_actions:
            if action == "file_quarantine":
                # Process quarantine action
                result = process_quarantine_action(task_uuid, file_uuid, local_file_copy, remote_info)
                results.append({"action": "quarantine", "result": result})
                
            elif action == "file_copy":
                # Process copy action
                result = process_copy_action(task_uuid, file_uuid, local_file_copy, remote_info)
                results.append({"action": "copy", "result": result})
                
            elif action == "notification":
                # Process notification action
                result = process_notification_action(task_uuid, file_uuid, file_info, matched_result)
                results.append({"action": "notification", "result": result})
        
        return {"status": "completed", "actions": results}
        
    except Exception as e:
        protection_logger.error(f"Error processing protection actions: {e}")
        raise


def do_fetch_storage(storage_config: dict):
    """
    Process storage fetching logic
    """
    try:
        storage_type = storage_config.get('type')
        storage_id = storage_config.get('id')
        
        # Process different storage types
        if storage_type == 'sharepoint':
            result = fetch_sharepoint_storage(storage_config)
        elif storage_type == 'aws':
            result = fetch_aws_storage(storage_config)
        elif storage_type == 'google':
            result = fetch_google_storage(storage_config)
        else:
            raise ValueError(f"Unsupported storage type: {storage_type}")
        
        return {"status": "completed", "storage_id": storage_id, "result": result}
        
    except Exception as e:
        raise


def do_unified_file_processing():
    """
    Process unified file queue
    """
    try:
        from service.unified_file_service import UnifiedFileScheduler
        
        scheduler = UnifiedFileScheduler()
        processed_count = 0
        
        # Process files from unified queue
        while True:
            file_item = scheduler.get_next_file_to_process()
            if not file_item:
                break
                
            # Process the file item
            if file_item['source_type'] == 'ddr':
                process_ddr_file_item(file_item)
            else:
                process_scan_policy_file_item(file_item)
                
            processed_count += 1
            
            # Limit processing per task
            if processed_count >= 10:
                break
        
        return {"status": "completed", "processed_count": processed_count}
        
    except Exception as e:
        raise


# Helper functions (abbreviated - would include full implementations)
def process_quarantine_action(task_uuid, file_uuid, local_file_copy, remote_info):
    """Process file quarantine"""
    pass

def process_copy_action(task_uuid, file_uuid, local_file_copy, remote_info):
    """Process file copy"""
    pass

def process_notification_action(task_uuid, file_uuid, file_info, matched_result):
    """Process notification"""
    pass

def fetch_sharepoint_storage(storage_config):
    """Fetch SharePoint storage"""
    pass

def fetch_aws_storage(storage_config):
    """Fetch AWS storage"""
    pass

def fetch_google_storage(storage_config):
    """Fetch Google storage"""
    pass

def process_ddr_file_item(file_item):
    """Process DDR file item"""
    pass

def process_scan_policy_file_item(file_item):
    """Process scan policy file item"""
    pass
