"""
Analyze Worker - Processes file analysis tasks from queue
Only handles queue data processing, no task submission interfaces
"""
import os
import sys
import time
import threading
from datetime import datetime
from pathlib import Path
from cachetools import TTLCache

from celery import Task
from celery.signals import worker_ready, worker_shutdown
from celery_worker.celery_app import app

from service.data_label_service import LabelFetcher
from file_transformer.file_extract import get_file_info
from service.custom_datatype_service import UserDefineDatatypeService
from util.common_log import get_logger
from util.random_password.service import RandomPasswordService
from util.utils import get_utc_timestamp
from util.config import get_global_config
from util.enum_ import ASBinName
from domain_model.global_cache import GlobalCache
from domain_model.tracker.pid_tracker import AnalyzeWorkerPidTracker
from exts import Session
from service.discovery_engine_v2_service import create_policy_engine, DiscoveryPolicyEngine
from service.discovery_policy_v2_service import get_entity_by_policy
from service.idm_template_service import get_idm_match_info
from service.edm_service import get_edm_match_info
from ddr.service.engine_service import DDRPolicyEngine, ddr_create_policy_engine

configs = get_global_config()
logger = get_logger("analyze_worker")
is_file_unified = configs.get("file_unified_queue", {}).get("enabled", False)

ANALYZE_CACHE_TTL = 900
global_analyze_ttl_cache = TTLCache(maxsize=1000, ttl=ANALYZE_CACHE_TTL)
file_upload_analyze_cache = TTLCache(maxsize=20, ttl=float('inf'))

global_discover_engine_ttl_cache = TTLCache(maxsize=1000, ttl=600)
global_ddr_engine_ttl_cache = TTLCache(maxsize=1000, ttl=600)

global_data = None
global_data_lock = threading.Lock()


@worker_ready.connect
def startup_handler(sender=None, **kwargs):
    """Initialize worker on startup"""
    from file_analyzer.analyzer_global import AnalyzerGlobal
    from file_analyzer.analyzer_log import setup_logging

    global global_data
    logger.info("Starting analyze_worker")

    try:
        with global_data_lock:
            if global_data is None:
                # setup logger for file_analyzer
                setup_logging(logger=logger, level=configs.get("file_analyzer", {}).get("log_level"))
                
                global_data = AnalyzerGlobal(
                    base_path=configs.get("file_analyzer", {}).get("config_path"),
                    session=Session,
                    use_gpu=configs.get("file_analyzer", {}).get("classifier_use_gpu", True),
                    mod_verify=configs.get("file_analyzer", {}).get("mod_verify", False),
                    sig_pub_key=configs.get("file_analyzer", {}).get("public_key_path", None),
                    classifier_thread_num=configs.get("file_analyzer", {}).get("classifier_thread_num", True),
                    classifier_token_limit=configs.get("file_analyzer", {}).get("classifier_token_limit", 512),
                    minimum_classifier_token=configs.get("file_analyzer", {}).get("minimum_classifier_token", 128),
                    nlp_thread_num=configs.get("file_analyzer", {}).get("nlp_thread_num", True),
                    nlp_recognizer_thread_num=configs.get("file_analyzer", {}).get("nlp_recognizer_thread_num", 1),
                    db_decryption_mode=configs.get("file_analyzer", {}).get("db_decryption_mode", 1)
                )

                LabelFetcher.init()
    except Exception as e:
        logger.error(f"Failed to initialize analyze_worker: {e}")
        sys.exit(1)


def update_cache_result(cache, file_uuid, session_key, scan_info, result, remote_info, analyze_statistic, tags, edm_match_info=None, idm_match_info=None):
    """Update cache with analysis results"""
    logger.debug(f'Scan uuid: {scan_info["scan_uuid"]}')
    match_info = []
    match_info_custom = []
    if result.compliances is not None:
        match_info = result.compliances.get('ENTITIES')
        match_info_custom = result.compliances.get('CUSTOM')

    class_details = {}
    class_details['mc_confidence'] = result.main_class_confidence
    class_details['sc_confidence'] = result.sub_class_confidence
    class_details['main_class_list'] = [result.main_class_id] if result.main_class_id else []
    class_details['main_confidence_list'] = [result.main_class_confidence] if result.main_class_id else []
    class_details['sub_class_list'] = [result.sub_class_id] if result.sub_class_id else []
    class_details['sub_confidence_list'] = [result.sub_class_confidence] if result.sub_class_id else []

    # Build payload for cache update
    payload = {
        "id": file_uuid,
        "file_name": remote_info['file'],
        "mime_type": remote_info['file_type'],
        "match_info": match_info,
        "match_info_custom": match_info_custom,
        "class_details": class_details,
        "analyze_statistic": analyze_statistic,
        "tags": tags,
        "edm_match_info": edm_match_info,
        "idm_match_info": idm_match_info,
        "updated_at": get_utc_timestamp()
    }
    
    cache.put(payload, update_only=True)


@app.task(bind=True)
def do_analyze(local_file, remote_info, file_uuid, scan_info, task_uuid, session_key: str, backlog_hash: str):
    """ 
    Process file analysis - extract content & identify content-type

    Args:
        local_file: Path to the local file to be processed
        remote_info: Remote server information
        file_uuid: Unique identifier (UUID) of the file
        scan_info: Scan configuration information
        task_uuid: Task UUID
        session_key: Session key
        backlog_hash: Backlog hash
    """
    import time
    from file_analyzer.analyzer_target import AnalyzerTarget
    from service.task_management_service import TaskManagementService, SessionExpired
    from file_analyzer.analyzer_result import EmptyResult
    
    cache = None
    updated_result = False
    analyze_statistic = {}

    try:
        logger.debug(f"Analyze worker begin, task: {task_uuid}, pid: {os.getpid()}, remote_info: {remote_info}, local_file: {local_file}, session_key: {session_key}")
        
        tms = TaskManagementService(task_uuid, session_key)
        if not tms.is_scanning():
            logger.info(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()} is not in SCANNING status.")
            return

        if local_file is None:
            logger.error(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()}, local file argument is None.")
            return

        if not os.path.exists(local_file):
            logger.error(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()}, local file {local_file} does not exist.")
            return
        if not os.access(local_file, os.R_OK):
            logger.error(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()}, no permission to read local file {local_file}.")
            return
        
        cache = GlobalCache(scan_info['scan_uuid'], session_key, is_ddr=scan_info.get("is_ddr", False))
        if not cache.get_by_file_uuid(file_uuid=file_uuid):
            logger.error(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()}, file record not found in db {file_uuid}")
            return

        # Extract content & identify content-type
        logger.info(f"Process file {local_file} begin, task: {task_uuid} pid: {os.getpid()}")
        logger.debug(f"Real file name: {remote_info.get('file')}, task: {task_uuid} pid: {os.getpid()}")
                
        analyze_statistic = {}
                
        ts_start = time.time()
        file_info = get_file_info(local_file, remote_info['file'], remote_info['file_type'], configs.get("file_extract", {}))
        content = file_info['file_content']
        if content is None or len(content.strip()) == 0:
            logger.error(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()}, file {local_file} has no content. Skipping further processing.")
            analyze_statistic["message"] = "no content"
            return
        
        ts_start_get_analyzer = time.time()
        analyzer = get_analyze(task_uuid=task_uuid, scan_info=scan_info, session_key=session_key, load_on_miss=False)
        if isinstance(content, bytes):
            content = content.decode('utf-8', 'ignore')
            
        ts_analyze = time.time()
        result = analyzer.analyze(AnalyzerTarget(
            text=content, 
            source=local_file, 
            source_type=file_info['file_type'],
            source_uuid=file_uuid,
            ),
            expected_entities=[],
            encrypt_function=RandomPasswordService.encrypt_analyzer_text)
        
        if result is None:
            logger.error(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()}, analyzer returned None. Skipping further processing for this file.")
            analyze_statistic["message"] = "analyze worker end"
            return
        
        ts_analyze_end = time.time()
        logger.debug(f"Analysis result: {result}, task: {task_uuid} pid: {os.getpid()}")

        # Process analysis results
        record = {
            "file_info": {
                "file_name": remote_info['file'],
                "mime_type": remote_info['file_type'],
                "encryption": remote_info.get("file_encryption", False),
                "file_location": remote_info.get("file_location", "UNKNOWN"),
                "shared_data": remote_info.get("shared_data") if remote_info.get("shared_data") else {}
            },
            "result": result,
            "idm": set(),
            "edm": set()
        }
        
        if scan_info.get("is_ddr", False):
            matched_result = get_ddr_policy_matched_result(task_uuid, scan_info, record)
        else:
            matched_result = get_discover_policy_matched_result(task_uuid, scan_info, record)
        
        tags = matched_result['tags']
        ts_update_cache = time.time()
        
        # Calculate timing statistics
        time_statistic_enable = configs.get("file_analyzer", {}).get("time_statistic", False)
        if time_statistic_enable:
            analyze_statistic["ex_time"] = round(ts_start_get_analyzer-ts_start, 3)  # extract
            analyze_statistic["ga_time"] = round(ts_analyze-ts_start_get_analyzer, 3)  # get analyzer
            analyze_statistic["an_time"] = round(ts_analyze_end-ts_analyze, 3)  # analyze file
            analyze_statistic["ml_time"] = result.ml_elapsed if hasattr(result, 'ml_elapsed') else 0  # file classification
            analyze_statistic["re_time"] = result.an_elapsed if hasattr(result, 'an_elapsed') else 0  # data type scan
            analyze_statistic["ma_time"] = round(ts_update_cache-ts_analyze_end, 3)  # match policies
            analyze_statistic["total"] = round(ts_update_cache-ts_start, 3)  # total time

        idm_match_info = get_idm_match_info(idm_ids=list(record.get("idm")), similarities=result.idm_res)
        edm_match_info = get_edm_match_info(edm_rule_ids=list(record.get("edm")))
        matched_result["edm_match_info"] = edm_match_info
        matched_result["idm_match_info"] = idm_match_info
        
        update_cache_result(cache, file_uuid, session_key, scan_info, result, remote_info, analyze_statistic, tags, edm_match_info=edm_match_info, idm_match_info=idm_match_info)
        updated_result = True

        # Handle protection actions
        remediation_actions = matched_result['remediation_actions']
        local_file_copy = None
        if "file_copy" in remediation_actions or "file_quarantine" in remediation_actions:
            from tasks.misc_tasks import submit_protection_action_task
            submit_protection_action_task(task_uuid, file_uuid, local_file_copy, file_info, scan_info, remote_info, matched_result)
                
    except SessionExpired as e:
        logger.error(f"Analyze worker end, task: {task_uuid} pid: {os.getpid()} session_key: {session_key} session expired!")
        raise
    except KeyboardInterrupt as e:
        logger.exception(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()}, KeyboardInterrupt happen")
        handle_interrupt(local_file, remote_info, file_uuid, scan_info, task_uuid, session_key, backlog_hash)
    except Exception as e:
        logger.exception(f"Analyze worker end, error processing file: {local_file}, task: {task_uuid}, pid: {os.getpid()}")
        raise
    finally:
        if cache and not updated_result:
            update_cache_result(cache, file_uuid, session_key, scan_info, EmptyResult(), remote_info, analyze_statistic, None)


# Helper functions (abbreviated for space - would include full implementations)
def get_analyze(task_uuid, scan_info, session_key, load_on_miss=True):
    """Get analyzer instance"""
    # Implementation would be here
    pass

def get_ddr_policy_matched_result(task_uuid, scan_info, record):
    """Get DDR policy matched results"""
    # Implementation would be here
    pass

def get_discover_policy_matched_result(task_uuid, scan_info, record):
    """Get discovery policy matched results"""
    # Implementation would be here
    pass

def handle_interrupt(local_file, remote_info, file_uuid, scan_info, task_uuid, session_key, backlog_hash):
    """Handle task interruption"""
    # Implementation would be here
    pass
