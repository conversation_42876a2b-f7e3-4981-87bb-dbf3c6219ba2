"""
DDR Worker - Processes DDR tasks from queue
Only handles queue data processing, no task submission interfaces
"""
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta, timezone

from celery_worker.celery_app import app
from ddr.service.ddr_service import DDRService
from ddr.model.task import get_ddr_tasks_dict, update_last_pull_time
from util.common_log import get_logger

logger = get_logger("ddr_worker")


@app.task(bind=True)
def process_ddr_events_task(self, task: dict, 
                           start_time: datetime, end_time: datetime,
                           ddr_policy_ids: Optional[List[str]] = None,
                           target_operations: Optional[List[str]] = None,
                           scan_trigger_events: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Process DDR events task - handles DDR event processing from queue
    
    Args:
        task: DDRTask dict
        start_time: Start time for event processing
        end_time: End time for event processing
        ddr_policy_ids: List of DDR policy IDs
        target_operations: List of target operation types
        scan_trigger_events: List of scan trigger event types
    Returns:
        Result dictionary
    """
    try:
        logger.info(f"Starting DDR event processing for task: {task['name']}")

        ddr_service = DDRService(task)
        # Process platform events
        result = ddr_service.process_events(
            start_time=start_time,
            end_time=end_time,
            target_operations=target_operations,
            scan_trigger_events=scan_trigger_events
        )

        logger.info(f"DDR event processing completed for task {task['name']}: {result}")
        return result

    except Exception as e:
        logger.error(f"Error in DDR event processing task: {e}")
        return {"error": str(e), "processed": 0, "scans_triggered": 0}


@app.task(bind=True)
def scheduled_ddr_dispatcher_task(self):
    """
    DDR task scheduler - checks all active DDR tasks and runs those whose interval has elapsed
    """
    now = datetime.now(timezone.utc)
    try:
        tasks = get_ddr_tasks_dict(enabled=True)
    except Exception as e:
        logger.error(f"Failed to fetch active DDR tasks: {e}")
        return

    processed_count = 0
    for task in tasks:
        try:
            last = now - timedelta(seconds=125)
            
            # Submit DDR event processing task
            from tasks.ddr_tasks import submit_ddr_events_task
            task_id = submit_ddr_events_task(
                task=task,
                start_time=last,
                end_time=now,
                scan_trigger_events=task.get('trigger_events', [])
            )
            
            logger.info(f"Scheduled DDR task {task['name']} with task_id: {task_id}")
            processed_count += 1

        except Exception as e:
            logger.error(f"Error processing DDR task {task['name']}: {e}")
    
    logger.info(f"DDR dispatcher processed {processed_count} tasks")
    return {"processed_tasks": processed_count}
