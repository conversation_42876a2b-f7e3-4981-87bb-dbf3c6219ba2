import os
import pickle
import sys
import uuid
import hashlib
from datetime import datetime
import time
from service.data_label_service import <PERSON><PERSON><PERSON><PERSON>
from celery import Task
from celery.signals import worker_ready, worker_shutdown
from pathlib import Path
from file_transformer.file_extract import get_file_info
from celery_worker.tag_worker import update_remote_tag
from celery_worker.protection_action_task import do_discover_actions, copy_local_file_to_protection_folder
from service.custom_datatype_service import UserDefineDatatypeService
from util.common_log import get_logger
from util.random_password.service import RandomPasswordService
from util.utils import get_utc_timestamp
from util.config import get_global_config
from util.enum_ import ASBinName
from domain_model.global_cache import GlobalCache
from domain_model.tracker.pid_tracker import AnalyzeWorkerPidTracker
from cachetools import TTLCache
import threading
from exts import Session
from service.discovery_engine_v2_service import create_policy_engine, DiscoveryPolicyEngine
from service.discovery_policy_v2_service import get_entity_by_policy
from service.idm_template_service import get_idm_match_info
from service.edm_service import get_edm_match_info
from ddr.service.engine_service import DDRPolicyEngine, ddr_create_policy_engine


ANALYZE_CACHE_TTL = 900
global_analyze_ttl_cache = TTLCache(maxsize=1000, ttl=ANALYZE_CACHE_TTL)
file_upload_analyze_cache = TTLCache(maxsize=20, ttl=float('inf'))

global_discover_engine_ttl_cache = TTLCache(maxsize=1000, ttl=600)
global_ddr_engine_ttl_cache = TTLCache(maxsize=1000, ttl=600)

global_data = None
global_data_lock = threading.Lock()

@worker_ready.connect
def startup_handler(sender=None, **kwargs):