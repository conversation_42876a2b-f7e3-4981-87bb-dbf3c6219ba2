from typing import List, Dict, Any, Optional
from celery import Task
from celery.schedules import crontab
from ddr.service.ddr_service import DDRService
from ddr.model.task import get_ddr_tasks_dict, update_last_pull_time
from datetime import datetime, timedelta, timezone
from util.common_log import get_logger
from celery_app import app

logger = get_logger("ddr_task")


@app.task(bind=True, priority=8, queue='ddr')
def process_ddr_events(self, task: dict, 
                       start_time: datetime, end_time: datetime,
                       ddr_policy_ids: Optional[List[str]] = None,
                       target_operations: Optional[List[str]] = None,
                       scan_trigger_events: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Task for processing DDR events.
    
    Args:
        task: DDRTask dict
        ddr_policy_ids: List of DDR policy IDs
        target_operations: List of target operation types
        scan_trigger_events: List of scan trigger event types
    Returns:
        Result dictionary
    """
    try:
        logger.info(f"Starting DDR event processing for task: {task['name']}")

        ddr_service = DDRService(task)
        # Process platform events
        result = ddr_service.process_events(
            start_time=start_time,
            end_time=end_time,
            target_operations=target_operations,
            scan_trigger_events=scan_trigger_events
        )

        logger.info(f"DDR event processing completed for task {task['name']}: {result}")
        return result

    except Exception as e:
        logger.error(f"Error in DDR event processing task: {e}")
        return {"error": str(e), "processed": 0, "scans_triggered": 0}


# Universal schedule: check all active DDR tasks and run if interval reached
@app.task(bind=True, priority=8, queue='ddr')
def scheduled_ddr_dispatcher(self):
    """
    Universal DDR task scheduler: checks all active DDR tasks and runs those whose interval has elapsed.
    """
    now = datetime.now(timezone.utc)
    try:
        tasks = get_ddr_tasks_dict(enabled=True)
    except Exception as e:
        logger.error(f"Failed to fetch active DDR tasks: {e}")
        return

    for task in tasks:
        try:
            last = now - timedelta(seconds=125)
            # Use apply_async for scheduling with delay
            process_ddr_events.apply_async(
                args=(task, last, now),
                kwargs={
                    'scan_trigger_events': task.get('trigger_events', [])
                },
                countdown=1,  # delay in seconds
                priority=8    # DDR priority
            )

        except Exception as e:
            logger.error(f"Error processing DDR task {task['name']}: {e}")


# High priority DDR task for emergency processing
@app.task(bind=True, priority=10, queue='ddr')  # EMERGENCY priority
def emergency_ddr_events(self, task: dict, 
                        start_time: datetime, end_time: datetime,
                        ddr_policy_ids: Optional[List[str]] = None,
                        target_operations: Optional[List[str]] = None,
                        scan_trigger_events: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Emergency DDR task processing with highest priority.
    
    Args:
        task: DDRTask dict
        ddr_policy_ids: List of DDR policy IDs
        target_operations: List of target operation types
        scan_trigger_events: List of scan trigger event types
    Returns:
        Result dictionary
    """
    try:
        logger.info(f"Starting EMERGENCY DDR event processing for task: {task['name']}")

        ddr_service = DDRService(task)
        # Process platform events
        result = ddr_service.process_events(
            start_time=start_time,
            end_time=end_time,
            target_operations=target_operations,
            scan_trigger_events=scan_trigger_events
        )

        logger.info(f"EMERGENCY DDR event processing completed for task {task['name']}: {result}")
        return result

    except Exception as e:
        logger.error(f"Error in EMERGENCY DDR event processing task: {e}")
        return {"error": str(e), "processed": 0, "scans_triggered": 0}
