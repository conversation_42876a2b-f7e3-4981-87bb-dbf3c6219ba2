from connector.aws_connector import AWSConnector
from connector.sharepoint_connector import S<PERSON>PointConnector
from connector.sharepoint_token_connector import SharePointTokenConnector
from util.enum_ import StorageType
from util.config import get_global_config
from exts import logger
from celery_app import app

configs = get_global_config()

@app.task(bind=True, priority=5, queue='default')
def update_remote_tag(self, remote_info, tags):
    """Update remote file tags"""
    try:
        if remote_info["type"] == "aws":
            aws_connector = AWSConnector(remote_info)
            aws_connector.update_file_tag(remote_info['file'], tags)
            logger.info(f"Updated tag for file {remote_info['file']} to {tags}")
        else:
            if remote_info["storage_type"] == StorageType.SHAREPOINT_OL:
                sp_connector = SharePointTokenConnector(remote_info)
            elif remote_info["storage_type"] == StorageType.SHAREPOINT_OP:
                sp_connector = SharePointConnector(remote_info)
            if remote_info["storage_type"] == StorageType.SHAREPOINT_OL and remote_info["usecredentials"] == True:
                sp_connector = SharePointConnector(remote_info)
            sp_connector.update_file_tag(remote_info['file'], tags)
            logger.info(f"Updated tag for file {remote_info['file']} to {tags}")
    except KeyError as e:
        logger.error(f"Missing key in remote_info: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Error updating file tag: {str(e)}")
        raise
