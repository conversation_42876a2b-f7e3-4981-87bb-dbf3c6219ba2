import re
import math
import pandas as pd
from collections import Counter

def detect_table_headers(preview_data, threshold=0.5):
    def get_col_type(val):
        if val is None or (isinstance(val, float) and math.isnan(val)):
            return 'NaN'
        
        if isinstance(val, str):
            val = val.replace(' ', '')
            if len(val) == 0:
                return 'NaN'
            if val.isalpha():
                return 'alpha'
            if val.isdigit():
                return 'numeric'
            if bool(re.search(r'\d', val)) and bool(re.search(r'[a-zA-Z]', val)):
                return 'alpha_numeric'
            if bool(re.search(r'[^a-zA-Z0-9\s]', val)):
                return 'special'

        elif isinstance(val, (int, float)):
            return 'numeric'

        return 'other'
    
    if len(preview_data) < 3:
        return False

    if isinstance(preview_data, pd.DataFrame):
        first_row = preview_data.iloc[0].tolist()
        other_rows = preview_data.iloc[1:].values.tolist()
    else:
        first_row = preview_data[0]
        other_rows = preview_data[1:]

    total_columns = len(first_row)
    matched_columns = 0

    for col_idx in range(total_columns):
        first_row_val = first_row[col_idx]
        other_row_vals = [row[col_idx] for row in other_rows]

        first_row_type = get_col_type(first_row_val)
        other_row_types = [get_col_type(val) for val in other_row_vals]

        type_counts = Counter(other_row_types)

        most_common_type, most_common_count = type_counts.most_common(1)[0]

        if not other_row_vals:
            continue

        first_row_avg_len = len(str(first_row_val))
        other_rows_avg_len = sum(len(str(val)) for val in other_row_vals) / len(other_row_vals)

        length_diff_first_other = abs(first_row_avg_len - other_rows_avg_len)

        max_other_length_diff = max(abs(len(str(val1)) - len(str(val2))) for val1 in other_row_vals for val2 in other_row_vals)

        if (most_common_count / len(other_row_types) > 0.8 and most_common_type != first_row_type and most_common_type != 'NaN') or\
            (length_diff_first_other > 15 and max_other_length_diff < 5):
            matched_columns += 1

    ratio = matched_columns / total_columns

    return ratio >= threshold