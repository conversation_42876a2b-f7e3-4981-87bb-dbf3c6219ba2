import os
import sys
import io
import signal
from pptx import Presentation
from image_extract import image_extract_text
import shutil
from lxml.etree import XMLSyntaxError
import time

# For ppt
PPT_MAX_EXTRACT_SLIDES=100
PPT_IMAGE_MAX_EXTRACT_SLIDES=10
PPT_IMAGE_PER_EXT_TIMEOUT=3

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from util.common_log import get_logger
logger = get_logger("dlp")

# Add global flag for graceful shutdown
_shutdown_requested = False

def signal_handler(signum, frame):
    """Handle termination signals gracefully"""
    global _shutdown_requested
    _shutdown_requested = True
    logger.info(f"Received signal {signum}, will attempt to save partial results")

# Setup signal handlers once at module level
signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)

def try_repair_pptx_content(file_path, max_length=None):
    """Try to repair corrupted PPTX by reading partial content"""
    try:
        from zipfile import ZipFile
        text_parts = []
        
        with ZipFile(file_path) as pptx:
            # Try to extract text from slide XMLs directly
            for item in pptx.namelist():
                if _shutdown_requested:
                    logger.info("Interrupted during PPTX repair, returning partial content")
                    break
                    
                if item.startswith('ppt/slides/slide'):
                    try:
                        content = pptx.read(item).decode('utf-8')
                        # Basic text extraction using string operations
                        start = 0
                        while not _shutdown_requested:  
                            start = content.find('<a:t>', start)
                            if start == -1:
                                break
                            end = content.find('</a:t>', start)
                            if end == -1:
                                break
                            text = content[start+5:end].strip()
                            if text:
                                if max_length and len(text_parts) + len(text) > max_length:
                                    break
                                text_parts.append(text)
                            start = end + 6
                    except:
                        continue
                        
        return ' '.join(text_parts)
    except Exception as e:
        logger.error(f"Failed to repair PPTX content: {str(e)}")
        return ' '.join(text_parts) if text_parts else ''

def pptx_content_extractor(file_path, config={}, filters_list=None, **kwargs):
    max_length = config.get("max_length")
    max_ext_slides = config.get('ppt', {}).get("max_ext_slides", PPT_MAX_EXTRACT_SLIDES)
    image_ext_slides = config.get('ppt', {}).get("image_ext_slides", PPT_IMAGE_MAX_EXTRACT_SLIDES)
    image_per_ext_timeout = config.get('ppt', {}).get("image_per_ext_timeout", 3)
    try:
        prs = Presentation(file_path)
    except XMLSyntaxError as e:
        logger.warning(f"XML syntax error in PPTX, attempting basic content extraction: {str(e)}")
        content = try_repair_pptx_content(file_path, max_length)
        return content
    except MemoryError as e:
        logger.warning(f"Memory error in PPTX, attempting basic content extraction: {str(e)}")
        return ''
    except Exception as e:
        logger.error(f"Error opening PPTX file: {str(e)}")
        return ''

    text = ''
    
    try:
        for idx, slide in enumerate(prs.slides):
            if _shutdown_requested or idx >= max_ext_slides:
                break
            
            for shape in slide.shapes:
                if _shutdown_requested:
                    print("Interrupted during PPTX extraction")
                    break

                if hasattr(shape, "text"):
                    if filters_list is not None and shape.text in filters_list:
                        continue
                    
                    if max_length is not None and len(text + shape.text) > max_length:
                        text += shape.text[:max_length - len(text)]
                        return text.strip()
                    else:
                        text += shape.text + " "
                elif shape.has_table:
                    table = shape.table
                    for row in table.rows:
                        if _shutdown_requested:
                            print("Interrupted during PPTX table extraction")
                            break
                        row_list = [cell.text.strip() for cell in row.cells]
                        row_text = '. '.join(row_list) + '\n'
                        
                        if max_length is not None and len(text + row_text) > max_length:
                            text += row_text[:max_length - len(text)]
                            return text.strip()
                        else:
                            text += row_text
                    text += " "
                elif shape.shape_type == 13:  # Picture
                    if idx >= image_ext_slides:
                        continue
                    try:
                        with io.BytesIO(shape.image.blob) as image_fp:
                            image_text = image_extract_text(
                                file_path, max_length, 
                                fp=image_fp, 
                                **kwargs,
                                from_ppt=True,
                                timeout=image_per_ext_timeout
                            )
                            if image_text:
                                if max_length is not None and len(text + image_text) > max_length:
                                    text += image_text[:max_length - len(text)]
                                    return text.strip()
                                else:
                                    text += image_text + " "
                    except Exception as e:
                        logger.debug(f"Error extracting text from image: {str(e)}")
                        continue

    except Exception as e:
        logger.error(f"Error processing PPTX content: {str(e)}")
        return text.strip()

    return text.strip()

def ppt_content_extractor(file_path, config={}, **kwargs):
    import aspose.slides as slides
    filters_list = ['Evaluation only.\nCreated with Aspose.Slides for Python via .NET 24.5.\nCopyright 2004-2024Aspose Pty Ltd.']
    file_path_pptx = file_path + 'x'

    content = ''
    blob_options = slides.BlobManagementOptions()
    blob_options.temp_files_root_path = os.path.dirname(file_path)
    blob_options.max_blobs_bytes_in_memory = 500 * 1024 * 1024 # 500 MB
    load_options = slides.LoadOptions()
    load_options.blob_management_options = blob_options
    
    with slides.Presentation(file_path, load_options) as prs: 
        prs.save(file_path_pptx, slides.export.SaveFormat.PPTX)
        content = pptx_content_extractor(file_path_pptx, config, filters_list, **kwargs)
        try:
            os.remove(file_path_pptx)
        except FileNotFoundError:
            logger.error(f"{kwargs.get('real_file_name')} tmp file not exist: {file_path_pptx}")
        except Exception as e:
            logger.error(f"{kwargs.get('real_file_name')} delete tmp file failed: {str(e)}")

    return content