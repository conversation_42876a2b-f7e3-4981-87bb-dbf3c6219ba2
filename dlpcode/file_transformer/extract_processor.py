#!/bin/python3
import os
import sys
import json
import argparse

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from util.common_log import get_logger
logger = get_logger("dlp")

from file_transformer.pdf_extract import pdf_content_extractor
from file_transformer.ppt_extract import pptx_content_extractor, ppt_content_extractor
from file_transformer.excel_extract import xls_content_extractor, xlsx_content_extractor

def main():
    parser = argparse.ArgumentParser(description='File information extractor')
    parser.add_argument('parsed_file', help='Path to the parsed file')
    parser.add_argument('original_filename', help='Original filename')
    parser.add_argument('config_json', help='Configuration as JSON string')
    parser.add_argument('output_file', help='Output file path for results')
    parser.add_argument('type', help='Type of file to process')
    parser.add_argument('temp_dir', help='Temporary directory for pytesseract')

    args = parser.parse_args()

    # Load configuration from JSON string
    try:
        config = json.loads(args.config_json)
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing config JSON: {e}")
        sys.exit(1)

    # Process file and get results
    try:
        text = ''
        extra_info = {}
        if args.type == 'pdf':
            text, extra_info = pdf_content_extractor(args.parsed_file, config, real_filename=args.original_filename, temp_dir=args.temp_dir)
        elif args.type == 'pptx':
            text = pptx_content_extractor(args.parsed_file, config, real_filename=args.original_filename, temp_dir=args.temp_dir)
        elif args.type == 'ppt':
            text = ppt_content_extractor(args.parsed_file, config, real_filename=args.original_filename, temp_dir=args.temp_dir)
        elif args.type == 'xls':
            text = xls_content_extractor(args.parsed_file, config, real_filename=args.original_filename, temp_dir=args.temp_dir)
        elif args.type == 'xlsx':
            text = xlsx_content_extractor(args.parsed_file, config, real_filename=args.original_filename, temp_dir=args.temp_dir)
        else:
            logger.error(f"Unsupported file type: {args.type}")
    except Exception as e:
        logger.error(f"Error processing file: {e}")
        sys.exit(1)

    # Write results to output file
    try:
        with open(args.output_file, 'w') as f:
            json.dump({'text': text, 'extra_info': extra_info}, f, indent=2)
    except Exception as e:
        logger.error(f"Error writing output file: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()