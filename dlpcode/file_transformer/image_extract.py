import os
import io
import tifffile  # Add this import
from PIL import Image, ImageFilter, ImageEnhance
import PIL.TiffImagePlugin  # Add this import
from PIL import UnidentifiedImageError  # Add this import
from PIL.Image import MAX_IMAGE_PIXELS
import pytesseract
from util.common_log import get_logger
import tempfile

import pillow_avif # Do not remove this. AVIF support for Pillow

# For Image
IMAGE_EXTRACT_TIMEOUT=10

logger = get_logger("dlp")

def preprocess_image(img):
    # Load the image from file
    image = img.convert('L')  # Convert to grayscale

    # Get original dimensions
    original_width, original_height = image.size

    # Resize image while maintaining aspect ratio
    max_width = 800
    max_height = 600

    min_width = 800
    min_height = 600

    # Calculate aspect ratio preserving dimensions
    aspect_ratio = original_width / original_height
    #print(f'Aspect ratio: {aspect_ratio}')
    if False and original_width > max_width or original_height > max_height:
        if aspect_ratio > 1:  # Width is greater than height
            new_width = min(original_width, max_width)
            new_height = int(new_width / aspect_ratio)
        else:  # Height is greater than width
            new_height = min(original_height, max_height)
            new_width = int(new_height * aspect_ratio)
    else:
        if original_width < min_width or original_height < min_height:
            if aspect_ratio > 1:
                new_width = max(original_width, min_width)
                new_height = int(new_width / aspect_ratio)
            else:
                new_height = max(original_height, min_height)
                new_width = int(new_height * aspect_ratio)
        else:
            new_width, new_height = original_width, original_height

    #print(f'Original dimensions: {original_width}x{original_height}')
    #print(f'New dimensions: {new_width}x{new_height}')
    image = image.resize((new_width, new_height), Image.LANCZOS)

    # Convert image to RGB if it is a palette image
    if image.mode == 'P':
        image = image.convert('RGB')
    # Apply a sharpen filter to enhance text edges
    image = image.filter(ImageFilter.SHARPEN)

    # Increase contrast to make text stand out more
    enhancer = ImageEnhance.Contrast(image)
    image = enhancer.enhance(2)  # Increase contrast by factor of 2

    return image

def image_extract_test_get_new_size(img):
    image = img.convert('L')
    original_width, original_height = image.size

    max_size = 1200
    min_size = 600

    # Calculate aspect ratio preserving dimensions
    aspect_ratio = original_width / original_height
    #print(f'Aspect ratio: {aspect_ratio}')
    if original_width > max_size or original_height > max_size:
        if aspect_ratio > 1:  # Width is greater than height
            new_width = max_size
            new_height = int(new_width / aspect_ratio)
        else:  # Height is greater than width
            new_height = max_size
            new_width = int(new_height * aspect_ratio)
    else:
        if original_width < min_size or original_height < min_size:
            if aspect_ratio > 1:
                new_height = min_size
                new_width = int(new_height * aspect_ratio)
            else:
                new_width = min_size
                new_height = int(new_width / aspect_ratio)
        else:
            new_width, new_height = original_width, original_height
    
    if new_width > max_size:
        new_width = max_size
    if new_height > max_size:
        new_height = max_size
    logger.debug(f'new_width: {new_width}, new_height: {new_height}, original_width: {original_width}, original_height: {original_height}')
    return new_width, new_height

def pytessract_image_to_string_tmpdir(file_path, img, timeout=3, temp_dir=None):
    original_tmpdir = None
    text = ''
    try:
        original_tmpdir = tempfile.tempdir
        if temp_dir is not None:
            tempfile.tempdir = temp_dir
        else:
            tempfile.tempdir = os.path.dirname(file_path)
        text = pytesseract.image_to_string(img, config='', timeout=timeout)
    except TypeError as e:
        logger.debug(f"pytessract_image_to_string_tmpdir failed: Unsupported image format/type: {img.format} ")
    except RuntimeError as e:
        if str(e) == 'Tesseract process timeout':
            logger.debug(f"Tesseract process timed out after {timeout} seconds")
        else:
            logger.error(f"Runtime error: {e}")
    except Exception as e:
        logger.error(f"pytessract_image_to_string_tmpdir failed: {str(e)}")
    finally:
        if original_tmpdir is not None:
            tempfile.tempdir = original_tmpdir
    return text

def image_extract_text_from_resize_file(file_path, **kwargs):
    text = ""
    timeout = kwargs.get('timeout', 3)

    file_path_resize = file_path + ".resize"
    logger.debug(f'resize file: {file_path_resize}')
    fp = kwargs.get('fp', file_path)

    with Image.open(fp) as img:
        new_width, new_height = image_extract_test_get_new_size(img)
        new_image = img.resize((new_width, new_height))
        #Fix bug: 1078376
        new_image.save(file_path_resize, img.format)
        with Image.open(file_path_resize) as img_resize:
            text = pytessract_image_to_string_tmpdir(file_path_resize, img_resize, timeout, kwargs.get('temp_dir', None))
        try:
            os.remove(file_path_resize)
        except FileNotFoundError:
            logger.error(f"{kwargs.get('real_file_name')} tmp file not exist: {file_path_resize}")
        except Exception as e:
            logger.error(f"{kwargs.get('real_file_name')} delete tmp file failed: {str(e)}")

    return text

def set_max_image_pixels(size):
    """
    Safely set the PIL Image MAX_IMAGE_PIXELS value to prevent decompression bomb DOS attacks.
    
    Memory usage calculation for RGB images:
    - Each pixel uses 3 bytes (1 byte each for R,G,B)
    - 60MP = 60,000,000 pixels
    - Base memory: 60MP * 3 bytes = 180MB
    - Actual memory usage will be higher due to:
      1. PIL processing buffers
      2. Temporary image copies
      3. Python object overhead
    """
    if size > Image.MAX_IMAGE_PIXELS:
        Image.MAX_IMAGE_PIXELS = size
    return Image.MAX_IMAGE_PIXELS


def image_extract_text(file_path, max_length=None, **kwargs):
    """
    Extract text from image files with size limits for memory safety.
    
    Image size reference:
    - 60MP limit can handle images up to ~10000x6000 pixels
    - Common professional camera photos: 30-50MP
    - Standard scanned documents: 2-8MP
    - 4K images: ~8.3MP (3840x2160)
    """
    fp = kwargs.get('fp', file_path)
    timeout = kwargs.get('timeout', IMAGE_EXTRACT_TIMEOUT)
    text = ''
    original_max_pixels = Image.MAX_IMAGE_PIXELS
    try:
        # Set to 60MP limit (approximately 180MB base memory usage)
        set_max_image_pixels(60000000)  # 60 million pixels
        
        try:
            with Image.open(fp) as img:
                # check if the image format is AVIF
                if img.format == 'AVIF':
                    with io.BytesIO() as output:
                        img.save(output, format="PNG")
                        output.seek(0)
                        # reopen the image in PNG format
                        with Image.open(output) as png_img:
                            png_img.load()
                            text = pytessract_image_to_string_tmpdir(file_path, png_img, timeout, kwargs.get('temp_dir', None))
                else:
                    text = pytessract_image_to_string_tmpdir(file_path, img, timeout, kwargs.get('temp_dir', None))
        except UnidentifiedImageError as e:
            logger.debug(f"UnidentifiedImageError: {str(e)}, trying to read image with tifffile")
            try:
                # if the image format is not recognized, try to read it with tifffile
                if fp.lower().endswith(('.tif', '.tiff')):
                    # Read the image with tifffile
                    img_array = tifffile.imread(fp)
                    # Check if the image has more than 4 channels
                    if img_array.ndim == 3 and img_array.shape[2] > 4:
                        # Keep only the first 3 channels (RGB)
                        img_array = img_array[:, :, :3]
                    # Convert the image array to a PIL image
                    img = Image.fromarray(img_array)
                    # Extract text from the image
                    return pytessract_image_to_string_tmpdir(
                        file_path, img, timeout, kwargs.get('temp_dir', None)
                    )
                else:
                    logger.error(f"File {fp} is not a TIFF image")
            except Exception as e:
                logger.debug(f"Failed to try read from other ways")
                return text

        # if the text is empty, try to resize the image and extract text again
        if text.isspace() and not kwargs.get('from_ppt', False):
            text = image_extract_text_from_resize_file(file_path, **kwargs)

        # truncate the text if it exceeds the max length
        if max_length is not None and len(text) > max_length:
            text = text[:max_length]
    except Image.DecompressionBombError as e:
        logger.debug(f"DecompressionBombError: Image size exceeds the pixel limit: {str(e)}")
    except Exception as e:
        logger.error(f"image_extract_text failed: {str(e)}")
    finally:
        # Always restore the original MAX_IMAGE_PIXELS value
        Image.MAX_IMAGE_PIXELS = original_max_pixels

    return text