import re
import os
import signal
import pymupdf 
from ocrmypdf.exceptions import TaggedPDFError
import PyPDF2
import resource
import subprocess
import tempfile
import sys
from file_transformer.header_detect import detect_table_headers

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from util.common_log import get_logger
logger = get_logger("dlp")

# For PDF
PDF_MAX_TAB_HDR_SEARCH_ROWS=32
PDF_MAX_EXTRACT_PAGES=50
PDF_MAX_OCR_EXTRACT_PAGES=10
PDF_IMAGE_PER_EXT_TIMEOUT=3
PDF_OCR_TIMEOUT=30

# Add global flag for graceful shutdown
_shutdown_requested = False

def signal_handler(signum, frame):
    """Handle termination signals gracefully"""
    global _shutdown_requested
    _shutdown_requested = True
    logger.info(f"Received signal {signum}, will attempt to save partial results")

# Setup signal handlers once at module level
signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)

def detect_pdf_table_header(row):
    def _is_valid_item(item):
        if len(item) > 32 or len(item) < 1:
            return False
        # Regex matches "numbers, spaces, slashes, -, %, ~, $, :, #", 
        # and the string can only contain these characters
        pattern = re.compile(r'^[\d\s\/\-%\.~,\$:#]*$')
        result = pattern.match(item)
        if result:
            return False
        if ':' in item or '?' in item or '@' in item:
            return False
        # contains digit
        if any(char.isdigit() for char in item):
            return False
        return True

    if all(isinstance(cell, str) and _is_valid_item(cell) for cell in row):
        return True
    return False

def check_pdf_table(tables, max_hdr_search_rows):
    def _element_format(element):
        format_info = {
            "type": type(element),
            "contains_digit": any(char.isdigit() for char in element if '@' not in element),
            "has_hyphen": '-' in element,
            "has_slash": '/' in element,
            "has_at": '@' in element
        }
        return format_info

    def _have_same_format(list1, list2):
        # Compare the formats of list1 and list2
        for item1, item2 in zip(list1, list2):
            if item1 is None or item2 is None:
                return False
            if _element_format(item1) != _element_format(item2):
                return False
        return True

    filter_tables = []
    if all(isinstance(row, list) for row in tables):
        # remove none row
        for i, row in enumerate(tables):
            none_count = sum(1 for cell in row if cell is None or len(cell) < 1)
            if none_count > len(row) / 2:
                continue
            filter_tables.append(row)
        # detect header
        # just detect the first max_hdr_search_rows rows
        is_header = detect_table_headers(filter_tables[:max_hdr_search_rows])
        if is_header:
            return filter_tables, filter_tables[0]
        # for i, row in enumerate(filter_tables[:max_hdr_search_rows]):
        #     if detect_pdf_table_header(row):
        #         unique_counts = [len(set(col)) for col in zip(*filter_tables[i+1:])]
        #         if i + 2 > len(filter_tables):
        #             return [], []
        #         if (len(unique_counts) > 0 
        #             and all(count > 1 for count in unique_counts)
        #             and _have_same_format(filter_tables[i + 1], filter_tables[i + 2])
        #             ):
        #             return filter_tables, row
    return [], []

def replace_pdf_table(tables, page_text, header):
    def _replace_func(page_text, cell, offset, replace_str, head_offset):
        '''
        replace table value with key: value
        Because the effect of '\n' will confuse the table information, 
        if the table information cannot be found according to the offset, 
        search again from the table header.
        '''
        tmp_position = offset
        start_position = page_text.find(cell, tmp_position)
        if start_position == -1:
            start_position = page_text.find(cell, head_offset)
            if start_position == -1:
                return page_text, offset
        end_position = start_position + len(cell)
        page_text = page_text[:start_position] + replace_str + page_text[end_position:]
        offset = start_position + len(replace_str)
        return page_text, offset

    def _deal_with_cell(page_text, cell, offset, head, head_offset):
        '''
        If the cell contains '\n', replace the first row 
        with the entire table content and delete the rest.
        '''
        cell_parts = cell.splitlines()
        cell = cell.replace('\n', '')
        head = head.replace('\n', '')
        if len(cell_parts) > 0:
            if head == cell:
                replace_str = ""
            else:
                replace_str = f"{head}:{cell},"
            page_text, offset = _replace_func(page_text, cell_parts[0], 
                                              offset, replace_str, head_offset)
        # delete the rest of cell_parts
        for i in range(1, len(cell_parts)):
            line = cell_parts[i]
            page_text, _ = _replace_func(page_text, line, 
                                         offset, "", head_offset)
        return page_text, offset

    # if cannot find header, return page_text
    head_offset = page_text.find(header[0])
    if head_offset == -1:
        return page_text
    offset = 0
    for table in tables:
        if offset > 0:
            head_offset = offset
        for idx, cell in enumerate(table):
            if cell and idx < len(header):
                page_text, offset = _deal_with_cell(page_text, cell, offset, 
                                                    header[idx], head_offset)
    return page_text

def extract_text_and_tables(pdf, config):
    text = ''
    extra_info = {'tables': []}

    # get config
    max_length = config.get("max_length")
    max_ext_pages = config.get("pdf", {}).get("max_ext_pages", PDF_MAX_EXTRACT_PAGES)
    max_hdr_search_rows = config.get("pdf", {}).get("max_hdr_search_rows", PDF_MAX_TAB_HDR_SEARCH_ROWS)
    
    page_number = 0
    for page in pdf:
        if _shutdown_requested:
            logger.info("Interrupted during PDF extraction, returning partial content")
            break
            
        page_number += 1
        if page_number > max_ext_pages:
            break
        page_text = page.get_text()

        # https://pymupdf.readthedocs.io/en/latest/page.html#Page.find_tables
        tabs = page.find_tables()
        for t in tabs.tables:
            table = t.extract()
            check_table, header = check_pdf_table(table, max_hdr_search_rows)
            if check_table and None not in header:
                extra_info['tables'].append(check_table)
                page_text = replace_pdf_table(check_table, page_text, header)
        if max_length and len(text + page_text) > max_length:
            text += page_text[:max_length - len(text)]
            break
        else:
            text += page_text + "\n"
    return text, extra_info

def pdf_content_extractor(file_path, config={}, **kwargs):
    tempprefix = None
    if kwargs.get("temp_dir"):
        tempprefix = f"{kwargs.get('temp_dir')}"
    elif file_path.startswith('/'):
        tempprefix = f"{file_path}.tmp"

    with tempfile.TemporaryDirectory(dir=tempprefix) as tempdir:
        ocr_pdf_path = f"{tempdir}/ocr_output.pdf"
        ocr_tmp_path = f"{tempdir}/ocr_tmp_output.pdf"
        try:
            ocr_ext_pages = config.get("pdf", {}).get("ocr_ext_pages", PDF_MAX_OCR_EXTRACT_PAGES)
            max_ext_pages = config.get("pdf", {}).get("max_ext_pages", PDF_MAX_EXTRACT_PAGES)
            if ocr_ext_pages > 0:
                segment_pdf_path = f"{tempdir}/segment__output.pdf"
                try:
                    with open(file_path, "rb") as infile:
                        reader = PyPDF2.PdfReader(infile)
                        writer = PyPDF2.PdfWriter()
                        for page_num, page in enumerate(reader.pages[:ocr_ext_pages]):
                            if page is None:
                                logger.debug(f"Page {page_num} is None")
                                continue
                            try:
                                logger.info(f"Adding page {page_num}")
                                writer.add_page(page)
                            except Exception as e:
                                logger.debug(f"Error adding page {page_num}")

                        with open(segment_pdf_path, "wb") as outfile:
                            writer.write(outfile)

                        try:
                            image_per_ext_timeout = config.get("pdf", {}).get("image_per_ext_timeout", PDF_IMAGE_PER_EXT_TIMEOUT)
                            process = subprocess.Popen(
                                [
                                    "ocrmypdf", 
                                    "--tesseract-timeout={}".format(image_per_ext_timeout), 
                                    "--skip-text",
                                    "--skip-big=5",
                                    "--output-type=pdf",
                                    "-leng",
                                    segment_pdf_path,
                                    ocr_tmp_path
                                ],
                                env={**os.environ, "TMPDIR": tempdir},
                                preexec_fn=lambda: resource.setrlimit(resource.RLIMIT_AS, (1024 * 1024 * 1024, 1024 * 1024 * 1024))  # Limit to 1GB
                            )
                            try:
                                ocr_ext_timeout = config.get("pdf", {}).get("ocr_ext_timeout", PDF_OCR_TIMEOUT)
                                return_code = process.wait(timeout=ocr_ext_timeout)
                                if return_code != 0:
                                    logger.debug(f"ocrmypdf failed with return code {return_code}")
                                    ocr_pdf_path = file_path
                                else:
                                    final_writer = PyPDF2.PdfWriter()
                                    with open(ocr_tmp_path, "rb") as ocr_file:
                                        ocr_reader = PyPDF2.PdfReader(ocr_file)
                                        for page in ocr_reader.pages:
                                            try:
                                                final_writer.add_page(page)
                                            except Exception as e:
                                                logger.debug(f"Error adding page")
                                    for page in reader.pages[ocr_ext_pages:max_ext_pages]:
                                        if page is None:
                                            logger.warning("Encountered a None page, skipping.")
                                            continue
                                        try:
                                            final_writer.add_page(page)
                                        except Exception as e:
                                            logger.debug(f"Error adding page")
                                    with open(ocr_pdf_path, "wb") as outfile:
                                        final_writer.write(outfile)
                            except subprocess.TimeoutExpired:
                                process.kill()
                                logger.debug(f"ocrmypdf process timed out after {ocr_ext_timeout}s")
                                ocr_pdf_path = file_path
                        except subprocess.CalledProcessError as e:
                            logger.debug(f"Error in ocrmypdf: {e}")
                            ocr_pdf_path = file_path
                except Exception as e:
                    logger.debug(f"Error in pdf segment: {e}")
                    ocr_pdf_path = file_path
            else:
                ocr_pdf_path = file_path
            with pymupdf.open(ocr_pdf_path) as pdf:
                logger.debug(f"Processing extract text and tables {ocr_pdf_path}")
                return extract_text_and_tables(pdf, config)
        except TaggedPDFError:
            with pymupdf.open(file_path) as pdf:
                logger.debug(f"Processing extract text and tables {ocr_pdf_path}")
                return extract_text_and_tables(pdf, config)
        except KeyboardInterrupt:
            logger.error(f"Process interrupted by user {kwargs.get('real_filename')} get pdf content")
            # Try to return any partial results if available
            try:
                with pymupdf.open(ocr_pdf_path) as pdf:
                    return extract_text_and_tables(pdf, config)
            except:
                return '', {}
        except Exception as e:
            logger.error(f"Error in {kwargs.get('real_filename')} get pdf content: {e}")
            return '', {}
        finally:
            pass