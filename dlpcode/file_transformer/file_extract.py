import os
import io
import shutil
import magic
import mimetypes
import docx
import pandas as pd
import sys

from odf import text, teletype, draw
from odf.opendocument import load
from striprtf.striprtf import rtf_to_text
from PIL import Image
from odf.table import Table, TableRow, TableCell
#from odf.text import P
import subprocess
from util.common_log import get_logger
from file_transformer.image_extract import image_extract_text
import json
import resource
import time
from file_transformer.header_detect import detect_table_headers
from cryptography import x509
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.serialization import pkcs7

logger = get_logger("dlp")

# Add constants at the top with other constants
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
EXTRACT_PROCESSOR = os.path.join(SCRIPT_DIR, 'extract_processor.py')



# For csv
CSV_MAX_EXTRACT_ROWS=1000
CSV_MAX_HDR_SEARCH_ROWS=10

# For Docx
DOC_MAX_EXT_PARAGRAPHS=3000

# For ODT
ODT_MAX_EXT_PARAGRAPHS=3000

# For ODP
ODP_MAX_EXTRACT_SLIDES=100

# For ODS
ODS_MAX_EXTRACT_ROWS=1000  # It takes about 3s
ODS_MAX_EXTRACT_SHEETS=16

# For Image
IMAGE_EXTRACT_TIMEOUT=10

def get_image_metadata(file_path, config={}, **kwargs):
    max_length = config.get("max_length")
    return image_extract_text(file_path, max_length, **kwargs, timeout=config.get("image_ext_timeout", IMAGE_EXTRACT_TIMEOUT))

def get_image_metadata_webp(file_path, config={}, **kwargs):
    max_length = config.get("max_length")
    file_path_jpg = file_path + '.jpg'

    content = ''
    with Image.open(file_path) as img:
        img.convert('RGB').save(file_path_jpg, 'JPEG', quality=90)
        content = image_extract_text(file_path_jpg, max_length, **kwargs, timeout=config.get("image_ext_timeout", IMAGE_EXTRACT_TIMEOUT))
        try:
            os.remove(file_path_jpg)
        except FileNotFoundError:
            logger.error(f"{kwargs.get('real_file_name')} tmp file not exist: {file_path_jpg}")
        except Exception as e:
            logger.error(f"{kwargs.get('real_file_name')} delete tmp file failed: {str(e)}")

    logger.debug(f'content: {content}')
    return content
        
def get_audio_metadata(file_path, config={}, **kwargs):
    max_length = config.get("max_length")
    # TODO: Implement this function
    return

def get_video_metadata(file_path, config={}, **kwargs):
    max_length = config.get("max_length")
    # TODO: Implement this function
    return

def get_text_content(file_path, config={}, **kwargs):
    max_length = config.get("max_length")
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            return file.read(max_length)
    except Exception as e:
        logger.error(f"Error in {kwargs.get('real_file_name')} get text content: {e}")
        return ''

def get_cert_content(file_path, config={}, **kwargs):
    max_length = config.get("max_length")
    try:
        with open(file_path, 'rb') as file:
            cer_data = file.read(max_length)
        try:
            cert = x509.load_pem_x509_certificate(cer_data)
        except Exception as e1:
            try:
                cert = x509.load_der_x509_certificate(cer_data)
            except ValueError as e:
                logger.error(f"Failed to parse certificate: {e1}")
                return '' 
        return cert.public_bytes(serialization.Encoding.PEM).decode()
    except Exception as e:
        logger.error(f"Error in {kwargs.get('real_file_name')} get cert content: {e}")
        return '' 

def get_p7b_content(file_path, config={}, **kwargs):
    max_length = config.get("max_length")
    try:
        with open(file_path, 'rb') as file:
            cer_data = file.read(max_length)
        try:
            certs = pkcs7.load_pem_pkcs7_certificates(cer_data)
        except Exception as e1:
            try:
                certs = pkcs7.load_der_pkcs7_certificates(cer_data)
            except ValueError as e:
                logger.error(f"Failed to parse certificate: {e1}")
                return '' 
        pem_certs = "\n".join(cert.public_bytes(serialization.Encoding.PEM).decode() for cert in certs)
        return pem_certs
    except Exception as e:
        logger.error(f"Error in {kwargs.get('real_file_name')} get p7b content: {e}")
        return ''

def get_binary_content(file_path, config={}, **kwargs):
    max_length = config.get("max_length")
    
    with open(file_path, 'rb') as file:
        return file.read(max_length)

def get_docx_content(file_path, config={}, **kwargs):
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"No such file: '{file_path}'")
    
    max_length = config.get("max_length")
    max_ext_paragraphs = config.get("doc", {}).get("max_ext_paragraphs", DOC_MAX_EXT_PARAGRAPHS)
    
    doc = docx.Document(file_path)
    full_text = ''

    # Extract text from paragraphs
    for idx, paragraph in enumerate(doc.paragraphs):
        if idx >= max_ext_paragraphs:
            break
        full_text += f"{paragraph.text}\n"
        if max_length is not None and len(full_text) > max_length:
            return full_text[:max_length]
    
    for rel in doc.part.rels.values():
        if "image" in rel.reltype:
            image_part = rel.target_part
            image_blob = image_part.blob
            #print(f"image_blob: {image_blob}")
            try:
                img_text = image_extract_text(file_path, max_length, fp=io.BytesIO(image_blob), **kwargs)
                if img_text.strip():
                    full_text += img_text + '\n'
                if max_length is not None and len(full_text) > max_length:
                    return full_text[:max_length]
            except Exception as e:
                logger.debug(f"Error extracting text from image: {str(e)}")
                continue
    # Extract text from tables
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                full_text += f"{cell.text}\n"
                if max_length is not None and len(full_text) > max_length:
                    return full_text[:max_length]
    
    return full_text

def get_doc_content(file_path, config={}, **kwargs):
    import chardet
    max_length = config.get("max_length")
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"No such file: '{file_path}'")
    try:
        with open(file_path, "rb") as fp:
            content = fp.read()
        # Safely run antiword with a list of arguments and an environment dict
        env = dict(os.environ, HOME="/root")
        out = subprocess.check_output(
            ["antiword", "-"],
            input=content,
            env=env
        )

        detected = chardet.detect(out)
        encoding = detected.get("encoding", "utf-8")
        txt = out.decode(encoding)
        if max_length is not None and len(txt) > max_length:
            return txt[:max_length]
    
        return txt
    except subprocess.CalledProcessError as e:
        logger.debug(f"Antiword {kwargs.get('real_file_name')} failed with return code: {e.returncode} try catdoc")
        try:
            with open(file_path, 'rb') as fp:
                content = fp.read()

                env = dict(os.environ, HOME="/root")
                out = subprocess.check_output(
                    ["catdoc", "-"],
                    input=content,
                    env=env
                )
                detected = chardet.detect(out)
                encoding = detected.get("encoding", "utf-8")
                txt = out.decode(encoding)
                if max_length is not None and len(txt) > max_length:
                    return txt[:max_length]
        except Exception as e:
            logger.error(f"catdoc {kwargs.get('real_file_name')} failed with error: {e}")
            return ''
        #logger.error(f"Antiword {kwargs.get('real_file_name')} failed with error: {e}")
        #print("Antiword failed with error:", e.stderr)
        return ''

def get_rtf_content(file_path, config={}, **kwargs):
    max_length = config.get("max_length")
    with open(file_path, 'r') as file:
        rtf = file.read(max_length)
    return rtf_to_text(rtf, errors='ignore')

# for csv
def extract_csv_content(filepath, config={}, **kwargs):
    max_ext_rows = config.get("csv", {}).get("max_ext_rows", CSV_MAX_EXTRACT_ROWS)
    
    extract_text = ""
    try:
        max_hdr_search_rows = config.get("csv", {}).get("max_hdr_search_rows", CSV_MAX_HDR_SEARCH_ROWS)
        preview_data = pd.read_csv(filepath, encoding_errors='ignore', nrows=max_hdr_search_rows, header=None)
        # Check if first row is likely a header
        is_header = detect_table_headers(preview_data)
        
        if not is_header:
            # failback to text/plain
            return "exception_error_01d5" 

        # Read the full data with appropriate header setting
        data = pd.read_csv(filepath, encoding_errors='ignore', 
                          nrows=max_ext_rows,
                          header=0 if is_header else None)
        
        # If we have headers, use them in the output
        for _, row in data.iterrows():
            row_text = '. '.join([f"{col}: {row[col]}" for col in data.columns])
            extract_text += f"{row_text}\n"
                
    except Exception as e:
        logger.debug(f"{kwargs.get('real_file_name')} extract csv content failed, continue process with mime type: text/plain")
        extract_text = "exception_error_01d5"
    
    return extract_text

# for .xls
XLS_TIMEOUT_DEFAULT = 60
def get_excel_content(file_path, config={}, **kwargs):
    try:
        temp_dir = None
        if kwargs.get('temp_dir'):
            temp_dir = kwargs.get('temp_dir')
        else:
            temp_dir = os.path.dirname(file_path)
        output_file = temp_dir+'/_xls_result.json'
        
        # Use a default name if real_file_name is None
        original_filename = kwargs.get('real_file_name') or os.path.basename(file_path)
        
        command = [
            sys.executable,
            EXTRACT_PROCESSOR,
            file_path,                     # parsed_file
            original_filename,             # original_filename 
            json.dumps(config),            # config_json
            output_file,                   # output_file
            'xls',                         # type
            temp_dir                       # temp_dir
        ]
        
        process = subprocess.Popen(
            command,
            env={**os.environ, "TMPDIR": temp_dir, "OPENBLAS_NUM_THREADS": "1"},
            preexec_fn=lambda: resource.setrlimit(resource.RLIMIT_AS, (1024 * 1024 * 1024, 1024 * 1024 * 1024))  # Limit to 1GB
        )
        try:
            timeout = config.get('excel', {}).get('max_ext_timeout', XLSX_TIMEOUT_DEFAULT)
            return_code = process.wait(timeout=timeout)
            if return_code != 0:
                logger.debug(f"extract_processor failed with return code: {return_code}")
                return ''
            return try_get_result(output_file)
        except subprocess.TimeoutExpired:
            logger.debug(f"extract_processor timed out after {timeout} seconds")
            process.terminate()
            logger.debug(f"extract_processor terminated, sleep 1s")
            time.sleep(1) # wait for extract_processor to terminate
            ret = try_get_result(output_file)
            if ret == '':
                logger.debug(f"extract_processor kill")
                process.kill()
            return ret
        finally:
            if process.poll() is None:
                process.kill()
    except Exception as e:
        logger.error(f"Error in {kwargs.get('real_file_name')} get pdf content: {e}")
        return ''

# for .xlsx
XLSX_TIMEOUT_DEFAULT = 60

def extract_excel_content(file_path, config={}, **kwargs):
    try:
        temp_dir = None
        if kwargs.get('temp_dir'):
            temp_dir = kwargs.get('temp_dir')
        else:
            temp_dir = os.path.dirname(file_path)
        output_file = temp_dir+'/_xlsx_result.json'
        
        # Use a default name if real_file_name is None
        original_filename = kwargs.get('real_file_name') or os.path.basename(file_path)
        
        command = [
            sys.executable,
            EXTRACT_PROCESSOR,
            file_path,                     # parsed_file
            original_filename,             # original_filename 
            json.dumps(config),            # config_json
            output_file,                   # output_file
            'xlsx',                         # type
            temp_dir                       # temp_dir
        ]
        
        process = subprocess.Popen(
            command,
            env={**os.environ, "TMPDIR": temp_dir, "OPENBLAS_NUM_THREADS": "1"},
            preexec_fn=lambda: resource.setrlimit(resource.RLIMIT_AS, (1024 * 1024 * 1024, 1024 * 1024 * 1024))  # Limit to 1GB
        )
        try:
            timeout = config.get('excel', {}).get('max_ext_timeout', XLSX_TIMEOUT_DEFAULT)
            return_code = process.wait(timeout=timeout)
            if return_code != 0:
                logger.debug(f"extract_processor failed with return code: {return_code}")
                return ''
            return try_get_result(output_file)
        except subprocess.TimeoutExpired:
            logger.debug(f"extract_processor timed out after {timeout} seconds")
            process.terminate()
            logger.debug(f"extract_processor terminated, sleep 1s")
            time.sleep(1) # wait for extract_processor to terminate
            ret = try_get_result(output_file)
            if ret == '':
                logger.debug(f"extract_processor kill")
                process.kill()
            return ret
        finally:
            if process.poll() is None:
                process.kill()
    except Exception as e:
        logger.error(f"Error in {kwargs.get('real_file_name')} get pdf content: {e}")
        return ''
def try_get_result_ext(output_file):
    try:
        with open(output_file, 'r') as f:
            json_data = json.load(f)
        return json_data.get('text', ''), json_data.get('extra_info', {})
    except Exception as e:
        logger.error(f"Error reading extract_processor output file: {e}")
        return '', {}
PDF_TIMEOUT_DEFAULT = 60
def get_pdf_content(file_path, config={}, **kwargs):
    try:
        temp_dir = None
        if kwargs.get('temp_dir'):
            temp_dir = kwargs.get('temp_dir')
        else:
            temp_dir = os.path.dirname(file_path)
        output_file = temp_dir+'/_pdf_result.json'
        
        # Use a default name if real_file_name is None
        original_filename = kwargs.get('real_file_name') or os.path.basename(file_path)
        
        command = [
            sys.executable,
            EXTRACT_PROCESSOR,
            file_path,                     # parsed_file
            original_filename,             # original_filename 
            json.dumps(config),            # config_json
            output_file,                   # output_file
            'pdf',                         # type
            temp_dir                       # temp_dir
        ]
        
        process = subprocess.Popen(
            command,
            env={**os.environ, "TMPDIR": temp_dir, "OPENBLAS_NUM_THREADS": "1"},
            preexec_fn=lambda: resource.setrlimit(resource.RLIMIT_AS, (1024 * 1024 * 1024, 1024 * 1024 * 1024))  # Limit to 1GB
        )
        try:
            timeout = config.get('pdf', {}).get('max_ext_timeout', PDF_TIMEOUT_DEFAULT)
            return_code = process.wait(timeout=timeout)
            if return_code != 0:
                logger.debug(f"extract_processor failed with return code: {return_code}")
                return '', {}
            return try_get_result_ext(output_file)
        except subprocess.TimeoutExpired:
            logger.debug(f"extract_processor timed out after {timeout} seconds")
            process.terminate()
            logger.debug(f"extract_processor terminated, sleep 1s")
            time.sleep(1) # wait for extract_processor to terminate
            ret, ext = try_get_result_ext(output_file)
            if ret == '':
                logger.debug(f"extract_processor kill")
                process.kill()
            return ret, ext
        finally:
            if process.poll() is None:
                process.kill()
        
    except Exception as e:
        logger.error(f"Error in {kwargs.get('real_file_name')} get pdf content: {e}")
        return '', {}

def try_get_result(output_file):
    try:
        with open(output_file, 'r') as f:
            json_data = json.load(f)
        return json_data.get('text', '')
    except Exception as e:
        logger.error(f"Error reading extract_processor output file: {e}")
        return ''
PPT_TIMEOUT_DEFAULT = 60
def get_ppt_and_pptx_content(file_path, config={}, **kwargs):
    try:
        file_type = 'ppt' if kwargs.get('file_type') == 'application/vnd.ms-powerpoint' else 'pptx'
        file_dir = os.path.dirname(file_path)
        temp_dir = None
        if kwargs.get('temp_dir'):
            temp_dir = kwargs.get('temp_dir')
        else:
            temp_dir = os.path.dirname(file_path)
        output_file = '{}/_{}_result.json'.format(temp_dir, file_type)
        print(f"output_file: {output_file}")
        
        # Use a default name if real_file_name is None
        original_filename = kwargs.get('real_file_name') or os.path.basename(file_path)
        
        # Use a default name if real_file_name is None
        original_filename = kwargs.get('real_file_name') or os.path.basename(file_path)
        tempdir = None
        if kwargs.get('temp_dir'):
            tempdir = kwargs.get('temp_dir')
        else:
            tempdir = os.path.dirname(file_path)

        command = [
            sys.executable,
            EXTRACT_PROCESSOR,
            file_path,                     # parsed_file
            original_filename,             # original_filename 
            json.dumps(config),            # config_json
            output_file,                   # output_file
            file_type,                     # type
            tempdir,                       # temp_dir

        ]
        process = subprocess.Popen(
            command,
            env={**os.environ, "TMPDIR": tempdir, "OPENBLAS_NUM_THREADS": "1"},
            preexec_fn=None if file_type == 'ppt' else lambda: resource.setrlimit(resource.RLIMIT_AS, (1024 * 1024 * 1024, 1024 * 1024 * 1024))  # Limit to 1GB
        )
        try:
            timeout = config.get('ppt', {}).get('max_ext_timeout', PPT_TIMEOUT_DEFAULT)
            return_code = process.wait(timeout=timeout)
            if return_code != 0:
                logger.debug(f"extract_processor failed with return code: {return_code}")
                return ''
            return try_get_result(output_file)
        except subprocess.TimeoutExpired:
            logger.debug(f"extract_processor timed out after {timeout} seconds")
            process.terminate()
            logger.debug(f"extract_processor terminated, sleep 1s")
            time.sleep(1) # wait for extract_processor to terminate
            ret = try_get_result(output_file)
            if ret == '':
                logger.debug(f"extract_processor kill")
                process.kill()
            return ret
        finally:
            if process.poll() is None:
                process.kill()
    except Exception as e:
        logger.error(f"Error in {kwargs.get('real_file_name')} get pdf content: {e}")
        return ''


def get_source_code_content(file_path, config={}, **kwargs):
    max_length = config.get("max_length")
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            code = file.read(max_length)
    except Exception as e:
        logger.error(f"Error in {kwargs.get('real_file_name')} get source code content: {e}")
        return None
    return code

def get_odt_content(file_path, config={}, **kwargs):
    max_length = config.get("max_length")
    max_ext_paragraphs = config.get("odt", {}).get("max_ext_paragraphs", ODT_MAX_EXT_PARAGRAPHS)
    
    doc = load(file_path)
    all_paragraphs = doc.getElementsByType(text.P)
    text_content = ''
    for idx, paragraph in enumerate(all_paragraphs):
        if idx >= max_ext_paragraphs:
            break
        paragraph_text = teletype.extractText(paragraph)
        if max_length is not None and len(text_content + paragraph_text) > max_length:
            text_content += paragraph_text[:max_length - len(text_content)]
            break
        else:
            text_content += paragraph_text + '\n'
    return text_content

def get_ods_content(file_path, config={}, **kwargs):
    max_length = config.get("max_length")
    max_ext_sheets = config.get("ods", {}).get("max_ext_sheets", ODS_MAX_EXTRACT_SHEETS)
    max_ext_rows = config.get("ods", {}).get("max_ext_rows", ODS_MAX_EXTRACT_ROWS)

    doc = load(file_path)
    table = doc.spreadsheet.getElementsByType(Table)[0]
    table_data = []
    for idx, row in enumerate(table.getElementsByType(TableRow)):
        if idx >= max_ext_rows:
            break
        row_data = []
        for cell in row.getElementsByType(TableCell):
            cell_data = ''
            for p in cell.getElementsByType(text.P):
                if p.firstChild is not None:
                    cell_data += str(p.firstChild)
            row_data.append(cell_data)
        table_data.append(row_data)
        if max_length is not None and len(table_data) >= max_length:
            break
    return '\n'.join(['\t'.join(row) for row in table_data])

def get_odp_content(file_path, config={}, **kwargs):
    max_length = config.get("max_length")
    max_ext_slides = config.get('odp', {}).get("max_ext_slides", ODP_MAX_EXTRACT_SLIDES)
    
    presentation = load(file_path)
    
    texts = []
    for idx, slide in enumerate(presentation.getElementsByType(draw.Page)):  # 使用draw.Page获取幻灯片
        if idx >= max_ext_slides:
            break
        textboxes = slide.getElementsByType(draw.Frame)
        slide_texts = []
        for textbox in textboxes:
            ps = textbox.getElementsByType(text.P)
            for p in ps:
                slide_texts.append(teletype.extractText(p))
        texts.append('\n'.join(slide_texts))
    return '\n'.join(texts)

def get_visio_content(file_path, config={}, **kwargs):
    # TODO: Implement this function
    #doc = Visio(file_path)
    #return doc.toXml()
    return

def handle_csv(path, config, real_file_name, **kwargs):
    content = extract_csv_content(path, config, real_file_name=real_file_name)
    return content if content != "exception_error_01d5" else get_text_content(path, config, real_file_name=real_file_name)

def get_file_info(file_path, real_file_name = None, remote_file_type = None, config={}):
    old_tmpdir = None
    file_type = magic.Magic(mime=True).from_file(file_path)
    if (file_type is None or file_type == 'application/octet-stream') and real_file_name is not None:
        file_type, _ = mimetypes.guess_type(real_file_name)
    if file_type is None:
        file_type = remote_file_type
    file_info = {'file_type': file_type, 'extra_info': {}, 'file_content': ''}

    if config is None:
        config = {}
            
    # Create temp directory for file extraction
    temp_dir = os.path.join("/var/log/file_transformer/temp",
                           f"{os.path.basename(os.path.dirname(file_path))}_{os.path.basename(file_path)}")
    os.makedirs(temp_dir, exist_ok=True)
    config['temp_dir'] = temp_dir

    logger.debug(f'get_file_info, file_type: {file_type}, temp_dir: {temp_dir}') 
    try:
        old_tmpdir = os.environ.get("TMPDIR")
        os.environ["TMPDIR"] = temp_dir
        # Define a dictionary to map file types to their corresponding handler functions
        file_handlers = {
            'text/plain': get_text_content,
            'text/markdown': get_text_content,
            'application/json': get_text_content,
            'application/x-ndjson': get_text_content,
            'application/msword': get_doc_content,
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': get_docx_content,
            'application/vnd.ms-excel': get_excel_content,
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': \
                lambda path,config,real_file_name,file_type,temp_dir: \
                extract_excel_content(path, config, real_file_name=real_file_name, file_type=file_type, temp_dir=temp_dir),
            'application/pdf': get_pdf_content,
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': get_ppt_and_pptx_content,
            'application/vnd.ms-powerpoint': get_ppt_and_pptx_content,
            'text/csv': handle_csv,
            'application/csv': handle_csv,
            'application/rtf': get_rtf_content,
            'text/rtf': get_rtf_content,
            'application/vnd.visio': get_visio_content,
            'application/yaml': get_text_content,
            'application/toml': get_text_content,
            'application/x-pem-file': get_cert_content,
            'application/pkix-cert': get_cert_content,
            'application/x-x509-ca-cert': get_cert_content,
            'application/x-pkcs7-certificates': get_p7b_content,
            'application/x-wine-extension-ini': get_text_content,
            'application/javascript': get_source_code_content
        }

        # Define a dictionary to map file type prefixes to their corresponding handler functions
        prefix_handlers = {
            'text/x-': get_source_code_content,
            'application/vnd.oasis.opendocument.text': get_odt_content,
            'application/vnd.oasis.opendocument.spreadsheet': get_ods_content,
            'application/vnd.oasis.opendocument.presentation': get_odp_content,
            'image/': lambda path, config, real_file_name, temp_dir: \
                get_image_metadata_webp(path, config, real_file_name=real_file_name, temp_dir=temp_dir) \
                if file_type == 'image/webp' \
                else get_image_metadata(path, config, real_file_name=real_file_name, temp_dir=temp_dir),
            'audio/': get_audio_metadata,
            'video/': get_video_metadata,
        }

        # Check if the file type has a specific handler
        if file_type in file_handlers:
            if file_type == 'application/pdf':
                file_content, file_info['extra_info'] = file_handlers[file_type](file_path, config, real_file_name=real_file_name, temp_dir=temp_dir)
            else:
                file_content = file_handlers[file_type](file_path, config, real_file_name=real_file_name, file_type=file_type, temp_dir=temp_dir)
        elif any(file_type.startswith(prefix) for prefix in prefix_handlers):
            for prefix, handler in prefix_handlers.items():
                if file_type.startswith(prefix):
                    file_content = handler(file_path, config, real_file_name=real_file_name, temp_dir=temp_dir)
                    break
        else:
            #file_content = get_binary_content(file_path, config)
            file_content = ''
            logger.error(f"Unsupported {real_file_name} file type: {file_type}")

        file_info['file_content'] = file_content
    except Exception as e:
        logger.error(f"Error in get {real_file_name} file content: {e}")
    finally:
        if old_tmpdir is None:
            del os.environ["TMPDIR"]
        else:
            os.environ["TMPDIR"] = old_tmpdir

        # Clean up temp directory
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except Exception as e:
            logger.error(f"Error cleaning up temp directory {temp_dir}: {e}")

        

    return file_info

