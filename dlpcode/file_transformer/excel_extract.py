from openpyxl import load_workbook
from openpyxl.utils.exceptions import InvalidFileException
import tempfile
import pandas as pd
import sys
import os
import shutil
import signal
from file_transformer.header_detect import detect_table_headers
# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from util.common_log import get_logger
logger = get_logger("dlp")

# Add global flag for graceful shutdown
_shutdown_requested = False

def signal_handler(signum, frame):
    """Handle termination signals gracefully"""
    global _shutdown_requested
    _shutdown_requested = True
    logger.info(f"Received signal {signum}, will attempt to save partial results")

# Setup signal handlers once at module level
signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)

# For excel
EXCEL_MAX_HDR_SEARCH_ROWS=32
EXCEL_MAX_EXTRACT_ROWS=1000  # It takes about 3s
EXCEL_MAX_EXTRACT_SHEETS=16

def xls_content_extractor(file_path, config={}, **kwargs):
    text = ''
    try:
        max_length = config.get("max_length")
        max_ext_sheets = config.get("excel", {}).get("max_ext_sheets", EXCEL_MAX_EXTRACT_SHEETS)
        max_ext_rows = config.get("excel", {}).get("max_ext_rows", EXCEL_MAX_EXTRACT_ROWS)

        with pd.ExcelFile(file_path) as xls:
            for sheet_name in xls.sheet_names[:max_ext_sheets]:
                if _shutdown_requested:
                    logger.info("Interrupted during XLS extraction, returning partial content")
                    break

                df = pd.read_excel(xls, sheet_name, header=None, nrows=max_ext_rows)
                for index, row in df.iterrows():
                    if _shutdown_requested:
                        break
                    row_list = [str(col) for col in row]
                    row_text = '. '.join(row_list) + '\n'
                    if max_length is not None and len(text + row_text) > max_length:
                        text += row_text[:max_length - len(text)]
                        return text
                    else:
                        text += row_text
    except Exception as e:
        logger.error(f"Error in XLS content extraction: {str(e)}")

    return text

# for .xlsx
def xlsx_content_extractor(download_fp, config={}, **kwargs):
    texts = []
    filepath = None
    temp_dir = None
    real_file_name = kwargs.get('real_file_name', None)
    try:
        if real_file_name is None:
            filepath = download_fp
        else:
            if kwargs.get('temp_dir'):
                temp_dir = kwargs.get('temp_dir')
            else:
                temp_dir = tempfile.mkdtemp(dir=os.path.dirname(download_fp))
            filepath = os.path.join(temp_dir, os.path.basename(real_file_name))
            os.symlink(download_fp, filepath)

        try:
            workbook = load_workbook(filepath, read_only=True)
        except InvalidFileException:
            new_filename = os.path.splitext(os.path.basename(download_fp))[0] + '.xlsx'
            if temp_dir is None:
                temp_dir = tempfile.mkdtemp(dir=os.path.dirname(download_fp))
            filepath = os.path.join(temp_dir, new_filename)
            os.symlink(download_fp, filepath)
            workbook = load_workbook(filepath, read_only=True)

        max_ext_sheets = config.get("excel", {}).get("max_ext_sheets", EXCEL_MAX_EXTRACT_SHEETS)
        max_hdr_search_rows = config.get("excel", {}).get("max_hdr_search_rows", EXCEL_MAX_HDR_SEARCH_ROWS)
        max_ext_rows = config.get("excel", {}).get("max_ext_rows", EXCEL_MAX_EXTRACT_ROWS)

        # just extract the first max_ext_sheets sheets
        for sheet_name in workbook.sheetnames[:max_ext_sheets]:
            if _shutdown_requested:
                logger.info("Interrupted during XLSX extraction, returning partial content")
                break

            sheet = workbook[sheet_name]

            # just detect the first max_hdr_search_rows rows
            df_det = pd.read_excel(filepath, header=None, sheet_name=sheet_name, nrows=max_hdr_search_rows)
            is_header = detect_table_headers(df_det)

            extract_text = f"Sheet:{sheet_name}\n"
            if is_header:
                df_data = pd.read_excel(filepath, header=0, sheet_name=sheet_name, nrows=max_ext_rows+1)
                for _, row in df_data.iterrows():
                    if _shutdown_requested:
                        break
                    row_text = '. '.join([f"{col}: {row[col]}" for col in df_data.columns])
                    extract_text += f"{row_text}\n"
                # print(f"{extract_text}")
                texts.append(extract_text)
            else:
                # just extract the first EXCEL_MAX_EXTRACT_ROWS rows
                df_data = pd.read_excel(filepath, header=None, sheet_name=sheet_name, nrows=EXCEL_MAX_EXTRACT_ROWS)
                extract_text += df_data.to_string()
                # print(f"{extract_text}")
                texts.append(extract_text)
    except Exception as e:
        logger.error(f"Error in {kwargs.get('real_file_name')} extract excel content: {e}")
    finally:
        #logger.info("out extract_excel_content")
        if temp_dir:
            shutil.rmtree(temp_dir)

    ret = "\n".join(texts)
    return ret