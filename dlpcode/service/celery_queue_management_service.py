import pickle
from exts import logger
from celery import Celery
from celery_app import app
from util.config import configs
from util.db import get_redis_client


class CeleryQueueManagementService:
    """Celery queue management service to replace HueyQueueManagementService"""

    @classmethod
    def clean_up_called_by_scheduler(cls) -> bool:
        """Clean up Celery results and expired tasks"""
        from domain_model.tracker.session_tracker import SessionTracker
        from util.db import get_redis_client

        try:
            rs_client = get_redis_client()
            
            # Clean up expired results from Celery result backend
            # Celery automatically handles result expiration based on result_expires setting
            # But we can manually clean up specific results if needed
            
            # Get Celery inspect instance to check active/scheduled tasks
            inspect = app.control.inspect()
            
            # Clean up tasks for expired sessions
            active_tasks = inspect.active()
            scheduled_tasks = inspect.scheduled()
            
            if active_tasks:
                for worker, tasks in active_tasks.items():
                    for task in tasks:
                        task_args = task.get('args', [])
                        task_kwargs = task.get('kwargs', {})
                        
                        # Check if task has session_key and if session is expired
                        session_key = None
                        scan_policy_id = None
                        
                        if len(task_args) > 0:
                            scan_policy_id = task_args[0] if isinstance(task_args[0], str) else None
                        
                        if 'session_key' in task_kwargs:
                            session_key = task_kwargs['session_key']
                        elif len(task_args) > 5:  # Assuming session_key is typically the 6th argument
                            session_key = task_args[5] if isinstance(task_args[5], str) else None
                        
                        if scan_policy_id and session_key:
                            if not SessionTracker(scan_policy_id, session_key).is_alive():
                                # Revoke the task
                                app.control.revoke(task['id'], terminate=True)
                                logger.info(f"Revoked expired task {task['id']} for session {session_key}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error in Celery cleanup: {e}")
            return False

    @classmethod
    def get_pending_count(cls, queue_name: str = None) -> int:
        """
        Get the number of pending tasks in a specific queue or all queues.
        
        Args:
            queue_name: Name of the queue to check. If None, checks all queues.
            
        Returns:
            int: Number of pending tasks.
        """
        try:
            inspect = app.control.inspect()
            
            # Get reserved (active) and scheduled tasks
            reserved = inspect.reserved()
            scheduled = inspect.scheduled()
            
            total_count = 0
            
            if reserved:
                for worker, tasks in reserved.items():
                    if queue_name:
                        # Filter by queue name
                        queue_tasks = [t for t in tasks if t.get('delivery_info', {}).get('routing_key') == queue_name]
                        total_count += len(queue_tasks)
                    else:
                        total_count += len(tasks)
            
            if scheduled:
                for worker, tasks in scheduled.items():
                    if queue_name:
                        # Filter by queue name
                        queue_tasks = [t for t in tasks if t.get('delivery_info', {}).get('routing_key') == queue_name]
                        total_count += len(queue_tasks)
                    else:
                        total_count += len(tasks)
            
            return total_count
            
        except Exception as e:
            logger.error(f"Error getting pending count: {e}")
            return 0

    @classmethod
    def is_task_enqueued(cls, scan_policy_id: str, worker_name: str = None) -> bool:
        """
        Check if a task with the given scan_policy_id is enqueued.
        
        Args:
            scan_policy_id: The scan policy ID to check for.
            worker_name: Worker name (not used in Celery, kept for compatibility).
            
        Returns:
            bool: True if the task is enqueued, False otherwise.
        """
        try:
            inspect = app.control.inspect()
            
            # Check active tasks
            active_tasks = inspect.active()
            if active_tasks:
                for worker, tasks in active_tasks.items():
                    for task in tasks:
                        task_args = task.get('args', [])
                        task_kwargs = task.get('kwargs', {})
                        
                        # Check if task matches scan_policy_id
                        if (task_args and task_args[0] == scan_policy_id) or \
                           (task_kwargs and task_kwargs.get("task_uuid", "") == scan_policy_id):
                            return True
            
            # Check scheduled tasks
            scheduled_tasks = inspect.scheduled()
            if scheduled_tasks:
                for worker, tasks in scheduled_tasks.items():
                    for task in tasks:
                        task_args = task.get('args', [])
                        task_kwargs = task.get('kwargs', {})
                        
                        # Check if task matches scan_policy_id
                        if (task_args and task_args[0] == scan_policy_id) or \
                           (task_kwargs and task_kwargs.get("task_uuid", "") == scan_policy_id):
                            return True
            
            # Check reserved tasks
            reserved_tasks = inspect.reserved()
            if reserved_tasks:
                for worker, tasks in reserved_tasks.items():
                    for task in tasks:
                        task_args = task.get('args', [])
                        task_kwargs = task.get('kwargs', {})
                        
                        # Check if task matches scan_policy_id
                        if (task_args and task_args[0] == scan_policy_id) or \
                           (task_kwargs and task_kwargs.get("task_uuid", "") == scan_policy_id):
                            return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking if task is enqueued: {e}")
            return False

    @classmethod
    def revoke_tasks_by_session(cls, scan_policy_id: str, session_key: str) -> int:
        """
        Revoke all tasks for a specific session.
        
        Args:
            scan_policy_id: The scan policy ID.
            session_key: The session key.
            
        Returns:
            int: Number of tasks revoked.
        """
        try:
            inspect = app.control.inspect()
            revoked_count = 0
            
            # Get all active and scheduled tasks
            all_tasks = {}
            
            active_tasks = inspect.active()
            if active_tasks:
                for worker, tasks in active_tasks.items():
                    all_tasks.update({task['id']: task for task in tasks})
            
            scheduled_tasks = inspect.scheduled()
            if scheduled_tasks:
                for worker, tasks in scheduled_tasks.items():
                    all_tasks.update({task['id']: task for task in tasks})
            
            reserved_tasks = inspect.reserved()
            if reserved_tasks:
                for worker, tasks in reserved_tasks.items():
                    all_tasks.update({task['id']: task for task in tasks})
            
            # Find and revoke matching tasks
            for task_id, task in all_tasks.items():
                task_args = task.get('args', [])
                task_kwargs = task.get('kwargs', {})
                
                # Check if task matches scan_policy_id and session_key
                matches_policy = (task_args and task_args[0] == scan_policy_id) or \
                               (task_kwargs and task_kwargs.get("task_uuid", "") == scan_policy_id)
                
                matches_session = False
                if 'session_key' in task_kwargs and task_kwargs['session_key'] == session_key:
                    matches_session = True
                elif len(task_args) > 5 and task_args[5] == session_key:
                    matches_session = True
                
                if matches_policy and matches_session:
                    app.control.revoke(task_id, terminate=True)
                    revoked_count += 1
                    logger.info(f"Revoked task {task_id} for session {session_key}")
            
            return revoked_count
            
        except Exception as e:
            logger.error(f"Error revoking tasks by session: {e}")
            return 0

    @classmethod
    def get_worker_stats(cls) -> dict:
        """
        Get statistics about Celery workers.
        
        Returns:
            dict: Worker statistics.
        """
        try:
            inspect = app.control.inspect()
            
            stats = {
                'active_workers': 0,
                'total_active_tasks': 0,
                'total_scheduled_tasks': 0,
                'total_reserved_tasks': 0,
                'workers': {}
            }
            
            # Get worker stats
            worker_stats = inspect.stats()
            if worker_stats:
                stats['active_workers'] = len(worker_stats)
                stats['workers'] = worker_stats
            
            # Get task counts
            active_tasks = inspect.active()
            if active_tasks:
                for worker, tasks in active_tasks.items():
                    stats['total_active_tasks'] += len(tasks)
            
            scheduled_tasks = inspect.scheduled()
            if scheduled_tasks:
                for worker, tasks in scheduled_tasks.items():
                    stats['total_scheduled_tasks'] += len(tasks)
            
            reserved_tasks = inspect.reserved()
            if reserved_tasks:
                for worker, tasks in reserved_tasks.items():
                    stats['total_reserved_tasks'] += len(tasks)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting worker stats: {e}")
            return {}
