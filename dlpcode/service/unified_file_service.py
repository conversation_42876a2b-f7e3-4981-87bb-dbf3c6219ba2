import json
import time
import hashlib
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from exts import rs_client
from util.common_log import get_logger
from util.config import configs
from ddr.util.config import ddr_configs
from util.enum_ import QueueStatus

logger = get_logger("unified_file")


class FileProcessingItem:
    """Unified file processing item"""
    def __init__(self, source_type: str, priority: int = 0):
        self.source_type = source_type  # "scan_policy" or "ddr"
        self.priority = priority
        self.timestamp = time.time()
        self.item_id = f"{source_type}_{int(self.timestamp * 1000000)}"

    def to_dict(self) -> dict:
        """Transform object to dictionary"""
        return {
            "source_type": self.source_type,
            "priority": self.priority,
            "timestamp": self.timestamp,
            "item_id": self.item_id
        }


class ScanPolicyFileItem(FileProcessingItem):
    """Scan Policy file processing item"""
    def __init__(self, task_uuid: str, file_info: dict, params: dict, 
                 session_key: str, backlog_hash: str, priority: int = 0):
        super().__init__("scan_policy", priority)
        self.task_uuid = task_uuid
        self.file_info = file_info
        self.params = params
        self.session_key = session_key
        self.backlog_hash = backlog_hash

    def to_dict(self) -> dict:
        data = super().to_dict()
        data.update({
            "task_uuid": self.task_uuid,
            "file_info": self.file_info,
            "params": self.params,
            "session_key": self.session_key,
            "backlog_hash": self.backlog_hash
        })
        return data


class DDRFileItem(FileProcessingItem):
    """DDR file processing item"""
    def __init__(self, task_uuid: str, file_info: dict, params: dict,
                 session_key: str, backlog_hash: str, priority: int = 8):  # DDR default priority
        super().__init__("ddr", priority)
        self.task_uuid = task_uuid
        self.file_info = file_info
        self.params = params
        self.session_key = session_key
        self.backlog_hash = backlog_hash

    def to_dict(self) -> dict:
        data = super().to_dict()
        data.update({
            "task_uuid": self.task_uuid,
            "file_info": self.file_info,
            "params": self.params,
            "session_key": self.session_key,
            "backlog_hash": self.backlog_hash
        })
        return data


class UnifiedFileScheduler:
    """Unified file scheduler - schedule scan_policy and DDR files at the file level"""

    db = rs_client
    hash_key = "unified_file"

    def __init__(self, task_id: str, session_key: str=""):
        try:
            self.task_id = task_id
            self.session_key = session_key
            self.db = self.__class__.db
            self.hash_key = self.__class__.hash_key

            if self.session_key:
                self.ddr_field_counter = f"{self.task_id}:{self.session_key}_ddr:counter"
                self.scan_policy_field_counter = f"{self.task_id}:{self.session_key}_scan_policy:counter"
                self.field_status = f"{self.task_id}:{self.session_key}:status"
            else:
                self.scan_policy_field_counter = f"{self.task_id}_scan_policy:counter"
                self.ddr_field_counter = f"{self.task_id}_ddr:counter"
                self.field_status = f"{self.task_id}:status"
        except Exception as e:
            logger.error(e)

        self.queue_key = "file_proc_queue"
        self.counter_key_prefix = "proc_count"

        # Read ratio settings from config file
        ratio_config = configs.get("file_unified_queue", {})
        self.ddr_weight = ratio_config.get("ddr_weight", 5)
        self.scan_policy_weight = ratio_config.get("scan_policy_weight", 3)
        self.max_concurrent_ddr = ratio_config.get("max_concurrent_ddr_files", 50)
        self.max_concurrent_scan_policy = ratio_config.get("max_concurrent_scan_policy_files", 20)

        logger.info(f"Unified File Scheduler initialized - DDR weight: {self.ddr_weight}, "
                   f"Scan Policy weight: {self.scan_policy_weight}")

    def add_scan_policy_file(self, item: ScanPolicyFileItem) -> bool:
        """Add scan_policy file to unified queue"""
        try:
            # current_count = self.get_processing_count("scan_policy")
            # if current_count >= self.max_concurrent_scan_policy:
            #     logger.warning(f"Scan policy queue is full. Current: {current_count}, Max: {self.max_concurrent_scan_policy}")
            #     return False

            return self._add_file_to_queue(item)

        except Exception as e:
            logger.error(f"Error adding scan policy file: {e}")
            return False

    def add_ddr_file(self, item: DDRFileItem) -> bool:
        """Add DDR file to unified queue"""
        try:
            # current_count = self.get_processing_count("ddr")
            # if current_count >= self.max_concurrent_ddr:
            #     logger.warning(f"DDR queue is full. Current: {current_count}, Max: {self.max_concurrent_ddr}")
            #     return False

            return self._add_file_to_queue(item)

        except Exception as e:
            logger.error(f"Error adding DDR file: {e}")
            return False

    def _add_file_to_queue(self, item: FileProcessingItem) -> bool:
        """Add file item to queue"""
        try:
            item_data = item.to_dict()

            # Use priority and timestamp as score, higher priority means lower score (higher priority)
            score = (100 - item.priority) * 1000000 + item.timestamp

            self.db.zadd(self.queue_key, {json.dumps(item_data): score})

            logger.info(f"Added {item.source_type} file to unified queue, priority: {item.priority}")
            return True

        except Exception as e:
            logger.error(f"Error adding file to queue: {e}")
            return False

    def get_next_file_to_process(self) -> Optional[Dict[str, Any]]:
        """Get next file to process based on ratio"""
        try:
            # Check current processing count
            ddr_count = self.get_processing_count("ddr")
            scan_policy_count = self.get_processing_count("scan_policy")
            total_count = ddr_count + scan_policy_count

            # If no files are being processed, get the highest priority file
            if total_count == 0:
                return self._get_highest_priority_file()

            # Calculate current ratio
            current_ddr_ratio = ddr_count / total_count
            target_ddr_ratio = self.ddr_weight / (self.ddr_weight + self.scan_policy_weight)

            preferred_type = None
            if current_ddr_ratio < target_ddr_ratio:
                # DDR ratio is not enough, process DDR file first
                preferred_type = "ddr"
            elif current_ddr_ratio > target_ddr_ratio:
                # DDR ratio is too high, process scan_policy file first
                preferred_type = "scan_policy"

            # Try to get a file of the specified type
            if preferred_type:
                file_item = self._get_file_by_type(preferred_type)
                if file_item:
                    return file_item

            # If no files of the specified type, get any type of file
            return self._get_highest_priority_file()

        except Exception as e:
            logger.error(f"Error getting next file to process: {e}")
            return None

    def _get_highest_priority_file(self) -> Optional[Dict[str, Any]]:
        """Get the highest priority file"""
        try:
            # Get the item with the lowest score (highest priority) from the sorted set
            result = self.db.zrange(self.queue_key, 0, 0, withscores=True)
            if not result:
                return None

            item_json, score = result[0]
            item_data = json.loads(item_json)

            # Check if this type of file can be processed
            source_type = item_data.get("source_type")
            current_count = self.get_processing_count(source_type)
            max_count = self.max_concurrent_ddr if source_type == "ddr" else self.max_concurrent_scan_policy

            if current_count >= max_count:
                logger.warning(f"Cannot process {source_type} file, queue is full: {current_count}/{max_count}")
                return None

            # Remove the item from the queue
            self.db.zrem(self.queue_key, item_json)

            # Increment the processing counter
            self.increment_processing_counter(source_type)

            logger.info(f"Retrieved {source_type} file from unified queue")
            return item_data

        except Exception as e:
            logger.error(f"Error getting highest priority file: {e}")
            return None

    def _get_file_by_type(self, source_type: str) -> Optional[Dict[str, Any]]:
        """Get a file of the specified type"""
        try:
            # Check concurrency limit
            current_count = self.get_processing_count(source_type)
            max_count = self.max_concurrent_ddr if source_type == "ddr" else self.max_concurrent_scan_policy

            if current_count >= max_count:
                return None

            # Get all files and filter by type
            all_items = self.db.zrange(self.queue_key, 0, -1, withscores=True)

            for item_json, score in all_items:
                try:
                    item_data = json.loads(item_json)
                    if item_data.get("source_type") == source_type:
                        # Found a matching file, remove it from the queue
                        self.db.zrem(self.queue_key, item_json)

                        # Increment the processing counter
                        self.increment_processing_counter(source_type)

                        logger.info(f"Retrieved {source_type} file from unified queue (by type)")
                        return item_data
                except json.JSONDecodeError:
                    # Remove invalid data
                    self.db.zrem(self.queue_key, item_json)
                    continue

            return None

        except Exception as e:
            logger.error(f"Error getting file by type {source_type}: {e}")
            return None

    def get_processing_count(self, source_type: str) -> int:
        """Get the number of files being processed of the specified type"""
        try:
            counter_key = f"{self.counter_key_prefix}:{source_type}"
            count = self.db.get(counter_key)
            return int(count) if count else 0
        except Exception as e:
            logger.error(f"Error getting processing count for {source_type}: {e}")
            return 0

    def increment_processing_counter(self, source_type: str) -> bool:
        """Increment the processing counter"""
        try:
            counter_key = f"{self.counter_key_prefix}:{source_type}"
            self.db.incr(counter_key)
            return True
        except Exception as e:
            logger.error(f"Error incrementing counter for {source_type}: {e}")
            return False

    def decrement_processing_counter(self, source_type: str) -> bool:
        """Decrement the processing counter"""
        try:
            counter_key = f"{self.counter_key_prefix}:{source_type}"
            current = self.db.get(counter_key)
            if current and int(current) > 0:
                self.db.decr(counter_key)
            return True
        except Exception as e:
            logger.error(f"Error decrementing counter for {source_type}: {e}")
            return False

    def get_queue_status(self) -> Dict[str, Any]:
        """Get the status of the queue"""
        try:
            # Count the number of files of each type in the queue
            all_items = self.db.zrange(self.queue_key, 0, -1)
            ddr_queue_count = 0
            scan_policy_queue_count = 0

            for item_json in all_items:
                try:
                    item_data = json.loads(item_json)
                    source_type = item_data.get("source_type")
                    if source_type == "ddr":
                        ddr_queue_count += 1
                    elif source_type == "scan_policy":
                        scan_policy_queue_count += 1
                except json.JSONDecodeError:
                    continue

            return {
                "total_queue_size": len(all_items),
                "ddr_queue_size": ddr_queue_count,
                "scan_policy_queue_size": scan_policy_queue_count,
                "ddr_processing_count": self.get_processing_count("ddr"),
                "scan_policy_processing_count": self.get_processing_count("scan_policy"),
                "max_concurrent_ddr": self.max_concurrent_ddr,
                "max_concurrent_scan_policy": self.max_concurrent_scan_policy,
                "ddr_weight": self.ddr_weight,
                "scan_policy_weight": self.scan_policy_weight,
                "timestamp": time.time()
            }
        except Exception as e:
            logger.error(f"Error getting queue status: {e}")
            return {}

    def clear_expired_items(self, max_age_hours: int = 24) -> int:
        """Clean up expired queue items"""
        try:
            cutoff_time = time.time() - (max_age_hours * 3600)
            all_items = self.db.zrange(self.queue_key, 0, -1, withscores=True)
            expired_count = 0

            for item_json, score in all_items:
                try:
                    item_data = json.loads(item_json)
                    if item_data.get("timestamp", 0) < cutoff_time:
                        self.db.zrem(self.queue_key, item_json)
                        expired_count += 1
                except json.JSONDecodeError:
                    # Remove invalid data
                    self.db.zrem(self.queue_key, item_json)
                    expired_count += 1

            if expired_count > 0:
                logger.info(f"Cleared {expired_count} expired items from unified queue")

            return expired_count

        except Exception as e:
            logger.error(f"Error clearing expired items: {e}")
            return 0
