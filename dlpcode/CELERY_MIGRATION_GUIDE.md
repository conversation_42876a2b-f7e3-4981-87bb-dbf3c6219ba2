# Huey到Celery迁移指南

## 概述

本文档描述了将DLP系统从Huey任务队列迁移到Celery的完整过程。迁移的主要目标是：

1. 提供更好的任务优先级支持
2. 增强DDR（Data Detection Response）任务的高优先级处理
3. 改善任务监控和管理能力
4. 提供更好的扩展性和可靠性

## 迁移内容

### 1. 核心配置更改

#### 配置文件更新
- `config.json`: 将`huey`配置段替换为`celery`配置段
- 新增任务路由、优先级队列、定时任务配置

#### 新增文件
- `celery_app.py`: Celery应用配置
- `requirements_celery.txt`: Celery相关依赖
- `celery_worker/`: 新的Celery任务模块目录

### 2. 任务文件迁移

#### 已迁移的任务模块
- `analyze_worker.py`: 文件分析任务（支持普通和高优先级）
- `ddr_task.py`: DDR任务（优先级8，新增紧急优先级10）
- `tag_worker.py`: 文件标签更新任务

#### 优先级映射
```python
PRIORITY_LEVELS = {
    'LOW': 1,
    'NORMAL': 5,
    'HIGH': 7,
    'DDR': 8,
    'CRITICAL': 9,
    'EMERGENCY': 10
}
```

### 3. 服务层更新

#### 队列管理服务
- `CeleryQueueManagementService`: 替换`HueyQueueManagementService`
- 支持任务撤销、状态查询、统计信息

#### 任务管理服务
- 更新`TaskManagementService`以使用Celery API
- 创建新的数据模型：`CeleryTaskBacklog`、`CeleryResumeBacklog`

### 4. DDR高优先级支持

#### DDR任务优先级
- 默认优先级：8（DDR级别）
- 紧急情况优先级：10（EMERGENCY级别）
- 独立的DDR队列：`ddr`

#### DDR相关更新
- `ddr_service.py`: 更新任务调度逻辑使用Celery
- `unified_file_service.py`: DDR文件项默认优先级设为8
- 支持DDR独立运行时的高优先级处理

## 启动和管理

### 启动Celery Workers
```bash
# 启动所有workers和beat调度器
./launch_celery_worker.sh

# 手动启动特定worker
celery -A celery_app worker --hostname=ddr_worker@%h --queues=ddr --concurrency=4
```

### 停止Celery Workers
```bash
# 停止所有workers
./stop_celery_worker.sh
```

### 监控
```bash
# 查看活跃任务
celery -A celery_app inspect active

# 查看队列状态
celery -A celery_app inspect active_queues

# 启动Flower监控界面（需要安装flower）
celery -A celery_app flower
```

## 队列配置

### 队列类型
- `analyze`: 文件分析任务
- `ddr`: DDR任务（高优先级）
- `download`: 文件下载任务
- `report`: 报告生成任务
- `protection`: 保护动作任务
- `default`: 默认队列

### Worker配置
- `analyze_worker`: 8个并发进程
- `ddr_worker`: 4个并发进程（高优先级）
- `download_worker`: 6个并发进程
- `default_worker`: 2个并发进程
- `report_worker`: 2个并发进程
- `protection_worker`: 2个并发进程

## 定时任务

### Beat调度器配置
```python
beat_schedule = {
    "ddr-dispatcher": {
        "task": "celery_worker.ddr_task.scheduled_ddr_dispatcher",
        "schedule": 120.0  # 每2分钟
    },
    "fetch-storage-task": {
        "task": "celery_worker.fetch_storage.fetch_storage_task",
        "schedule": "crontab(minute='*/10')"  # 每10分钟
    }
}
```

## 安装依赖

```bash
# 安装Celery相关依赖
pip install -r requirements_celery.txt
```

## 测试迁移

```bash
# 运行迁移测试脚本
python test_celery_migration.py
```

## 注意事项

### 1. 数据库迁移
- 需要创建新的Celery相关数据表
- 可能需要迁移现有的Huey任务数据

### 2. 监控和日志
- Celery日志位于`log/`目录
- 使用Flower或Celery inspect命令监控任务状态

### 3. 性能调优
- 根据实际负载调整worker并发数
- 监控Redis内存使用情况
- 调整任务超时设置

### 4. 回滚计划
- 保留原有Huey代码作为备份
- 确保数据库兼容性
- 准备快速回滚脚本

## 待完成的任务

1. 完成所有Huey任务模块的迁移
2. 更新所有引用Huey的服务代码
3. 创建数据库迁移脚本
4. 完善错误处理和重试机制
5. 性能测试和优化

## 联系信息

如有问题，请联系开发团队进行支持。
