import redis
import json
import time


def load_config():
    return {
        "redis_host": "localhost",
        "redis_port": 6379,
        "stream_key": "email:send_queue",
    }


def send_email_request(template_id, variables, timestamp=None):
    """
    Send an email request to the Redis stream.
    """
    cfg = load_config()
    r = redis.Redis(host=cfg["redis_host"], port=cfg["redis_port"], decode_responses=True)
    if timestamp is None:
        timestamp = int(time.time())
    msg = {
        "template_id": template_id,
        "variables": json.dumps(variables),
        "timestamp": timestamp
    }
    stream_id = r.xadd(cfg["stream_key"], msg)
    print(f"Sent email request to stream {cfg['stream_key']} with ID {stream_id}: {msg}")


if __name__ == "__main__":
    import uuid as uuidlib
    from datetime import datetime
    variables = {
        "recipient_name": "Security Team",
        "incident_id": f"INC-{datetime.now().strftime('%Y%m%d')}-{uuidlib.uuid4().hex[:8].upper()}",
        "severity": "High",
        "incident_type": "Unauthorized Access Attempt",
        "description": "Multiple failed login attempts detected from suspicious IP address. Potential brute force attack in progress.",
        "detected_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "source_ip": "*************",
        "affected_system": "Web Application Server",
        "recommended_actions": "1. Block the source IP\n2. Review logs\n3. Notify administrators"
    }
    send_email_request(
        template_id="8f7517d5-1f16-4837-ae26-722291d8b7fe",
        variables=variables
    )
