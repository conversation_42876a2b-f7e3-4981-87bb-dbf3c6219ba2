import json
import signal
import sys
import threading
import time
from queue import Queue, Empty
from typing import Dict, Set

import redis

from exts import Session
from notifications.model.notification import Template
from notifications.notifier.factory import notifier_factory
from notifications.templates import TemplateEngine
from util.common_log import get_logger

# Configuration for retry
MAX_RETRY = 3
RETRY_INTERVAL = 10  # seconds

aggregation_queues: Dict[str, Queue] = {}
worker_threads: Dict[str, threading.Thread] = {}
stop_events: Dict[str, threading.Event] = {}
last_sent_time: Dict[str, float] = {}

cfg = None
logger = get_logger("email_daemon")


def load_config():
    # TODO: Implement config loading logic or load from file
    cfg = {
        "redis_host": "localhost",
        "redis_port": 6379,
        "stream_key": "email:send_queue",
        "group": "email_daemon_group",
        "consumer": "email_daemon_consumer",
        "aggregation_window": 10,  # seconds
        "template_cleanup_interval": 300,  # seconds
    }
    logger and logger.debug(f"Loaded config: {cfg}")
    return cfg


class EmailRequest:
    def __init__(self, template_id: str, variables: Dict[str, str], timestamp: int, retry_count: int = 0):
        self.template_id = template_id
        self.variables = variables
        self.timestamp = timestamp
        self.retry_count = retry_count
        logger and logger.debug(f"Created EmailRequest: template_id={template_id}, variables={variables}, timestamp={timestamp}, retry_count={retry_count}")


def get_min_interval_seconds(extra):
    try:
        min_interval = int(extra.get('min_interval', 60))
        logger and logger.debug(f"Parsed min_interval_seconds: {min_interval} from extra: {extra}")
        return min_interval
    except Exception as e:
        logger and logger.error(f"Failed to parse min_interval from extra {extra}: {e}")
        return 60


def initialize_redis_stream(r: redis.Redis, stream_key: str, group: str, logger):
    logger.debug(f"Initializing Redis stream: {stream_key}, group: {group}")
    try:
        r.xgroup_create(name=stream_key, groupname=group, id='0', mkstream=True)
        logger.info(f"[SUCCESS][NEW]: XGROUP CREATE {stream_key} {group} 0 MKSTREAM")
    except redis.exceptions.ResponseError as e:
        if "BUSYGROUP" in str(e):
            logger.info(f"[SUCCESS][EXISTED]: XGROUP CREATE {stream_key} {group} 0 MKSTREAM")
        else:
            logger.error(f"[FAILED]: XGROUP CREATE {stream_key} {group} 0 MKSTREAM: {e}")
            sys.exit(1)


def listen_stream(r: redis.Redis, stream_key: str, group: str, consumer: str, logger, message_callback):
    logger.debug(f"Starting listen_stream for stream_key={stream_key}, group={group}, consumer={consumer}")
    while True:
        try:
            resp = r.xreadgroup(group, consumer, {stream_key: '>'}, count=10, block=2000)
            logger.debug(f"xreadgroup response: {resp}")
            for stream, messages in resp:
                for msg_id, msg_data in messages:
                    logger.info(f"Received message {msg_id} from stream {stream_key}")
                    logger.debug(f"Message data: {msg_data}")
                    message_callback(msg_data)
                    r.xack(stream_key, group, msg_id)
                    r.xdel(stream_key, msg_id)
        except redis.exceptions.TimeoutError:
            logger.debug("Redis stream read timeout, continuing...")
            continue
        except Exception as e:
            logger.error(f"Error reading stream {stream_key}: {e}")
            time.sleep(1)


def send_email(template_id, variables, timestamp, subject_override=None):
    logger.debug(f"send_email called with template_id={template_id}, variables={variables}, timestamp={timestamp}, subject_override={subject_override}")
    context = variables.copy() if isinstance(variables, dict) else {}
    if subject_override:
        context['subject'] = subject_override
    context['timestamp'] = timestamp
    result = notifier_factory.send_notification(template_id=template_id, context=context)
    logger.info(f"Notification send result: {result}")
    logger.debug(f"Notification send context: {context}")


def aggregation_worker(template_id: str, queue: Queue, stop_event: threading.Event, cfg, logger):
    logger.debug(f"Starting aggregation_worker for template_id={template_id}")
    while not stop_event.is_set():
        logger.debug(f"aggregation_worker({template_id}) sleeping for {cfg['aggregation_window']}s")
        time.sleep(cfg["aggregation_window"])
        emails = []
        while True:
            try:
                req = queue.get_nowait()
                logger.debug(f"Dequeued EmailRequest for aggregation: {req.__dict__}")
                emails.append(req)
            except Empty:
                break
        logger.debug(f"aggregation_worker({template_id}) collected {len(emails)} emails for aggregation.")
        if not emails:
            continue
        session = Session()
        template = session.query(Template).filter_by(id=template_id).first()
        min_interval = get_min_interval_seconds(template.extra) if template else 0
        session.close()
        now = time.time()
        last_time = last_sent_time.get(template_id, 0)
        logger.debug(f"aggregation_worker({template_id}) now={now}, last_time={last_time}, min_interval={min_interval}")
        if now - last_time < min_interval:
            logger.info(f"Skip sending for {template_id}: cooldown {min_interval}s not reached.")
            for req in emails:
                queue.put(req)
                logger.debug(f"Re-queued EmailRequest due to cooldown: {req.__dict__}")
            continue
        if len(emails) == 1:
            req = emails[0]
            logger.debug(f"Sending single email for template {req.template_id}")
            send_email(req.template_id, req.variables, req.timestamp)
            last_sent_time[template_id] = now
            logger.info(f"Sent email for template {req.template_id}")
        else:
            # --- Aggregation logic for multiple emails ---
            session = Session()
            template_obj = session.query(Template).filter_by(id=template_id).first()
            session.close()
            if not template_obj:
                logger.error(f"Template not found for id {template_id}")
                continue
            template_data = template_obj.template
            notifier_type = template_obj.notifier_type if hasattr(template_obj, 'notifier_type') else 'email'
            # Render the subject of the first email and prepend [AGGR]
            first_context = emails[0].variables.copy() if isinstance(emails[0].variables, dict) else {}
            subject_rendered = TemplateEngine.process_template_for_notifier(template_data, first_context, notifier_type).get('subject', '')
            aggr_subject = f"[AGGR] {subject_rendered}"
            # Render and concatenate the body of all emails
            bodies = []
            for req in emails:
                context = req.variables.copy() if isinstance(req.variables, dict) else {}
                rendered = TemplateEngine.process_template_for_notifier(template_data, context, notifier_type)
                bodies.append(rendered.get('content', ''))
            aggr_body = '\n\n'.join(bodies)
            aggr_timestamp = int(time.time())
            logger.debug(f"Sending aggregated email for template {template_id} with subject: {aggr_subject}")
            # Use the context of the first email, but override subject and body for aggregation
            context = emails[0].variables.copy() if isinstance(emails[0].variables, dict) else {}
            context['subject'] = aggr_subject  # Aggregated subject
            context['body'] = aggr_body        # Aggregated body
            context['timestamp'] = aggr_timestamp
            result = notifier_factory.send_notification(template_id=template_id, context=context)
            last_sent_time[template_id] = now
            logger.info(f"Sent aggregated email for template {template_id} ({len(emails)} requests)")
    logger.debug(f"aggregation_worker for template_id={template_id} exiting.")


def handle_email_request(msg_data):
    logger.debug(f"handle_email_request called with msg_data: {msg_data}")
    try:
        req_dict = dict(msg_data)
        variables = req_dict.get("variables", {})
        if isinstance(variables, str):
            try:
                variables = json.loads(variables)
            except Exception as e:
                logger.error(f"Failed to parse variables JSON: {e}")
                variables = {}
        req = EmailRequest(
            template_id=req_dict.get("template_id"),
            variables=variables,
            timestamp=int(req_dict.get("timestamp", 0))
        )
        logger.debug(f"Parsed EmailRequest: {req.__dict__}")
    except Exception as e:
        logger.error(f"Invalid message: {e}")
        return
    if req.template_id not in aggregation_queues:
        logger.debug(f"Creating new aggregation queue and worker for template_id={req.template_id}")
        queue = Queue()
        stop_event = threading.Event()
        aggregation_queues[req.template_id] = queue
        stop_events[req.template_id] = stop_event
        t = threading.Thread(target=aggregation_worker, args=(req.template_id, queue, stop_event, cfg, logger),
                             daemon=True)
        worker_threads[req.template_id] = t
        t.start()
        logger.debug(f"Started aggregation_worker thread for template_id={req.template_id}")
    aggregation_queues[req.template_id].put(req)
    logger.debug(f"Put EmailRequest into aggregation queue for template_id={req.template_id}")


def get_live_template_ids() -> Set[str]:
    session = Session()
    results = session.query(Template.id).filter(Template.notifier_type == 'email')
    ids = set()
    for row in results:
        ids.add(str(row[0]))
    logger.debug(f"Fetched live template ids: {ids}")
    session.close()
    return ids


def cleanup_dead_templates(live_template_ids: Set[str], logger):
    logger.debug(f"cleanup_dead_templates called with live_template_ids: {live_template_ids}")
    for template_id in list(aggregation_queues.keys()):
        if template_id not in live_template_ids:
            logger.info(f"Cleaning up resources for deleted template: {template_id}")
            stop_events[template_id].set()
            del aggregation_queues[template_id]
            del worker_threads[template_id]
            del stop_events[template_id]
            if template_id in last_sent_time:
                del last_sent_time[template_id]
            logger.debug(f"Cleaned up resources for template_id={template_id}")


def periodic_template_cleanup(get_live_template_ids_func, logger, interval=300):
    logger.debug(f"Starting periodic_template_cleanup with interval={interval}")
    while True:
        time.sleep(interval)
        live_template_ids = get_live_template_ids_func()
        logger.debug(f"periodic_template_cleanup fetched live_template_ids: {live_template_ids}")
        cleanup_dead_templates(live_template_ids, logger)


def main():
    global cfg
    cfg = load_config()
    r = redis.Redis(host=cfg["redis_host"], port=cfg["redis_port"], decode_responses=True)

    initialize_redis_stream(r, cfg["stream_key"], cfg["group"], logger)

    # Start periodic template cleanup thread
    cleanup_thread = threading.Thread(
        target=periodic_template_cleanup,
        args=(get_live_template_ids, logger, cfg["template_cleanup_interval"]),
        daemon=True
    )
    cleanup_thread.start()
    logger.debug("Started periodic_template_cleanup thread.")

    logger.info("Email daemon started. Waiting for messages...")

    def shutdown_handler(signum, frame):
        logger.info("Shutting down...")
        sys.exit(0)

    signal.signal(signal.SIGINT, shutdown_handler)
    signal.signal(signal.SIGTERM, shutdown_handler)

    # Main listen loop
    listen_stream(
        r,
        cfg["stream_key"],
        cfg["group"],
        cfg["consumer"],
        logger,
        handle_email_request
    )


if __name__ == "__main__":
    main()
