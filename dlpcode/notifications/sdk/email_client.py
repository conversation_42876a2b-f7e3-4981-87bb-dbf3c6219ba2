import json
import time
import uuid
from typing import Dict
from util.common_log import get_logger

logger = get_logger("email_client")


def load_config():
    return {
        "redis_host": "localhost",
        "redis_port": 6379,
        "stream_key": "email:send_queue",
    }


def send_email_by_template(
        template_id: str,
        variables: Dict[str, str],
) -> bool:
    """
    Send an email request to the Redis stream using a template.
    
    Args:
        template_id: UUID format template identifier
        variables: Dictionary with string keys and string values
        
    Returns:
        bool: True if successful, False otherwise
        
    Raises:
        Exception: If validation fails
    """
    try:
        # Validate template_id is a valid UUID
        try:
            uuid.UUID(template_id)
        except ValueError:
            error_msg = f"Invalid template_id format: {template_id}. Must be a valid UUID."
            logger.error(error_msg)
            raise Exception(error_msg)

        # Validate variables is a dictionary
        if not isinstance(variables, dict):
            error_msg = f"variables must be a dictionary, got {type(variables).__name__}"
            logger.error(error_msg)
            raise Exception(error_msg)

        # Validate all keys and values are strings
        for key, value in variables.items():
            if not isinstance(key, str):
                error_msg = f"All keys in variables must be strings, got {type(key).__name__} for key: {key}"
                logger.error(error_msg)
                raise Exception(error_msg)
            if not isinstance(value, str):
                error_msg = f"All values in variables must be strings, got {type(value).__name__} for value: {value}"
                logger.error(error_msg)
                raise Exception(error_msg)

        cfg = load_config()
        import redis
        r = redis.Redis(host=cfg["redis_host"], port=cfg["redis_port"], decode_responses=True)

        timestamp = int(time.time())
        msg = {
            "template_id": template_id,
            "variables": json.dumps(variables),
            "timestamp": timestamp
        }

        stream_id = r.xadd(cfg["stream_key"], msg)

        logger.info(
            f"Email request sent successfully to stream {cfg['stream_key']} with ID {stream_id}: template_id={template_id}, variables_count={len(variables)}")

        return True

    except redis.RedisError as e:
        error_msg = f"Redis error: {str(e)}"
        logger.error(error_msg)
        return False
    except Exception as e:
        logger.error(f"Failed to send email request: {str(e)}")
        raise


def main():
    import uuid as uuidlib
    from datetime import datetime

    # Example variables for a security incident notification
    variables = {
        "recipient_name": "Security Team",
        "incident_id": f"INC-{datetime.now().strftime('%Y%m%d')}-{uuidlib.uuid4().hex[:8].upper()}",
        "severity": "High",
        "incident_type": "Unauthorized Access Attempt",
        "description": "Multiple failed login attempts detected from suspicious IP address. Potential brute force attack in progress.",
        "detected_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "source_ip": "*************",
        "affected_system": "Web Application Server",
        "recommended_actions": "1. Block the source IP\n2. Review logs\n3. Notify administrators"
    }

    # Example template ID (replace with actual UUID)
    template_id = "8f7517d5-1f16-4837-ae26-722291d8b7fe"

    try:
        result = send_email_by_template(
            template_id=template_id,
            variables=variables
        )
        if result:
            print("Email request sent successfully!")
        else:
            print("Failed to send email request")
    except Exception as e:
        print(f"Error sending email request: {e}")


if __name__ == "__main__":
    main()
