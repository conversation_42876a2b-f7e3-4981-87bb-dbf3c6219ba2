# Notification API Documentation

## Overview

The notification system provides a comprehensive API for managing notification connectors and templates, supporting multiple channels (Email, Slack, Teams, Webhook, Jira, ServiceNow) with template-based messaging.

## Base URL

```
/api/v1/notification
```

## Authentication

All API endpoints require authentication. Include your session cookies or API key in requests.

## API Endpoints

### 1. Template Management

#### 1.1 List Templates

**Endpoint:** `GET /template`

**Description:** List notification templates with filtering, sorting, and pagination.

**Query Parameters:**
- `name` (optional): Filter by template name (partial match)
- `notification_type` (optional): Filter by notification type
- `notifier_type` (optional): Filter by notifier type
- `connector_id` (optional): Filter by connector ID
- `sort_field` (optional): Sort field (default: `created_at`)
  - Allowed values: `name`, `notifier_type`, `notification_type`, `created_at`, `updated_at`
- `sort_method` (optional): Sort method (default: `desc`)
  - Allowed values: `asc`, `desc`
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 100)

**Response:**
```json
{
    "list": [
        {
            "connector_id": "uuid",
            "extra": {
                "min_interval": 0,
                "priority": "High",
                "receivers": [
                    "<EMAIL>",
                    "<EMAIL>"
                ],
                "sender": "<EMAIL>"
            },
            "id": "uuid",
            "is_default": false,
            "name": "incident_alert_template",
            "notification_type": "incident",
            "notifier_type": "email",
            "template": {
                "content": "xxxx",
                "content_type": "html",
                "subject": "Security Incident Alert - {{ incident_id }}"
            },
            "template_type": "message",
            "created_at": 1753315783.813065,
            "updated_at": 1753315783.813067
        }
    ],
    "page": 1,
    "per_page": 100,
    "total": 1
}
```

#### 1.2 Create Template

**Endpoint:** `POST /template`

**Description:** Create a new notification template.

**Request Body:**
```json
{
  "name": "security_alert",
  "notifier_type": "email",
  "notification_type": "incident",
  "template": {
    "subject": "Security Alert: {{ incident_id }}",
    "content": "Security incident detected on {{ detected_at }}...",
    "content_type": "html"
  },
  "connector_id": "uuid",
  "extra": {
    "priority": "high",
    "sender": "<EMAIL>",
    "receivers": ["<EMAIL>", "<EMAIL>"],
    "min_interval": 0
  }
}
```

**Response:**
```json
{
    "id": "uuid"
}
```

#### 1.3 Update Template

**Endpoint:** `PUT /template`

**Description:** Update an existing notification template by ID or name.

**Request Body:**
```json
{
    "id": "uuid",
    "name": "incident_alert_template",
    "notifier_type": "email",
    "notification_type": "incident",
    "template": {
        "subject": "Security Incident Alert - {{ incident_id }}",
        "content": "Updated content...",
        "content_type": "html"
    },
    "connector_id": "uuid",
    "extra": {
        "priority": "High",
        "sender": "<EMAIL>",
        "receivers": ["<EMAIL>"],
        "min_interval": 3
    }
}
```

**Response:**
```json
{
    "connector_id": "uuid",
    "created_at": 1753315783.813065,
    "extra": {
        "min_interval": 3,
        "priority": "High",
        "receivers": ["<EMAIL>"],
        "sender": "<EMAIL>"
    },
    "id": "uuid",
    "is_default": false,
    "name": "incident_alert_template",
    "notification_type": "incident",
    "notifier_type": "email",
    "template": {
        "content": "Updated content...",
        "content_type": "html",
        "subject": "Security Incident Alert - {{ incident_id }}"
    },
    "template_type": "message",
    "updated_at": 1753315783.813067
}
```

#### 1.4 Delete Templates (Batch)

**Endpoint:** `DELETE /template`

**Description:** Batch delete notification templates by id list.

**Request Body:**
```json
{
  "id": ["uuid1", "uuid2", "uuid3"]
}
```

**Response:**
```json
{
    "deleted": ["uuid1", "uuid2"],
    "not_found": ["uuid3"]
}
```

### 2. Connector Management

#### 2.1 List Connectors

**Endpoint:** `GET /connector`

**Description:** List notification connectors with filtering and pagination.

**Query Parameters:**
- `name` (optional): Filter by connector name (partial match)
- `notifier_type` (optional): Filter by notifier type
- `sort_field` (optional): Sort field (default: `created_at`)
- `sort_method` (optional): Sort method (default: `desc`)
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 100)

**Response:**
```json
{
    "list": [
        {
            "config": {
                "authentication": "false",
                "encrypted": "true",
                "password": "encrypted_password",
                "security": "ssl",
                "smtp_host": "localhost",
                "smtp_port": 2525,
                "username": "<EMAIL>"
            },
            "created_at": 1753315783.796511,
            "extra": {},
            "id": "uuid",
            "name": "Test Email Connector",
            "notifier_type": "email",
            "updated_at": 1753315783.796512
        }
    ],
    "page": 1,
    "per_page": 100,
    "total": 1
}
```

#### 2.2 Create Connector

**Endpoint:** `POST /connector`

**Description:** Create a new notification connector.

**Request Body:**
```json
{
    "name": "email_test",
    "notifier_type": "email",
    "config": { 
        "smtp_host": "localhost", 
        "smtp_port": 2525, 
        "username": "<EMAIL>", 
        "password": "test123", 
        "authentication": "false", 
        "encrypted": "false",
        "security": "none"
    } 
}
```

**Response:**
```json
{
    "id": "uuid"
}
```

#### 2.3 Update Connector

**Endpoint:** `PUT /connector`

**Description:** Update an existing notification connector.

**Request Body:**
```json
{
    "id": "uuid",
    "name": "email_test",
    "notifier_type": "email",
    "config": {
        "smtp_host": "localhost",
        "smtp_port": 2525,
        "username": "<EMAIL>",
        "password": "new_password",
        "authentication": "false",
        "encrypted": "false",
        "security": "none"
    },
    "extra": {}
}
```

**Response:**
```json
{
    "config": {
        "authentication": "false",
        "encrypted": "true",
        "password": "encrypted_password",
        "security": "none",
        "smtp_host": "localhost",
        "smtp_port": 2525,
        "username": "<EMAIL>"
    },
    "created_at": 1753321038.23,
    "extra": {},
    "id": "uuid",
    "name": "email_test",
    "notifier_type": "email",
    "updated_at": 1753321038.23
}
```

#### 2.4 Delete Connectors (Batch)

**Endpoint:** `DELETE /connector`

**Description:** Batch delete notification connectors by id list.

**Request Body:**
```json
{
  "id": ["uuid1", "uuid2", "uuid3"]
}
```

**Response:**
```json
{
    "deleted": ["uuid1", "uuid2"],
    "not_found": ["uuid3"],
    "referenced": []
}
```

### 3. Notification Sending

#### 3.1 Send Notification

**Endpoint:** `POST /send`

**Description:** Send notification using template and context data.

**Request Body:**
```json
{
    "template_id": "uuid",
    "context": {
        "incident_id": "INC-001",
        "severity": "High",
        "description": "Security breach detected",
        "detected_at": "2024-01-15 10:30:00"
    }
}
```

**Response:**
```json
{
    "success": true,
    "message": "Notification sent successfully",
    "data": {
        "ticket_id": "JIRA-123",
        "ticket_key": "PROJ-123",
        "ticket_link": "https://company.atlassian.net/browse/JIRA-123",
        "status": "Open",
        "recipient": {
            "recipient_email": "<EMAIL>",
            "recipient_name": "John Doe"
        },
        "creator": {
            "ticket_creator": "admin",
            "ticket_creator_email": "<EMAIL>"
        },
        "extra": {"custom_field": "value"}
    }
}
```

### 4. Ticket Management

#### 4.1 List Tickets

**Endpoint:** `GET /ticket`

**Description:** List notification tickets with filtering and pagination.

**Query Parameters:**
- `notifier_type` (optional): Filter by notifier type
- `status` (optional): Filter by ticket status
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 100)

**Response:**
```json
{
    "list": [
        {
            "id": "uuid",
            "ticket_key": "PROJ-123",
            "ticket_link": "https://company.atlassian.net/browse/PROJ-123",
            "ticket_id": "123",
            "notifier_type": "jira",
            "status": "Open",
            "extra": {"custom_field": "value"},
            "recipient_email": "<EMAIL>",
            "recipient_name": "John Doe",
            "ticket_creator": "admin",
            "ticket_creator_email": "<EMAIL>",
            "template_id": "template-uuid",
            "created_at": 1753315783.813065,
            "updated_at": 1753315783.813067
        }
    ],
    "page": 1,
    "per_page": 100,
    "total": 1
}
```

**Note:** The `status` field is reserved for future ticket status tracking functionality and is not currently used in the system.

## Configuration Examples

### Email Connector Configuration

```json
{
  "name": "SMTP Email Connector",
  "notifier_type": "email",
  "config": {
    "encrypted": "false",
    "smtp_host": "smtp.gmail.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "app_password",
    "security": "starttls",
    "authentication": "true"
  }
}
```

**Notes:**
- security support "starttls, ssl, none"
- encrypted "true" or "false"

### Slack Connector Configuration

```json
{
  "name": "Slack Webhook Connector",
  "notifier_type": "slack",
  "config": {
    "webhook_url": "https://hooks.slack.com/services/xxx/xxx/xxx"
  }
}
```

### Teams Connector Configuration

```json
{
  "name": "Teams Webhook Connector",
  "notifier_type": "teams",
  "config": {
    "webhook_url": "https://outlook.office.com/webhook/xxx"
  }
}
```

### Webhook Connector Configuration

```json
{
  "name": "Generic Webhook Connector",
  "notifier_type": "webhook",
  "config": {
    "webhook_url": "https://your-webhook-endpoint.com/webhook",
    "headers": {
      "Content-Type": "application/json"
    }
  }
}
```

### Jira Connector Configuration

```json
{
    "name": "jira_test",
    "notifier_type": "jira",
    "config": { 
        "encrypted": "false",
        "domain": "forticasbqa.atlassian.net", 
        "user_email": "<EMAIL>", 
        "api_token": "xxxx"
    } 
}
```

### ServiceNow Connector Configuration

```json
{
    "name": "servicenow_test",
    "notifier_type": "servicenow",
    "config": { 
        "instance": "dev182979.service-now.com", 
        "user": "api_tester", 
        "password": "xxxx",
        "encrypted": "false"
    } 
}
```

## Template Examples

### Email Template

```json
{
  "name": "Security Incident Email",
  "notifier_type": "email",
  "notification_type": "incident",
  "template": {
    "subject": "Security Alert: {{ incident_id }}",
    "content": "<h1>Security Incident Alert</h1><p>Incident ID: {{ incident_id }}</p><p>Severity: {{ severity }}</p><p>Description: {{ description }}</p>",
    "content_type": "html"
  },
  "extra": {
    "sender": "<EMAIL>",
    "receivers": ["<EMAIL>"],
    "priority": "high",
    "min_interval": 0
  }
}
```

### Slack Template

```json
{
  "name": "Slack Alert",
  "notifier_type": "slack",
  "notification_type": "incident",
  "template": {
    "content": "Alert: {{ incident_id }} - {{ severity }}",
    "content_type": "markdown"
  },
  "connector_id": "uuid"
}
```

### Teams Template

```json
{
  "name": "Teams Alert",
  "notifier_type": "teams", 
  "notification_type": "incident",
  "template": {
    "content": "**Alert:** {{ incident_id }} - {{ description }}",
    "content_type": "markdown"
  },
  "connector_id": "uuid"
}
```

### Webhook Template

```json
{
  "name": "Webhook Alert",
  "notifier_type": "webhook",
  "notification_type": "incident", 
  "template": {
    "content": "{\"alert\": \"{{ incident_id }}\", \"level\": \"{{ severity }}\"}",
    "content_type": "json"
  },
  "connector_id": "uuid"
}
```

### Jira Template

```json
{ 
  "name": "incident_jira_template", 
  "notifier_type": "jira", 
  "notification_type": "incident", 
  "connector_id": "uuid", 
  "template": { 
    "subject": "[Security] Incident: {{incident_id}}", 
    "content": "Incident: {{incident_id}}\nSeverity: {{severity}}\nDescription: {{description}}", 
    "content_type": "plaintext" 
  }, 
  "extra": { 
    "priority": "High", 
    "project_key": "SEC",
    "issue_type": "Task",
    "min_interval": 0 
  }
}
```

### ServiceNow Template

```json
{ 
  "name": "incident_servicenow_template", 
  "notifier_type": "servicenow", 
  "notification_type": "incident", 
  "connector_id": "uuid", 
  "template": { 
    "subject": "[Security] Incident: {{incident_id}}", 
    "content": "Incident: {{incident_id}}\nSeverity: {{severity}}\nDescription: {{description}}", 
    "content_type": "plaintext" 
  }, 
  "extra": { 
    "caller_id": "user_id",
    "priority": "3", 
    "category": "security",
    "urgency": "3",
    "impact": "3",
    "min_interval": 0 
  }
}
```

## Supported Notification Types

- `incident` - Security incident notifications
- `ddr_incident` - DDR incident notifications  
- `report` - Report notifications

## Supported Notifier Types

- `email` - SMTP email notifications
- `slack` - Slack webhook notifications
- `teams` - Microsoft Teams webhook notifications
- `webhook` - Generic webhook notifications
- `jira` - Jira ticket creation
- `servicenow` - ServiceNow ticket creation

## Error Codes

| Error Code | Description |
|------------|-------------|
| 400 | Bad Request - Validation Error |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 409 | Conflict - Duplicate Resource |
| 500 | Internal Server Error |

## Security Notes

- All sensitive configuration fields (passwords, API tokens, webhook URLs) are automatically encrypted when `encrypted` is set to `true`
- Connector validation is performed before creation/update
- Template parameters are validated against notification type requirements
