#!/usr/bin/env python3
"""
Template Engine for Notification System

This module provides template rendering functionality:
- Template processing for different notifier types
- Template rendering with context
- Template validation
"""

import logging
from typing import Dict, Any, Tuple
from jinja2 import Environment, BaseLoader
from notifications.enums import (
    validate_notifier_type,
    validate_notification_type,
    ErrorMessages
)

logger = logging.getLogger(__name__)


class TemplateEngine:
    """Template engine for notification system"""
    
    def __init__(self):
        """Initialize template engine with security settings"""
        self.env = Environment(
            loader=BaseLoader(),
            autoescape=True,
            # security settings
            finalize=lambda x: x if x is not None else '',
            trim_blocks=True,
            lstrip_blocks=True
        )
        # disable dangerous built-in functions
        self.env.globals.pop('range', None)
        self.env.globals.pop('lipsum', None)
      
    @staticmethod
    def process_template_for_notifier(template_data: Dict[str, Any], context: Dict[str, Any], notifier_type: str) -> Dict[str, Any]:
        """
        Render and process template for a given notifier type.
        Always return a dict with at least 'content', and optionally 'subject', 'content_type'.
        """
        template_fields = {}
        env = Environment(loader=BaseLoader())
        if notifier_type == 'email':
            subject = template_data.get('subject', '')
            content = template_data.get('content', '')
            content_type = template_data.get('content_type', 'plaintext')
            rendered_subject = env.from_string(subject).render(**context) if subject else "Notification"
            rendered_content = env.from_string(content).render(**context) if content else "No content"
            template_fields = {
                'subject': rendered_subject,
                'content': rendered_content,
                'content_type': content_type
            }
        else:
            content = template_data.get('content', '')
            rendered_content = env.from_string(content).render(**context) if content else "No content"
            template_fields = {
                'content': rendered_content
            }
        return template_fields
    
    @staticmethod
    def render_template_static(template_content: str, context: Dict[str, Any]) -> str:
        """
        Render template content with context (static method)
        
        Args:
            template_content: Template content string
            context: Context data
            
        Returns:
            Rendered content
        """
        env = Environment(loader=BaseLoader())
        return env.from_string(template_content).render(**context)
    
    @staticmethod
    def validate_template_static(template_data: Dict[str, Any], notifier_type: str) -> Tuple[bool, str]:
        """
        Validate template data for specific notifier type (static method)
        
        Args:
            template_data: Template data to validate
            notifier_type: Type of notifier
            
        Returns:
            Tuple of (is_valid, message)
        """
        try:
            required_fields = ['name', 'notifier_type', 'notification_type', 'template']
            for field in required_fields:
                if field not in template_data:
                    return False, ErrorMessages.TEMPLATE_MISSING_FIELD.format(field=field)
            if not validate_notifier_type(template_data['notifier_type']):
                return False, ErrorMessages.TEMPLATE_INVALID_NOTIFIER_TYPE
            if not validate_notification_type(template_data['notification_type']):
                return False, ErrorMessages.TEMPLATE_INVALID_NOTIFICATION_TYPE
            template = template_data.get('template', {})
            if notifier_type == 'email':
                if 'subject' not in template and 'content' not in template:
                    return False, ErrorMessages.TEMPLATE_EMAIL_MISSING_SUBJECT_CONTENT
            else:
                if 'content' not in template:
                    return False, ErrorMessages.TEMPLATE_MISSING_CONTENT
            return True, "Template is valid"
        except Exception as e:
            return False, ErrorMessages.TEMPLATE_VALIDATION_ERROR.format(error=str(e))
