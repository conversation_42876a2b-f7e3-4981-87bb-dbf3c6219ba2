#!/usr/bin/env python3
"""
Enums for notification system

This module defines the enums used throughout the notification system.
"""

import enum
from typing import List


# Error message constants
class ErrorMessages:
    """Centralized error messages for the notification system"""
    
    # Template related errors
    TEMPLATE_ID_REQUIRED = "template_id is required"
    TEMPLATE_NOT_FOUND = "Template not found"
    TEMPLATE_MISSING_FIELD = "Missing required field: {field}"
    TEMPLATE_INVALID_NOTIFIER_TYPE = "Invalid notifier type"
    TEMPLATE_INVALID_NOTIFICATION_TYPE = "Invalid notification type"
    TEMPLATE_EMAIL_MISSING_SUBJECT_CONTENT = "Email template must have subject or content"
    TEMPLATE_MISSING_CONTENT = "Template must have content"
    TEMPLATE_VALIDATION_ERROR = "Validation error: {error}"
    
    # Connector related errors
    CONNECTOR_ID_REQUIRED = "Connector ID is required (either as argument or in template)"
    CONNECTOR_NOT_FOUND = "Connector not found"
    CONNECTOR_UNSUPPORTED_TYPE = "Unsupported notifier_type: {notifier_type}. Supported: {supported_types}"
    CONNECTOR_UNKNOWN_TYPE = "Unknown notifier_type: {notifier_type}"
    
    # Configuration errors
    CONFIG_MISSING_WEBHOOK_URL = "{notifier} requires webhook_url in config"
    CONFIG_MISSING_SMTP_HOST_PORT = "{notifier} requires smtp_host and smtp_port in config"
    CONFIG_MISSING_CREDENTIALS = "{notifier} requires username and password when authentication is enabled"
    
    # Send operation errors
    SEND_FAILED = "Send failed"
    SEND_GENERIC_ERROR = "Failed to send message: {error}"
    SEND_SUCCESS = "Message sent successfully"
    
    # Platform specific success messages
    MESSAGE_SENT_SUCCESS = "Message sent successfully"
    
    # Platform specific errors
    API_ERROR = "API returned HTTP {status_code}"
    TIMEOUT = "Request timeout"
    CONNECTION_ERROR = "Connection failed"
    REQUEST_ERROR = "Request failed: {error}"
    SENDING_FAILED = "Sending failed: {error}"
    
    # Email specific errors
    EMAIL_MISSING_RECIPIENTS = "Email recipients are required"
    EMAIL_AUTH_FAILED = "SMTP authentication failed"
    EMAIL_RECIPIENTS_REFUSED = "Email recipients refused"
    EMAIL_SERVER_DISCONNECTED = "SMTP server disconnected"
    EMAIL_SMTP_ERROR = "SMTP error: {error}"
    
    # HTTP errors (generic)
    HTTP_TIMEOUT = "{service} request timeout"
    HTTP_CONNECTION_ERROR = "{service} connection failed"
    HTTP_REQUEST_ERROR = "{service} request failed: {error}"
    HTTP_UNKNOWN_ERROR = "{service} sending failed: {error}"
    
    # Database errors
    DB_SESSION_ERROR = "Database session error: {error}"
    DB_COMMIT_ERROR = "Database commit error: {error}"
    DB_ROLLBACK_ERROR = "Database rollback error: {error}"
    
    # Template engine errors
    TEMPLATE_RENDER_ERROR = "Template rendering error: {error}"
    TEMPLATE_CONTEXT_ERROR = "Template context error: {error}"
    
    # Validation errors
    VALIDATION_MISSING_PARAMS = "Missing required parameters for {notification_type}: {missing_params}"
    VALIDATION_INVALID_CONFIG = "Invalid configuration for {notifier_type}"
    VALIDATION_WEBHOOK_URL_INVALID = "Invalid webhook URL format"
    
    # Success messages
    SEND_SUCCESS = "Message sent successfully"


class NotificationTypeEnum(str, enum.Enum):
    """Notification types enumeration"""
    INCIDENT = 'incident'
    DDR_INCIDENT = 'ddr_incident'
    REPORT = 'report'
    # Notification types based on ticket status changes
    TICKET_CREATED = 'ticket_created'
    TICKET_CLOSED = 'ticket_closed'
    TICKET_DELAYED = 'ticket_delayed'

    @classmethod
    def is_valid(cls, value):
        try:
            cls(value)
            return True
        except ValueError:
            return False

    @classmethod
    def get_all_values(cls):
        return [e.value for e in cls]


class NotifierTypeEnum(str, enum.Enum):
    """Notifier types enumeration"""
    EMAIL = 'email'
    SLACK = 'slack'
    TEAMS = 'teams'
    WEBHOOK = 'webhook'
    JIRA = 'jira'
    SERVICENOW = 'servicenow'


    @classmethod
    def is_valid(cls, value):
        try:
            cls(value)
            return True
        except ValueError:
            return False

    @classmethod
    def get_all_values(cls):
        return [e.value for e in cls]


class TicketTypeEnum(str, enum.Enum):
    """Ticket types enumeration for issue/ticket related APIs"""
    JIRA = 'jira'
    SERVICENOW = 'servicenow'

    @classmethod
    def is_valid(cls, value):
        try:
            cls(value)
            return True
        except ValueError:
            return False

    @classmethod
    def get_all_values(cls):
        return [e.value for e in cls]


def get_notification_types() -> List[str]:
    """Get list of all notification types"""
    return [nt.value for nt in NotificationTypeEnum]


def get_notifier_types() -> List[str]:
    """Get list of all notifier types"""
    return [nt.value for nt in NotifierTypeEnum]


# Template parameters for each notification type
TEMPLATE_PARAMETERS = {
    'incident': [
        'recipient_name',
        'incident_id',
        'severity',
        'incident_type',
        'description',
        'detected_at'
    ],
    'ddr_incident': [
        'recipient_name',
        'ddr_incident_id',
        'severity',
        'ip',
        'user',
        'action',
        'detected_at'
    ],
    'report': [
        'recipient_name',
        'report_title',
        'report_type',
        'period',
        'generated_at',
        'summary',
        'key_findings'
    ]
}


def get_template_parameters(notification_type: str) -> List[str]:
    """Get template parameters for a notification type"""
    return TEMPLATE_PARAMETERS.get(notification_type, [])


def validate_notification_type(notification_type: str) -> bool:
    """Validate if notification type is valid"""
    try:
        NotificationTypeEnum(notification_type)
        return True
    except ValueError:
        return False


def validate_notifier_type(notifier_type: str) -> bool:
    """Validate if notifier type is valid"""
    try:
        NotifierTypeEnum(notifier_type)
        return True
    except ValueError:
        return False


def validate_template_parameters(notification_type: str, parameters: dict) -> bool:
    """Validate if all required parameters are provided"""
    required_params = get_template_parameters(notification_type)
    provided_params = set(parameters.keys())
    
    missing_params = set(required_params) - provided_params
    if missing_params:
        raise ValueError(f"Missing required parameters for {notification_type}: {missing_params}")
    
    return True 


SENSITIVE_FIELDS_MAP = {
    NotifierTypeEnum.EMAIL.value: ["password"],
    NotifierTypeEnum.SERVICENOW.value: ["password"],
    NotifierTypeEnum.JIRA.value: ["api_token"],
    NotifierTypeEnum.SLACK.value: ["webhook_url", "url"],
    NotifierTypeEnum.TEAMS.value: ["webhook_url", "url"],
    NotifierTypeEnum.WEBHOOK.value: ["webhook_url", "url"]
}