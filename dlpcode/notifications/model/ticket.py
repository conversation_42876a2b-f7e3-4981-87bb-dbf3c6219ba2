"""
Ticket ORM model for tracking external system tickets (Jira, ServiceNow, etc.)
Located in notifications/model/ticket.py
"""
from sqlalchemy import Column, String, ForeignKey, DateTime
from sqlalchemy.dialects.postgresql import UUID, JSONB, TIMESTAMP
from sqlalchemy.sql import func
import uuid
from exts import Base
from datetime import timezone

class Ticket(Base):
    """
    ORM model for external system tickets (Jira, ServiceNow, etc.).
    Stores ticket metadata, status, and notification-related information.
    """
    __tablename__ = "notification_tickets"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    ticket_key = Column(String(128), nullable=False)
    ticket_link = Column(String(1024))
    ticket_id = Column(String(128), nullable=False)
    notifier_type = Column(String(64), nullable=False)
    status = Column(String(64))
    extra = Column(JSONB)  # Extra data (for extensibility)
    recipient_email = Column(String(255))
    recipient_name = Column(String(255))
    ticket_creator = Column(String(255))
    ticket_creator_email = Column(String(255))
    template_id = Column(UUID(as_uuid=True), ForeignKey('notification_templates.id'))
    created_at = Column(DateTime, nullable=False, server_default=func.now())
    updated_at = Column(DateTime, nullable=False, server_default=func.now(), onupdate=func.now())

    def to_dict(self):
        """
        Convert the Ticket object to a dictionary for serialization or API response.
        ticket_key: Unique identifier of the ticket in the external system (e.g. Jira's PROJ-123)
        ticket_id: Internal ID of the ticket in the external system (e.g. Jira's numeric ID)
        """
        return {
            'id': str(self.id),
            'ticket_key': self.ticket_key,
            'ticket_link': self.ticket_link,
            'ticket_id': self.ticket_id,
            'notifier_type': self.notifier_type,
            'status': self.status,
            'extra': self.extra,
            'recipient_email': self.recipient_email,
            'recipient_name': self.recipient_name,
            'ticket_creator': self.ticket_creator,
            'ticket_creator_email': self.ticket_creator_email,
            'template_id': str(self.template_id) if self.template_id else None,
            'created_at': self.created_at.replace(tzinfo=timezone.utc).timestamp() if self.created_at.tzinfo is None else self.created_at.timestamp(),
            'updated_at': self.updated_at.replace(tzinfo=timezone.utc).timestamp() if self.updated_at.tzinfo is None else self.updated_at.timestamp(),
        }

    def __repr__(self):
        """
        String representation for debugging and logging.
        """
        return (
            f"<Ticket(id={self.id}, ticket_key={self.ticket_key}, notifier_type={self.notifier_type}, status={self.status})>"
        ) 