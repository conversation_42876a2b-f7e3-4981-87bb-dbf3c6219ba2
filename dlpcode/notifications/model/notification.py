"""
Notification ORM models for connectors and templates.
Located in notifications/model/notification.py
"""
from exts import Base
from datetime import timezone
from sqlalchemy import Column, String, ForeignKey, Boolean, func, DateTime
from sqlalchemy.dialects.postgresql import JSONB, UUID, TIMESTAMP
import uuid

class Template(Base):
    """
    ORM model for notification templates.
    Stores template content, type, and related metadata.
    """
    __tablename__ = 'notification_templates'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False, unique=True)
    notifier_type = Column(String(64), nullable=False)
    notification_type = Column(String(64), nullable=False)
    template_type = Column(String(64), nullable=False, default='message', server_default='message')
    template = Column(JSONB, nullable=False)
    is_default = Column(Boolean, default=False)
    connector_id = Column(UUID(as_uuid=True), ForeignKey('notification_connectors.id'), nullable=True)
    extra = Column(JSONB, nullable=False, default=dict)
    created_at = Column(DateTime, nullable=False, server_default=func.now())
    updated_at = Column(DateTime, nullable=False, server_default=func.now(), onupdate=func.now())

    def to_dict(self):
        """
        Convert the Template object to a dictionary for serialization or API response.
        """
        return {
            'id': str(self.id),
            'name': self.name,
            'notifier_type': self.notifier_type,
            'notification_type': self.notification_type,
            'template_type': self.template_type,
            'template': self.template,
            'is_default': self.is_default,
            'connector_id': str(self.connector_id) if self.connector_id else None,
            'extra': self.extra,
            'created_at': self.created_at.replace(tzinfo=timezone.utc).timestamp() if self.created_at.tzinfo is None else self.created_at.timestamp(),
            'updated_at': self.updated_at.replace(tzinfo=timezone.utc).timestamp() if self.updated_at.tzinfo is None else self.updated_at.timestamp(),
        }

    def __repr__(self):
        """
        String representation for debugging and logging.
        """
        return (
            f"<Template(id={self.id}, name={self.name}, notifier_type={self.notifier_type}, "
            f"notification_type={self.notification_type})>"
        )

class NotificationConnector(Base):
    """
    ORM model for notification connectors.
    Stores notifier configuration and metadata.
    """
    __tablename__ = 'notification_connectors'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False, unique=True)
    notifier_type = Column(String(64), nullable=False)
    config = Column(JSONB, nullable=False)
    extra = Column(JSONB, nullable=False, default=dict)
    created_at = Column(DateTime, nullable=False, server_default=func.now())
    updated_at = Column(DateTime, nullable=False, server_default=func.now(), onupdate=func.now())

    def to_dict(self):
        """
        Convert the NotificationConnector object to a dictionary for serialization or API response.
        """
        return {
            'id': str(self.id),
            'name': self.name,
            'notifier_type': self.notifier_type,
            'config': self.config,
            'extra': self.extra,
            'created_at': self.created_at.replace(tzinfo=timezone.utc).timestamp() if self.created_at.tzinfo is None else self.created_at.timestamp(),
            'updated_at': self.updated_at.replace(tzinfo=timezone.utc).timestamp() if self.updated_at.tzinfo is None else self.updated_at.timestamp(),
        }

    def __repr__(self):
        """
        String representation for debugging and logging.
        """
        return (
            f"<NotificationConnector(id={self.id}, name={self.name}, notifier_type={self.notifier_type})>"
        ) 