#!/usr/bin/env python3
"""Notification System Package"""

from notifications.enums import NotifierTypeEnum, ErrorMessages
from notifications.notifier import (
    BaseNotifier, notifier_factory, SmtpEmailNotifier, 
    SlackNotifier, TeamsNotifier, WebhookNotifier, SUPPORTED_PLATFORMS
)
from notifications.templates import TemplateEngine

__all__ = [
    'NotifierTypeEnum', 
    'ErrorMessages',
    'BaseNotifier', 
    'notifier_factory', 
    'SmtpEmailNotifier',
    'SlackNotifier', 
    'TeamsNotifier', 
    'WebhookNotifier', 
    'SUPPORTED_PLATFORMS',
    'TemplateEngine'
]

__version__ = '1.0.0' 