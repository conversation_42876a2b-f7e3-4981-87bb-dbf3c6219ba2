#!/usr/bin/env python3
"""
Teams Notification Test Script

This script demonstrates how to:
1. Create a Teams connector
2. Create a Teams template
3. Send a test notification via Teams
"""

import sys
import os
import json
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) )

from notifications.notifier.factory import notifier_factory
from exts import Session
from notifications.model.notification import NotificationConnector, Template

def create_teams_connector_direct(connector_data):
    """Create Teams connector directly in database"""
    try:
        from notifications.notifier.teams import TeamsNotifier
        config = connector_data.get('config', {})
        if not TeamsNotifier.validate_config(config):
            raise ValueError("Invalid Teams connector configuration")
        if not TeamsNotifier.test_connection(config):
            raise ValueError("Teams connection test failed")
        session = Session()
        connector = NotificationConnector(
            name=connector_data['name'],
            notifier_type='teams',
            config=config
        )
        session.add(connector)
        session.commit()
        connector_id = connector.id
        session.close()
        print(f"SUCCESS: Created Teams connector: {connector_data['name']} (ID: {connector_id})")
        return connector_id
    except Exception as e:
        print(f"ERROR: Error creating Teams connector: {e}")
        raise

def create_teams_template_direct(template_data, connector_id):
    """Create Teams template directly in database"""
    try:
        session = Session()
        template = Template(
            name=template_data['name'],
            notifier_type='teams',
            notification_type=template_data['notification_type'],
            template_type=template_data.get('template_type', 'message'),
            template=template_data['template'],
            is_default=False,
            connector_id=connector_id,
            extra=template_data.get('extra', {})
        )
        session.add(template)
        session.commit()
        template_id = template.id
        session.close()
        print(f"SUCCESS: Created Teams template: {template_data['name']} (ID: {template_id})")
        return template_id
    except Exception as e:
        print(f"ERROR: Error creating Teams template: {e}")
        raise

def test_teams_connection(webhook_url):
    """Test Teams webhook connection"""
    print("Testing Teams webhook connection...")
    try:
        from notifications.notifier.teams import TeamsNotifier
        config = {"webhook_url": webhook_url}
        notifier = TeamsNotifier(config=config)
        result = notifier.test_connection(config)
        if result:
            print("SUCCESS: Teams connection test successful!")
            return True
        else:
            print("FAILED: Teams connection test failed!")
            return False
    except Exception as e:
        print(f"ERROR: Teams connection test error: {e}")
        return False

def create_teams_test_data(webhook_url):
    """Create test Teams connector and template"""
    print("\nCreating Teams test data...")
    connector_data = {
        "name": "Test Teams Connector",
        "notifier_type": "teams",
        "config": {
            "webhook_url": webhook_url,
        }
    }
    template_data = {
        "name": "Test Teams Template",
        "notifier_type": "teams",
        "notification_type": "incident",
        "template": {
            "content": """Security Incident Alert!\n\n**Incident ID:** {{ incident_id }}\n**Severity:** {{ severity }}\n**Type:** {{ incident_type }}\n**Description:** {{ description }}\n\n**Recommended Actions:**\n{{ recommended_actions }}\n\n_Generated at {{ timestamp }}_"""
        },
        "extra": {
            "priority": "high"
        }
    }
    try:
        connector_id = create_teams_connector_direct(connector_data)
        print(f"SUCCESS: Created Teams connector with ID: {connector_id}")
        template_id = create_teams_template_direct(template_data, connector_id)
        print(f"SUCCESS: Created Teams template with ID: {template_id}")
        return connector_id, template_id
    except Exception as e:
        print(f"ERROR: Error creating test data: {e}")
        return None, None

def send_test_notification(template_id, connector_id=None):
    """Send a test notification via Teams"""
    print("\nSending test Teams notification...")
    context = {
        "incident_id": "INC-2024-001",
        "severity": "High",
        "incident_type": "Data Breach",
        "description": "Unauthorized access detected on production server",
        "recommended_actions": "1. Isolate affected systems\n2. Review access logs\n3. Update security policies",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    try:
        result = notifier_factory.send_notification(
            connector_id=connector_id,
            template_id=template_id,
            context=context
        )
        if result.get('success'):
            print("SUCCESS: Teams notification sent successfully!")
            print(f"Result: {result}")
        else:
            print("FAILED: Failed to send Teams notification!")
            print(f"Error: {result}")
        return result
    except Exception as e:
        print(f"ERROR: Error sending notification: {e}")
        return None

def main():
    """Main test function"""
    print("Teams Notification Test")
    print("=" * 50)
    webhook_url = os.environ.get('TEAMS_WEBHOOK_URL', '')
    if not webhook_url:
        webhook_url = input("Please enter your Teams webhook URL: ").strip()
    if not webhook_url:
        print("ERROR: No Teams webhook URL provided. Exiting.")
        return
    if not test_teams_connection(webhook_url):
        print("ERROR: Teams connection test failed. Exiting.")
        return
    connector_id, template_id = create_teams_test_data(webhook_url)
    if connector_id and template_id:
        send_test_notification(template_id, connector_id)
    else:
        print("ERROR: Failed to create test data. Exiting.")

def test_existing_data():
    """Test with existing Teams connector and template"""
    print("\nTesting with existing data...")
    session = Session()
    try:
        # Find existing Teams template
        template = session.query(Template).filter_by(notifier_type='teams').first()
        if not template:
            print("ERROR: No Teams template found in database")
            return
        print(f"SUCCESS: Found existing template: {template.name} (ID: {template.id})")
        # Send test notification
        send_test_notification(template.id)
    finally:
        session.close()

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="Test Teams notification system")
    parser.add_argument("--existing", action="store_true", help="Test with existing data")
    args = parser.parse_args()
    if args.existing:
        test_existing_data()
    else:
        main() 