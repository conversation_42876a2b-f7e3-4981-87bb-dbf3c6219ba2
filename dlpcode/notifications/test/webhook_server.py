# -*- coding: utf-8 -*-
#!/usr/bin/env python3
"""
Local webhook server for testing notification sending
Supports various webhook formats with web interface
"""

import os
import sys
import json
import time
from datetime import datetime
from flask import Flask, request, jsonify
from collections import deque

# Add parent directories to path
sys.path.insert(0, os.path.abspath('../..'))

app = Flask(__name__)

# Store received webhooks
received_webhooks = deque(maxlen=1000)
webhook_stats = {
    'total': 0
}

@app.route('/')
def home():
    """Home page with webhook URLs and stats"""
    stats = {
        'stats': dict(webhook_stats),
        'recent_webhooks': list(received_webhooks)[-10:],  # Last 10 webhooks
        'total_received': len(received_webhooks)
    }
    
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Webhook Test Server</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
            .webhook-url {{ background: #f5f5f5; padding: 10px; margin: 5px 0; border-radius: 3px; font-family: monospace; }}
            .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; }}
            .stat-item {{ background: #e8f4fd; padding: 10px; border-radius: 3px; text-align: center; }}
            .webhook-item {{ background: #f9f9f9; padding: 10px; margin: 5px 0; border-radius: 3px; border-left: 4px solid #007cba; }}
            .timestamp {{ color: #666; font-size: 0.9em; }}
            .channel {{ font-weight: bold; color: #007cba; }}
            button {{ background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 3px; cursor: pointer; }}
            button:hover {{ background: #005a8b; }}
        </style>
        <script>
            function refreshStats() {{
                location.reload();
            }}
            
            function clearWebhooks() {{
                fetch('/clear', {{method: 'POST'}})
                    .then(() => location.reload());
            }}
            
            // Auto-refresh every 5 seconds
            setInterval(refreshStats, 5000);
        </script>
    </head>
    <body>
        <div class="container">
            <h1>Webhook Test Server</h1>
            <p>This server receives webhooks from notification channels for testing purposes.</p>
            
            <div class="section">
                <h2>Statistics</h2>
                <div class="stats">
                    <div class="stat-item">
                        <div>Total Webhooks</div>
                        <div><strong>{stats['stats']['total']}</strong></div>
                    </div>
                </div>
                <button onclick="refreshStats()">Refresh</button>
                <button onclick="clearWebhooks()">Clear All</button>
            </div>
            
            <div class="section">
                <h2>Webhook URL</h2>
                <p>Use this URL in your notification connector configurations:</p>
                
                <div class="webhook-url">http://localhost:8082/webhook/your-channel-name</div>
                <p>Replace 'your-channel-name' with any channel name you want to test.</p>
            </div>
            
            <div class="section">
                <h2>Recent Webhooks</h2>
                <p>Last {len(stats['recent_webhooks'])} received webhooks:</p>
    """
    
    if stats['recent_webhooks']:
        for webhook in reversed(stats['recent_webhooks']):
            html += f"""
                <div class="webhook-item">
                    <div class="channel">{webhook['channel'].upper()}</div>
                    <div class="timestamp">{webhook['timestamp']}</div>
                    <div><strong>Content:</strong> {webhook['content']}</div>
                    <div><strong>Raw Data:</strong> <pre>{json.dumps(webhook['raw_data'], indent=2)}</pre></div>
                </div>
            """
    else:
        html += "<p>No webhooks received yet.</p>"
    
    html += """
            </div>
        </div>
    </body>
    </html>
    """
    
    return html

@app.route('/webhook/<channel>', methods=['POST'])
def generic_webhook(channel):
    """Handle generic webhook for any channel"""
    try:
        data = request.get_json()
        headers = dict(request.headers)
        
        webhook_data = {
            'channel': channel,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'content': data.get('text', str(data)),
            'headers': headers,
            'raw_data': data
        }
        
        # Print detailed information to console
        print(f"\n=== Received {channel.upper()} Webhook ===")
        print(f"Timestamp: {webhook_data['timestamp']}")
        print(f"Content: {webhook_data['content']}")
        print(f"Raw Data: {json.dumps(data, indent=2)}")
        print("=" * 50)
        
        received_webhooks.append(webhook_data)
        webhook_stats['total'] += 1
        
        print(f"Received {channel} webhook: {webhook_data['content']}")
        return jsonify({'status': 'ok', 'message': f'{channel} webhook received'})
    except Exception as e:
        print(f"Error processing {channel} webhook: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 400

@app.route('/clear', methods=['POST'])
def clear_webhooks():
    """Clear all stored webhooks"""
    received_webhooks.clear()
    webhook_stats['total'] = 0
    print("All webhooks cleared")
    return jsonify({'status': 'ok', 'message': 'All webhooks cleared'})

@app.route('/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})

def main():
    port = 8082
    host = '0.0.0.0'
    
    print("Starting Webhook Test Server")
    print(f"Server will run on http://{host}:{port}")
    print("Available webhook endpoints:")
    print(f"   - Generic: http://{host}:{port}/webhook/<channel>")
    print(f"   - Health: http://{host}:{port}/health")
    print(f"   - Web UI: http://{host}:{port}/")
    print("\nUse these URLs in your notification connector configurations")
    print("Press Ctrl+C to stop the server")
    
    try:
        app.run(host=host, port=port, debug=False)
    except KeyboardInterrupt:
        print("\nServer stopped by user")

if __name__ == '__main__':
    main() 