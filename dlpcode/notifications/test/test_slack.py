#!/usr/bin/env python3
"""
Slack Notification Test Script

This script demonstrates how to:
1. Create a Slack connector
2. Create a Slack template
3. Send a test notification via Slack
"""

import sys
import os
import json
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from notifications.notifier.factory import notifier_factory
from exts import Session
from notifications.model.notification import NotificationConnector, Template


def create_slack_connector_direct(connector_data):
    """Create Slack connector directly in database"""
    try:
        from notifications.notifier.slack import SlackNotifier
        
        # Test connection first
        config = connector_data.get('config', {})
        if not SlackNotifier.validate_config(config):
            raise ValueError("Invalid Slack connector configuration")
        
        # Test connection
        if not SlackNotifier.test_connection(config):
            raise ValueError("Slack connection test failed")
        
        # Create connector
        session = Session()
        connector = NotificationConnector(
            name=connector_data['name'],
            notifier_type='slack',
            config=config
        )
        
        session.add(connector)
        session.commit()
        connector_id = connector.id
        session.close()
        
        print(f"SUCCESS: Created Slack connector: {connector_data['name']} (ID: {connector_id})")
        return connector_id
        
    except Exception as e:
        print(f"ERROR: Error creating Slack connector: {e}")
        raise


def create_slack_template_direct(template_data, connector_id):
    """Create Slack template directly in database"""
    try:
        session = Session()
        template = Template(
            name=template_data['name'],
            notifier_type='slack',
            notification_type=template_data['notification_type'],
            template=template_data['template'],
            is_default=False,
            connector_id=connector_id,
            extra=template_data.get('extra', {})
        )
        
        session.add(template)
        session.commit()
        template_id = template.id
        session.close()
        
        print(f"SUCCESS: Created Slack template: {template_data['name']} (ID: {template_id})")
        return template_id
        
    except Exception as e:
        print(f"ERROR: Error creating Slack template: {e}")
        raise


def test_slack_connection(webhook_url):
    """Test Slack webhook connection"""
    print("Testing Slack webhook connection...")
    
    try:
        from notifications.notifier.slack import SlackNotifier
        config = {"webhook_url": webhook_url}
        notifier = SlackNotifier(config=config)
        result = notifier.test_connection(config)
        
        if result:
            print("SUCCESS: Slack connection test successful!")
            return True
        else:
            print("FAILED: Slack connection test failed!")
            return False
    except Exception as e:
        print(f"ERROR: Slack connection test error: {e}")
        return False


def create_slack_test_data(webhook_url):
    """Create test Slack connector and template"""
    print("\nCreating Slack test data...")
    
    # Test connector data
    connector_data = {
        "name": "Test Slack Connector",
        "notifier_type": "slack",
        "config": {
            "webhook_url": webhook_url,
        }
    }

    # Test template data
    template_data = {
        "name": "Test Slack Template",
        "notifier_type": "slack",
        "notification_type": "incident",
        "template": {
            "content": """Security Incident Alert!

**Incident ID:** {{ incident_id }}
**Severity:** {{ severity }}
**Type:** {{ incident_type }}
**Description:** {{ description }}

**Recommended Actions:**
{{ recommended_actions }}

_Generated at {{ timestamp }}_"""
        },
        "extra": {
            "priority": "high",
            "channel": "#security-alerts"
        }
    }
    
    try:
        # Create connector
        connector_id = create_slack_connector_direct(connector_data)
        print(f"SUCCESS: Created Slack connector with ID: {connector_id}")
        
        # Create template
        template_id = create_slack_template_direct(template_data, connector_id)
        print(f"SUCCESS: Created Slack template with ID: {template_id}")
        
        return connector_id, template_id
        
    except Exception as e:
        print(f"ERROR: Error creating test data: {e}")
        return None, None


def send_test_notification(template_id, connector_id=None):
    """Send a test notification via Slack"""
    print("\nSending test Slack notification...")
    
    # Test context data
    context = {
        "incident_id": "INC-2024-001",
        "severity": "High",
        "incident_type": "Data Breach",
        "description": "Unauthorized access detected on production server",
        "recommended_actions": "1. Isolate affected systems\n2. Review access logs\n3. Update security policies",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    try:
        # Send notification
        result = notifier_factory.send_notification(
            connector_id=connector_id,
            template_id=template_id,
            context=context
        )
        
        if result.get('success'):
            print("SUCCESS: Slack notification sent successfully!")
            print(f"Result: {result}")
        else:
            print("FAILED: Failed to send Slack notification!")
            print(f"Error: {result}")
            
        return result
        
    except Exception as e:
        print(f"ERROR: Error sending notification: {e}")
        return None


def main():
    """Main test function"""
    print("Slack Notification Test")
    print("=" * 50)
    webhook_url = os.environ.get('SLACK_WEBHOOK_URL', '')
    if not webhook_url:
        webhook_url = input("Enter Slack webhook URL: ").strip()
    if not webhook_url:
        print("ERROR: No Slack webhook URL provided. Exiting.")
        return

    # Step 1: Test connection (optional - requires real webhook URL)
    if not test_slack_connection(webhook_url):
        print("WARNING: Connection test failed, but continuing with database test...")
    
    # Step 2: Create test data
    connector_id, template_id = create_slack_test_data(webhook_url)
    if not connector_id or not template_id:
        print("ERROR: Failed to create test data. Exiting.")
        return
    
    # Step 3: Send test notification
    result = send_test_notification(template_id)
    
    # Step 4: Summary
    print("\nTest Summary:")
    print(f"  Connector ID: {connector_id}")
    print(f"  Template ID: {template_id}")
    print(f"  Send Result: {'Success' if result and result.get('success') else 'Failed'}")
    
    if result and result.get('success'):
        print("\nSUCCESS: All tests passed! Slack notification system is working correctly.")
    else:
        print("\nWARNING: Some tests failed. Check the error messages above.")


def test_existing_data():
    """Test with existing connector and template"""
    print("\nTesting with existing data...")
    
    session = Session()
    try:     
        # Find existing Slack template
        template = session.query(Template).filter_by(notifier_type='slack').first()
        if not template:
            print("ERROR: No Slack template found in database")
            return

        print(f"SUCCESS: Found existing template: {template.name} (ID: {template.id})")
        
        # Send test notification
        send_test_notification(template.id)
        
    finally:
        session.close()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Slack notification system")
    parser.add_argument("--existing", action="store_true", help="Test with existing data")
    
    args = parser.parse_args()
    
    if args.existing:
        test_existing_data()
    else:
        main() 