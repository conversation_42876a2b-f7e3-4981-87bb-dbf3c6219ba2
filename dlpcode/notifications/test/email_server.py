#!/usr/bin/env python3
"""
Email test server for notification testing
"""

import os
import sys
import time
import threading
import asyncore
import smtpd
import argparse
from email import message_from_bytes
import json

# Try to import Flask for web interface
try:
    from flask import Flask, jsonify, request
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    print("Flask not available, web interface will be disabled")

# Global variables to store received emails
received_emails = []
email_stats = {
    'total': 0,
    'from_addresses': set(),
    'to_addresses': set(),
    'subjects': []
}

class EmailMessage:
    """Simple email message class"""
    def __init__(self, from_addr, to_addrs, data):
        self.from_addr = from_addr
        self.to_addrs = to_addrs
        self.data = data
        self.timestamp = time.time()
        
        # Parse email content
        self.message = message_from_bytes(data)
        self.subject = self.message.get('Subject', 'No Subject')
        self.text_content = ""
        self.html_content = None
        self.content_type = "text/plain"
        
        # Extract text content
        if self.message.is_multipart():
            print (f"is multi message")
            for part in self.message.walk():
                if part.get_content_type() == "text/plain":
                    self.text_content = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    break
                elif part.get_content_type() == "text/html":
                    self.html_content = part.get_payload(decode=True).decode('utf-8', errors='ignore')
        else:
            self.text_content = self.message.get_payload(decode=True).decode('utf-8', errors='ignore')
            self.content_type = self.message.get_content_type()

class TestSMTPServer(smtpd.SMTPServer):
    def __init__(self, localaddr, remoteaddr):
        super().__init__(localaddr, remoteaddr)
        print(f"SMTP server initialized on {localaddr}")
        
    def process_message(self, peer, mailfrom, rcpttos, data, **kwargs):
        """Process received email message"""
        try:
            # Create email message object
            email_msg = EmailMessage(mailfrom, rcpttos, data)
            
            # Store email
            received_emails.append(email_msg)
            email_stats['total'] += 1
            email_stats['from_addresses'].add(mailfrom)
            email_stats['to_addresses'].update(rcpttos)
            email_stats['subjects'].append(email_msg.subject)
            
            # Print email info
            print(f"Received email:")
            print(f"   From: {mailfrom}")
            print(f"   To: {', '.join(rcpttos)}")
            print(f"   Subject: {email_msg.subject}")
            print(f"   Content: {email_msg.text_content[:100]}...")
            print(f"   Total emails: {email_stats['total']}")
            
            return None  # Accept the message
            
        except Exception as e:
            print(f"Error processing email: {e}")
            return None

class EmailServer:
    def __init__(self, smtp_port=2525, web_port=8081):
        self.smtp_port = smtp_port
        self.web_port = web_port
        self.smtp_server = None
        self.web_app = None
        self.smtp_thread = None
        self.web_thread = None
        self.running = False
        
    def start_smtp_server(self):
        """Start SMTP server in a separate thread"""
        if self.running:
            print("SMTP server is already running")
            return
            
        self.running = True
        self.smtp_server = TestSMTPServer(('localhost', self.smtp_port), None)
        self.smtp_thread = threading.Thread(target=self._run_smtp_server)
        self.smtp_thread.daemon = True
        self.smtp_thread.start()
        print(f"SMTP server started on localhost:{self.smtp_port}")
        
    def stop_smtp_server(self):
        """Stop SMTP server"""
        self.running = False
        if self.smtp_server:
            self.smtp_server.close()
        if self.smtp_thread:
            self.smtp_thread.join(timeout=2)
        print("SMTP server stopped")
        
    def _run_smtp_server(self):
        """Run the SMTP server"""
        try:
            asyncore.loop()
        except Exception as e:
            if self.running:
                print(f"SMTP server error: {e}")
                
    def start_web_server(self):
        """Start web interface server"""
        if not FLASK_AVAILABLE:
            print("Flask not available, web interface disabled")
            return
            
        self.web_app = Flask(__name__)
        self._setup_web_routes()
        
        self.web_thread = threading.Thread(target=self._run_web_server)
        self.web_thread.daemon = True
        self.web_thread.start()
        print(f"Web interface started on http://localhost:{self.web_port}")
        
    def stop_web_server(self):
        """Stop web interface server"""
        if self.web_thread:
            self.web_thread.join(timeout=2)
        print("Web interface stopped")
        
    def _run_web_server(self):
        """Run the web server"""
        try:
            self.web_app.run(host='0.0.0.0', port=self.web_port, debug=False, use_reloader=False)
        except Exception as e:
            print(f"Web server error: {e}")
            
    def _setup_web_routes(self):
        """Setup web routes"""
        
        @self.web_app.route('/')
        def home():
            """Home page with email list and stats"""
            stats = self.get_stats()
            
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Email Test Server</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .container {{ max-width: 1200px; margin: 0 auto; }}
                    .section {{ margin: 20px 0; padding: 15px; border: 1px solid \\#ddd; border-radius: 5px; }}
                    .email-item {{ background: \\#f9f9f9; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid \\#007cba; }}
                    .email-header {{ margin-bottom: 10px; }}
                    .email-subject {{ font-weight: bold; color: \\#007cba; font-size: 1.1em; }}
                    .email-meta {{ color: \\#666; font-size: 0.9em; margin: 5px 0; }}
                    .email-content {{ background: white; padding: 10px; border-radius: 3px; margin-top: 10px; }}
                    .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; }}
                    .stat-item {{ background: \\#e8f4fd; padding: 10px; border-radius: 3px; text-align: center; }}
                    .smtp-info {{ background: \\#f0f8ff; padding: 10px; border-radius: 3px; font-family: monospace; }}
                    button {{ background: \\#007cba; color: white; border: none; padding: 10px 20px; border-radius: 3px; cursor: pointer; }}
                    button:hover {{ background: \\#005a8b; }}
                    .timestamp {{ color: \\#666; font-size: 0.9em; }}
                </style>
                <script>
                    function refreshPage() {{
                        location.reload();
                    }}
                    
                    function clearEmails() {{
                        fetch('/clear', {{method: 'POST'}})
                            .then(() => location.reload());
                    }}
                    
                    // Auto-refresh every 10 seconds
                    setInterval(refreshPage, 10000);
                </script>
            </head>
            <body>
                <div class="container">
                    <h1>Email Test Server</h1>
                    <p>This server receives emails for testing notification functionality.</p>
                    
                    <div class="section">
                        <h2>Statistics</h2>
                        <div class="stats">
                            <div class="stat-item">
                                <div>Total Emails</div>
                                <div><strong>{stats['total']}</strong></div>
                            </div>
                            <div class="stat-item">
                                <div>From Addresses</div>
                                <div><strong>{len(stats['from_addresses'])}</strong></div>
                            </div>
                            <div class="stat-item">
                                <div>To Addresses</div>
                                <div><strong>{len(stats['to_addresses'])}</strong></div>
                            </div>
                        </div>
                        <button onclick="refreshPage()">Refresh</button>
                        <button onclick="clearEmails()">Clear All</button>
                    </div>
                    
                    <div class="section">
                        <h2>SMTP Configuration</h2>
                        <div class="smtp-info">
                            <strong>SMTP Server:</strong> localhost:{self.smtp_port}<br>
                            <strong>Use in your email connector configuration:</strong><br>
                            <code>
                            {{<br>
                            &nbsp;&nbsp;"smtp_host": "localhost",<br>
                            &nbsp;&nbsp;"smtp_port": {self.smtp_port},<br>
                            &nbsp;&nbsp;"username": "<EMAIL>",<br>
                            &nbsp;&nbsp;"password": "test123",<br>
                            &nbsp;&nbsp;"sender": "<EMAIL>",<br>
                            &nbsp;&nbsp;"security": "none"<br>
                            }}
                            </code>
                        </div>
                    </div>
                    
                    <div class="section">
                        <h2>Received Emails</h2>
                        <p>Last {len(stats['emails'])} received emails:</p>
            """
            
            if stats['emails']:
                for email_msg in reversed(stats['emails']):
                    # Compatible with dict and object
                    subject = email_msg['subject'] if isinstance(email_msg, dict) else email_msg.subject
                    from_addr = email_msg['from_addr'] if isinstance(email_msg, dict) else email_msg.from_addr
                    to_addrs = email_msg['to_addrs'] if isinstance(email_msg, dict) else email_msg.to_addrs
                    timestamp = email_msg['timestamp'] if isinstance(email_msg, dict) else email_msg.timestamp
                    text_content = email_msg['text_content'] if isinstance(email_msg, dict) else email_msg.text_content
                    html += f"""
                        <div class="email-item">
                            <div class="email-header">
                                <div class="email-subject">{subject}</div>
                                <div class="email-meta">
                                    <strong>From:</strong> {from_addr}<br>
                                    <strong>To:</strong> {', '.join(to_addrs)}<br>
                                    <strong>Time:</strong> {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))}
                                </div>
                            </div>
                            <div class="email-content">
                                <pre>{text_content}</pre>
                            </div>
                        </div>
                    """
            else:
                html += "<p>No emails received yet.</p>"
                
            html += """
                    </div>
                </div>
            </body>
            </html>
            """
            
            return html
        
        @self.web_app.route('/stats', methods=['GET'])
        def get_stats():
            """Get email statistics as JSON"""
            return jsonify(self.get_stats())
        
        @self.web_app.route('/clear', methods=['POST'])
        def clear_emails():
            """Clear all received emails"""
            self.clear_emails()
            return jsonify({'status': 'cleared'})
        
        @self.web_app.route('/health', methods=['GET'])
        def health():
            """Health check endpoint"""
            return jsonify({'status': 'healthy', 'service': 'email-test-server'})
    
    def get_stats(self):
        """Get email statistics"""
        return {
            'total': email_stats['total'],
            'from_addresses': list(email_stats['from_addresses']),
            'to_addresses': list(email_stats['to_addresses']),
            'subjects': email_stats['subjects'][-10:],  # Last 10 subjects
            'emails': [
                {
                    'from_addr': email.from_addr,
                    'to_addrs': email.to_addrs,
                    'subject': email.subject,
                    'text_content': email.text_content,
                    'timestamp': email.timestamp
                }
                for email in received_emails[-10:]  # Last 10 emails
            ]
        }
    
    def clear_emails(self):
        """Clear all received emails"""
        global received_emails, email_stats
        received_emails.clear()
        email_stats = {
            'total': 0,
            'from_addresses': set(),
            'to_addresses': set(),
            'subjects': []
        }
        print("All emails cleared")
    
    def get_smtp_config(self):
        """Get SMTP configuration for testing"""
        return {
            "smtp_host": "localhost",
            "smtp_port": self.smtp_port,
            "username": "<EMAIL>",
            "password": "test123",
            "sender": "<EMAIL>",
            "security": "none"
        }

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Email Test Server')
    parser.add_argument('--smtp-port', type=int, default=2525, help='SMTP server port')
    parser.add_argument('--web-port', type=int, default=8081, help='Web interface port')
    parser.add_argument('--no-web', action='store_true', help='Disable web interface')
    args = parser.parse_args()
    
    print("Starting Email Test Server")
    print(f"SMTP server will run on localhost:{args.smtp_port}")
    if not args.no_web and FLASK_AVAILABLE:
        print(f"Web interface will run on localhost:{args.web_port}")
    elif args.no_web:
        print("Web interface disabled")
    else:
        print("Web interface disabled (Flask not available)")
    print("Press Ctrl+C to stop the server")
    
    server = EmailServer(args.smtp_port, args.web_port)
    
    try:
        # Start SMTP server
        server.start_smtp_server()
        
        # Start web interface if available and not disabled
        if not args.no_web and FLASK_AVAILABLE:
            server.start_web_server()
        
        # Keep running
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\nStopping Email Test Server...")
        server.stop_smtp_server()
        if not args.no_web and FLASK_AVAILABLE:
            server.stop_web_server()
        print("Email Test Server stopped")

if __name__ == "__main__":
    main() 