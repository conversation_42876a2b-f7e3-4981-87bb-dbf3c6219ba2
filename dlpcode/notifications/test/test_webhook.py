#!/usr/bin/env python3
"""
Webhook Notification Test Script

This script demonstrates how to:
1. Create a webhook connector
2. Create a webhook template
3. Send a test notification via webhook
"""

import sys
import os
import json
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from notifications.notifier.factory import notifier_factory
from exts import Session
from notifications.model.notification import NotificationConnector, Template


def create_webhook_connector_direct(connector_data):
    """Create webhook connector directly in database"""
    try:
        from notifications.notifier.webhook import WebhookNotifier
        
        # Test connection first
        config = connector_data.get('config', {})
        if not WebhookNotifier.validate_config(config):
            raise ValueError("Invalid webhook connector configuration")
        
        # Test connection
        if not WebhookNotifier.test_connection(config):
            raise ValueError("Webhook connection test failed")
        
        # Create connector
        session = Session()
        connector = NotificationConnector(
            name=connector_data['name'],
            notifier_type='webhook',
            config=config
        )
        
        session.add(connector)
        session.commit()
        connector_id = connector.id
        session.close()
        
        print(f"SUCCESS: Created webhook connector: {connector_data['name']} (ID: {connector_id})")
        return connector_id
        
    except Exception as e:
        print(f"ERROR: Error creating webhook connector: {e}")
        raise


def create_webhook_template_direct(template_data, connector_id):
    """Create webhook template directly in database"""
    try:
        session = Session()
        template = Template(
            name=template_data['name'],
            notifier_type='webhook',
            notification_type=template_data['notification_type'],
            template=template_data['template'],
            is_default=False,
            connector_id=connector_id,
            extra=template_data.get('extra', {})
        )
        
        session.add(template)
        session.commit()
        template_id = template.id
        session.close()
        
        print(f"SUCCESS: Created webhook template: {template_data['name']} (ID: {template_id})")
        return template_id
        
    except Exception as e:
        print(f"ERROR: Error creating webhook template: {e}")
        raise


def test_webhook_connection(webhook_url):
    """Test webhook connection"""
    print("Testing webhook connection...")
    
    try:
        from notifications.notifier.webhook import WebhookNotifier
        config = {"webhook_url": webhook_url}
        notifier = WebhookNotifier(config=config)
        result = notifier.test_connection(config)
        
        if result:
            print("SUCCESS: Webhook connection test successful!")
            return True
        else:
            print("FAILED: Webhook connection test failed!")
            return False
    except Exception as e:
        print(f"ERROR: Webhook connection test error: {e}")
        return False


def create_webhook_test_data(webhook_url):
    """Create test webhook connector and template"""
    print("\nCreating webhook test data...")
    
    # Test connector data
    connector_data = {
        "name": "Test Webhook Connector",
        "notifier_type": "webhook",
        "config": {
            "webhook_url": webhook_url,
        }
    }

    # Test template data
    template_data = {
        "name": "Test Webhook Template",
        "notifier_type": "webhook",
        "notification_type": "incident",
        "template": {
            "content": """Webhook Test Notification!

**Message:** {{ message }}
**Subject:** {{ subject }}
**Timestamp:** {{ timestamp }}

This is a test notification sent via webhook."""
        },
        "extra": {
            "priority": "high",
            "min_interval": 10
        }
    }
    
    try:
        # Create connector
        connector_id = create_webhook_connector_direct(connector_data)
        print(f"SUCCESS: Created webhook connector with ID: {connector_id}")
        
        # Create template
        template_id = create_webhook_template_direct(template_data, connector_id)
        print(f"SUCCESS: Created webhook template with ID: {template_id}")
        
        return connector_id, template_id
        
    except Exception as e:
        print(f"ERROR: Error creating test data: {e}")
        return None, None


def send_test_notification(template_id, connector_id=None):
    """Send a test notification via webhook"""
    print("\nSending test webhook notification...")
    
    # Test context data
    context = {
        "message": "Hello from webhook!",
        "subject": "Test Subject",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    try:
        # Send notification
        result = notifier_factory.send_notification(
            connector_id=connector_id,
            template_id=template_id,
            context=context
        )
        
        if result.get('success'):
            print("SUCCESS: Webhook notification sent successfully!")
            print(f"Result: {result}")
        else:
            print("FAILED: Failed to send webhook notification!")
            print(f"Error: {result}")
            
        return result
        
    except Exception as e:
        print(f"ERROR: Error sending notification: {e}")
        return None


def main():
    """Main test function"""
    print("Webhook Notification Test")
    print("=" * 50)
    webhook_url = os.environ.get('WEBHOOK_URL', '')
    if not webhook_url:
        webhook_url = input("Enter webhook URL (e.g., http://localhost:8082/webhook/test): ").strip()
    if not webhook_url:
        print("ERROR: No webhook URL provided. Exiting.")
        return

    # Step 1: Test connection (optional - requires real webhook URL)
    if not test_webhook_connection(webhook_url):
        print("WARNING: Connection test failed, but continuing with database test...")
    
    # Step 2: Create test data
    connector_id, template_id = create_webhook_test_data(webhook_url)
    if not connector_id or not template_id:
        print("ERROR: Failed to create test data. Exiting.")
        return
    
    # Step 3: Send test notification
    result = send_test_notification(template_id)
    
    # Step 4: Summary
    print("\nTest Summary:")
    print(f"  Connector ID: {connector_id}")
    print(f"  Template ID: {template_id}")
    print(f"  Send Result: {'Success' if result and result.get('success') else 'Failed'}")
    
    if result and result.get('success'):
        print("\nSUCCESS: All tests passed! Webhook notification system is working correctly.")
    else:
        print("\nWARNING: Some tests failed. Check the error messages above.")


def test_existing_data():
    """Test with existing connector and template"""
    print("\nTesting with existing data...")
    
    session = Session()
    try:     
        # Find existing webhook template
        template = session.query(Template).filter_by(notifier_type='webhook').first()
        if not template:
            print("ERROR: No webhook template found in database")
            return

        print(f"SUCCESS: Found existing template: {template.name} (ID: {template.id})")
        
        # Send test notification
        send_test_notification(template.id)
        
    finally:
        session.close()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test webhook notification system")
    parser.add_argument("--existing", action="store_true", help="Test with existing data")
    
    args = parser.parse_args()
    
    if args.existing:
        test_existing_data()
    else:
        main() 