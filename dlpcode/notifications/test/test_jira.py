#!/usr/bin/env python3
"""
Example: Send incident notification to <PERSON><PERSON>

This example demonstrates how to send an incident notification using:
1. Incident data
2. Template ID
3. Jira connector
"""

import uuid
from datetime import datetime
from notifications.notifier.factory import notifier_factory
from exts import Session
from notifications.model.notification import Template, NotificationConnector
import argparse

CONFIG={
    "domain": "forticasbqa.atlassian.net",
    "user_email": "<EMAIL>",
    "api_token": "ATATT3xFfGF0RC5Mc3VlpixD5S8Z1HbVFhNG1PRuO7F66KD73we5dBJQVzv2Aa5xHEwBYtVVy9vof8R0bwmFBHk5r9Ej7d_NmYmgUZHO-xIsYPDNr_JxV467Z6wuogMVWHCuhrJwQssdUt7p3xHu0u7-AwgbE5lP-ZLQLgh2obqK50w5JjmV1Nc=4D258FCD"
}

def create_jira_connector_direct():
    session = Session()
    try:
        connector = NotificationConnector(
            id=uuid.uuid4(),
            name="Test Jira Connector",
            notifier_type="jira",
            config=CONFIG,
            extra={},
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        session.add(connector)
        session.commit()
        connector_id = str(connector.id)
        print("Connector created:", connector_id)
        return connector_id
    finally:
        session.close()

def create_jira_template_direct(connector_id):
    session = Session()
    try:
        template = Template(
            id=uuid.uuid4(),
            name="jira_incident_template",
            notifier_type="jira",
            notification_type="incident",
            template={
                "subject": "Security Incident: {{ incident_id }}",
                "content": "A security incident was detected at {{ detected_at }}.\n\nDetails:\n- Type: {{ incident_type }}\n- Severity: {{ severity }}\n- Description: {{ description }}",
            },
            extra = { 
                "priority": "High", 
                "project_key": "FT",
                "issue_type": "Task",
                "min_interval": 0, 
                "description": "DLP alert jira template" 
            },
            connector_id=connector_id,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        session.add(template)
        session.commit()
        template_id = str(template.id)
        print("Template created:", template_id)
        return template_id
    finally:
        session.close()

def init_db_info():
    connector_id = create_jira_connector_direct()
    if not connector_id:
        print("Failed to create connector. Exiting.")
        return
    print(f"   Connector ID: {connector_id}\n")

    template_id = create_jira_template_direct(connector_id)
    if not template_id:
        print("Failed to create template. Exiting.")
        return
    print(f"   Template ID: {template_id}\n")
    print("Direct DB initialization complete. You can now use these IDs for notification sending.")
    return template_id, connector_id

def get_db_info(template_id=None):
    session = Session()
    try:
        if template_id:
            template = session.query(Template).filter_by(id=template_id).first()
            if not template:
                print(f"ERROR: No template found for id {template_id}")
                return None, None
        else:
            template = session.query(Template).filter_by(notifier_type='jira', notification_type='incident').first()
            if not template:
                print("ERROR: No jira incident template found in database")
                return None, None
        template_id = str(template.id)
        connector_id = str(template.connector_id)
        print(f"SUCCESS: Found existing template: {template.name} (ID: {template_id})")
        if connector_id:
            connector = session.query(NotificationConnector).filter_by(id=connector_id).first()
            if connector:
                print(f"SUCCESS: Found connector from template: {connector.name} (ID: {connector_id})")
            else:
                print(f"WARNING: Connector ID {connector_id} from template not found in database.")
        else:
            print("WARNING: Template has no connector_id.")
        return template_id, connector_id
    finally:
        session.close()

def prepare_context_data():
    incident_data = {
        "incident_id": f"INC-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:8].upper()}",
        "severity": "High",
        "incident_type": "Unauthorized Access Attempt",
        "description": "Multiple failed login attempts detected from suspicious IP address. Potential brute force attack in progress.",
        "detected_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "assignee": "<EMAIL>"
    }
    print("   Incident Data:")
    for key, value in incident_data.items():
        print(f"     {key}: {value}")
    print()
    return incident_data

def send_incident_notification(template_id: str, incident_data: dict):
    result = notifier_factory.send_notification(
        template_id=template_id,
        context=incident_data
    )
    print("Incident notification sent result:")
    print(result)
    return result

def get_jira_projects(connector_id):
    """Fetch and print all projects from Jira using the connector config."""
    session = Session()
    try:
        connector = session.query(NotificationConnector).filter_by(id=connector_id).first()
        if not connector:
            print(f"ERROR: No connector found for id {connector_id}")
            return
        config = connector.config
        from notifications.notifier.jira import JiraNotifier
        notifier = JiraNotifier(config)
        url = f"https://{config['domain']}/rest/api/3/project/search"
        resp = notifier.session.get(url, timeout=getattr(notifier, 'timeout', 10))
        print(f"Request URL: {url}")
        print(f"Status Code: {resp.status_code}")
        if resp.status_code == 200:
            data = resp.json()
            print(f"Found {data.get('total', 0)} projects:")
            for proj in data.get('values', []):
                print(f"  - {proj.get('key')}: {proj.get('name')}")
        else:
            print(f"ERROR: Jira API returned status {resp.status_code}: {resp.text}")
    except Exception as e:
        print(f"Exception occurred while fetching projects: {e}")
    finally:
        session.close()

def get_jira_issue_status(connector_id, issue_key):
    """Fetch and print the status of a Jira issue by its key."""
    session = Session()
    try:
        connector = session.query(NotificationConnector).filter_by(id=connector_id).first()
        if not connector:
            print(f"ERROR: No connector found for id {connector_id}")
            return
        config = connector.config
        from notifications.notifier.jira import JiraNotifier
        notifier = JiraNotifier(config)
        url = f"https://{config['domain']}/rest/api/3/issue/{issue_key}"
        resp = notifier.session.get(url, timeout=getattr(notifier, 'timeout', 10))
        print(f"Request URL: {url}")
        print(f"Status Code: {resp.status_code}")
        if resp.status_code == 200:
            data = resp.json()
            status = data.get('fields', {}).get('status', {})
            print(f"Issue {issue_key} status: {status.get('name', 'Unknown')}")
            print(f"Full status object: {status}")
        else:
            print(f"ERROR: Jira API returned status {resp.status_code}: {resp.text}")
    except Exception as e:
        print(f"Exception occurred while fetching issue status: {e}")
    finally:
        session.close()

def test_get_project_list_func(connector_id, notifier_type='jira'):
    session = Session()
    try:
        c = session.query(NotificationConnector).filter_by(id=connector_id).first()
        if not c:
            print ('connector not found')

        notifier = notifier_factory.get_notifier(c.id, c.notifier_type, c.config)
        if not notifier:
            return print ('Notifier not found')
        result = notifier.get_project_list()
        print (result)
        if isinstance(result, dict) and not result.get('success', True):
            print ("successfully")
            return True
        else:
            print ('Failed to get project list')
            return False
    finally:
        session.close()

def test_get_user_detail(email):
    import requests
    from requests.auth import HTTPBasicAuth

    url = f"https://{CONFIG['domain']}/rest/api/3/user/search"
    params = {
        "query": email
    }
    headers = {
        "Accept": "application/json"
    }
    auth = HTTPBasicAuth(CONFIG['user_email'], CONFIG["api_token"])
    response = requests.get(url, headers=headers, params=params, auth=auth)

    print("Status code:", response.status_code)
    print("Response:", response.json())

def test_get_priority():
    import requests
    from requests.auth import HTTPBasicAuth

    url = f"https://{CONFIG['domain']}/rest/api/3/priority"
    headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
    }
    auth = HTTPBasicAuth(CONFIG['user_email'], CONFIG["api_token"])
    response = requests.get(url, headers=headers, auth=auth)

    print("Status code:", response.status_code)
    print("Response:", response.json())

def main(is_exist=False, template_id_arg=None, issue_key=None):
    if is_exist:
        print("1. Get Jira connector/template from DB ...")
        template_id, connector_id = get_db_info(template_id_arg)
    else:
        print("1. Creating Jira connector/template (direct DB)...")
        template_id, connector_id = init_db_info()

    if not template_id or not connector_id:
        print("ERROR: Could not get or create template/connector.")
        return

    print("2. Preparing incident data...")
    incident_data = prepare_context_data()

    print("3. Sending incident notification...")
    result = notifier_factory.send_notification(template_id=template_id, context=incident_data)
    if result and result.get('success'):
        print()
        print("Notification Sent Successfully")
        print(result)
        # parse issue_key
        if not issue_key:
            issue_key = None
            if isinstance(result.get('data'), dict):
                issue_key = result['data'].get('ticket_id')  # Updated to use unified field name
                if not issue_key:
                    issue_key = result['data'].get('raw_response', {}).get('key')
            if issue_key:
                print(f"Auto-detected new issue key: {issue_key}")
    else:
        print()
        print("Notification Failed")
        print(result)

    print("\n4. Fetching Jira projects for this connector...")
    get_jira_projects(connector_id)

    if issue_key:
        print(f"\n5. Fetching status for issue {issue_key}...")
        get_jira_issue_status(connector_id, issue_key)

if __name__ == "__main__":
    print("Jira Incident Notification Example")
    parser = argparse.ArgumentParser(description="Send Jira incident notification example")
    parser.add_argument("--existing", "-e", action="store_true", help="Test with existing data")
    parser.add_argument("--template-id", "-t", type=str, help="Specify template_id to use in existing mode")
    parser.add_argument("--connector-id", "-c", type=str, help="Specify connector_id to use in existing mode")
    parser.add_argument("--issue-key", "-k", type=str, help="Specify Jira issue key to fetch status (e.g., SEC-123)")
    parser.add_argument("--user-email", "-u", type=str, help="Specify Jira email")
    parser.add_argument("--priority", "-p", action="store_true", help="Test to get priority")
    args = parser.parse_args()

    if args.priority:
        test_get_priority()
        exit(0)
    if args.connector_id:
        test_get_project_list_func(args.connector_id)
        exit(0) 
    if args.user_email:
        test_get_user_detail(args.user_email)
        exit(0) 
    main(args.existing, args.template_id, args.issue_key)
