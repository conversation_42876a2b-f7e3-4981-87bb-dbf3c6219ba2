#!/usr/bin/env python3
"""
Example: Send incident notification to email

This example demonstrates how to send an incident notification using:
1. Incident data
2. Template ID
3. Email connector
"""

import uuid
from datetime import datetime
from typing import Dict, Any
from notifications.notifier.factory import notifier_factory
from exts import Session
from notifications.model.notification import Template, NotificationConnector
import smtplib
import argparse


def create_email_connector_direct():
    # Test SMTP connectivity first
    smtp_host = "localhost"
    smtp_port = 2525
    try:
        with smtplib.SMTP(smtp_host, smtp_port, timeout=5) as smtp:
            smtp.noop()  # Simple command to check connection
        print(f"SMTP server {smtp_host}:{smtp_port} is reachable.")
    except Exception as e:
        print(f"Failed to connect to SMTP server {smtp_host}:{smtp_port}: {e}")
        return None

    session = Session()
    connector = NotificationConnector(
        id=uuid.uuid4(),
        name="Test Email Connector",
        notifier_type="email",
        config={
            "smtp_host": smtp_host,
            "smtp_port": smtp_port,
            "username": "<EMAIL>",
            "password": "test123",
            'authentication': 'false',
            "security": "none"
        },
        extra={},
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    session.add(connector)
    session.commit()
    connector_id = str(connector.id)
    session.close()
    print("Connector created:", connector_id)
    return connector_id

def create_incident_template_direct(connector_id):
    session = Session()
    template = Template(
        id=uuid.uuid4(),
        name="incident_alert_template",
        notifier_type="email",
        notification_type="incident",
        template={
            "subject": "Security Incident Alert - {{ incident_id }}",
            "content": """<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: \\#333; }
        .header { background: linear-gradient(135deg, \\#d32f2f, \\#b71c1c); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
        .content { padding: 20px; background: \\#f9f9f9; }
        .details { background: white; padding: 15px; border-left: 4px solid \\#d32f2f; margin: 15px 0; }
        .actions { background: \\#fff3e0; padding: 15px; border-left: 4px solid \\#ff9800; margin: 15px 0; }
        .footer { background: \\#f5f5f5; padding: 15px; text-align: center; border-radius: 0 0 8px 8px; }
        .severity-high { color: \\#d32f2f; font-weight: bold; }
        .severity-medium { color: \\#f57c00; font-weight: bold; }
        .severity-low { color: \\#388e3c; font-weight: bold; }
    </style>
</head>
<body>
    <div class=\"header\">\n        <h1>Security Incident Alert</h1>\n        <p>Incident ID: {{ incident_id }}</p>\n    </div>\n    \n    <div class=\"content\">\n        <p>Dear {{ recipient_name }},</p>\n        \n        <p>A security incident has been detected and requires your immediate attention.</p>\n        \n        <div class=\"details\">\n            <h3>Incident Details</h3>\n            <ul>\n                <li><strong>Incident ID:</strong> {{ incident_id }}</li>\n                <li><strong>Severity:</strong> <span class=\"severity-{{ severity|lower }}\">{{ severity }}</span></li>\n                <li><strong>Type:</strong> {{ incident_type }}</li>\n                <li><strong>Description:</strong> {{ description }}</li>\n                <li><strong>Detected At:</strong> {{ detected_at }}</li>\n                <li><strong>Source IP:</strong> {{ source_ip }}</li>\n                <li><strong>Affected System:</strong> {{ affected_system }}</li>\n            </ul>\n        </div>\n        \n        <div class=\"actions\">\n            <h3>Recommended Actions</h3>\n            <p>{{ recommended_actions }}</p>\n        </div>\n        \n        <p><strong>Please review and take appropriate action as soon as possible.</strong></p>\n    </div>\n    \n    <div class=\"footer\">\n        <p>Best regards,<br>Security Operations Team</p>\n        <p><small>This is an automated security alert. Please do not reply to this email.</small></p>\n    </div>\n</body>\n</html>""",
            "content_type": "html"
        },
        extra={
            "priority": "High",
            "sender": "<EMAIL>",
            "receivers": ["<EMAIL>", "<EMAIL>"],
            "min_interval": 0
        },
        connector_id=connector_id,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    session.add(template)
    session.commit()
    template_id = str(template.id)
    session.close()
    print("Template created:", template_id)
    return template_id

def init_db_info():
    connector_id = create_email_connector_direct()
    if not connector_id:
        print("Failed to create connector. Exiting.")
        return
    print(f"   Connector ID: {connector_id}\n")

    template_id = create_incident_template_direct(connector_id)
    if not template_id:
        print("Failed to create template. Exiting.")
        return
    print(f"   Template ID: {template_id}\n")
    print("Direct DB initialization complete. You can now use these IDs for notification sending.")
    return template_id, connector_id

def get_db_info():
    # Use existing template and its connector
    session = Session()
    template = session.query(Template).filter_by(notifier_type='email', notification_type='incident').first()
    if not template:
        print("ERROR: No email incident template found in database")
        session.close()
        return
    template_id = str(template.id)
    connector_id = str(template.connector_id)
    print(f"SUCCESS: Found existing template: {template.name} (ID: {template_id})")
    if connector_id:
        connector = session.query(NotificationConnector).filter_by(id=connector_id).first()
        if connector:
            print(f"SUCCESS: Found connector from template: {connector.name} (ID: {connector_id})")
        else:
            print(f"WARNING: Connector ID {connector_id} from template not found in database.")
    else:
        print("WARNING: Template has no connector_id.")
    session.close()
    return template_id, connector_id

def prepare_context_data():
    incident_data = {
        "recipient_name": "Security Team",
        "incident_id": f"INC-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:8].upper()}",
        "severity": "High",
        "incident_type": "Unauthorized Access Attempt",
        "description": "Multiple failed login attempts detected from suspicious IP address. Potential brute force attack in progress.",
        "detected_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "ip": "*************",
        "affected_system": "Web Application Server",
        "recommended_actions": """1. Immediately block the source IP address\n2. Review system logs for any successful unauthorized access\n3. Check for any data exfiltration attempts\n4. Update firewall rules to prevent similar attacks\n5. Consider implementing additional authentication measures\n6. Notify system administrators for further investigation"""
    }
    print("   Incident Data:")
    for key, value in incident_data.items():
        if key == "recommended_actions":
            print(f"     {key}: [Multi-line text]")
        else:
            print(f"     {key}: {value}")
    print()
    return incident_data


def send_incident_notification(template_id: str, incident_data: dict):
    result = notifier_factory.send_notification(
        template_id=template_id,
        context=incident_data
    )
    print("Incident notification sent result:")
    print(result)
    return result


def main(is_exist=False):
    if is_exist:
        print("1. Get email connector/tmplate from DB ...")
        template_id, connector_id = get_db_info()
    else:
        print("1. Creating email connector/tmplate (direct DB)...")
        template_id, connector_id = init_db_info()

    print("2. Preparing incident data...")
    incident_data = prepare_context_data()

    print("3. Sending incident notification...")
    result = notifier_factory.send_notification(template_id=template_id, context=incident_data)
    if result and result.get('success'):
        print()
        print("Notification Sent Successfully")
        print("Check your email or the test server web interface:")
        print("  http://localhost:8081")
    else:
        print()
        print("Notification Failed")
        print("Please check the error messages above.")

if __name__ == "__main__":
    print("Incident Notification Example")
    print("For testing, also start the email test server:")
    print("  python notifications/test/email_server.py")
    print()
    parser = argparse.ArgumentParser(description="Send incident notification example")
    parser.add_argument("--existing", action="store_true", help="Test with existing data")
    args = parser.parse_args()

    main(args.existing) 