#!/usr/bin/env python3
"""Notifier Package"""

from notifications.notifier.base import BaseNotifier
from notifications.notifier.http_base import HttpNotifierBase

# Import specific notifiers
from notifications.notifier.email import SmtpEmailNotifier
from notifications.notifier.slack import SlackNotifier
from notifications.notifier.teams import TeamsNotifier
from notifications.notifier.webhook import WebhookNotifier
from notifications.notifier.jira import JiraNotifier
from notifications.notifier.servicenow import ServiceNowNotifier

# Import factory
from notifications.notifier.factory import notifier_factory, SUPPORTED_PLATFORMS

__all__ = [
    'BaseNotifier', 
    'notifier_factory', 
    'HttpNotifierBase',
    'SmtpEmailNotifier', 
    'SlackNotifier', 
    'TeamsNotifier', 
    'WebhookNotifier', 
    'JiraNotifier',
    'ServiceNowNotifier',
    'SUPPORTED_PLATFORMS'
]

__version__ = '1.0.0'   