import smtplib
import html2text
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from email.header import <PERSON><PERSON>
from typing import Dict, Any, Optional, Union
from notifications.notifier.base import BaseNotifier
from notifications.enums import ErrorMessages
from notifications.service import decrypt_config
from util.common_log import get_logger

logger = get_logger("notification")

class SmtpEmailNotifier(BaseNotifier):
    """SMTP Email notifier with comprehensive error handling."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self._validate_config()

    def _validate_config(self):
        """Validate SMTP configuration."""
        smtp_host = self.config.get('smtp_host')
        smtp_port = self.config.get('smtp_port')
        username = self.config.get('username')
        password = self.config.get('password')
        user_auth = self.config.get('authentication', 'true').lower() == 'true'
        encrypted = self.config.get('encrypted', 'false').lower() == 'true'

        if not smtp_host or not smtp_port:
            raise ValueError(ErrorMessages.CONFIG_MISSING_SMTP_HOST_PORT.format(notifier="SmtpEmailNotifier"))
        if user_auth and not (username and password):
            raise ValueError(ErrorMessages.CONFIG_MISSING_CREDENTIALS.format(notifier="SmtpEmailNotifier"))
        if password and encrypted:
            password = decrypt_config(password)

        self.smtp_host = smtp_host
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.security = self.config.get('security', 'starttls').lower()
        self.user_auth = user_auth

    @staticmethod
    def validate_config(config):
        """Validate config dict for required SMTP fields."""
        try:
            smtp_host = config.get('smtp_host')
            smtp_port = config.get('smtp_port')
            if not smtp_host or not smtp_port:
                return False
            user_auth = config.get('authentication', 'true').lower() == 'true'
            if user_auth:
                username = config.get('username')
                password = config.get('password')
                return bool(username and password)
            else:
                return True
        except Exception:
            return False

    @staticmethod
    def test_connection(config):
        """Test SMTP connection with given config."""
        try:
            notifier = SmtpEmailNotifier(config)
            security = notifier.security
            smtp_host = notifier.smtp_host
            smtp_port = notifier.smtp_port
            user_auth = notifier.user_auth
            username = notifier.username
            password = notifier.password

            # Connect to SMTP server
            if security == 'ssl':
                server = smtplib.SMTP_SSL(smtp_host, smtp_port, timeout=5)
            else:
                server = smtplib.SMTP(smtp_host, smtp_port, timeout=5)
                if security == 'starttls':
                    server.starttls()
            
            # Test basic SMTP command to verify connection
            try:
                server.ehlo()
                if user_auth:
                    if not username or not password:
                        raise ValueError("Username and password required for authentication")
                    server.login(username, password)
            except smtplib.SMTPException as e:
                logger.error(f"SMTP command test failed: {e}")
                return False
            return True
        finally:
            try:
                server.quit()
            except Exception:
                pass

    def _send_impl(self, message: str, subject: Optional[str] = None,
                   recipients: Optional[Union[str, list]] = None,
                   context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Send email via SMTP with error handling.
        Args:
            message: Email content (HTML or plaintext)
            subject: Email subject
            recipients: Email recipient(s)
            context: Additional context with content_type and sender
        Returns:
            Dict with detailed result information
        """
        if not recipients:
            return {
                "success": False,
                "error": ErrorMessages.EMAIL_MISSING_RECIPIENTS
            }
        to_emails = [recipients] if isinstance(recipients, str) else recipients
        email_body = message
        email_subject = subject or 'Notification'
        try:
            msg = MIMEMultipart('alternative')
            sender = context.get('sender', self.username) if context else self.username
            msg['From'] = sender
            msg['To'] = ', '.join(to_emails)
            msg['Subject'] = str(Header(email_subject, 'utf-8'))
            content_type = context.get('content_type', 'html') if context else 'html'
            if content_type == 'html':
                html_part = MIMEText(email_body, 'html', 'utf-8')
                msg.attach(html_part)

                plaintext_content = html2text.html2text(email_body).strip()
                text_part = MIMEText(plaintext_content, 'plain', 'utf-8')
                msg.attach(text_part)
            else:
                html_content = f"<html><body><pre>{email_body}</pre></body></html>"
                html_part = MIMEText(html_content, 'html', 'utf-8')
                msg.attach(html_part)

                text_part = MIMEText(email_body, 'plain', 'utf-8')
                msg.attach(text_part)

            if self.security == 'ssl':
                server = smtplib.SMTP_SSL(self.smtp_host, self.smtp_port)
            else:
                server = smtplib.SMTP(self.smtp_host, self.smtp_port)
                if self.security == 'starttls':
                    server.starttls()
            if self.user_auth:
                server.login(self.username, self.password)
            server.sendmail(sender, to_emails, msg.as_string())
            server.quit()

            return {
                "success": True,
                "message": ErrorMessages.MESSAGE_SENT_SUCCESS,
                "recipients": to_emails,
                "subject": email_subject
            }
        except smtplib.SMTPAuthenticationError as e:
            logger.error(f"SMTP authentication failed: {e}")
            return {
                "success": False,
                "error": ErrorMessages.EMAIL_AUTH_FAILED
            }
        except smtplib.SMTPRecipientsRefused as e:
            logger.error(f"SMTP recipients refused: {e}")
            return {
                "success": False,
                "error": ErrorMessages.EMAIL_RECIPIENTS_REFUSED
            }
        except smtplib.SMTPServerDisconnected as e:
            logger.error(f"SMTP server disconnected: {e}")
            return {
                "success": False,
                "error": ErrorMessages.EMAIL_SERVER_DISCONNECTED
            }
        except smtplib.SMTPException as e:
            logger.error(f"SMTP error: {e}")
            return {
                "success": False,
                "error": ErrorMessages.EMAIL_SMTP_ERROR.format(error=str(e))
            }
        except Exception as e:
            logger.error(f"Email sending failed: {e}")
            return {
                "success": False,
                "error": ErrorMessages.SENDING_FAILED.format(error=str(e))
            }
