import requests
from typing import Dict, Any, Optional, Union
from util.common_log import get_logger
from notifications.notifier.http_base import HttpNotifierBase
from notifications.notifier.base import BaseNotifier
from notifications.enums import ErrorMessages
from notifications.service import decrypt_config

logger = get_logger("notification")

class SlackNotifier(BaseNotifier, HttpNotifierBase):
    def __init__(self, config: Dict[str, Any]):
        timeout = config.get('timeout', 10)
        HttpNotifierBase.__init__(self, timeout=timeout)
        BaseNotifier.__init__(self, config)
        self._validate_config()

    def _validate_config(self):
        webhook_url = self.config.get('webhook_url')
        encrypted = self.config.get('encrypted', 'false').lower() == 'true'
        if not webhook_url:
            raise ValueError(ErrorMessages.CONFIG_MISSING_WEBHOOK_URL.format(notifier="SlackNotifier"))
        if encrypted:
            webhook_url = decrypt_config(webhook_url)
        self.webhook_url = webhook_url

    def _send_impl(self, message: str, subject: Optional[str] = None,
                   recipients: Optional[Union[str, list]] = None,
                   context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Send Slack notification with comprehensive error handling
        
        Args:
            message: Slack message content
            subject: Message pretext/subject
            recipients: Channel name(s)
            context: Additional context (color, title, fields, etc.)
            
        Returns:
            Dict with detailed result information
        """
        try:
            # Use provided parameters
            payload = {"text": message}
            
            # Add subject as pretext if provided
            if subject:
                payload["pretext"] = subject
            
            # Add Slack-specific fields from context
            if context:
                color = context.get('color', 'good')
                title = context.get('title', '')
                fields = context.get('fields', [])
                
                if color or title or fields:
                    attachment = {"text": message, "color": color}
                    if title:
                        attachment["title"] = title
                    if fields and isinstance(fields, list):
                        attachment["fields"] = fields
                    # Only set attachments if attachment is a dict
                    payload = {"attachments": [attachment]}
            
            # Add channel if specified in recipients
            if recipients:
                if isinstance(recipients, str):
                    payload["channel"] = recipients
                elif isinstance(recipients, list) and recipients and isinstance(recipients[0], str):
                    payload["channel"] = recipients[0]  # Use first channel
            
            response = self.post(self.webhook_url, json=payload)
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "message": ErrorMessages.MESSAGE_SENT_SUCCESS,
                    "channel": payload.get("channel", "default"),
                    "pretext": subject
                }
            else:
                return {
                    "success": False,
                    "error": ErrorMessages.API_ERROR.format(status_code=response.status_code)
                }
                
        except requests.exceptions.Timeout:
            logger.error("Slack request timeout")
            return {
                "success": False,
                "error": ErrorMessages.TIMEOUT
            }
        except requests.exceptions.ConnectionError:
            logger.error("Slack connection error")
            return {
                "success": False,
                "error": ErrorMessages.CONNECTION_ERROR
            }
        except requests.exceptions.RequestException as e:
            logger.error(f"Slack request error: {e}")
            return {
                "success": False,
                "error": ErrorMessages.REQUEST_ERROR.format(error=str(e))
            }
        except Exception as e:
            logger.error(f"Slack sending failed: {e}")
            return {
                "success": False,
                "error": ErrorMessages.SENDING_FAILED.format(error=str(e))
            }

    @staticmethod
    def validate_config(config):
        import re
        webhook_url = config.get('webhook_url') or config.get('url')
        if not webhook_url or not re.match(r'^https://hooks\.slack\.com/services/[\w\-]+/[\w\-]+/[\w\-]+$', webhook_url):
            return False
        return True

    @staticmethod
    def test_connection(config):
        try:
            notifier = SlackNotifier(config)
            result = notifier.send('[notify] Connector test message')
            return result.get('success', False)
        except Exception:
            return False

if __name__ == "__main__":
    # webhook_url = "https://hooks.slack.com/services/TEST/BOT/TOKEN"
    webhook_url = ""
    config = {"webhook_url": webhook_url}
    notifier = SlackNotifier(config)
    result = notifier.send("Hello from notify SlackNotifier!", 
                          subject="Test Subject",
                          recipients="#general")
    print("Slack notification result:", result) 