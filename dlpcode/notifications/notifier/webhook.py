import requests
from typing import Dict, Any, Optional, Union
from util.common_log import get_logger
from notifications.notifier.http_base import HttpNotifierBase
from notifications.notifier.base import BaseNotifier
from notifications.enums import ErrorMessages
from notifications.service import decrypt_config

logger = get_logger("notification")

class WebhookNotifier(BaseNotifier, HttpNotifierBase):
    """Generic webhook notifier for sending notifications to any webhook endpoint"""
    
    def __init__(self, config: Dict[str, Any]):
        timeout = config.get('timeout', 10)
        HttpNotifierBase.__init__(self, timeout=timeout)
        BaseNotifier.__init__(self, config)
        self._validate_config()

    def _validate_config(self):
        """Validate webhook configuration"""
        webhook_url = self.config.get('webhook_url') or self.config.get('url')
        encrypted = self.config.get('encrypted', 'false').lower() == 'true'
        if not webhook_url:
            raise ValueError(ErrorMessages.CONFIG_MISSING_WEBHOOK_URL.format(notifier="WebhookNotifier"))
        if encrypted:
            webhook_url = decrypt_config(webhook_url)

        # Validate URL format
        if not webhook_url.startswith(('http://', 'https://')):
            raise ValueError("Webhook URL must start with http:// or https://")
        
        self.webhook_url = webhook_url
        self.headers = self.config.get('headers', {'Content-Type': 'application/json'})

    @staticmethod
    def validate_config(config):
        """Validate webhook configuration"""
        webhook_url = config.get('webhook_url') or config.get('url')
        if not webhook_url:
            return False
        if not webhook_url.startswith(('http://', 'https://')):
            return False
        return True

    @staticmethod
    def test_connection(config):
        """Test webhook connection"""
        try:
            notifier = WebhookNotifier(config)
            result = notifier.send('[test] Webhook connection test message')
            return result.get('success', False)
        except Exception as e:
            logger.error(f"Webhook connection test failed: {e}")
            return False

    def _send_impl(self, message: str, subject: Optional[str] = None, 
                   recipients: Optional[Union[str, list]] = None, 
                   context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Send notification via webhook"""
        try:
            # Prepare payload
            payload = self._prepare_payload(message)
            
            # Send request using HttpNotifierBase
            response = self.post(
                url=self.webhook_url,
                json=payload,
                headers=self.headers
            )
            
            # Check response
            if response.status_code in [200, 201, 202]:
                logger.info(f"Webhook notification sent successfully to {self.webhook_url}")
                return {
                    'success': True,
                    'message': ErrorMessages.MESSAGE_SENT_SUCCESS,
                    'status_code': response.status_code,
                    'response': response.text,
                    'url': self.webhook_url
                }
            else:
                logger.error(f"Webhook request failed: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': ErrorMessages.API_ERROR.format(status_code=response.status_code),
                    'url': self.webhook_url
                }
                
        except requests.exceptions.Timeout:
            error_msg = ErrorMessages.TIMEOUT
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'url': self.webhook_url
            }
        except requests.exceptions.ConnectionError:
            error_msg = ErrorMessages.CONNECTION_ERROR
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'url': self.webhook_url
            }
        except requests.exceptions.RequestException as e:
            error_msg = ErrorMessages.REQUEST_ERROR.format(error=str(e))
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'url': self.webhook_url
            }
        except Exception as e:
            error_msg = ErrorMessages.SENDING_FAILED.format(error=str(e))
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'url': self.webhook_url
            }

    def _prepare_payload(self, message: str) -> Dict[str, Any]:
        """Prepare webhook payload"""
        
        # Simple JSON format
        payload = {
            'text': message,
            'timestamp': self._get_timestamp()
        }
        
        return payload

    def _get_timestamp(self) -> str:
        """Get current timestamp in ISO format"""
        from datetime import datetime
        return datetime.now().isoformat()

    def get_webhook_info(self) -> Dict[str, Any]:
        """Get webhook configuration information"""
        return {
            'url': self.webhook_url,
            'headers': self.headers,
            'timeout': self.timeout
        } 