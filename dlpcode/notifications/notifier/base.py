from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union
from notifications.enums import ErrorMessages


class BaseNotifier(ABC):
    """Base class for all notifiers with unified send method interface"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config

    @abstractmethod
    def _validate_config(self):
        """Validate the configuration for this notifier"""
        pass
    
    def send(self, message: str, subject: Optional[str] = None, 
             recipients: Optional[Union[str, list]] = None, 
             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Unified send method for all notifiers
        
        Args:
            message: The main message content
            subject: Subject/title (for email)
            recipients: Recipients (email addresses, channels, etc.)
            context: Additional context data
        
        Returns:
            Dict with keys:
            - success: bool - whether the operation was successful
            - message: str - success/error message
            - data: Any - additional data
        """
        try:
            result = self._send_impl(message, subject, recipients, context)
            if isinstance(result, dict) and not result.get('success', True):
                return {
                    'success': False,
                    'message': result.get('error', ErrorMessages.SEND_FAILED),
                    'data': result
                }
            return {
                'success': True,
                'message': ErrorMessages.SEND_SUCCESS,
                'data': result
            }
        except Exception as e:
            return {
                'success': False,
                'message': ErrorMessages.SEND_GENERIC_ERROR.format(error=str(e)),
                'data': None
            }
    
    @abstractmethod
    def _send_impl(self, message: str, subject: Optional[str] = None,
                   recipients: Optional[Union[str, list]] = None,
                   context: Optional[Dict[str, Any]] = None) -> Any:
        """Implementation of the actual send logic"""
        pass