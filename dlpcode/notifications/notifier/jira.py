import requests
import base64
from notifications.notifier.base import BaseNotifier
from typing import Dict, Optional, Union, Any
from notifications.enums import ErrorMessages
from notifications.service import decrypt_config
from util.common_log import get_logger
from requests.auth import HTTPBasicAuth

logger = get_logger("notification")

class JiraNotifier(BaseNotifier):
    def __init__(self, config: Dict[str, Optional[str]]):
        super().__init__(config)
        self._validate_config()
        self._init_session()

    def _validate_config(self):
        domain = self.config.get('domain')
        user_email = self.config.get('user_email')
        api_token = self.config.get('api_token')
        encrypted = self.config.get('encrypted', 'false').lower() == 'true'
        # Normalize domain: remove http(s):// and trailing slashes
        if domain:
            import re
            domain = re.sub(r'^https?://', '', domain)
            domain = domain.rstrip('/')
        # Decrypt api_token if encrypted
        if api_token and encrypted:
            api_token = decrypt_config(api_token)
        if not (domain and user_email and api_token):
            raise ValueError("JiraNotifier requires domain, user_email, api_token in config")
        self.domain = domain
        self.user_email = user_email
        self.api_token = api_token
        self.base_url = f"https://{domain}/rest/api/3"
        self.timeout = self.config.get('timeout', 30)

    def _init_session(self):
        # Configure session with auth and common headers
        self.session = requests.Session()
        self.session.auth = HTTPBasicAuth(self.user_email, self.api_token)
        self.session.headers.update({
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })

    def _send_impl(self, message: str, subject: Optional[str] = None,
                   recipients: Optional[Union[str, list]] = None,
                   context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create Jira issue with comprehensive error handling.
        Args:
            message: Issue description
            subject: Issue summary/title
            context: Additional context (project_key, issue_type, priority, attachments, etc.)
        Returns:
            Dict with detailed result information
        """
        try:
            project_key = context.get('project_key') if context else None
            if not project_key:
                return {
                    'success': False,
                    'error': "Jira project_key is required in context"
                }

            summary = subject or "[FDT Ticket] Notification"
            description = self._str_to_adf(message)
            issue_type = context.get('issue_type', 'Task') if context else 'Task'
            priority = context.get('priority', 'Medium') if context else 'Medium'
            parent = context.get('parent') if context else None

            # Validate sub-task requirements
            if issue_type.lower() in ['sub-task', 'subtask']:
                if not parent:
                    return {
                        'success': False,
                        'error': "Sub-task requires parent issue key"
                    }
            elif parent:
                # Regular issue types should not have parent
                return {
                    'success': False,
                    'error': f"Issue type '{issue_type}' cannot have parent"
                }

            url = f"{self.base_url}/issue"
            payload = {
                "fields": {
                    "project": {"key": project_key},
                    "summary": summary,
                    "description": description,
                    "issuetype": {"name": issue_type},
                }
            }

            if priority:
                payload["fields"]["priority"] = {"name": priority}
            if parent:
                payload["fields"]["parent"] = {"key": parent}

            custom_fields = context.get('custom_fields', {}) if context else {}
            for field_id, field_value in custom_fields.items():
                payload["fields"][field_id] = field_value

            response = self.session.post(url, json=payload, timeout=self.timeout)
            if response.status_code == 201:
                issue_data = response.json()
                issue_key = issue_data.get("key")
                issue_id = issue_data.get("id")

                # Handle attachments if provided
                attachments = context.get("attachments", []) if context else []
                for attachment in attachments:
                    attachment_name = attachment.get('name')
                    attachment_data = attachment.get('data')
                    if attachment_name and attachment_data:
                        self._attach_file(issue_id, attachment_name, attachment_data)

                result = {
                    'success': True,
                    'message': 'Jira issue created successfully',
                    'ticket_id': issue_id,
                    'ticket_key': issue_key,
                    'short_description': summary,
                    'category': issue_type,
                    'ticket_link': self.get_issue_url(issue_key),
                    'creator': {
                        'ticket_creator_email': self.user_email,
                        'ticket_creator': self._get_user_by_email(self.user_email)
                    },
                    'raw_response': issue_data
                }

                assignee_id = context.get('assignee_id') if context else None
                if assignee_id:
                    recipient_data = self._get_user_details(assignee_id)
                    if recipient_data is not None:
                        result['recipient'] = {
                            'recipient_email': recipient_data.get('emailAddress'),
                            'recipient_name': recipient_data.get('displayName'),
                        }
                return result
            else:
                error_text = response.text if response.text else "No error details"
                logger.error(f"Jira issue creation API error: HTTP {response.status_code}, Response: {error_text}, URL: {url}")
                return {
                    'success': False,
                    'error': f"Jira API returned HTTP {response.status_code}"
                }
        except requests.exceptions.Timeout:
            logger.error(f"Jira request timeout: URL={url}, timeout={self.timeout}")
            return {
                'success': False,
                'error': ErrorMessages.TIMEOUT
            }
        except requests.exceptions.ConnectionError:
            logger.error(f"Jira connection error: URL={url}, domain={self.domain}")
            return {
                'success': False,
                'error': ErrorMessages.CONNECTION_ERROR
            }
        except requests.exceptions.RequestException as e:
            logger.error(f"Jira request error: {e}, URL={url}")
            return {
                'success': False,
                'error': ErrorMessages.REQUEST_ERROR.format(error=str(e))
            }
        except Exception as e:
            logger.error(f"Jira issue creation failed: {e}, URL={url}")
            return {
                'success': False,
                'error': ErrorMessages.SENDING_FAILED.format(error=str(e))
            }

    def _str_to_adf(self, text):
        return {
            "type": "doc",
            "version": 1,
            "content": [
                {
                    "type": "paragraph",
                    "content": [
                        {
                            "type": "text",
                            "text": text
                        }
                    ]
                }
            ]
        }

    def _attach_file(self, issue_id: str, attachment_name: str, attachment_data: bytes) -> bool:
        """Attach file to Jira issue"""
        try:
            url = f"{self.base_url}/issue/{issue_id}/attachments"
            # Override Content-Type for file upload
            headers = {'X-Atlassian-Token': 'no-check', 'Content-Type': None}
            files = {'file': (attachment_name, attachment_data)}
            response = self.session.post(url, files=files, headers=headers, timeout=self.timeout)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Failed to attach file {attachment_name}: {e}")
            return False

    def _get_user_details(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user details from Jira"""
        try:
            url = f"{self.base_url}/user"
            params = {'accountId': user_id}
            response = self.session.get(url, params=params, timeout=self.timeout)
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            logger.error(f"Failed to get user details for {user_id}: {e}")
            return None
    
    def _get_user_by_email(self, email: str):
        url = f"{self.base_url}/user/search"
        params = {
            "query": email
        }
        try:
            response = self.session.get(url, params=params, timeout=self.timeout)
            if response.status_code == 200:
                data = response.json()
                if data and data[0]:
                    return data[0].get('displayName')
            return None
        except Exception as e:
            logger.error(f"Failed to get user details for {email}: {e}")
            return None

    def _fetch_all_records(self, endpoint, params=None, page_limit=100):
        """
        General method to fetch all records from a Jira endpoint with paging.
        Returns (all_records, error_text or None)
        """
        all_records = []
        start_at = 0
        while True:
            page_params = params.copy() if params else {}
            page_params["maxResults"] = page_limit
            page_params["startAt"] = start_at
            try:
                resp = self.session.get(endpoint, params=page_params, timeout=self.timeout)
                if resp.status_code != 200:
                    return None, resp.text
                data = resp.json()
                if isinstance(data, list):
                    batch = data
                elif isinstance(data, dict):
                    batch = data.get("values", data.get("users", []))
                else:
                    batch = []
                all_records.extend(batch)
                if len(batch) < page_limit:
                    break
                start_at += page_limit
            except Exception as e:
                return None, str(e)
        return all_records, None

    def get_issue_status(self, issue_id_or_key: str) -> Dict[str, Any]:
        """Get issue status with error handling"""
        try:
            url = f"{self.base_url}/issue/{issue_id_or_key}"
            response = self.session.get(url, timeout=self.timeout)
            if response.status_code == 200:
                data = response.json()
                status = data.get('fields', {}).get('status', {}).get('name')
                return {
                    'success': True,
                    'status': status,
                    'issue_key': issue_id_or_key,
                    'raw_data': data
                }
            else:
                error_text = response.text if response.text else "No error details"
                logger.error(f"Jira issue status API error: HTTP {response.status_code}, Response: {error_text}, URL: {url}")
                return {
                    'success': False,
                    'error': f"Failed to get issue status: HTTP {response.status_code}"
                }
        except Exception as e:
            logger.error(f"Exception in get_issue_status: {e}, URL: {url}")
            return {
                'success': False,
                'error': f"Failed to get issue status: {str(e)}"
            }

    def get_issues_status(self, issue_ids_or_keys: list) -> Dict[str, Optional[str]]:
        """Get status for multiple issues"""
        result = {}
        for issue_id in issue_ids_or_keys:
            status_result = self.get_issue_status(issue_id)
            if status_result.get('success'):
                result[issue_id] = status_result.get('status')
            else:
                result[issue_id] = None
        return result

    def get_project_list(self, max=50):
        """
        Get Jira project list, return key/name/id fields. If max=0, fetch all by paging.
        """
        url = f"{self.base_url}/project/search"
        params = {}
        if max != 0:
            params["maxResults"] = max
        if max == 0:
            projects, error = self._fetch_all_records(url, params)
            if error:
                return {"success": False, "error": error}
            return {
                "success": True,
                "data": [{"key": p["key"], "name": p["name"], "id": p.get("id")} for p in projects]
            }
        else:
            try:
                resp = self.session.get(url, params=params, timeout=self.timeout)
                if resp.status_code == 200:
                    data = resp.json()
                    projects = data.get('values', [])
                    return {
                        "success": True,
                        "data": [{"key": p["key"], "name": p["name"], "id": p.get("id")} for p in projects]
                    }
                else:
                    error_text = resp.text if resp.text else "No error details"
                    return {"success": False, "error": f"Failed to get project list: HTTP {resp.status_code} - {error_text}"}
            except Exception as e:
                return {"success": False, "error": f"Failed to get project list: {str(e)}"}

    def get_issue_url(self, issue_key: str) -> str:
        """Generate a direct URL to view a Jira issue."""
        return f"https://{self.domain}/browse/{issue_key}"

    def get_user_list(self, query="", project_key=None, max=0, page=0, filter_system_users=True):
        """
        Get Jira user list (accountId, displayName, ...).
        If max=0, fetch all users by paging; otherwise, use max and page for pagination.
        """
        try:
            if project_key:
                url = f"{self.base_url}/user/assignable/search"
                base_params = {
                    "query": query,
                    "project": project_key
                }
            else:
                url = f"{self.base_url}/user/search"
                base_params = {
                    "query": query,
                    "accountType": "user"
                }
            users = []
            if max == 0:
                users, error = self._fetch_all_records(url, base_params)
                if error:
                    return {"success": False, "error": error}
            else:
                params = base_params.copy()
                params["maxResults"] = max
                params["startAt"] = max * page
                resp = self.session.get(url, params=params, timeout=self.timeout)
                if resp.status_code != 200:
                    error_text = resp.text if resp.text else "No error details"
                    logger.error(f"Jira user list API error: HTTP {resp.status_code}, Response: {error_text}, URL: {url}, Params: {params}")
                    return {
                        "success": False,
                        "error": f"Failed to get user list: HTTP {resp.status_code} - {error_text}"
                    }
                data = resp.json()
                if isinstance(data, list):
                    users = data
                elif isinstance(data, dict):
                    users = data.get("values", data.get("users", []))
                else:
                    users = []
            return {
                "success": True,
                "users": users
            }
        except Exception as e:
            logger.error(f"Exception in get_user_list: {e}, URL: {locals().get('url', '')}, Params: {locals().get('params', {})}")
            return {
                "success": False,
                "error": f"Failed to get user list: {str(e)}"
            }

    def get_labels(self):
        """
        Get all labels from Jira.
        Returns:
            dict: { "success": True, "labels": [...] } or error info
        """
        url = f"{self.base_url}/label"
        try:
            resp = self.session.get(url, timeout=self.timeout)
            if resp.status_code == 200:
                data = resp.json()
                return {
                    "success": True,
                    "labels": data.get("values", [])
                }
            else:
                error_text = resp.text if resp.text else "No error details"
                logger.error(f"Jira labels API error: HTTP {resp.status_code}, Response: {error_text}, URL: {url}")
                return {
                    "success": False,
                    "error": f"Failed to get labels: HTTP {resp.status_code}"
                }
        except Exception as e:
            logger.error(f"Exception in get_labels: {e}, URL: {url}")
            return {
                "success": False,
                "error": f"Failed to get labels: {str(e)}"
            }

    def get_parent_candidates(self, project_key=None, jql=None, max_results=50):
        """
        Get a list of issues that can be used as parent for sub-tasks.
        You can specify project_key or custom JQL.
        Returns key, summary, id, issuetype, etc.
        """
        url = f"{self.base_url}/search"
        if jql is None:
            # By default, query all issues that are not sub-tasks
            jql = "issuetype != Sub-task AND statusCategory != Done"
        
        # Always apply project filter if project_key is provided
        if project_key and "project=" not in jql:
            jql = f"project={project_key} AND {jql}"

        params = {
            "jql": jql,
            "fields": "key,summary,issuetype",
            "maxResults": max_results
        }
        try:
            resp = self.session.get(url, params=params, timeout=self.timeout)
            if resp.status_code == 200:
                issues = resp.json().get("issues", [])
                return {
                    "success": True,
                    "data": [
                        {
                            "key": i.get("key"),
                            "id": i.get("id"),
                            "summary": i.get("fields", {}).get("summary"),
                            "issuetype": i.get("fields", {}).get("issuetype", {}).get("name")
                        } for i in issues
                    ]
                }
            else:
                error_text = resp.text if resp.text else "No error details"
                logger.error(f"Jira parent candidates API error: HTTP {resp.status_code}, Response: {error_text}, URL: {url}, JQL: {jql}")
                return {
                    "success": False,
                    "error": f"Failed to get parent candidates: HTTP {resp.status_code} - {error_text}"
                }
        except Exception as e:
            logger.error(f"Exception in get_parent_candidates: {e}, URL: {url}, JQL: {jql}")
            return {
                "success": False,
                "error": f"Failed to get parent candidates: {str(e)}"
            }

    def get_issue_types(self, project_key):
        """
        Get all issue types for a given project_key.
        Returns a list of issue types with id and name.
        """
        url = f"{self.base_url}/issue/createmeta?projectKeys={project_key}"
        try:
            resp = self.session.get(url, timeout=self.timeout)
            if resp.status_code == 200:
                data = resp.json()
                projects = data.get("projects", [])
                if projects:
                    # Get issue types from the first project
                    issue_types = projects[0].get("issuetypes", [])
                    # Filter out Epic issue types
                    filtered_issue_types = [
                        {"id": it.get("id"), "name": it.get("name")} 
                        for it in issue_types 
                        if it.get("name", "").lower() != "epic"
                    ]
                    return {
                        "success": True,
                        "data": filtered_issue_types
                    }
                else:
                    logger.error(f"No project found for key: {project_key}, URL: {url}")
                    return {
                        "success": False,
                        "error": f"No project found for key: {project_key}"
                    }
            else:
                error_text = resp.text if resp.text else "No error details"
                logger.error(f"Jira issue types API error: HTTP {resp.status_code}, Response: {error_text}, URL: {url}")
                return {
                    "success": False,
                    "error": f"Failed to get issue types: HTTP {resp.status_code}"
                }
        except Exception as e:
            logger.error(f"Exception in get_issue_types: {e}, URL: {url}")
            return {
                "success": False,
                "error": f"Failed to get issue types: {str(e)}"
            }

    def get_priorities(self):
        """
        Get Jira priorities and return list of {value: id, label: name}.
        """
        url = f"{self.base_url}/priority"
        try:
            response = self.session.get(url, timeout=self.timeout)
            if response.status_code == 200:
                priorities = response.json()
                return {
                    "success": True,
                    "data": [{"value": p["id"], "label": p["name"]} for p in priorities]
                }
            else:
                return {
                    "success": False,
                    "error": f"Failed to fetch priorities: HTTP {response.status_code} - {response.text}"
                }
        except Exception as e:
            return {
                "success": False,
                "error": f"Exception occurred: {str(e)}"
            }

    def delete_ticket(self, issue_id_or_key: str) -> dict:
        """
        Delete a Jira issue by issue_id_or_key.
        Returns {'success': True} if deleted, or {'success': False, 'error': ...} if failed.
        """
        url = f"{self.base_url}/issue/{issue_id_or_key}"
        try:
            resp = self.session.delete(url, timeout=self.timeout)
            if resp.status_code == 204:
                return {"success": True}
            else:
                error_text = resp.text if resp.text else "No error details"
                return {"success": False, "error": f"Failed to delete ticket: HTTP {resp.status_code} - {error_text}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    @staticmethod
    def validate_config(config: Dict[str, Any]) -> bool:
        """Validate Jira config dict for required fields."""
        return bool(config.get('domain'))

    @staticmethod
    def test_connection(config: Dict[str, Any]) -> bool:
        """Test Jira connection with given config."""
        try:
            notifier = JiraNotifier(config)
            url = f"https://{config['domain']}/rest/api/3/myself"
            resp = notifier.session.get(url, timeout=notifier.timeout)
            logger.info(f"Test jira connection: {resp}")
            return resp.status_code == 200
        except Exception as e:
            logger.error(f"Failed to test jira connection: {e}")
            return False 
