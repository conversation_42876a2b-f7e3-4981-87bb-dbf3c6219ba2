import requests
from typing import Dict, Any, Optional, Union
from util.common_log import get_logger
from notifications.notifier.http_base import HttpNotifierBase
from notifications.notifier.base import BaseNotifier
from notifications.enums import ErrorMessages
from notifications.service import decrypt_config

logger = get_logger("notification")

class TeamsNotifier(BaseNotifier, HttpNotifierBase):
    def __init__(self, config: Dict[str, Any]):
        timeout = config.get('timeout', 10)
        HttpNotifierBase.__init__(self, timeout=timeout)
        BaseNotifier.__init__(self, config)
        self._validate_config()

    def _validate_config(self):
        webhook_url = self.config.get('webhook_url')
        encrypted = self.config.get('encrypted', 'false').lower() == 'true'
        if not webhook_url:
            raise ValueError(ErrorMessages.CONFIG_MISSING_WEBHOOK_URL.format(notifier="TeamsNotifier"))
        if encrypted:
            webhook_url = decrypt_config(webhook_url)
        self.webhook_url = webhook_url

    @staticmethod
    def validate_config(config):
        import re
        webhook_url = config.get('webhook_url') or config.get('url')
        if not webhook_url:
            return False
        if (
            webhook_url.startswith('https://outlook.office.com/webhook/') and '/IncomingWebhook/' in webhook_url
        ) or (
            re.match(r'^https://[a-zA-Z0-9\-]+\.webhook\.office\.com/webhookb2/', webhook_url) and '/IncomingWebhook/' in webhook_url
        ):
            return True
        return False

    @staticmethod
    def test_connection(config):
        try:
            notifier = TeamsNotifier(config)
            result = notifier.send('[notify] Connector test message')
            return result.get('success', False)
        except Exception:
            return False

    def _send_impl(self, message: str, subject: Optional[str] = None,
                   recipients: Optional[Union[str, list]] = None,
                   context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Send Teams notification with comprehensive error handling
        
        Args:
            message: Teams message content
            subject: Message title
            recipients: Not used for Teams (webhook-based)
            context: Additional context (theme_color, sections, etc.)
            
        Returns:
            Dict with detailed result information
        """
        try:
            # Use provided parameters
            payload = {
                "@type": "MessageCard",
                "@context": "http://schema.org/extensions",
                "text": message
            }
            
            # Add title if subject is provided
            if subject:
                payload["title"] = subject
            
            # Add Teams-specific fields from context
            if context:
                sections = context.get('sections', [])
                
                if sections and isinstance(sections, list):
                    payload["sections"] = sections
            
            response = self.post(self.webhook_url, json=payload)
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "message": ErrorMessages.MESSAGE_SENT_SUCCESS,
                    "title": subject
                }
            else:
                return {
                    "success": False,
                    "error": ErrorMessages.API_ERROR.format(status_code=response.status_code)
                }
                
        except requests.exceptions.Timeout:
            logger.error("Teams request timeout")
            return {
                "success": False,
                "error": ErrorMessages.TIMEOUT
            }
        except requests.exceptions.ConnectionError:
            logger.error("Teams connection error")
            return {
                "success": False,
                "error": ErrorMessages.CONNECTION_ERROR
            }
        except requests.exceptions.RequestException as e:
            logger.error(f"Teams request error: {e}")
            return {
                "success": False,
                "error": ErrorMessages.REQUEST_ERROR.format(error=str(e))
            }
        except Exception as e:
            logger.error(f"Teams sending failed: {e}")
            return {
                "success": False,
                "error": ErrorMessages.SENDING_FAILED.format(error=str(e))
            }

if __name__ == "__main__":
    # webhook_url = "https://company.webhook.office.com/webhookb2/TEST/IncomingWebhook/TOKEN"
    webhook_url = ""
    config = {"webhook_url": webhook_url}
    notifier = TeamsNotifier(config)
    result = notifier.send("Hello from notify TeamsNotifier!",
                          subject="Test Subject")
    print("Teams notification result:", result) 