import requests
import base64
from requests.auth import H<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Dict, Optional, Union, Any
from notifications.notifier.base import BaseNotifier
from notifications.enums import ErrorMessages
from notifications.service import decrypt_config
from util.common_log import get_logger

logger = get_logger("notification")

class ServiceNowNotifier(BaseNotifier):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self._validate_config()
        self._init_session()

    def _validate_config(self):
        instance = self.config.get('instance')
        user = self.config.get('user')
        password = self.config.get('password')
        encrypted = self.config.get('encrypted', 'false').lower() == 'true'
        # Normalize instance: remove http(s):// and trailing slashes
        if instance:
            import re
            instance = re.sub(r'^https?://', '', instance)
            instance = instance.rstrip('/')
        if password and encrypted:
            password = decrypt_config(password)
        if not (instance and user and password):
            raise ValueError("ServiceNowNotifier requires instance, user, password in config")
        self.instance = instance
        self.user = user
        self.password = password
        self.base_url = f"https://{instance}"
        self.api_base_url = f"{self.base_url}/api/now/v1/table"
        self.timeout = self.config.get('timeout', 30)

    def _init_session(self):
        # Configure session with auth and common headers
        self.session = requests.Session()
        self.session.auth = HTTPBasicAuth(self.user, self.password)
        self.session.headers.update({
            "Accept": "application/json",
            "Content-Type": "application/json"
        })

    def _fetch_all_records(self, table, fields, query_params=None, page_limit=100):
        """
        General method to fetch all records from a ServiceNow table with paging.
        Returns (all_records, error_text or None)
        """
        url = f"{self.api_base_url}/{table}"
        all_records = []
        offset = 0
        while True:
            params = {
                "sysparm_fields": fields,
                "sysparm_limit": page_limit,
                "sysparm_offset": offset
            }
            if query_params:
                params.update(query_params)
            try:
                resp = self.session.get(url, params=params, timeout=self.timeout)
                if resp.status_code != 200:
                    return None, resp.text
                batch = resp.json().get("result", [])
                all_records.extend(batch)
                if len(batch) < page_limit:
                    break
                offset += page_limit
            except Exception as e:
                return None, str(e)
        return all_records, None

    def get_project_list(self, limit=50):
        """
        Get ServiceNow table list (as project/business line), return key/name/id fields.
        If limit=0, fetch all by paging.
        """
        if limit == 0:
            projects, error = self._fetch_all_records("sys_db_object", "name,label,sys_id")
            if error:
                return {"success": False, "error": error}
            return {
                "success": True,
                "data": [{"key": t["name"], "name": t["label"], "id": t.get("sys_id", t["name"])} for t in projects]
            }
        else:
            url = f"{self.api_base_url}/sys_db_object"
            params = {"sysparm_fields": "name,label,sys_id", "sysparm_limit": limit}
            try:
                resp = self.session.get(url, params=params, timeout=self.timeout)
                if resp.status_code == 200:
                    tables = resp.json().get('result', [])
                    return {
                        "success": True,
                        "data": [{"key": t["name"], "name": t["label"], "id": t.get("sys_id", t["name"])} for t in tables]
                    }
                else:
                    return {"success": False, "error": f"Failed to get table list: HTTP {resp.status_code}"}
            except Exception as e:
                return {"success": False, "error": f"Failed to get table list: {str(e)}"}

    def get_user_list(self, query="", project_key=None, max=0, page=0, filter_system_users=True):
        """Get ServiceNow user list, return accountId/displayName fields. If max=0, fetch all users by paging."""
        if max == 0:
            query_params = {}
            if query:
                query_params["sysparm_query"] = f"nameLIKE{query}"
            users, error = self._fetch_all_records("sys_user", "sys_id,name,email,user_name", query_params)
            if error:
                return {"success": False, "error": error}
            return {"success": True, "users": self.filter_system_users(users) if filter_system_users else users}
        else:
            url = f"{self.api_base_url}/sys_user"
            params = {
                "sysparm_fields": "sys_id,name,email,user_name",
                "sysparm_limit": max,
                "sysparm_offset": max * page
            }
            if query:
                params["sysparm_query"] = f"nameLIKE{query}"
            resp = self.session.get(url, params=params, timeout=self.timeout)
            users = resp.json().get('result', [])
            return {"success": True, "users": self.filter_system_users(users) if filter_system_users else users}

    def filter_system_users(self, users):
        system_email_domains = [
            'example.com', 'demo.com', 'test.com', 'sample.com', 'training.com'
        ]
        def is_system_user(u):
            email = u.get("email", "").lower()
            user_name = u.get("user_name", "").lower() if "user_name" in u else ""
            if not email and user_name != self.user:
                return True
            return any(email.endswith("@" + domain) for domain in system_email_domains)
        return [u for u in users if not is_system_user(u)]

    def get_ticket_status(self, ticket_id: str):
        """Get ServiceNow ticket status, return structure consistent with Jira."""
        url = f"{self.api_base_url}/incident/{ticket_id}"
        try:
            resp = self.session.get(url, timeout=self.timeout)
            if resp.status_code == 200:
                result = resp.json().get('result', {})
                return {
                    "success": True,
                    "status": result.get("state"),
                    "raw": result
                }
            else:
                return {
                    "success": False,
                    "error": f"Failed to get ticket status: HTTP {resp.status_code}"
                }
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to get ticket status: {str(e)}"
            }

    def _send_impl(self, message: str, subject: Optional[str] = None,
                   recipients: Optional[Union[str, list]] = None,
                   context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create ServiceNow incident with comprehensive error handling
        
        Args:
            message: Incident description
            subject: Incident short description
            recipients: Not used for ServiceNow
            context: Additional context (category, impact, urgency, priority, etc.)
            
        Returns:
            Dict with detailed result information, fields mapped to local Ticket ORM
        """
        try:
            # Use provided parameters
            short_description = subject or "[FDT Ticket] Notification"
            description = message
            category = context.get('category', 'software') if context else 'software'
            impact = context.get('impact', '2') if context else '2'
            urgency = context.get('urgency', '2') if context else '2'
            priority = context.get('priority', '3') if context else '3'
            channel = context.get('channel', '') if context else None
            service = context.get('service', '') if context else None
            subcategory = context.get('subcategory', '') if context else ''
            caller_id = context.get('caller_id') if context else None
            assignee_id = context.get('assignee_id') if context else None

            payload = {
                "short_description": short_description,
                "description": description,
                "category": category,
                "impact": impact,
                "urgency": urgency,
                "priority": priority
            }
            if channel:
                payload["contact_type"] = channel
            if service:
                payload["business_service"] = service
            if subcategory:
                payload["subcategory"] = subcategory
            if caller_id:
                payload["caller_id"] = caller_id
            if assignee_id:
                payload["assigned_to"] = assignee_id
            # Add custom fields if provided in context
            custom_fields = context.get('custom_fields', {}) if context else {}
            for field_name, field_value in custom_fields.items():
                payload[field_name] = field_value
            
            url = f"{self.api_base_url}/incident"
            response = self.session.post(url, json=payload, timeout=self.timeout)
            if response.status_code in (200, 201):
                data = response.json().get("result", {})
                ticket_key = data.get("number")
                sys_id = data.get("sys_id")
                status = data.get("state")
                caller_id = data.get("caller_id")
                ticket_link = self.get_issue_url(sys_id)
                result = {
                    "success": True,
                    "message": "ServiceNow incident created successfully",
                    "ticket_key": ticket_key,
                    "ticket_id": sys_id,
                    "short_description": data.get("short_description"),
                    "category": data.get("category"),
                    "status": status,
                    "ticket_link": ticket_link,
                    "raw_response": data
                }
                # Add optional recipient/creator if needed
                assignee_id = context.get('assignee_id') if context else None
                if assignee_id:
                    email, name = self._get_user_details(assignee_id)
                    result['recipient'] = {
                        'recipient_email': email,
                        'recipient_name': name,
                    }
                caller_id_ctx = context.get('caller_id') if context else None
                if caller_id_ctx:
                    email, name = self._get_user_details(caller_id_ctx)
                    result['creator'] = {
                        'ticket_creator_email': email,
                        'ticket_creator': name
                    }
                return result
            else:
                error_text = response.text if response.text else "No error details"
                logger.error(f"ServiceNow incident creation API error: HTTP {response.status_code}, Response: {error_text}, URL: {url}")
                return {
                    "success": False,
                    "error": f"ServiceNow API returned HTTP {response.status_code} - {error_text}"
                }
                
        except requests.exceptions.Timeout:
            logger.error(f"ServiceNow request timeout: URL={self.base_url}, timeout={self.timeout}")
            return {
                "success": False,
                "error": ErrorMessages.TIMEOUT
            }
        except requests.exceptions.ConnectionError:
            logger.error(f"ServiceNow connection error: URL={self.base_url}, instance={self.config.get('instance')}")
            return {
                "success": False,
                "error":  ErrorMessages.CONNECTION_ERROR
            }
        except requests.exceptions.RequestException as e:
            logger.error(f"ServiceNow request error: {e}, URL={self.base_url}")
            return {
                "success": False,
                "error": ErrorMessages.REQUEST_ERROR.format(error=str(e))
            }
        except Exception as e:
            logger.error(f"ServiceNow incident creation failed: {e}, URL={self.base_url}")
            return {
                "success": False,
                "error": ErrorMessages.SENDING_FAILED.format(error=str(e))
            }

    def get_issue_status(self, sys_id):
        """Get incident status with error handling"""
        try:
            url = f"{self.base_url}/{sys_id}"
            response = self.session.get(url, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json().get('result', {})
                status = data.get('state')
                return {
                    "success": True,
                    "status": status,
                    "sys_id": sys_id,
                    "raw": data
                }
            else:
                error_text = response.text if response.text else "No error details"
                logger.error(f"ServiceNow incident status API error: HTTP {response.status_code}, Response: {error_text}, URL: {url}")
                return {
                    "success": False,
                    "error": f"Failed to get incident status: HTTP {response.status_code}"
                }
        except Exception as e:
            logger.error(f"Exception in get_issue_status: {e}, URL: {url}")
            return {
                "success": False,
                "error": f"Failed to get incident status: {str(e)}"
            }

    def get_issues_status(self, sys_ids):
        """Get status for multiple incidents"""
        try:
            query = f"sys_idIN{','.join(sys_ids)}"
            params = {"sysparm_query": query, "sysparm_display_value": "all"}
            response = self.session.get(self.base_url, params=params, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json().get('result', [])
                result = {}
                for item in data:
                    result[item['sys_id']] = item.get('state')
                return {
                    "success": True,
                    "statuses": result
                }
            else:
                return {
                    "success": False,
                    "error": f"Failed to get incidents status: HTTP {response.status_code}"
                }
        except Exception as e:
            logger.error(f"Failed to get incidents status: {e}")
            return {
                "success": False,
                "error": f"Failed to get incidents status: {str(e)}"
            }

    def get_issue_url(self, sys_id: str) -> str:
        """
        Public method to generate a direct URL to view a ServiceNow incident.
        Args:
            sys_id (str): The sys_id of the incident.
        Returns:
            str: Full URL to the ServiceNow incident detail page.
        """
        return f"https://{self.config['instance']}/nav_to.do?uri=incident.do%3Fsys_id={sys_id}"

    def get_ticket(self, sys_id):
        """
        Get full incident details by sys_id.
        """
        url = f"{self.api_base_url}/incident/{sys_id}"
        try:
            resp = self.session.get(url, timeout=self.timeout)
            if resp.status_code == 200:
                return {"success": True, "ticket": resp.json().get("result")}
            else:
                return {"success": False, "error": resp.text}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def query_tickets(self, query, limit=50):
        """
        Query incidents with sysparm_query.
        """
        url = f"{self.api_base_url}/incident"
        params = {"sysparm_query": query, "sysparm_limit": limit}
        try:
            resp = self.session.get(url, params=params, timeout=self.timeout)
            if resp.status_code == 200:
                return {"success": True, "tickets": resp.json().get("result", [])}
            else:
                return {"success": False, "error": resp.text}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def add_comment(self, sys_id, comment):
        """
        Add a comment (work note) to an incident.
        """
        url = f"{self.api_base_url}/incident/{sys_id}"
        data = {"work_notes": comment}
        try:
            resp = self.session.patch(url, json=data, timeout=self.timeout)
            if resp.status_code in (200, 204):
                return {"success": True}
            else:
                return {"success": False, "error": resp.text}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def upload_attachment(self, sys_id, file_name, file_data):
        """
        Upload an attachment to an incident.
        """
        url = f"{self.base_url}/api/now/attachment/file"
        params = {"table_name": "incident", "table_sys_id": sys_id, "file_name": file_name}
        files = {"file": (file_name, file_data)}
        try:
            resp = self.session.post(url, params=params, files=files, timeout=self.timeout)
            if resp.status_code == 201:
                return {"success": True, "attachment": resp.json().get("result")}
            else:
                return {"success": False, "error": resp.text}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_metadata(self, name='incident', element="category", fields="value,label"):
        """
        Get incident metadata (category, impact, urgency, priority, etc.)
        """
        url = f"{self.api_base_url}/sys_choice"
        params = {
            "name": name,
            "element": element,
            "sysparm_fields": fields
        }
        try:
            resp = self.session.get(url, params=params, timeout=self.timeout)
            if resp.status_code == 200:
                choices = resp.json().get("result", [])
            else:
                choices = []
            # If no choices and element is urgency, impact, or priority, return default values
            if not choices and element in ("urgency", "impact", "priority"):
                if element in ("urgency", "impact"):
                    choices = [
                        {"value": "1", "label": "1 - High"},
                        {"value": "2", "label": "2 - Medium"},
                        {"value": "3", "label": "3 - Low"}
                    ]
                elif element == "priority":
                    choices = [
                        {"value": "1", "label": "1 - Critical"},
                        {"value": "2", "label": "2 - High"},
                        {"value": "3", "label": "3 - Moderate"},
                        {"value": "4", "label": "4 - Low"},
                        {"value": "5", "label": "5 - Planning"}
                    ]
                logger.info(f"{element} get from default value!")
            if choices:
                return {"success": True, "data": choices}
            else:
                return {"success": False, "error": resp.text}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def delete_ticket(self, sys_id: str) -> dict:
        """
        Delete an incident (ticket) by sys_id.
        Returns {'success': True} if deleted, or {'success': False, 'error': ...} if failed.
        """
        url = f"{self.api_base_url}/incident/{sys_id}"
        try:
            resp = self.session.delete(url, timeout=self.timeout)
            if resp.status_code == 204:
                return {"success": True}
            else:
                error_text = resp.text if resp.text else "No error details"
                return {"success": False, "error": f"Failed to delete ticket: HTTP {resp.status_code} - {error_text}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_categories_with_subcategories(self):
        """
        Get all categories and their subcategories for incident, structured for cascading selection.
        Returns:
            {
                "success": True,
                "data": [
                    {
                        "category": "software",
                        "label": "Software",
                        "subcategories": [
                            {"value": "application", "label": "Application"},
                            ...
                        ]
                    },
                    ...
                ]
            }
        """
        # Get all categories
        cat_resp = self.get_metadata(element="category", fields="")
        if not cat_resp.get("success"):
            return {"success": False, "error": cat_resp.get("error")}
        categories = cat_resp.get("data", [])

        # Get all subcategories
        subcat_resp = self.get_metadata(element="subcategory", fields="")
        if not subcat_resp.get("success"):
            return {"success": False, "error": subcat_resp.get("error")}
        subcategories = subcat_resp.get("data", [])

        # Organize subcategories under their parent category
        cat_map = {}
        for cat in categories:
            cat_map[cat["value"]] = {
                "category": cat["value"],
                "label": cat.get("label", cat["value"]),
                "subcategories": []
            }
        for sub in subcategories:
            parent = sub.get("dependent_value")
            if parent in cat_map:
                cat_map[parent]["subcategories"].append({
                    "value": sub["value"],
                    "label": sub.get("label", sub["value"])
                })
        return {"success": True, "data": list(cat_map.values())}

    def _get_user_details(self, sys_id):
        url = f"{self.api_base_url}/sys_user"
        params = {
            "sysparm_query": f"sys_id={sys_id}",
            "sysparm_fields": "sys_id,name,email,user_name"
            }
        resp = self.session.get(url, params=params, timeout=self.timeout)
        if resp.status_code == 200:
            data = resp.json().get("result", {})
            if data and data[0]:
                return data[0].get("email"), data[0].get("name")
        return None, None

    def get_service_list(self, limit=0, page=0):
        """
        Get business service list from cmdb_ci_service table.
        If limit=0, fetch all services by paging.
        Returns a list of dicts with sys_id and name.
        """
        if limit == 0:
            services, error = self._fetch_all_records("cmdb_ci_service", "sys_id,name")
            if error:
                return {"success": False, "error": error}
            return {"success": True, "services": services}
        else:
            url = f"{self.api_base_url}/cmdb_ci_service"
            params = {
                "sysparm_fields": "sys_id,name",
                "sysparm_limit": limit,
                "sysparm_offset": limit * page
            }
            try:
                resp = self.session.get(url, params=params, timeout=self.timeout)
                if resp.status_code == 200:
                    services = resp.json().get("result", [])
                    return {"success": True, "services": services}
                else:
                    return {"success": False, "error": resp.text}
            except Exception as e:
                return {"success": False, "error": str(e)}

    def get_priorities(self):
        return self.get_metadata(name='task', element="priority")

    @staticmethod
    def validate_config(config):
        """Validate ServiceNow config dict for required fields."""
        instance = config.get('instance')
        user = config.get('user')
        password = config.get('password')
        return bool(instance and user and password)

    @staticmethod
    def test_connection(config):
        """Test ServiceNow connection with given config."""
        try:
            notifier = ServiceNowNotifier(config)
            url = notifier.base_url
            resp = notifier.session.get(url, params={'sysparm_limit': 1})
            logger.info(f"Test servicenow connection: {resp}")
            return resp.status_code == 200
        except Exception as e:
            logger.error(f"Failed to test servicenow connection: {e}")
            return False 
