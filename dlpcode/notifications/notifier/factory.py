from notifications.enums import NotifierTypeEnum, ErrorMessages
from collections import OrderedDict
import time
from typing import Dict, Any
from exts import Session
from notifications.model.notification import NotificationConnector, Template
from notifications.templates.template_engine import TemplateEngine
import importlib

# Supported platforms configuration
SUPPORTED_PLATFORMS = {
    'email': {
        'class': 'SmtpEmailNotifier',
        'description': 'SMTP email notifications',
        'config_fields': ['smtp_host', 'smtp_port', 'username', 'password']
    },
    'slack': {
        'class': 'SlackNotifier', 
        'description': 'Slack webhook notifications',
        'config_fields': ['webhook_url']
    },
    'teams': {
        'class': 'TeamsNotifier',
        'description': 'Microsoft Teams webhook notifications', 
        'config_fields': ['webhook_url']
    },
    'webhook': {
        'class': 'WebhookNotifier',
        'description': 'Generic webhook notifications',
        'config_fields': ['webhook_url']
    },
    'jira': {
        'class': 'JiraNotifier',
        'description': 'Jira notifications',
        'config_fields': ['domain', 'user_email', 'api_token']
    },
    'servicenow': {
        'class': 'ServiceNowNotifier',
        'description': 'ServiceNow notifications',
        'config_fields': ['instance', 'user', 'password']
    }
}

class NotifierFactory:
    def __init__(self, max_cache_size=100, ttl=300):
        self._cache = OrderedDict()
        self._max_cache_size = max_cache_size
        self._ttl = ttl  # Cache time-to-live (seconds)
        self._timestamps = {}

    def get_notifier(self, connector_id, notifier_type, config):
        # Validate notifier_type
        if not NotifierTypeEnum.is_valid(notifier_type):
            raise ValueError(ErrorMessages.CONNECTOR_UNSUPPORTED_TYPE.format(
                notifier_type=notifier_type, 
                supported_types=NotifierTypeEnum.get_all_values()
            ))
        key = (connector_id, notifier_type)
        now = time.time()
        if key in self._cache:
            if now - self._timestamps[key] < self._ttl:
                self._cache.move_to_end(key)
                return self._cache[key]
            else:
                del self._cache[key]
                del self._timestamps[key]
        
        # Get notifier class dynamically
        notifier_class = self.get_notifier_class(notifier_type)
        notifier = notifier_class(config=config)
        
        self._cache[key] = notifier
        self._timestamps[key] = now
        if len(self._cache) > self._max_cache_size:
            old_key, _ = self._cache.popitem(last=False)
            self._timestamps.pop(old_key, None)
        return notifier

    def get_notifier_class(self, notifier_type):
        """Get notifier class dynamically from SUPPORTED_PLATFORMS"""
        if notifier_type not in SUPPORTED_PLATFORMS:
            raise ValueError(ErrorMessages.CONNECTOR_UNKNOWN_TYPE.format(notifier_type=notifier_type))
        
        class_name = SUPPORTED_PLATFORMS[notifier_type]['class']
        
        # Dynamic import using importlib
        module_name = f"notifications.notifier.{notifier_type}"
        try:
            module = importlib.import_module(module_name)
            return getattr(module, class_name)
        except (ImportError, AttributeError):
            raise ValueError(ErrorMessages.CONNECTOR_UNKNOWN_TYPE.format(notifier_type=notifier_type))

    def clear_notifier(self, connector_id, notifier_type):
        key = (connector_id, notifier_type)
        if key in self._cache:
            del self._cache[key]
            self._timestamps.pop(key, None)

    def get_notifier_by_id(self, connector_id=None, template_id=None):
        """
        Retrieve a notifier instance by connector_id or template_id.
        Logic:
        - If connector_id is provided, fetch the connector directly.
        - If only template_id is provided, fetch the template, then use its connector_id to fetch the connector.
        - Returns a tuple: (error_message, notifier_instance). If successful, error_message is an empty string.
        """
        session = Session()
        try:
            # 1. Find connector
            if not template_id and not connector_id:
                return 'template_id or connector_id is required', None
            if connector_id:
                connector = session.query(NotificationConnector).filter_by(id=connector_id).first()
                if not connector:
                    return ErrorMessages.CONNECTOR_NOT_FOUND, None
            elif template_id: # 2. Find template
                template_dict = session.query(Template).filter_by(id=template_id).first()
                if not template_dict:
                    return ErrorMessages.TEMPLATE_NOT_FOUND, None
                connector = session.query(NotificationConnector).filter_by(id=template_dict.connector_id).first()
                if not connector:
                    return ErrorMessages.CONNECTOR_NOT_FOUND, None
            
            use_connector_id = connector_id or template_dict.connector_id
            if not use_connector_id:
                return ErrorMessages.CONNECTOR_ID_REQUIRED, None
            
            notifier_type = connector.notifier_type
            config = connector.config
            notifier = self.get_notifier(use_connector_id, notifier_type, config)
            return '', notifier
        finally:
            session.close()

    def send_notification(self, connector_id=None, template_id=None, context=None):
        """
        Unified notification sending interface:
        1. Find template by template_id
        2. Find connector (use connector_id or template.connector_id)
        3. Render template and prepare message
        4. Call the corresponding Notifier's send method
        """
        session = Session()
        try:
            # 1. Find template
            if not template_id:
                return {'success': False, 'message': ErrorMessages.TEMPLATE_ID_REQUIRED, 'data': None}
            template_dict = session.query(Template).filter_by(id=template_id).first()
            if not template_dict:
                return {'success': False, 'message': ErrorMessages.TEMPLATE_NOT_FOUND, 'data': None}

            # 2. Find connector
            use_connector_id = connector_id or template_dict.connector_id
            if not use_connector_id:
                return {'success': False, 'message': ErrorMessages.CONNECTOR_ID_REQUIRED, 'data': None}
            connector = session.query(NotificationConnector).filter_by(id=use_connector_id).first()
            if not connector:
                return {'success': False, 'message': ErrorMessages.CONNECTOR_NOT_FOUND, 'data': None}
            notifier_type = connector.notifier_type
            config = connector.config
            notifier = self.get_notifier(use_connector_id, notifier_type, config)

            # 3. Render template and prepare message
            recipients = None
            send_context = {}
            send_context.update(template_dict.extra)
            result = TemplateEngine.process_template_for_notifier(template_dict.template, context or {}, notifier_type)
            subject = (context or {}).get('subject') if (context and (context.get('subject') is not None)) else result.get('subject')
            message = (context or {}).get('body') if (context and (context.get('body') is not None)) else result.get('content', '')
            if notifier_type == NotifierTypeEnum.EMAIL.value:
                recipients = template_dict.extra.get('receivers')

            # 4. Call Notifier's send method
            result = notifier.send(message, subject, recipients, send_context)
            return result
        finally:
            session.close()

# Create singleton instance
notifier_factory = NotifierFactory() 