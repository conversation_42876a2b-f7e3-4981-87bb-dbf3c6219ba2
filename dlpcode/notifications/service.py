from notifications.enums import SENSITIVE_FIELDS_MAP
from util.random_password.service import RandomPasswordService
from notifications.notifier.factory import notifier_factory

def encrypt_config(notifier_type: str, config: dict) -> dict:
    if config.get("encrypted", "false").lower() == "true":
        return config
    fields_to_encrypt = SENSITIVE_FIELDS_MAP.get(notifier_type.lower(), [])

    for key in fields_to_encrypt:
        if key in config and config[key]:
            config[key] = RandomPasswordService.encrypt_notifier_info(config[key])

    config['encrypted'] = 'true'
    return config

def decrypt_config(value: str) -> dict:
    return RandomPasswordService.decrypt_notifier_info(value)

def validate_connector(notifier_type, config, test_connection=False):
    notifier_cls = notifier_factory.get_notifier_class(notifier_type)
    if hasattr(notifier_cls, 'validate_config') and not notifier_cls.validate_config(config):
        return False, 'Invalid configuration'

    if test_connection and hasattr(notifier_cls, 'test_connection'):
        result = notifier_cls.test_connection(config)
        if result is True:
            return True, None
        else:
            return False, result if isinstance(result, str) else 'Connection test failed'
    return True, None