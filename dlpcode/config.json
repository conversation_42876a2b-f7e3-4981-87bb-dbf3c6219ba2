{"log": {"print_stacktrace": true}, "task_queue": {"wait_for_result_timeout_in_ms": 10000}, "schedule_job": {"check_schedule": {"interval": 10}, "check_status": {"max_thread": 32, "interval": 5, "timeout": 900}, "clean_up_files": {"interval": 45}, "clean_up_redis": {"interval": 600}, "clean_up_postgresql": {"interval": 600}, "check_huey_task_timeout": {"interval": 10}, "check_license": {"interval": 60}, "check_report_schedule": {"interval": 30}, "clean_up_reports": {"interval": 3600}, "report_running_timeout": {"interval": 2700}, "ad_interval": 2}, "reports": {"report_retention_days": 30}, "redis": {"host": "localhost", "port": 6379, "socket_timeout": 10}, "aerospike": {"hosts": ["localhost", 3000], "namespace": "db1", "ad_namespace": "db2"}, "file_unified_queue": {"enabled": true, "max_files_per_cycle": 10, "ddr_weight": 7, "scan_policy_weight": 3, "max_concurrent_ddr": 100, "max_concurrent_scan_policy": 30}, "download_queue": {"min_process_count": 4, "max_process_count": 16, "min_ram_gb": 16, "max_ram_gb": 64, "task_lock_timeout": 300, "conn_error_threshold": 50, "batch_size": 500, "backoff_time": 1, "file_info_queue_path": "/var/log/queue", "file_download_path": "/var/log/file_download", "ddr_file_download_path": "/var/log/ddr_file_download"}, "file_catalog_path": "/var/log/file_catalog", "postgresql": {"db_host": "localhost", "db_port": 5432, "db_name": "dlp_db", "db_user": "postgresql", "db_pass": "will_update_by_get_global_config()", "min_conn": 2, "max_conn": 8}, "log_dir": "/var/log/dlp", "log_size_limit": 41943040, "log_backup_count": 10, "retry": {"max_retry": 3, "min_wait": 0.2, "max_wait": 3, "multiplier": 1}, "celery": {"broker_url": "redis://localhost:6379/0", "result_backend": "redis://localhost:6379/0", "task_routes": {"celery_worker.analyze_worker.analyze_worker": {"queue": "analyze"}, "celery_worker.analyze_worker.priority_analyze_worker": {"queue": "ddr_analyze"}, "celery_worker.ddr_task.*": {"queue": "ddr"}, "celery_worker.dispatch_download_task.*": {"queue": "download"}, "celery_worker.doing_download_task.*": {"queue": "download"}, "celery_worker.fetch_storage.*": {"queue": "default"}, "celery_worker.report_generation_task.*": {"queue": "report"}, "celery_worker.protection_action_task.*": {"queue": "protection"}, "celery_worker.tag_worker.*": {"queue": "default"}, "celery_worker.unified_file_process.*": {"queue": "default"}}, "task_default_queue": "default", "worker_prefetch_multiplier": 1, "task_acks_late": true, "worker_disable_rate_limits": false, "task_reject_on_worker_lost": true, "analyze_task_timeout": 60, "download_task_timeout": 600, "task_soft_time_limit": 300, "task_time_limit": 600, "worker_max_tasks_per_child": 1000, "beat_schedule": {"ddr-dispatcher": {"task": "celery_worker.ddr_worker.scheduled_ddr_dispatcher", "schedule": 120.0}, "fetch-storage-task": {"task": "celery_worker.fetch_storage.sync_storage", "schedule": "crontab(minute='*/2')"}}}, "connector": {"smb": {"connect_timeout": 16, "connect_port": 139, "connect_retry_pst": {"interval": 5, "retry_time": 3}}, "aws": {"connect_timeout": 5, "read_timeout": 20, "all_files_fetch_scan_min_interval_count": 5, "all_files_fetch_scan_min_interval_time": 600, "all_files_fetch_recycle_min_interval_count": 100, "target_operations": ["GetObject", "HeadObject", "CopyObject", "PutObject", "DeleteObject", "DeleteObjects", "PutObjectAcl", "GetObjectAcl", "AccessDenied"]}, "sharepoint": {"forti_tag": "FortiTag", "retry_count": 3, "connect_timeout": 5, "read_timeout": 20, "top_count": 1000, "max_workers": 10, "max_audit_log_once_fetch_hours": 10, "max_audit_log_fetch_hours": 166, "all_files_fetch_scan_min_interval_count": 5, "all_files_fetch_scan_min_interval_time": 600, "all_files_fetch_recycle_min_interval_count": 100, "sharepoint_data_location": ["AU", "BR", "CA", "EU", "FR", "DE", "IN", "JP", "NO", "QA", "ZA", "KR", "SE", "CH", "GB", "AE", "US"], "target_operations": ["FileUploaded", "FileCreated", "FileDeleted", "FileModified", "FileRenamed", "FileCopied", "FileMoved", "FileRestored", "FileRecycled", "FileAccessed", "FileDownloaded", "FileShared", "FileUnshared", "SharingSet", "SharingRevoked", "ShareLinkCreated", "ShareLinkModified", "ShareLinkDeleted", "AnonymousLinkCreated", "AnonymousLinkRemoved", "SecureLinkCreated", "SecureLinkDeleted", "CompanyLinkCreated", "CompanyLinkRemoved", "AddedToSecureLink", "RemovedFromSecureLink", "SecureLinkUsed", "SharingLinkUsed", "CompanyLinkUsed", "AnonymousLinkUsed", "PermissionChanged", "AccessDenied"]}, "google": {"max_audit_log_once_fetch_hours": 72, "max_audit_log_fetch_hours": 2160, "all_files_fetch_scan_min_interval_count": 5, "all_files_fetch_scan_min_interval_time": 600, "all_files_fetch_recycle_min_interval_count": 100, "target_operations": ["create", "upload", "untrash", "edit", "copy", "move", "download", "view", "rename", "update", "delete", "share", "unshare"]}}, "file_extract": {"comment_max_length": "10M", "max_length": 10485760, "image_ext_timeout": 10, "excel": {"max_hdr_search_rows": 32, "max_ext_rows": 1000, "max_ext_sheets": 16, "max_ext_timeout": 30}, "comment_doc": "for docx only", "doc": {"comment_max_ext_paragraphs": "average 30 paragraphs per page. 30 paragraphs * 100 pages", "max_ext_paragraphs": 3000}, "ppt": {"max_ext_timeout": 30, "max_ext_slides": 20, "image_ext_slides": 10, "image_per_ext_timeout": 3}, "odt": {"comment_max_ext_paragraphs": "average 30 paragraphs per page. 30 paragraphs * 100 pages", "max_ext_paragraphs": 3000}, "ods": {"max_ext_rows": 1000, "max_ext_sheets": 16}, "odp": {"max_ext_slides": 100}, "csv": {"max_ext_rows": 1000, "max_hdr_search_rows": 32}, "pdf": {"max_ext_timeout": 30, "max_hdr_search_rows": 32, "max_ext_pages": 20, "ocr_ext_pages": 10, "ocr_ext_timeout": 10, "image_per_ext_timeout": 3}}, "file_analyzer": {"time_statistic": true, "log_level": "INFO", "mod_verify": true, "config_path": "/var/log/file_analyzer/config", "tmp_config_path": "/var/log/file_analyzer/config/tmp", "public_key_path": "/migadmin/analyzer_package/certs/package_public_key.pem", "classifier_use_gpu": false, "classifier_thread_num": 4, "classifier_token_limit": 512, "minimum_classifier_token": 128, "nlp_thread_num": 4, "nlp_process_size": 51200, "recognizer_process_size": 10485760, "max_num_return_entity": 16, "max_times_per_entity": 32, "nlp_recognizer_thread_num": 1, "phone_matcher_max_retry": 1000, "db_decryption_mode": 1, "base64_enabled": false, "classifier_probability_threshold_low": 0.85, "classifier_probability_threshold_medium": 0.9, "classifier_probability_threshold_high": 0.95, "ml_certainty_level": 2, "recognizer_score_threshold_low": 0.4, "recognizer_score_threshold_medium": 0.5, "recognizer_score_threshold_high": 0.65, "nlp_reload_threshold": 4718592000, "encrypt_sensitive_data": true, "ignore_text_category": true}, "source_file": {"original_edm_dir": "/var/log/source_file/edm", "original_idm_dir": "/var/log/source_file/idm", "expiration_time": 24, "maximum_edm_file_num": 32, "maximum_idm_file_num": 64, "maximum_file_size": 512, "maximum_edm_row": 1000000, "maximum_edm_column": 16, "minimum_idm_file_length": 128, "maximum_edm_file_size": 512, "maximum_idm_file_size": 50}, "discover_rule_confidence_level": {"HIGH": 90, "MEDIUM": 70, "LOW": 0}, "discover_rule_similarity_level": {"HIGH": 80, "MEDIUM": 70, "LOW": 60}, "recycle_backoff_time": 0.02, "max_scan_policy": 16, "max_discover_policy": 32, "max_discover_rule_per_policy": 256, "max_dlp_policy": 32, "max_dlp_rule_per_policy": 256, "no_license_limitation": {"max_scan_policy_creation": 1, "max_sensitive_data": 1000, "max_scan_incidents": 1000}, "system_certs": {"file_path": "/var/log/cert_system", "ca_file_path": "/var/log/cacert_system"}, "sys_tmp_path": {"download_path": "/var/log/dlp_ui/download", "upload_path": "/var/log/dlp_ui/upload", "cli_download_path": "/var/log/dlp_ui/cli_download"}, "system_gui_upload_dir": "/var/log/gui/uploads", "event_log_types": ["Dashboard", "Scans", "Discovery Policies", "Data Types", "Data Labels", "Users", "System", "Packages", "Scan Incidents", "EDM", "IDM", "Remediation"], "system_license": {"file_path": "/var/log/license_system"}, "backlog_dir": "/var/log/backlog", "ga_version": {"//_comment": "Here will record the build number of each GA version.", "760": "0050", "761": "0103"}, "h2": {"upload_folder": "/var/log/h2"}, "file_upload_scan": {"record_storage_time": 1}, "protection_action": {"target_file_name_max": 160, "notification_file_name_max": 160}, "fetch_storage": {"task_interval": 5}}