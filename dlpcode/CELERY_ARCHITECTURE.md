# Celery架构设计文档

## 架构概述

本系统采用分离式Celery架构设计，将任务提交接口和任务处理逻辑完全分离：

- **`tasks/`** 目录：提供写入队列的接口，负责任务提交和队列管理
- **`celery_worker/`** 目录：只处理队列数据，负责实际的业务逻辑执行

## 目录结构

```
dlpcode/
├── tasks/                          # 任务提交接口
│   ├── __init__.py
│   ├── analyze_tasks.py            # 文件分析任务接口
│   ├── ddr_tasks.py               # DDR任务接口
│   ├── download_tasks.py          # 下载任务接口
│   └── misc_tasks.py              # 其他任务接口
├── celery_worker/                  # 任务处理器
│   ├── __init__.py
│   ├── celery_app.py              # Celery应用配置
│   ├── analyze_worker.py          # 文件分析处理器
│   ├── ddr_worker.py              # DDR任务处理器
│   ├── download_worker.py         # 下载任务处理器
│   └── misc_worker.py             # 其他任务处理器
└── service/
    └── celery_queue_management_service.py  # 队列管理服务
```

## 设计原则

### 1. 职责分离
- **Tasks模块**：只负责任务提交，不包含业务逻辑
- **Workers模块**：只负责处理队列中的任务，不提供外部接口

### 2. 高优先级DDR支持
- DDR任务默认优先级：8
- 紧急DDR任务优先级：10
- 独立的DDR队列：`ddr`

### 3. 队列分类
- `analyze`: 文件分析任务
- `ddr`: DDR任务（高优先级）
- `download`: 文件下载任务
- `report`: 报告生成任务
- `protection`: 保护动作任务
- `default`: 默认队列

## 任务接口 (tasks/)

### analyze_tasks.py
提供文件分析任务的提交接口：

```python
# 提交普通分析任务
submit_analyze_task(local_file, remote_info, file_uuid, scan_info, 
                   task_uuid, session_key, backlog_hash, priority='NORMAL')

# 提交高优先级分析任务
submit_priority_analyze_task(local_file, remote_info, file_uuid, 
                            scan_info, task_uuid, session_key, backlog_hash)

# 获取分析队列状态
get_analyze_queue_status()
```

### ddr_tasks.py
提供DDR任务的提交接口：

```python
# 提交DDR事件处理任务
submit_ddr_events_task(task, start_time, end_time, ddr_policy_ids, 
                      target_operations, scan_trigger_events, priority='DDR')

# 提交紧急DDR任务
submit_emergency_ddr_task(task, start_time, end_time, ddr_policy_ids, 
                         target_operations, scan_trigger_events)

# 调度DDR分发器
schedule_ddr_dispatcher()

# 获取DDR队列状态
get_ddr_queue_status()
```

### download_tasks.py
提供下载任务的提交接口：

```python
# 提交分发下载任务
submit_dispatch_download_task(task_uuid, group_file_path, file_path, 
                             params, session_key, priority='NORMAL')

# 提交文件下载任务
submit_download_file_task(task_uuid, file_info, params, session_key, 
                         backlog_hash, priority='NORMAL')

# 提交DDR下载任务
submit_ddr_download_task(task_uuid, file_info, params, session_key, backlog_hash)

# 创建下载-分析链
create_download_analyze_chain(task_uuid, file_info, params, session_key, 
                             backlog_hash, priority='DDR')
```

### misc_tasks.py
提供其他任务的提交接口：

```python
# 提交标签更新任务
submit_tag_update_task(remote_info, tags, priority='NORMAL')

# 提交报告生成任务
submit_report_generation_task(report_id, priority='NORMAL')

# 提交保护动作任务
submit_protection_action_task(task_uuid, file_uuid, local_file_copy, 
                             file_info, scan_info, remote_info, matched_result)

# 获取所有队列状态
get_all_queue_status()
```

## 任务处理器 (celery_worker/)

### analyze_worker.py
处理文件分析任务：

```python
@app.task(bind=True, priority=5, queue='analyze')
def analyze_worker_task(self, local_file, remote_info, file_uuid, 
                       scan_info, task_uuid, session_key, backlog_hash)

@app.task(bind=True, priority=9, queue='analyze')
def priority_analyze_worker_task(self, local_file, remote_info, file_uuid, 
                                scan_info, task_uuid, session_key, backlog_hash)
```

### ddr_worker.py
处理DDR任务：

```python
@app.task(bind=True, priority=8, queue='ddr')
def process_ddr_events_task(self, task, start_time, end_time, ...)

@app.task(bind=True, priority=10, queue='ddr')
def emergency_ddr_events_task(self, task, start_time, end_time, ...)

@app.task(bind=True, priority=8, queue='ddr')
def scheduled_ddr_dispatcher_task(self)
```

### download_worker.py
处理下载任务：

```python
@app.task(bind=True, priority=5, queue='download')
def dispatch_download_task(self, task_uuid, group_file_path, file_path, ...)

@app.task(bind=True, priority=5, queue='download')
def download_file_task(self, task_uuid, file_info, params, ...)

@app.task(bind=True, priority=8, queue='download')
def ddr_download_file_task(self, task_uuid, file_info, params, ...)
```

### misc_worker.py
处理其他任务：

```python
@app.task(bind=True, priority=5, queue='default')
def update_remote_tag_task(self, remote_info, tags)

@app.task(bind=True, priority=5, queue='report')
def generate_report_task(self, report_id)

@app.task(bind=True, priority=7, queue='protection')
def protection_action_task(self, task_uuid, file_uuid, ...)
```

## 优先级系统

```python
PRIORITY_LEVELS = {
    'LOW': 1,
    'NORMAL': 5,
    'HIGH': 7,
    'DDR': 8,        # DDR默认优先级
    'CRITICAL': 9,
    'EMERGENCY': 10  # DDR紧急优先级
}
```

## 使用示例

### 提交DDR任务
```python
from tasks.ddr_tasks import submit_ddr_events_task
from datetime import datetime, timezone

task_id = submit_ddr_events_task(
    task={'id': 'ddr-001', 'name': 'DDR Task'},
    start_time=datetime.now(timezone.utc),
    end_time=datetime.now(timezone.utc),
    priority='DDR'
)
```

### 提交分析任务
```python
from tasks.analyze_tasks import submit_analyze_task

task_id = submit_analyze_task(
    local_file='/path/to/file',
    remote_info={'file': 'document.pdf', 'type': 'pdf'},
    file_uuid='uuid-123',
    scan_info={'scan_uuid': 'scan-456'},
    task_uuid='task-789',
    session_key='session-abc',
    backlog_hash='hash-def',
    priority='HIGH'
)
```

### 获取队列状态
```python
from tasks.misc_tasks import get_all_queue_status

status = get_all_queue_status()
print(f"DDR队列待处理任务: {status['ddr']['total_pending']}")
```

## 启动和管理

### 启动Workers
```bash
./launch_celery_worker.sh
```

### 监控
```bash
# 查看活跃任务
celery -A celery_worker.celery_app inspect active

# 查看队列状态
celery -A celery_worker.celery_app inspect active_queues
```

## 配置

### Beat调度器
```python
beat_schedule = {
    "ddr-dispatcher": {
        "task": "celery_worker.ddr_worker.scheduled_ddr_dispatcher_task",
        "schedule": 120.0
    }
}
```

### 任务路由
```python
task_routes = {
    "celery_worker.analyze_worker.*": {"queue": "analyze"},
    "celery_worker.ddr_worker.*": {"queue": "ddr"},
    "celery_worker.download_worker.*": {"queue": "download"}
}
```

这种架构设计确保了：
1. 清晰的职责分离
2. DDR任务的高优先级处理
3. 易于维护和扩展
4. 良好的监控和管理能力
