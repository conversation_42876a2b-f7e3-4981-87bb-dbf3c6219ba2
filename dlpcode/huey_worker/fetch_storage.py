from huey import <PERSON><PERSON><PERSON><PERSON>y
from huey import crontab
from util.config import configs
from exts import get_logger
from domain_model.tracker.fetch_storage_task_tracker import FetchStorageTaskTracker
from connector.sharepoint_token_connector import SharePointTokenConnector
from connector.google_connector import GoogleConnector
from connector.aws_connector import AWSConnector
from util.enum_ import StorageType, IdentityType
from storage.service.identity import cleanup_and_switch_version
from datetime import datetime, timedelta

huey = RedisHuey('fetch_storage', db=configs["huey"]["db"])
fetch_storage_log = get_logger("fetch_storage_log")
status_manager = FetchStorageTaskTracker()
task_interval = configs["fetch_storage"]["task_interval"]

@huey.task()
def sync_sites_task(storage_id, auth_info):
    from storage.model.sites import add_storage_sites

    if not status_manager.try_set_running(storage_id, "sites"):
        fetch_storage_log.info(f"Storage {storage_id}:sites sync already running, skipping...")
        return

    try:
        connector = SharePointTokenConnector(auth_info)
        result = connector.fetch_sites()
        if result:
            add_storage_sites(storage_id, result)
            cleanup_and_switch_version(storage_id, "sites")
            fetch_storage_log.info(f"Successfully saved {len(result)} sites to database for storage {storage_id}")
        return
    except Exception as e:
        fetch_storage_log.error(f"Sites sync failed for {storage_id}: {e}")
        return
    finally:
        status_manager.clear_status(storage_id, "sites")

@huey.task()
def sync_users_task(storage_id, storage_type, auth_info):
    from storage.service.identity import add_storage_identity
    connector = None

    if not status_manager.try_set_running(storage_id, "users"):
        fetch_storage_log.info(f"Storage {storage_id}:users sync already running, skipping...")
        return

    try:
        if storage_type == StorageType.GOOGLE:
            connector = GoogleConnector(auth_info)
        else:
            connector = SharePointTokenConnector(auth_info)

        result = connector.fetch_users()
        if result:
            add_storage_identity(storage_id, result, IdentityType.PEOPLE)
            cleanup_and_switch_version(storage_id, "users")
            fetch_storage_log.info(f"Successfully saved {len(result)} users to database for storage {storage_id}")
        return
    except Exception as e:
        fetch_storage_log.error(f"Users sync failed for {storage_id}: {e}")
        return
    finally:
        status_manager.clear_status(storage_id, "users")

@huey.task()
def sync_groups_task(storage_id, storage_type, auth_info):
    from storage.service.identity import add_storage_identity
    connector = None

    if not status_manager.try_set_running(storage_id, "groups"):
        fetch_storage_log.info(f"Storage {storage_id}:groups sync already running, skipping...")
        return

    try:
        if storage_type == StorageType.GOOGLE:
            connector = GoogleConnector(auth_info)
        else:
            connector = SharePointTokenConnector(auth_info)

        result = connector.fetch_groups()
        if result:
            add_storage_identity(storage_id, result, IdentityType.GROUP)
            cleanup_and_switch_version(storage_id, "groups")
            fetch_storage_log.info(f"Successfully saved {len(result)} groups to database for storage {storage_id}")
        return
    except Exception as e:
        fetch_storage_log.error(f"Groups sync failed for {storage_id}: {e}")
        return
    finally:
        status_manager.clear_status(storage_id, "groups")

@huey.task()
def sync_shared_drives_task(storage_id, auth_info):
    from storage.model.shared_drives import add_storage_shared_drives

    if not status_manager.try_set_running(storage_id, "shared_drives"):
        fetch_storage_log.info(f"Storage {storage_id}:shared_drives sync already running, skipping...")
        return

    try:
        connector = GoogleConnector(auth_info)
        result = connector.fetch_shared_drives()
        if result:
            add_storage_shared_drives(storage_id, result)
            cleanup_and_switch_version(storage_id, "shared_drives")
            fetch_storage_log.info(f"Successfully saved {len(result)} shared_drives to database for storage {storage_id}")
        return
    except Exception as e:
        fetch_storage_log.error(f"Users sync failed for {storage_id}: {e}")
        return
    finally:
        status_manager.clear_status(storage_id, "shared_drives")

@huey.task()
def sync_activity_task(storage_id, storage_type, auth_info, last_fetch_time):
    from storage.model.activity import add_fetched_activities
    from storage.service.profiles import update_storage_profile

    if not status_manager.try_set_running(storage_id, "activity"):
        fetch_storage_log.info(f"Storage {storage_id}:activity sync already running, skipping...")
        return

    try:
        if storage_type == StorageType.GOOGLE:
            connector = GoogleConnector(auth_info)
        elif storage_type == StorageType.AWS:
             connector = AWSConnector(auth_info)
        else:
            connector = SharePointTokenConnector(auth_info)

        start_time = None
        end_time = datetime.utcnow()
        if last_fetch_time is None:
            start_time = end_time - timedelta(minutes=task_interval)
        else:
            start_time = last_fetch_time

        result = connector.fetch_logs(start_time, end_time)
        if result:
            add_fetched_activities(result)
            update_storage_profile(storage_id, 1, 1, last_fetched=end_time)
            fetch_storage_log.info(f"Successfully saved {len(result)} activities to database for storage {storage_id}")
        return
    except Exception as e:
        fetch_storage_log.error(f"Activity sync failed for {storage_id}: {e}")
        return
    finally:
        status_manager.clear_status(storage_id, "activity")


@huey.periodic_task(crontab(minute=f'*/{task_interval}'))
def sync_storage(storage_id: str = ""):
    from storage.service.profiles import get_storage_profiles

    try:
        storages = None

        if storage_id:
            storages,_ = get_storage_profiles({'id':storage_id})
        else:
            storages,_ = get_storage_profiles({})
        for storage in storages:
            storage_type = storage['type']
            auth_info = storage['auth_info']
            auth_info['storage_id'] = storage['id']

            if storage_type == StorageType.GOOGLE or (storage_type == StorageType.SHAREPOINT_OL and not auth_info.get("usecredentials")):
                users_task = sync_users_task(storage['id'], storage_type, auth_info).get()

                groups_task = sync_groups_task(storage['id'], storage_type, auth_info).get()

                activity_task = sync_activity_task(storage['id'], storage_type, auth_info, storage['last_fetched']).get()

                if storage_type == StorageType.SHAREPOINT_OL:
                    sites_task = sync_sites_task(storage['id'], auth_info).get()
                elif storage_type == StorageType.GOOGLE:
                    shared_drives_task = sync_shared_drives_task(storage['id'], auth_info).get()
            elif storage_type == StorageType.AWS:
                activity_task = sync_activity_task(storage['id'], storage_type, auth_info, storage['last_fetched']).get()

    except Exception as e:
        fetch_storage_log.info(f"Error sync_storage: {e}")

