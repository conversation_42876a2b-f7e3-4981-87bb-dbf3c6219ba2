import os
import pickle
import shutil
import sys
import time
from domain_model.global_cache import GlobalCache
from domain_model.task_queue import ChangeResultEvent
from domain_model.tracker.pid_tracker import DoingDownloadTaskPidTracker
from exts import get_logger
from huey import RedisHuey
from huey.exceptions import CancelExecution
from huey.signals import (
    SIGNAL_EXECUTING,
    SIGNAL_INTERRUPTED,
    SIGNAL_COMPLETE,
    SIGNAL_ERROR,
)
from pathlib import Path
from service.task_management_service import TaskManagementService, SessionExpired
from service.task_queue_service import TaskQueueService
from util.config import configs, get_file_category_from_ext
from util.enum_ import ScanResult, HueyTaskStage, ProtectionQuarantineStatus, ProtectionCopyStatus
from util.utils import calculate_sha1, get_utc_timestamp, calculate_tlsh_hash
import magic
from domain_model.scan_policy import get_scan_policy

download_huey = RedisHuey("doing-download-task", db=configs["huey"]["db"])
skip_files = get_logger("skip_files")
logger = get_logger("doing_download")

def clean_result(huey_object: RedisHuey, huey_task_id: str) -> bool:
    """
    Cleans the result of a Huey task.

    Args:
        huey_object (RedisHuey): The Huey instance managing the task.
        huey_task_id (str): The unique identifier of the Huey task.

    Returns:
        bool: True if the result was successfully cleaned, False otherwise.

    Notes:
        We retrieve the results of the tasks to remove them from Redis.
        Huey automatically deletes task results from Redis when they are read.
    """
    try:
        huey_object.result(huey_task_id)
        huey_object.result(f"r:{huey_task_id}")
        return True
    except Exception as e:
        logger.error(e)
        return False


@download_huey.task()
def download_config_file(task_uuid: str, session_key: str, file_info: dict):
    """
    Downloads a configuration file based on the provided task UUID, session key, and file information.

    Args:
        task_uuid (str): The unique identifier for the task.
        session_key (str): The session key for the current session.
        file_info (dict): A dictionary containing information about the file to be downloaded, including:
            - folder (str): The folder where the file is located.
            - file_name (str): The name of the file to be downloaded.

    Notes:
        This function performs the following steps:
        1. Logs the start of the download process.
        2. Checks the validity of the session.
        3. Creates the configuration backlog directory if it does not exist.
        4. Retrieves the connector for the task.
        5. Checks if the download is required for the configuration file.
        6. Downloads the file if required.
        7. Handles connection and download errors.
        8. Creates a configuration file backlog.
        9. Copies the downloaded file to the configuration backlog directory.
    """
    local_file_path = None
    try:
        logger.debug(f"Downloading config file: {file_info}")

        # Since tms.get_connector() and connector.download() do not raise SessionExpired,
        # we need to check session validity here.
        tms = TaskManagementService(task_uuid, session_key)
        if not tms.session_tracker.is_alive():
            raise SessionExpired()

        '''
        config_backlog_dir = Path(configs["config_backlog_dir"]) / task_uuid
        config_backlog_dir.mkdir(exist_ok=True, parents=True)
        '''

        connector = tms.get_connector()
        if connector is None:
            logger.error(f"Failed to get connector for {task_uuid}")
            return

        is_download_required, attr_ext = connector.is_download_required_for_config_file(
            file_info["folder"], file_info["file_name"]
        )
        if not is_download_required:
            return

        file_uuid, local_file_path, obj_uri = connector.download(
            file_info["folder"], file_info["file_name"], task_uuid
        )

        # check if we have connection error
        if (file_uuid, local_file_path, obj_uri) == (-1, -1, -1):
            logger.error(f"Connection error for {file_info['file_name']}")
            return

        # check if we have download error (other than connection error)
        if (file_uuid, local_file_path, obj_uri) == (None, None, None):
            logger.error(f"Download error for {file_info['file_name']}")
            return

        '''
        create_config_file_backlog(file_uuid, task_uuid, obj_uri)
        shutil.copy(local_file_path, config_backlog_dir)
        '''
    except SessionExpired as e:
        logger.info(f"Task {task_uuid} session expired!")
        raise CancelExecution()
    except KeyError as e:
        logger.error(f"KeyError occurred: {e}")
    except OSError as e:
        logger.error(f"OSError occurred: {e}")
    except Exception as e:
        logger.error(e)
    finally:
        if local_file_path:
            Path(local_file_path).unlink(missing_ok=True)


@download_huey.task()
def download_file(
    task_uuid: str, file_info: dict, params: dict, session_key: str, backlog_hash: str,
) -> dict:
    """
    Downloads a file from a remote server.

    Args:
        task_uuid (str): The UUID of the task.
        file_info (dict): Information about the file to be downloaded.
        params (dict): Additional parameters for the download.
        session_key (str): The session key for authentication.
        backlog_hash (str): The hash of the backlog.

    Returns:
        dict: A dictionary containing the following keys:
            - "local_file": The local file path where the file is downloaded.
            - "remote_info": Information about the remote file.
            - "file_uuid": The UUID of the downloaded file.
            - "scan_info": Information about the scan.
            - "task_uuid": The UUID of the task.
            - "session_key": The session key for authentication.
            - "backlog_hash": The hash of the backlog.
    """
    try:
        logger.debug(f"Download file begin: file_info: {file_info}, task_uuid: {task_uuid}, session_key: {session_key}, pid: {os.getpid()}")
        task_start = time.time()
        download_start = None
        download_end = None
        check_requried_start = None
        check_requried_end = None
        is_ddr = params.get("scan_info", {}).get("is_ddr", False)

        null_result = {
            "local_file": None,
            "remote_info": None,
            "file_uuid": None,
            "scan_info": None,
            "task_uuid": task_uuid,
            "session_key": session_key,
            "backlog_hash": backlog_hash,
        }
        tqs = TaskQueueService()
        tms = TaskManagementService(task_uuid, session_key, is_ddr)
        if not tms.is_scanning():
            logger.error(f"Download file end, task: {task_uuid},  is not in SCANNING status, pid: {os.getpid()}.")
            return null_result

        if tms.download_error_threshold_reached():
            logger.error(f"Download file end, threshold reached for task: {task_uuid}, pid: {os.getpid()}")
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Too many errors occurred during file download",
                    session_key=session_key,
                )
            )
            return null_result

        connector = tms.get_connector()
        if connector is None:
            logger.error(f"Download file end, failed to get connector for task: {task_uuid}, pid: {os.getpid()}")
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Failed to connect to storage",
                    session_key=session_key,
                )
            )
            return null_result

        tms.scan_backoff()

        check_requried_start = time.time()
        cutoff_time = None
        if not file_info.get("skip_modify_time_check", False):
            cutoff_time = tms.get_cutoff_time()
        is_download_needed, attr_ext = connector.is_download_required(
            file_info["folder"],
            file_info["file_name"],
            cutoff_time,
            file_info.get("display_path")
        )
        check_requried_end = time.time()
        if is_download_needed == 0:
            logger.info(f"Download file end, no need to download, task: {task_uuid}, pid: {os.getpid()}")
            tms.increment_conn_error_count(False)
            return null_result

        elif is_download_needed == 1:
            download_start = time.time()
            file_uuid, local_file_path, obj_uri = connector.download(
                file_info["folder"], file_info["file_name"], task_uuid
            )
            download_end = time.time()
            # check if we have connection error
            if (file_uuid, local_file_path, obj_uri) == (-1, -1, -1):
                tms.increment_conn_error_count(True)
                logger.error(f"Download file end, connection error encountered during download, {file_info['file_name']} pass, task: {task_uuid}, pid: {os.getpid()}")
                skip_files.error(f"Task {task_uuid}: Connection error encountered during download, {file_info['file_name']} pass")
                if tms.download_error_threshold_reached():
                    logger.error(f"Download error threshold reached for {task_uuid}")
                    tqs.add_priority_event(
                        ChangeResultEvent(
                            scan_policy_id=task_uuid,
                            scan_result=ScanResult.FAILED,
                            fail_reason="Too many errors occurred during file download",
                            session_key=session_key,
                        )
                    )
                return null_result

        elif is_download_needed == 2:
            # Connection error when getting file info
            tms.increment_conn_error_count(True)
            logger.error(f"Download file end, connection error encountered during download, {file_info['file_name']} pass, task: {task_uuid}, pid: {os.getpid()}")
            skip_files.error(f"Task {task_uuid}: Connection error encountered during download, {file_info['file_name']} pass")
            if tms.download_error_threshold_reached():
                logger.error(f"Download file, error: threshold reached for {task_uuid}, pid: {os.getpid()}")
                tqs.add_priority_event(
                    ChangeResultEvent(
                        scan_policy_id=task_uuid,
                        scan_result=ScanResult.FAILED,
                        fail_reason="Too many errors occurred during file download",
                        session_key=session_key,
                    )
                )
            return null_result

        tms.increment_conn_error_count(False)

        # check if we have download error (other than connection error)
        if (file_uuid, local_file_path, obj_uri) == (None, None, None):
            logger.error(f"Download file end, error encountered during download, {file_info['file_name']} pass, task: {task_uuid}, pid: {os.getpid()}")
            skip_files.error(f"Task {task_uuid}: Error encountered during download, {file_info['file_name']} pass")
            return null_result

        params["remote_info"]["file"] = obj_uri
        params["remote_info"]["file_name"] = file_info["file_name"].split("/")[-1]
        params["remote_info"]["file_size"] = attr_ext.get("file_size", 0)
        params["remote_info"]["last_modified"] = attr_ext.get("last_modified", "")
        params["remote_info"]["user"] = attr_ext.get("user", "UNKNOWN")
        params["remote_info"]["email"] = attr_ext.get("email", "UNKNOWN")
        params['remote_info']['collaborators'] = attr_ext.get("collaborators", {})
        params['remote_info']['share_link'] = attr_ext.get("share_link", [])
        params["remote_info"]["file_type"] = attr_ext.get("file_type")
        params["remote_info"]["shared_data"] = attr_ext.get("shared_data")
        params["remote_info"]["file_encryption"] = attr_ext.get("encryption")
        params["remote_info"]["file_location"] = attr_ext.get("location", "UNKNOWN")
        file_hash = calculate_sha1(local_file_path)
        params["remote_info"]["file_hash"] = file_hash

        cache = GlobalCache(task_uuid, session_key, is_ddr=is_ddr)
        records, record_count = cache.query_items({"full_path": obj_uri})
        if record_count > 0 and records[0].get("file_hash", "") == file_hash and connector.is_file_attr_changed(records[0], attr_ext) == False:
            logger.debug(f"Download file end, the file {obj_uri} with file_hash {file_hash} have been analyzed, skip it, task: {task_uuid}, pid: {os.getpid()}")
            skip_files.error(f"Task {task_uuid}: The file {obj_uri} with file_hash {file_hash} have been analyzed, skip it")
            return null_result
        params["remote_info"]["mime_type"] = magic.Magic(mime=True).from_file(local_file_path)

        task_end = time.time()
        check_required_ts = -1
        download_ts = -1
        if check_requried_start and check_requried_end:
            check_required_ts = round(check_requried_end -  check_requried_start, 3)
        if download_start and download_end:
            download_ts = round(download_end - download_start, 3)
        download_statistics = {
            "check_required": check_required_ts,
            "download": download_ts,
            "total": round(task_end - task_start, 3),
        }

        file_metadata = {
            "collaborators": attr_ext.get("collaborators", {}),
            "share_link": attr_ext.get("share_link", []),
        }
        if "owners" in attr_ext:
            file_metadata["owners"] = attr_ext["owners"]
        if "create_by" in attr_ext:
            file_metadata["create_by"] = attr_ext["create_by"]

        file_attributes = {
            "file_size": attr_ext.get("file_size", 0),
            "file_ext": "",
            "file_cat": "",
            "file_user_name": attr_ext.get("user", "UNKNOWN"),
            "file_user_email": attr_ext.get("email", "UNKNOWN"),
            "file_user_id": attr_ext.get("id", ""),
            "file_location": attr_ext.get("location", "UNKNOWN"),
            "file_encryption": attr_ext.get("encryption")
        }
        if len(params["remote_info"]["file_name"].split(".")) > 1:
            file_attributes["file_ext"] = params["remote_info"]["file_name"].split(".")[-1].lower()
            file_attributes["file_cat"] = get_file_category_from_ext(file_attributes["file_ext"])
            file_attributes["file_display_path"] = file_info.get("display_path", "N/A")
            file_attributes["site_id"] = file_info.get("site_id", "N/A")
            file_attributes["drive_id"] = file_info.get("drive_id", "N/A")
            if "catlog_folder" in file_info:
                file_attributes["catlog_folder"] = file_info["catlog_folder"]

        init_remediation_info = {
            "copy_status": ProtectionCopyStatus.INIT,
            "copy_targets": [],
            "copy_failed_message": "",
            "quarantine_status": ProtectionQuarantineStatus.INIT,
            "quarantine_targets": [],
            "quarantine_failed_message": "",
            "restore_quarantine_failed_message": ""
            }
   
        payload = {
            "id": file_uuid,
            "file_hash": params["remote_info"]["file_hash"],
            "file_tlsh": calculate_tlsh_hash(local_file_path),
            "scan_uuid": task_uuid,
            "storage_id": params["remote_info"]["storage_id"],
            "full_path": params["remote_info"]["file"],
            "file_name": params["remote_info"]["file_name"],
            "file_attributes": file_attributes,
            "file_metadata": file_metadata,
            "storage_type": params["remote_info"]["storage_type"],
            "reserve_json2": download_statistics,
            "reserve_json3": init_remediation_info,
            "tm": get_utc_timestamp(),
        }

        cache.put(payload)

        return {
            "local_file": local_file_path,
            "remote_info": params["remote_info"],
            "file_uuid": file_uuid,
            "scan_info": params["scan_info"],
            "task_uuid": task_uuid,
            "session_key": session_key,
            "backlog_hash": backlog_hash,
        }
    except KeyError as e:
        logger.error(f"Download file end, task: {task_uuid}, pid: {os.getpid()}, KeyError occurred: {e}")
        skip_files.error(f"Task {task_uuid}: KeyError encountered during download, {file_info['file_name']} pass")
        return null_result
    except SessionExpired as e:
        logger.info(f"Download file end, task: {task_uuid}, pid: {os.getpid()}, session_key: {session_key} session expired!")
        raise CancelExecution()
        # return null_result
    except Exception as e:
        logger.error(f"Download file end, task: {task_uuid}, pid: {os.getpid()}, exception: {e}")
        skip_files.error(f"Task {task_uuid}: Error encountered during download, {file_info['file_name']} pass")
        return null_result


@download_huey.task()
def add_analyze_task(*args, **kwargs):
    """
    This is a dumy task for pipeline usage.
    """
    pass


@download_huey.signal(SIGNAL_EXECUTING)
def right_before_execute(signal, task, exc=None):
    """
    Signal handler executed right before a task is executed.

    This function is called when a task is about to be executed. It sets up tracking
    for download tasks, including recording the process ID and starting the
    download track in the TaskManagementService.

    Args:
        signal: The signal that triggered this handler.
        task: The task object that is about to be executed.
        exc (Exception, optional): Any exception that occurred, if applicable.
    """
    try:
        logger.info(f"Download file SIGNAL_EXECUTING begin, task: {task.name}, task.kwargs: {task.kwargs} pid: {os.getpid()}")
        if task.name == "download_file":
            pid_tracker = DoingDownloadTaskPidTracker()
            pid_tracker.set(str(os.getpid()), task.kwargs["task_uuid"])
            TaskManagementService(
                task.kwargs["task_uuid"], task.kwargs["session_key"]
            ).start_download_track(task.kwargs, task.kwargs["backlog_hash"])
            logger.info(f"Download file SIGNAL_EXECUTING start pid_tracker, download_file, pid: {os.getpid()}, task.kwargs: {task.kwargs}")
        elif task.name == "add_analyze_task":
            pid_tracker = DoingDownloadTaskPidTracker()
            pid_tracker.set(str(os.getpid()), task.kwargs["task_uuid"])
            logger.info(f"Download file SIGNAL_EXECUTING start pid_tracker, add_analyze_task, pid: {os.getpid()}, task.kwargs: {task.kwargs}")
    except SessionExpired as e:
        logger.error(f"Download file SIGNAL_EXECUTING end, task: {task.kwargs['task_uuid']}, pid: {os.getpid()}, session expired!")
        raise CancelExecution()
    except Exception as e:
        logger.error(f"Download file SIGNAL_EXECUTING end, task: {task.kwargs['task_uuid']}, pid: {os.getpid()}, Exception: {str(e)}")


@download_huey.signal(SIGNAL_INTERRUPTED)
def right_after_interrupted(signal, task, exc=None):
    """
    Signal handler for when a download_file or add_analyze_task is interrupted.

    This function is called when a task is interrupted during execution. It attempts to
    retry the task if it's still in SCANNING status, and clean up Redis entries.

    Args:
        signal: The signal that triggered this handler.
        task: The task object that was interrupted.
        exc (Exception, optional): The exception that caused the interruption, if any.
    """
    from domain_model.tracker.timeout_task_tracker import TimeoutTaskTracker

    try:
        logger.info(f"Download file SIGNAL_INTERRUPTED begin, task: {task.name}, task.kwargs: {task.kwargs} pid: {os.getpid()}")
        timeout_tracker = TimeoutTaskTracker()

        if task.name == "download_file":
            tms = TaskManagementService(
                task.kwargs["task_uuid"], task.kwargs["session_key"]
            )
            if not tms.is_scanning():
                logger.debug(f"Download file SIGNAL_INTERRUPTED end, task: {task.kwargs['task_uuid']} is not in SCANNING status pid: {os.getpid()}.")
                return

            is_timeout_task, created_at = timeout_tracker.is_timeout_task(
                task.kwargs["task_uuid"],
                task.kwargs["session_key"],
                HueyTaskStage.IN_DOWNLOAD,
                task.kwargs["backlog_hash"],
            )
            if is_timeout_task:
                download_statistic = {
                    "download_execute_at": str(created_at),
                    "download_interrupt_at": str(get_utc_timestamp()),
                }
                full_path = f"{task.kwargs['file_info']['folder']}/{task.kwargs['file_info']['file_name']}"
                if not full_path.startswith("/"):
                    full_path = "/" + full_path

                payload = {
                    "scan_uuid": task.kwargs["task_uuid"],
                    "full_path": full_path,
                    "file_name": task.kwargs["file_info"]["file_name"].split("/")[-1],
                    "storage_type": task.kwargs["params"]["remote_info"]["storage_type"],
                    "file_hash": "DUMMY_HASH",
                    "file_attributes": {},
                    "reserve_json2": download_statistic,
                    "tm": get_utc_timestamp(),
                }
                cache = GlobalCache(task.kwargs["task_uuid"], task.kwargs["session_key"], is_ddr=task.kwargs["scan_info"].get("is_ddr", False))
                cache.put(payload)
                logger.debug(f"Download file SIGNAL_INTERRUPTED end, task: {task.kwargs['task_uuid']} is timeout already, pid: {os.getpid()}.")
                return

            logger.info(f"Download file SIGNAL_INTERRUPTED, add to huey enqueue again, task: {task.name}, task.kwargs: {task.kwargs}, pid: {os.getpid()}")
            flag = False
            for i in range(5):
                download_file_task = download_file.s(
                    task_uuid=task.kwargs["task_uuid"],
                    file_info=task.kwargs["file_info"],
                    params=task.kwargs["params"],
                    session_key=task.kwargs["session_key"],
                    backlog_hash=task.kwargs["backlog_hash"],
                )
                pipeline = download_file_task.then(
                    add_analyze_task,
                    previous_task_id=download_file_task.id
                )
                download_huey.enqueue(pipeline)

                for item in download_huey.storage.enqueued_items():
                    decoded_item = pickle.loads(item)
                    if decoded_item.id == pipeline.id:
                        flag = True
                        break

                if flag == True:
                    break
                else:
                    logger.debug(f"Download file SIGNAL_INTERRUPTED, add to huey enqueue again failed, try count: {i+1}, pid: {os.getpid()}")
                    time.sleep(0.5)

            if flag == False:
                tms.end_download_track(task.kwargs["backlog_hash"], error=True)
                logger.info(f"Download file SIGNAL_INTERRUPTED, add to huey enqueue again failed, pid: {os.getpid()}")
            else:
                tms.doing_download_task_tracker.stop_track_without_increment_counter(session_key=task.kwargs["session_key"], backlog_hash=task.kwargs["backlog_hash"])
                logger.info(f"Download file SIGNAL_INTERRUPTED, add to huey enqueue again success, id: {pipeline.id}, pid: {os.getpid()}")
        elif task.name == "add_analyze_task":
            tms = TaskManagementService(
                task.kwargs["task_uuid"], task.kwargs["session_key"]
            )
            if not tms.is_scanning():
                logger.info(f"Download file SIGNAL_INTERRUPTED end, task {task.kwargs['task_uuid']} is not in SCANNING status, pid: {os.getpid()}.")
                return

            add_analyze_task(
                local_file=task.kwargs["local_file"],
                remote_info=task.kwargs["remote_info"],
                file_uuid=task.kwargs["file_uuid"],
                scan_info=task.kwargs["scan_info"],
                task_uuid=task.kwargs["task_uuid"],
                session_key=task.kwargs["session_key"],
                backlog_hash=task.kwargs["backlog_hash"],
            )
            clean_result(download_huey, task.kwargs["previous_task_id"])
            clean_result(download_huey, task.id)
    except SessionExpired as e:
        logger.error(f"Download file SIGNAL_INTERRUPTED end, task: {task.kwargs['task_uuid']}, pid: {os.getpid()}, session expired!")
    except AttributeError as e:
        logger.error(f"Download file SIGNAL_INTERRUPTED end, task: {task.kwargs['task_uuid']}, pid: {os.getpid()}, AttributeError: {str(e)}")
    except KeyError as e:
        logger.error(f"Download file SIGNAL_INTERRUPTED end, task: {task.kwargs['task_uuid']}, pid: {os.getpid()}, KeyError: {str(e)}")
    except Exception as e:
        logger.error(f"Download file SIGNAL_INTERRUPTED end, task: {task.kwargs['task_uuid']}, pid: {os.getpid()}, Exception: {str(e)}")
    finally:
        logger.info(f"Download file SIGNAL_INTERRUPTED delete pid_tracker, task: {task.name}, task.kwargs: {task.kwargs}, pid: {os.getpid()}")
        pid_tracker = DoingDownloadTaskPidTracker()
        pid_tracker.delete(str(os.getpid()))
        sys.exit(0)


@download_huey.signal(SIGNAL_COMPLETE)
def right_after_completed(signal, task, exc=None):
    """
    Signal handler for when a task is completed.

    This function is called when a task completes its execution successfully. It handles
    the completion of 'add_analyze_task' by potentially adding task to the analyze worker,
    ending the download track, and cleaning up resources.

    Args:
        signal: The signal that triggered this handler.
        task: The task object that was completed.
        exc (Exception, optional): The exception that occurred during task execution, if any.
    """
    try:
        logger.info(f"Download file SIGNAL_COMPLETE begin, task: {task.name}, task.kwargs: {task.kwargs} pid: {os.getpid()}")
        if task.name == "add_analyze_task":
            from huey_worker.analyze_worker import analyze_worker

            tms = TaskManagementService(
                task.kwargs["task_uuid"], task.kwargs["session_key"]
            )
            if not tms.is_scanning():
                logger.info(f"Download file SIGNAL_COMPLETE end, task {task.kwargs['task_uuid']} is not in SCANNING status, pid: {os.getpid()}.")
                return

            have_all_parameters = all(
                [
                    task.kwargs["local_file"],
                    task.kwargs["remote_info"],
                    task.kwargs["file_uuid"],
                    task.kwargs["scan_info"],
                ]
            )
            if have_all_parameters:
                logger.info(f"Download file SIGNAL_COMPLETE, call the analyze_worker, pid: {os.getpid()}")
                analyze_worker(
                    local_file=task.kwargs["local_file"],
                    remote_info=task.kwargs["remote_info"],
                    file_uuid=task.kwargs["file_uuid"],
                    scan_info=task.kwargs["scan_info"],
                    task_uuid=task.kwargs["task_uuid"],
                    session_key=task.kwargs["session_key"],
                    backlog_hash=task.kwargs["backlog_hash"],
                )
                TaskManagementService(
                    task.kwargs["task_uuid"], task.kwargs["session_key"]
                ).end_download_track(task.kwargs["backlog_hash"], error=False)
            else:
                logger.info(f"Download file SIGNAL_COMPLETE, not call the analyze_worker, pid: {os.getpid()}")
                TaskManagementService(
                    task.kwargs["task_uuid"], task.kwargs["session_key"]
                ).end_download_track(task.kwargs["backlog_hash"], error=True)

            clean_result(download_huey, task.kwargs["previous_task_id"])
            clean_result(download_huey, task.id)
    except SessionExpired as e:
        logger.error(f"Download file SIGNAL_COMPLETE end, task: {task.kwargs['task_uuid']}, pid: {os.getpid()}, session expired!")
    except AttributeError as e:
        logger.error(f"Download file SIGNAL_COMPLETE end, task: {task.kwargs['task_uuid']}, pid: {os.getpid()}, AttributeError: {str(e)}")
    except KeyError as e:
        logger.error(f"Download file SIGNAL_COMPLETE end, task: {task.kwargs['task_uuid']}, pid: {os.getpid()}, KeyError: {str(e)}")
    except Exception as e:
        logger.error(f"Download file SIGNAL_COMPLETE end, task: {task.kwargs['task_uuid']}, pid: {os.getpid()}, Exception: {str(e)}")
    finally:
        pid_tracker = DoingDownloadTaskPidTracker()
        pid_tracker.delete(str(os.getpid()))
        logger.info(f"Download file SIGNAL_COMPLETE delete pid_tracker, task: {task.name}, task.kwargs: {task.kwargs}, pid: {os.getpid()}")

@download_huey.signal(SIGNAL_ERROR)
def right_after_error(signal, task, exc=None):
    """
    Signal handler for errors in download tasks.

    This function is called immediately after an error occurs in a task
    managed by the download_huey queue. It handles cleanup operations and end the download track.

    Args:
        signal: The signal that triggered this handler.
        task: The task object that encountered an error.
        exc (Exception, optional): The exception that was raised, if any.
    """
    try:
        logger.info(f"Download file SIGNAL_ERROR begin, task: {task.name}, task.kwargs: {task.kwargs} pid: {os.getpid()}")
        if task.name == "download_file":
            TaskManagementService(
                task.kwargs["task_uuid"], task.kwargs["session_key"]
            ).end_download_track(task.kwargs["backlog_hash"], error=True)
        elif task.name == "add_analyze_task":
            clean_result(download_huey, task.kwargs["previous_task_id"])
            clean_result(download_huey, task.id)
    except SessionExpired as e:
        logger.error(f"Download file SIGNAL_ERROR end, task: {task.kwargs['task_uuid']}, pid: {os.getpid()}, session expired!")
    except AttributeError as e:
        logger.error(f"Download file SIGNAL_ERROR end, task: {task.kwargs['task_uuid']}, pid: {os.getpid()}, AttributeError: {str(e)}")
    except KeyError as e:
        logger.error(f"Download file SIGNAL_ERROR end, task: {task.kwargs['task_uuid']}, pid: {os.getpid()}, AttributeError: {str(e)}")
    except Exception as e:
        logger.error(f"Download file SIGNAL_ERROR end, task: {task.kwargs['task_uuid']}, pid: {os.getpid()}, AttributeError: {str(e)}")
    finally:
        pid_tracker = DoingDownloadTaskPidTracker()
        pid_tracker.delete(str(os.getpid()))
        logger.info(f"Download file SIGNAL_ERROR delete pid_tracker, task: {task.name}, task.kwargs: {task.kwargs}, pid: {os.getpid()}")
