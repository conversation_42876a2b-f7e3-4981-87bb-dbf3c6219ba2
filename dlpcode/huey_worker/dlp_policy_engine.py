import sys
import threading
import time
import os
import signal
from readerwriterlock import rwlock
from service.dlp_engine_service import PolicyEngine, MatchEngine
from service.dlp_policy_service import get_all_policies, read_policy
from service.dlp_rule_service import get_rules_by_policy_id
from huey import RedisHuey
from util.common_log import get_logger
from util.config import get_global_config
from util.redis_stream import RedisStream
configs = get_global_config()
huey_db = configs["huey"]["db"]
huey = RedisHuey("dlp_policy_engine", db=huey_db)
logger = get_logger("dlp_policy_engine")

policy_engine = PolicyEngine()
policy_engine_lock = rwlock.RWLockFairD()
policy_engine_lock_reader = policy_engine_lock.gen_rlock()
policy_engine_lock_writer = policy_engine_lock.gen_wlock()

shutdown_event = threading.Event()

def signal_handler(signum, frame):
    logger.info(f"Received signal {signum}, shutting down policy engine")
    shutdown_event.set()

def notify_update_policy(policy_id):
    message_dict = {}
    message_dict["msg_type"] = "policy_update"
    message_dict["policy_id"] = str(policy_id)
    redis_stream = RedisStream("dlp_config_update")

    if redis_stream.send_redis_stream_msg(message_dict) == False:
        logger.error(f"Failed to send redis stream msg: {message_dict}")
        return False

    return True

def notify_delete_policy(policy_id):
    message_dict = {}
    message_dict["msg_type"] = "policy_delete"
    message_dict["policy_id"] = str(policy_id)
    redis_stream = RedisStream("dlp_config_update")

    if redis_stream.send_redis_stream_msg(message_dict) == False:
        logger.error(f"Failed to send redis stream msg: {message_dict}")
        return False

    return True

def config_update_process():
    logger.info(f"Start config update thread, pid: {os.getpid()}")
    redis_stream = RedisStream("dlp_config_update")
    if redis_stream.add_consume_group() == False:
        logger.error(f"Failed to add consume group for pid: {os.getpid()}")
        return

    consumer_id = ">"
    id = 0
    stream_timeout = 5000
    while not shutdown_event.is_set():
        try:
            items = redis_stream.db.xreadgroup(redis_stream.group_name, redis_stream.consume_name, {redis_stream.stream_name:consumer_id}, block=stream_timeout, count=1)
            if not items:
                time.sleep(0.5)
                #logger.info("xreadgroup timeout!")
                continue
            elif len(items[0][1]) == 0:
                #logger.info("xreadgroup read length is 0")
                continue

            for id, fields in items[0][1]:
                logger.info(f"Group {redis_stream.group_name} get message from stream {redis_stream.stream_name}, the message: {str(fields)}")
                redis_stream.db.xack(redis_stream.stream_name, redis_stream.group_name, id)
                message_dict = {}
                for k,v in fields.items():
                    message_dict[k.decode('utf-8')] = v.decode('utf-8')

                if "msg_type" not in message_dict:
                    continue
                msg_type = message_dict['msg_type']
                if msg_type == "policy_update":
                    if "policy_id" not in message_dict:
                        continue
                    policy_id =  message_dict['policy_id']
                    update_policy_engine(policy_id)
                elif msg_type == "policy_delete":
                    if "policy_id" not in message_dict:
                        continue
                    policy_id =  message_dict['policy_id']
                    delete_policy_engine(policy_id)
                else:
                    logger.info(f"Unknown msg type: {msg_type}")
                    continue
        except Exception as e:
            if shutdown_event.is_set():
                logger.info(f"Config update thread is shutting down")
                break
            logger.error(f"Exception while stream consuming: {str(e)}")
            time.sleep(1)

def delete_policy_engine(policy_id):
    policy_engine_lock_writer.acquire()
    try:
        policy_engine.delete_policy(policy_id)
    except Exception as e:
        logger.error(f"Error delete policy engine: {e}")
    finally:
        policy_engine_lock_writer.release()

def update_policy_engine(policy_id):
    policy = read_policy(policy_id)
    if policy is None:
        logger.error(f"Not find policy: {policy_id}")
        return

    policy_engine_lock_writer.acquire()
    try:
        policy_engine.delete_policy(policy_id)
        match_engine = MatchEngine()
        rules = get_rules_by_policy_id(policy_id)
        if rules is not None:
            for rule in rules:
                match_engine.create_rule(**rule.to_dict())
            policy_engine.create_policy(policy_id, policy.name, policy.priority, policy.status, match_engine)
    except Exception as e:
        logger.error(f"Error update policy engine: {e}")
    finally:
        policy_engine_lock_writer.release()
        logger.info(f"Policy engine updated for policy: {policy_id}")

def create_policy_engine():
    policies = get_all_policies()

    policy_engine_lock_writer.acquire()
    try:
        for policy in policies:
            match_engine = MatchEngine()
            rules = get_rules_by_policy_id(policy.id)
            for rule in rules:
                match_engine.create_rule(**rule.to_dict())
            policy_engine.create_policy(str(policy.id), policy.name, policy.priority, policy.status, match_engine)
    except Exception as e:
        logger.error(f"Error creating policy engine: {e}")
    finally:
        policy_engine_lock_writer.release()
    logger.info("Policy engine created")

    return policy_engine



def create_config_update_thread():
    config_thread = threading.Thread(target=config_update_process, daemon=True)
    config_thread.start()

@huey.on_startup()
def startup():
    try:
        logger.info("Starting up policy engine")
        create_policy_engine()
        create_config_update_thread()
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    except Exception as e:
        logger.error(f"Failed to start policy engine or config update thread: {e}")
        sys.exit(1)

@huey.task()
def match_policy_engine(data):
    logger.info(f"Match policy request process by {os.getpid()}, data: {data}")
    result = []
    try:
        policy_engine_lock_reader.acquire()
        result = policy_engine.match(data)
    except Exception as e:
        logger.error(f"Error matching policy engine: {e}")
    finally:
        policy_engine_lock_reader.release()
    return result

