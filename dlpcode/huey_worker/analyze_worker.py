import os
import pickle
import sys
import uuid
import hashlib
from datetime import datetime
import time
from service.data_label_service import <PERSON><PERSON><PERSON><PERSON>
from huey import PriorityRedisHuey
from pathlib import Path
from file_transformer.file_extract import get_file_info
from huey_worker.tag_worker import update_remote_tag
from huey_worker.protection_action_task import do_discover_actions, copy_local_file_to_protection_folder
from huey.exceptions import CancelExecution
from huey.signals import SIGNAL_COMPLETE, SIGNAL_ERROR, SIGNAL_EXECUTING, SIGNAL_INTERRUPTED
from service.custom_datatype_service import UserDefineDatatypeService
from util.common_log import get_logger
from util.random_password.service import RandomPasswordService
from util.utils import get_utc_timestamp
from util.config import get_global_config
from util.enum_ import ASBinName
from domain_model.global_cache import GlobalCache
from domain_model.tracker.pid_tracker import AnalyzeWorkerPidT<PERSON>
from cachetools import TTLCache
import threading
from exts import Session
from service.discovery_engine_v2_service import create_policy_engine, DiscoveryPolicyEngine
from service.discovery_policy_v2_service import get_entity_by_policy
from service.idm_template_service import get_idm_match_info
from service.edm_service import get_edm_match_info
from ddr.service.engine_service import DDRPolicyEngine, ddr_create_policy_engine

configs = get_global_config()
huey_db = configs["huey"]["db"]
huey = PriorityRedisHuey("analyze_worker", db=huey_db)
logger = get_logger("analyze_worker")
is_file_unified = configs.get("file_unified_queue", {}).get("enabled", False)

ANALYZE_CACHE_TTL = 900
global_analyze_ttl_cache = TTLCache(maxsize=1000, ttl=ANALYZE_CACHE_TTL)
file_upload_analyze_cache = TTLCache(maxsize=20, ttl=float('inf'))

global_discover_engine_ttl_cache = TTLCache(maxsize=1000, ttl=600)
global_ddr_engine_ttl_cache = TTLCache(maxsize=1000, ttl=600)

global_data = None

global_data_lock = threading.Lock()

@huey.on_startup()
def startup():
    from file_analyzer.analyzer_global import AnalyzerGlobal
    from file_analyzer.analyzer_log import setup_logging

    global global_data
    logger.info("Starting analyze_worker")

    try:
        with global_data_lock:
            if global_data is None:
                # setup logger for file_analyzer
                setup_logging(logger=logger, level=configs.get("file_analyzer", {}).get("log_level"))
                
                global_data = AnalyzerGlobal(
                    base_path=configs.get("file_analyzer", {}).get("config_path"),
                    session=Session,
                    use_gpu=configs.get("file_analyzer", {}).get("classifier_use_gpu", True),
                    mod_verify=configs.get("file_analyzer", {}).get("mod_verify", False),
                    sig_pub_key=configs.get("file_analyzer", {}).get("public_key_path", None),
                    classifier_thread_num=configs.get("file_analyzer", {}).get("classifier_thread_num", True),
                    classifier_token_limit=configs.get("file_analyzer", {}).get("classifier_token_limit", 512),
                    minimum_classifier_token=configs.get("file_analyzer", {}).get("minimum_classifier_token", 128),
                    nlp_thread_num=configs.get("file_analyzer", {}).get("nlp_thread_num", True),
                    nlp_recognizer_thread_num=configs.get("file_analyzer", {}).get("nlp_recognizer_thread_num", 1),
                    db_decryption_mode=configs.get("file_analyzer", {}).get("db_decryption_mode", 1)
                )

                LabelFetcher.init()
    except Exception as e:
        logger.error(f"Failed to initialize analyze_worker: {e}")
        sys.exit(1)

def analyze_worker_pending_count():
    return huey.pending_count()
    
def update_cache_result(cache, file_uuid, session_key, scan_info, result, remote_info, analyze_statistic, tags, edm_match_info = None, idm_match_info=None):
    # cache = GlobalCache(scan_info['scan_uuid'], session_key)
    logger.debug(f'Scan uuid: {scan_info["scan_uuid"]}')
    match_info = []
    match_info_custom = []
    if result.compliances is not None:
        match_info = result.compliances.get('ENTITIES')
        match_info_custom = result.compliances.get('CUSTOM')

    class_details = {}
    class_details['mc_confidence'] = result.main_class_confidence
    class_details['sc_confidence'] = result.sub_class_confidence
    class_details['main_class_list'] = [result.main_class_id] if result.main_class_id else []
    class_details['main_confidence_list'] = [result.main_class_confidence] if result.main_class_id else []
    class_details['sub_class_list'] = [result.sub_class_id] if result.sub_class_id else []
    class_details['sub_confidence_list'] = [result.sub_class_confidence] if result.sub_class_id else []

    fr = cache.get_by_file_uuid(file_uuid)
    file_attributes = dict(fr.get("file_attributes", {}))
    remote_info["file_display_path"] = file_attributes.get("file_display_path", "N/A")
    update_data = {
        'owner': {
            "name": remote_info['user'],
            "email": remote_info['email']
        },
        'file_size': remote_info['file_size'],
        'last_modified': remote_info['last_modified']
    }

    file_attributes.update(update_data)

    payload = {
        'id': file_uuid,
        'scan_uuid': scan_info['scan_uuid'], 
        'full_path': remote_info['file'],
        'file_name': remote_info['file_name'],
        'file_attributes': file_attributes,
        'main_class_id': result.main_class_id if result.main_class_id else None,
        'sub_class_id': result.sub_class_id if result.sub_class_id else None,
        'class_details': class_details,
        'match_info': match_info if match_info else None,
        'match_info_custom': match_info_custom if match_info_custom else None,
        'reserve_json1': analyze_statistic or {},
        'tm': get_utc_timestamp(),
        'last_scan_tm': get_utc_timestamp()
    }
    if edm_match_info:
        payload["reserve_json1"]["edm_match_info"] = edm_match_info
    if idm_match_info:
        payload["reserve_json1"]["idm_match_info"] = idm_match_info

    payload["reserve_json1"]["sensitive_data"] = False
    payload["reserve_json1"]["compliance_data"] = False

    shared_data = remote_info.get('shared_data')
    if shared_data:
        payload["reserve_json1"]["shared_data"] = shared_data

    # Only update the file tag if we have tags to update
    if tags:
        payload['file_tag'] = tags
        payload['tag_tm'] = get_utc_timestamp()

        compliance_labels = global_data.analyzer_global_path.get_sensitive_label_config().get("compliance_data", [])
        sensitive_labels = global_data.analyzer_global_path.get_sensitive_label_config().get("sensitive_data", [])
        payload["reserve_json1"]["compliance_data"] = any(item in compliance_labels for item in tags["predefine"])
        payload["reserve_json1"]["sensitive_data"] = any(item in sensitive_labels for item in tags["predefine"])

    cache.put(payload, update_only=True)
    
def calculate_sha1(file_path):
    try:
        with open(file_path, 'rb') as file:
            bytes = file.read()  # read entire file as bytes
            readable_hash = hashlib.sha1(bytes).hexdigest()
        return readable_hash
    except FileNotFoundError:
        logger.error(f"File {file_path} not found.")
    except PermissionError:
        logger.error(f"No permission to read file {file_path}.")


def get_analyze_threshold(scan_info):
    # 1:High, 2:Medium, 3:Low
    
    ml_threshold_map = {
        1: configs.get('file_analyzer', {}).get("classifier_probability_threshold_high", 0.95),
        2: configs.get('file_analyzer', {}).get("classifier_probability_threshold_medium", 0.90),
        3: configs.get('file_analyzer', {}).get("classifier_probability_threshold_low", 0.85),
    }
    
    re_threshold_map = {
        1: configs.get('file_analyzer', {}).get("recognizer_score_threshold_high", 0.6),
        2: configs.get('file_analyzer', {}).get("recognizer_score_threshold_medium", 0.5),
        3: configs.get('file_analyzer', {}).get("recognizer_score_threshold_low", 0.4),
    }
    
    ml_certainty_level = scan_info.get("ml_certainty_level", 2)
    re_precision_level = scan_info.get("re_precision_level", 2)
    
    return ml_threshold_map[ml_certainty_level], re_threshold_map[re_precision_level]


def get_analyze(task_uuid, scan_info, session_key, task_timeout=60, load_on_miss=True):
    from file_analyzer import FileAnalyzer, AnalyzerConfig
    from file_analyzer.analyzer_data_types import AnalyzerDataTypes
    # Combine task_uuid and thread id to form a unique key
    key = f"{session_key}_{threading.get_ident()}"
    expiration_key = f"{key}_expiration"

    # Try to get the analyze instance from cache
    try:
        expiration_time = global_analyze_ttl_cache[expiration_key]
        if load_on_miss and (expiration_time - time.time()) <= task_timeout:
            analyze_instance = None
            logger.info("Analyze instance ttl is shorter than task timeout.")
        else:
            analyze_instance = global_analyze_ttl_cache[key]
            logger.info("Analyze instance found in cache.")
    except KeyError:
        analyze_instance = None

    # If the analyze instance does not exist in cache
    if analyze_instance is None: 
        if not load_on_miss:
            raise Exception("Analyze instance not found in cache")
        logger.info("Creating new analyze instance.")

        standard_entity_ids, custom_entity_ids, idm_mapping = get_entity_by_policy(policy_ids=scan_info.get("discover_policy",[]))
        logger.info(f"standard_entity_ids: {standard_entity_ids}, custom_entity_ids: {custom_entity_ids}")
        try:
            custom_dtypes = UserDefineDatatypeService.get_user_define_dtype_by_ids(ids=custom_entity_ids)
        except Exception as e:
            logger.error(f"Get user define dtyoe by custom entity ids failed: {e}")
            custom_dtypes = []
        standard_analyzer_enabled = False if not standard_entity_ids else True
        custom_analyzer_enabled = False if not custom_dtypes else True

        regions = []
        region_info, e = AnalyzerDataTypes(global_path=global_data.analyzer_global_path).get_supported_regions()
        if e is not None:
            logger.error(f"get region info failed: {e}")
        else:
            for continent in scan_info.get("regions", []):
                if "continent" in region_info and continent in region_info["continent"]:
                    regions.extend(region_info["continent"][continent]["country"].keys())
                    
        classifier_probability_threshold, recognizer_score_threshold = get_analyze_threshold(scan_info)
                    
        analyze_instance = FileAnalyzer(
            config = AnalyzerConfig(
                data_classification_enabled = scan_info.get("ml_enabled", True),
                standard_analyzer_enabled=standard_analyzer_enabled,
                custom_analyzer_enabled=custom_analyzer_enabled,
                ignore_text_category=configs.get('file_analyzer', {}).get('ignore_text_category', True),
                languages = scan_info.get("languages", ['en']),
                regions = regions if regions else ['US'],
                # scan_category_ids = scan_info.get("scan_category", []),
                entity_ids=standard_entity_ids,
                idm_mapping=idm_mapping,
                nlp_process_size = configs.get('file_analyzer', {}).get('nlp_process_size', 50*1024),
                scan_process_size = configs.get('file_analyzer', {}).get("recognizer_process_size", 50 *1024 * 1024),
                max_num_return_entity = configs.get('file_analyzer', {}).get("max_num_return_entity", 16),
                max_times_per_entity = configs.get('file_analyzer', {}).get("max_times_per_entity", 32),
                phone_matcher_max_retry = configs.get('file_analyzer', {}).get("phone_matcher_max_retry", 1000),
                base64_enabled = configs.get('file_analyzer', {}).get("base64_enabled", True),
                classifier_probability_threshold = classifier_probability_threshold,
                recognizer_score_threshold = recognizer_score_threshold,
                nlp_reload_threshold = configs.get('file_analyzer', {}).get("nlp_reload_threshold", 4000 * 1024 * 1024),
                encrypt_sensitive_data = configs.get('file_analyzer', {}).get("encrypt_sensitive_data", True)
            ),
            global_data = global_data,
            user_defined_datatypes = custom_dtypes,
        )

        # Store the new analyze instance in cache
        global_analyze_ttl_cache[key] = analyze_instance
        global_analyze_ttl_cache[expiration_key] = time.time() + ANALYZE_CACHE_TTL

    # Return the analyze instance
    return analyze_instance

def get_discover_policy_engine(session_key, discover_policy_ids) -> DiscoveryPolicyEngine:
    # Combine task_uuid and thread id to form a unique key
    key = f"{session_key}_{threading.get_ident()}"

    policy_engine = None

    try:
        discover_cache_obj = global_discover_engine_ttl_cache[key]
        policy_engine = discover_cache_obj['engine']
        if policy_engine.update_needed(discover_policy_ids):
            policy_engine = None
            logger.debug("discover policy updated.")
    except KeyError:
        policy_engine = None

    if policy_engine is None: 
        policy_engine = create_policy_engine(discover_policy_ids)
        if policy_engine is not None:
            logger.debug("Creating new discover policy engine.")
            global_discover_engine_ttl_cache[key] = {'engine': policy_engine}

    return policy_engine

def get_ddr_policy_engine(session_key, ddr_policy_ids) -> DDRPolicyEngine:
    # Combine task_uuid and thread id to form a unique key
    key = f"{session_key}_{threading.get_ident()}"

    policy_engine = None

    try:
        discover_cache_obj = global_ddr_engine_ttl_cache[key]
        policy_engine = discover_cache_obj['engine']
        if policy_engine.update_needed(ddr_policy_ids):
            policy_engine = None
            logger.debug("ddr policy updated.")
    except KeyError:
        policy_engine = None

    if policy_engine is None: 
        policy_engine = ddr_create_policy_engine(ddr_policy_ids)
        if policy_engine is not None:
            logger.debug("Creating new ddr policy engine.")
            global_ddr_engine_ttl_cache[key] = {'engine': policy_engine}

    return policy_engine

def get_discover_policy_matched_result(session_key, scan_info, record):
    if scan_info.get('discover_policy') is None:
        logger.info("No discover policy found.")
        return None
    policy_engine = get_discover_policy_engine(session_key, scan_info.get('discover_policy'))
    if policy_engine is None:
        logger.error("Discover policy engine is None.")
        return None
    return policy_engine.match_all(record)

def get_ddr_policy_matched_result(session_key, scan_info, record):
    if scan_info.get('ddr_policy') is None:
        logger.info("No ddr policy found.")
        return None
    policy_engine = get_ddr_policy_engine(session_key, scan_info.get('ddr_policy'))
    if policy_engine is None:
        logger.error("DDR policy engine is None.")
        return None
    return policy_engine.match_all(record)

def is_compliances_none(compliances):
    if compliances is None or len(compliances) == 0:
        return True
    if (compliances.get('ENTITIES') is None or len(compliances.get('ENTITIES')) == 0) and \
        (compliances.get('CUSTOM') is None or len(compliances.get('CUSTOM')) == 0):
        return True
    return False

def is_main_class_none(main_class_name):
    if main_class_name is None or len(main_class_name) == 0 or main_class_name == 'Other':
        return True
    return False

# Default priority is set to 0. Explicitly setting the priority for clarity.
# Higher numbers indicate higher priority.
# We plan to use 100 as maximum priority.
@huey.task(priority=0)
def analyze_worker(local_file, remote_info, file_uuid, scan_info, task_uuid, session_key: str, backlog_hash: str):
    do_analyze(local_file, remote_info, file_uuid, scan_info, task_uuid, session_key, backlog_hash)

@huey.task(priority=90)
def priority_analyze_worker(local_file, remote_info, file_uuid, scan_info, task_uuid, session_key: str, backlog_hash: str):
    do_analyze(local_file, remote_info, file_uuid, scan_info, task_uuid, session_key, backlog_hash)


def do_analyze(local_file, remote_info, file_uuid, scan_info, task_uuid, session_key: str, backlog_hash: str):
    """ 
    extract content & identify content-type

    Args:
        local_file: This is a string that represents the path of the local file to be processed.
        remote_info: This is a dictionary that contains information about the remote server, such as the server's address, port, username, and password.
        file_uuid: This is a string that represents the unique identifier (UUID) of the file.
        scan_info: This is a dictionary that contains the configuration information for the scan, such as the types of files to scan.

    """
    import time
    from file_analyzer.analyzer_target import AnalyzerTarget
    from service.task_management_service import TaskManagementService, SessionExpired
    from file_analyzer.analyzer_result import EmptyResult
    
    cache = None
    updated_result = False
    analyze_statistic = {}

    try:
        logger.debug(f"Analyze worker begin, task: {task_uuid}, pid: {os.getpid()}, remote_info: {remote_info}, local_file: {local_file}, session_key: {session_key}")
        tms = TaskManagementService(task_uuid, session_key)
        if not tms.is_scanning():
            logger.info(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()} is not in SCANNING status.")
            return

        if local_file is None:
            logger.error(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()}, local file argument is None.")
            return

        if not os.path.exists(local_file):
            logger.error(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()}, local file {local_file} does not exist.")
            return
        if not os.access(local_file, os.R_OK):
            logger.error(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()}, no permission to read local file {local_file}.")
            return
        
        cache = GlobalCache(scan_info['scan_uuid'], session_key, is_ddr=scan_info.get("is_ddr", False))
        if not cache.get_by_file_uuid(file_uuid=file_uuid):
            logger.error(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()}, file record not found in db {file_uuid}")
            return

        # extract content & identify content-type
        logger.info(f"process file {local_file} begin, task: {task_uuid} pid: {os.getpid()}")
        logger.debug(f"real file name: {remote_info.get('file')}, task: {task_uuid} pid: {os.getpid()}" )
                
        analyze_statistic = {}
                
        ts_start = time.time()
        file_info= get_file_info(local_file, remote_info['file'], remote_info['file_type'], configs.get("file_extract", {}))
        content = file_info['file_content']
        if content is None or len(content.strip()) == 0:
            logger.error(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()}, file {local_file} has no content. Skipping further processing.")
            analyze_statistic["message"] = "no content"
            return
        
        ts_start_get_analyzer = time.time()
        analyzer = get_analyze(task_uuid=task_uuid, scan_info=scan_info, session_key=session_key, load_on_miss=False)
        if isinstance(content, bytes):
            content = content.decode('utf-8', 'ignore')
            
        ts_analyze = time.time()
        result = analyzer.analyze(AnalyzerTarget(
            text=content, 
            source=local_file, 
            source_type=file_info['file_type'],
            source_uuid=file_uuid,
            ),
            expected_entities=[],
            encrypt_function=RandomPasswordService.encrypt_analyzer_text)
        
        if result is None:
            logger.error(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()}, analyzer returned None. Skipping further processing for this file.")
            analyze_statistic["message"] = "analyze worker end"
            return
        
        ts_analyze_end = time.time()
        logger.debug(f"Analysis result: {result}, task: {task_uuid} pid: {os.getpid()}")

        record = {
            "file_info" : {
                "file_name": remote_info['file'],
                "mime_type": remote_info['file_type'],
                "encryption": remote_info.get("file_encryption", False),
                "file_location": remote_info.get("file_location", "UNKNWON"),
                "shared_data": remote_info.get("shared_data") if remote_info.get("shared_data") else {}
            },
            "result": result,
            "idm": set(),
            "edm": set()
        }
        if scan_info.get("is_ddr", False):
            matched_result = get_ddr_policy_matched_result(task_uuid, scan_info, record)
        else:
            matched_result = get_discover_policy_matched_result(task_uuid, scan_info, record)
        tags = matched_result['tags']
        ts_update_cache = time.time()
        
        time_statistic_enable = configs.get("file_analyzer", {}).get("time_statistic", False)
        if time_statistic_enable:
            # analyze_statistic["ts_start"] = ts_start
            # analyze_statistic["ts_get_analyzer"] = ts_start_get_analyzer
            # analyze_statistic["ts_analyze"] = ts_analyze
            # analyze_statistic["ts_analyze_end"] = ts_analyze_end
            # analyze_statistic["ts_update_cache"] = ts_update_cache

            analyze_statistic["ex_time"] = round(ts_start_get_analyzer-ts_start, 3) # extract
            analyze_statistic["ga_time"] = round(ts_analyze-ts_start_get_analyzer, 3) # get analyzer
            analyze_statistic["an_time"] = round(ts_analyze_end-ts_analyze, 3) # analyze file including both file classification and data type scan
            analyze_statistic["ml_time"] = result.ml_elapsed if hasattr(result, 'ml_elapsed') else 0 # file classification
            analyze_statistic["re_time"] = result.an_elapsed if hasattr(result, 'an_elapsed') else 0 # data type scan
            analyze_statistic["ma_time"] = round(ts_update_cache-ts_analyze_end, 3) # match policies
            analyze_statistic["total"] = round(ts_update_cache-ts_start, 3) # total time

        idm_match_info = get_idm_match_info(idm_ids=list(record.get("idm")), similarities=result.idm_res)
        edm_match_info = get_edm_match_info(edm_rule_ids=list(record.get("edm")))
        matched_result["edm_match_info"] = edm_match_info
        matched_result["idm_match_info"] = idm_match_info
        update_cache_result(cache, file_uuid, session_key, scan_info, result, remote_info, analyze_statistic, tags, edm_match_info=edm_match_info, idm_match_info=idm_match_info)
        updated_result = True

        remediation_actions = matched_result['remediation_actions']
        local_file_copy = None
        if "file_copy" in remediation_actions or "file_quarantine" in remediation_actions:
            local_file_copy = copy_local_file_to_protection_folder(task_uuid, local_file)
        do_discover_actions(task_uuid, file_uuid, local_file_copy, file_info, scan_info, remote_info, matched_result)
                
    except SessionExpired as e:
        logger.error(f"Analyze worker end, task: {task_uuid} pid: {os.getpid()} session_key: {session_key} session expired!")
        raise CancelExecution()
    except KeyboardInterrupt as e:
        # Huey's signal handling is compromised, so we need to handle SIGTERM
        # within the KeyboardInterrupt exception. 
        logger.exception(f"Analyze worker end, task: {task_uuid}, pid: {os.getpid()}, KeyboardInterrupt happen")
        handle_interrupt(local_file, remote_info, file_uuid, scan_info, task_uuid, session_key, backlog_hash)
    except:
        logger.exception(f"Analyze worker end, error processing file: {local_file}, task: {task_uuid}, pid: {os.getpid()}")
    finally:
        if cache and not updated_result:
            update_cache_result(cache, file_uuid, session_key, scan_info, EmptyResult(), remote_info, analyze_statistic, None)

        if is_file_unified:
            try:
                from huey_worker.unified_file_process import handle_ddr_analyze_complete, handle_scan_policy_analyze_complete
                if scan_info.get("is_ddr", False):
                    # DDR finished
                    activity_id = scan_info.get("activity_id", "")
                    if activity_id:
                        result_data = {
                            "error": not updated_result,
                            "analyze_statistic": analyze_statistic,
                            "updated_result": updated_result
                        }
                        handle_ddr_analyze_complete.schedule(
                            args=(task_uuid, activity_id, result_data),
                            delay=1,
                            priority=70
                        )
                else:
                    # scan_policy finished
                    result_data = {
                        "error": not updated_result,
                        "analyze_statistic": analyze_statistic,
                        "updated_result": updated_result
                    }
                    handle_scan_policy_analyze_complete.schedule(
                        args=(task_uuid, remote_info, result_data),
                        delay=1,
                        priority=50
                    )
            except Exception as e:
                logger.error(f"Error notifying analyze completion: {e}")

        Path(local_file).unlink(missing_ok=True)


def file_upload_update_cache_result(cache, file_uuid, remote_info, scan_info, result, analyze_statistic, tags, edm_match_info = None, idm_match_info=None):
    match_info = []
    match_info_custom = []
    if result.compliances is not None:
        match_info = result.compliances.get('ENTITIES')
        match_info_custom = result.compliances.get('CUSTOM')

    class_details = {}
    class_details['mc_confidence'] = result.main_class_confidence
    class_details['sc_confidence'] = result.sub_class_confidence
    class_details['main_class_list'] = [result.main_class_id] if result.main_class_id else []
    class_details['main_confidence_list'] = [result.main_class_confidence] if result.main_class_id else []
    class_details['sub_class_list'] = [result.sub_class_id] if result.sub_class_id else []
    class_details['sub_confidence_list'] = [result.sub_class_confidence] if result.sub_class_id else []

    payload = {
        'id': file_uuid,
        'scan_uuid': scan_info['scan_uuid'], 
        'full_path': remote_info['file'],
        'file_name': remote_info['file_name'],
        'main_class_id': result.main_class_id if result.main_class_id else None,
        'sub_class_id': result.sub_class_id if result.sub_class_id else None,
        'class_details': class_details,
        'match_info': match_info if match_info else None,
        'match_info_custom': match_info_custom if match_info_custom else None,
        'reserve_json1': analyze_statistic or {},
        'tm': get_utc_timestamp(),
        'file_hash': remote_info['file_hash'],
        'file_attributes': remote_info['file_attributes'],
        'storage_type': remote_info['storage_type'],
        'last_scan_tm': get_utc_timestamp()
    }

    if edm_match_info:
        payload["reserve_json1"]["edm_match_info"] = edm_match_info
    if idm_match_info:
        payload["reserve_json1"]["idm_match_info"] = idm_match_info

    payload["reserve_json1"]["sensitive_data"] = False
    payload["reserve_json1"]["compliance_data"] = False

    # Only update the file tag if we have tags to update
    if tags:
        payload['file_tag'] = tags
        payload['tag_tm'] = get_utc_timestamp()

        compliance_labels = global_data.analyzer_global_path.get_sensitive_label_config().get("compliance_data", [])
        sensitive_labels = global_data.analyzer_global_path.get_sensitive_label_config().get("sensitive_data", [])
        payload["reserve_json1"]["compliance_data"] = any(item in compliance_labels for item in tags["predefine"])
        payload["reserve_json1"]["sensitive_data"] = any(item in sensitive_labels for item in tags["predefine"])

    cache.put(payload, update_only=True)
    

def get_file_upload_analyze(task_uuid, scan_info, load_on_miss=True):
    from file_analyzer import FileAnalyzer, AnalyzerConfig
    from file_analyzer.analyzer_data_types import AnalyzerDataTypes

    key = f"{task_uuid}_{threading.get_ident()}"
    scan_info_key = f"{key}_scan_info"

    # Add extended information into original scan_info
    standard_entity_ids, custom_entity_ids, idm_mapping = get_entity_by_policy(policy_ids=scan_info.get("discover_policy",[]))
    try:
        custom_dtypes = UserDefineDatatypeService.get_user_define_dtype_by_ids(ids=custom_entity_ids)
    except Exception as e:
        logger.error(f"Get user define dtyoe by custom entity ids failed: {e}")
        custom_dtypes = []
    scan_info["extended_entity_info"] = {
        "standard_entity_ids": standard_entity_ids,
        "custom_dtypes": custom_dtypes,
        "idm_mapping": idm_mapping
    }

    # Get reserved scan_info from cache
    try:
        analyze_instance = None
        cached_scan_info = file_upload_analyze_cache[scan_info_key]
        logger.info(f"[File upload analyzer] result {scan_info == cached_scan_info}")
        if cached_scan_info == scan_info:
            analyze_instance = file_upload_analyze_cache[key]
    except KeyError:
        logger.info(f"[File upload analyzer] scan_info not found in cache")

    # Create reserved analyze instance if cache miss or scan config changed
    if analyze_instance is None:
        if not load_on_miss:
            raise Exception("File upload analyze instance not found in cache")
        logger.info("Creating new File upload analyze instance.")
        standard_analyzer_enabled = False if not standard_entity_ids else True
        custom_analyzer_enabled = False if not custom_dtypes else True

        regions = []
        region_info, e = AnalyzerDataTypes(global_path=global_data.analyzer_global_path).get_supported_regions()
        if e is not None:
            logger.error(f"get region info failed: {e}")
        else:
            for continent in scan_info.get("regions", []):
                if "continent" in region_info and continent in region_info["continent"]:
                    regions.extend(region_info["continent"][continent]["country"].keys())
                    
        classifier_probability_threshold, recognizer_score_threshold = get_analyze_threshold(scan_info)
                    
        analyze_instance = FileAnalyzer(
            config = AnalyzerConfig(
                data_classification_enabled = scan_info.get("ml_enabled", True),
                standard_analyzer_enabled=standard_analyzer_enabled,
                custom_analyzer_enabled=custom_analyzer_enabled,
                ignore_text_category=configs.get('file_analyzer', {}).get('ignore_text_category', True),
                languages = scan_info.get("languages", ['en']),
                regions = regions if regions else ['US'],
                entity_ids=standard_entity_ids,
                idm_mapping=idm_mapping,
                nlp_process_size = configs.get('file_analyzer', {}).get('nlp_process_size', 50*1024),
                scan_process_size = configs.get('file_analyzer', {}).get("recognizer_process_size", 50 *1024 * 1024),
                max_num_return_entity = configs.get('file_analyzer', {}).get("max_num_return_entity", 16),
                max_times_per_entity = configs.get('file_analyzer', {}).get("max_times_per_entity", 32),
                phone_matcher_max_retry = configs.get('file_analyzer', {}).get("phone_matcher_max_retry", 1000),
                base64_enabled = configs.get('file_analyzer', {}).get("base64_enabled", True),
                classifier_probability_threshold = classifier_probability_threshold,
                recognizer_score_threshold = recognizer_score_threshold,
                nlp_reload_threshold = configs.get('file_analyzer', {}).get("nlp_reload_threshold", 4000 * 1024 * 1024),
                encrypt_sensitive_data = configs.get('file_analyzer', {}).get("encrypt_sensitive_data", True)
            ),
            global_data = global_data,
            user_defined_datatypes = custom_dtypes,
        )

        # update reserved analyze cache
        file_upload_analyze_cache[key] = analyze_instance
        file_upload_analyze_cache[scan_info_key] = scan_info
    return analyze_instance

# Default priority is set to 0. Explicitly setting the priority for clarity.
# Higher numbers indicate higher priority.
# We plan to use 100 as maximum priority.
@huey.task(priority=100)
def file_upload_analyze_worker(local_file, remote_info, file_uuid, scan_info, task_uuid):
    """ 
    extract content & identify content-type

    Args:
        local_file: This is a string that represents the path of the local file to be processed.
        file_uuid: This is a string that represents the unique identifier (UUID) of the file.
        remote_info: This is a dictionary that contains information about the remote info.
        scan_info: This is a dictionary that contains the scan info.
    """
    import time
    from file_analyzer.analyzer_target import AnalyzerTarget
    from service.task_management_service import TaskManagementService, SessionExpired
    from file_analyzer.analyzer_result import EmptyResult

    cache = None
    updated_result = False
    analyze_statistic = {}

    try:
        logger.debug(f"File upload analyze current process begin, pid: {os.getpid()}, local_file: {local_file}, file_uuid: {file_uuid}, remote_info: {remote_info}, scan_info: {scan_info}")
        if local_file is None:
            logger.error("File upload analyze error, Local file argument is None.")
            return

        if not os.path.exists(local_file):
            logger.error(f"File upload analyze error, Local file {local_file} does not exist.")
            return
        if not os.access(local_file, os.R_OK):
            logger.error(f"File upload analyze error, No permission to read local file {local_file}.")
            return
        
        cache = GlobalCache(task_uuid)
        if not cache.get_by_file_uuid(file_uuid=file_uuid):
            logger.error(f"File upload analyze error, file record not found in db {file_uuid}")
            return

        # extract content & identify content-type
        logger.info(f"File upload analyze, process file {local_file} begin")
        logger.debug(f"File upload analyze, real file name: {remote_info['file']}")
                
        ts_start = time.time()
        file_info= get_file_info(local_file, remote_info['file'], remote_info['file_type'], configs.get("file_extract", {}))
        content = file_info['file_content']
        if content is None or len(content.strip()) == 0:
            logger.error(f"File upload analyze error, File {local_file} has no content. Skipping further processing.")
            analyze_statistic["message"] = "no content"
            return
        
        ts_start_get_analyzer = time.time()
        analyzer = get_file_upload_analyze(task_uuid=task_uuid, scan_info=scan_info)
        if isinstance(content, bytes):
            content = content.decode('utf-8', 'ignore')
            
        ts_analyze = time.time()
        result = analyzer.analyze(AnalyzerTarget(
            text=content, 
            source=local_file, 
            source_type=file_info['file_type'],
            source_uuid=file_uuid,
            ),
            expected_entities=[],
            encrypt_function=RandomPasswordService.encrypt_analyzer_text)
        
        if result is None:
            logger.error("File upload analyze error, Analyzer returned None. Skipping further processing for this file.")
            analyze_statistic["message"] = "file upload analyze error"
            return
        
        ts_analyze_end = time.time()
        logger.debug(f"File upload analyze, Analysis result: {result}")

        record = {
            "file_info" : {
                "file_name": remote_info['file'],
                "mime_type": remote_info['file_type'],
                "encryption": remote_info.get("file_encyption"),
                "file_location": remote_info.get("file_location"),
                "shared_data": remote_info.get("shared_data", {})
            },
            "result": result,
            "idm": set(),
            "edm": set()
        }
        matched_result = get_discover_policy_matched_result(task_uuid, scan_info, record)
        tags = matched_result['tags']
        ts_update_cache = time.time()
        
        time_statistic_enable = configs.get("file_analyzer", {}).get("time_statistic", False)
        if time_statistic_enable:
            analyze_statistic["ex_time"] = round(ts_start_get_analyzer-ts_start, 3) # extract
            analyze_statistic["ga_time"] = round(ts_analyze-ts_start_get_analyzer, 3) # get analyzer
            analyze_statistic["an_time"] = round(ts_analyze_end-ts_analyze, 3) # analyze file including both file classification and data type scan
            analyze_statistic["ml_time"] = result.ml_elapsed if hasattr(result, 'ml_elapsed') else 0 # file classification
            analyze_statistic["re_time"] = result.an_elapsed if hasattr(result, 'an_elapsed') else 0 # data type scan
            analyze_statistic["ma_time"] = round(ts_update_cache-ts_analyze_end, 3) # match policies
            analyze_statistic["total"] = round(ts_update_cache-ts_start, 3) # total time

        idm_match_info = get_idm_match_info(idm_ids=list(record.get("idm")), similarities=result.idm_res)
        edm_match_info = get_edm_match_info(edm_rule_ids=list(record.get("edm")))
        file_upload_update_cache_result(cache, file_uuid, remote_info, scan_info, result, analyze_statistic, tags, edm_match_info=edm_match_info, idm_match_info=idm_match_info)
        updated_result = True
    except:
        logger.exception(f"File upload analyze error, processing file: {local_file}")
    finally:
        if cache and not updated_result:
            file_upload_update_cache_result(cache, file_uuid, remote_info, scan_info, EmptyResult(), analyze_statistic, None)
            
        if "file_hash" in remote_info:
            flag_filename = remote_info["file_name"] + "-" + remote_info["file_hash"] + ".flagfile"
            flag_file = Path(configs["h2"]["upload_folder"]) / flag_filename
            flag_file.unlink(missing_ok=True)

        Path(local_file).unlink(missing_ok=True)
        logger.info(f"File upload analyze current process end, pid: {os.getpid()}, local_file: {local_file}")

def handle_interrupt(local_file, remote_info, file_uuid, scan_info, task_uuid, session_key, backlog_hash):
    """
    Handle the interrupt signal during the file analysis process.

    This function is called when an interrupt signal (e.g., SIGTERM) is received during the file analysis process.
    Normally, SIGTERM is handled by the right_after_analyze_interrupted function, which is registered to the
    SIGNAL_INTERRUPTED signal. However, since Huey's signal handling is compromised, this function is still 
    needed.

    Args:
        local_file (str): The path to the local file being analyzed.
        remote_info (dict): Information about the remote file.
        file_uuid (str): The unique identifier of the file.
        scan_info (dict): Information about the scan.
        task_uuid (str): The unique identifier of the task.
        session_key (str): The session key for the task.
        backlog_hash (str): The hash of the backlog.
    """
    logger.debug(f"Analyze worker interrupt begin: local_file: {local_file}, remote_info: {remote_info} task: {task_uuid}, session_key: {session_key}, pid: {os.getpid()}")
    from domain_model.tracker.timeout_task_tracker import TimeoutTaskTracker
    from exts import SessionExpired
    from service.task_management_service import TaskManagementService
    from util.enum_ import HueyTaskStage
    from file_analyzer.analyzer_result import EmptyResult

    try:
        timeout_tracker = TimeoutTaskTracker()
        tms = TaskManagementService(task_uuid, session_key)
        if not tms.is_scanning():
            logger.info(f"Analyze worker interrupt end, task: {task_uuid}, pid: {os.getpid()} is not in SCANNING status.")
            return 

        is_timeout_task, created_at = timeout_tracker.is_timeout_task(
            task_uuid,
            session_key,
            HueyTaskStage.IN_ANALYZE,
            backlog_hash,
        )
        if is_timeout_task:
            analyze_statistic = {
                "analyze_execute_at": str(created_at),
                "analyze_interrupt_at": str(get_utc_timestamp()),
            }
            
            cache = GlobalCache(scan_info['scan_uuid'], session_key, is_ddr=scan_info.get("is_ddr", False))
            update_cache_result(
                cache=cache,
                file_uuid=file_uuid,
                session_key=session_key,
                scan_info=scan_info,
                result=EmptyResult(),
                remote_info=remote_info,
                analyze_statistic=analyze_statistic,
                tags=None,
            )
            logger.debug(f"Analyze worker interrupt end, task: {task_uuid} is timeout already, pid: {os.getpid()}.")
            return                

        logger.info(f"Analyze worker interrupt, add to huey enqueue again, task: {task_uuid}, pid: {os.getpid()}")
        flag = False
        for i in range(5):
            res = analyze_worker(
                local_file=local_file,
                remote_info=remote_info,
                file_uuid=file_uuid,
                scan_info=scan_info,
                task_uuid=task_uuid,
                session_key=session_key,
                backlog_hash=backlog_hash,
            )
            
            for item in huey.storage.enqueued_items():
                decoded_item = pickle.loads(item)
                if decoded_item.id == res.id:
                    flag = True
                    break
                
            if flag == True:                    
                break
            else:
                logger.debug(f"Analyze worker interrupt, add to huey enqueue again failed, try count: {i+1}, pid: {os.getpid()}")
                time.sleep(0.5)

        if flag == False:                
            tms.analyze_worker_tracker.end_track_with_error(session_key=session_key, backlog_hash=backlog_hash)
            logger.info(f"Analyze worker interrupt, add to huey enqueue again failed, pid: {os.getpid()}")
        else:                        
            tms.analyze_worker_tracker.stop_track_without_increment_counter(session_key=session_key, backlog_hash=backlog_hash)
            logger.info(f"Analyze worker interrupt, add to huey enqueue again success, id: {res.id}, pid: {os.getpid()}")  

    except SessionExpired as e:
        logger.info(f"Analyze worker interrupt end, task: {task_uuid}, pid: {os.getpid()} session expired!")
    except Exception as e:
        logger.error(f"Analyze worker interrupt end, task: {task_uuid}, pid: {os.getpid()}, Exception: {e}")
    finally:
        huey.storage.flush_results()
        pid_tracker = AnalyzeWorkerPidTracker()
        pid_tracker.delete(str(os.getpid()))            
        sys.exit(0)
    
@huey.signal(SIGNAL_EXECUTING)
def right_before_execute(signal, task, exc=None):
    """
    Signal handler executed right before a task is executed.

    This function is called when a task is about to be executed. It sets up tracking
    for analyze worker tasks, including recording the process ID and starting the
    analyze track in the TaskManagementService.

    Args:
        signal: The signal that triggered this handler.
        task: The task object that is about to be executed.
        exc (Exception, optional): Any exception that occurred, if applicable.
    """
    from service.task_management_service import TaskManagementService, SessionExpired

    try:
        logger.info(f"Analyze worker SIGNAL_EXECUTING begin, task: {task.name}, task.kwargs: {task.kwargs} pid: {os.getpid()}")
        if task.name == "analyze_worker":
            logger.debug(f"right_before_execute: {task.kwargs}, pid: {os.getpid()}")
            pid_tracker = AnalyzeWorkerPidTracker()
            pid_tracker.set(str(os.getpid()), task.kwargs["task_uuid"])
            scan_policy_id = task.kwargs["task_uuid"]
            session_key = task.kwargs["session_key"]
            backlog_hash = task.kwargs["backlog_hash"]  
            get_analyze(
                task_uuid=scan_policy_id, 
                scan_info=task.kwargs["scan_info"], 
                session_key=session_key, 
                task_timeout=configs['huey']['analyze_task_timeout'],
                load_on_miss=True
            )
            TaskManagementService(scan_policy_id, session_key).start_analyze_track(task.kwargs, backlog_hash)
    except SessionExpired as e:
        logger.info(f"Analyze worker SIGNAL_EXECUTING end, task: {task.name}, pid: {os.getpid()}, session expired!")
        raise CancelExecution()
    except Exception as e:
        logger.info(f"Analyze worker SIGNAL_EXECUTING end, task: {task.name}, pid: {os.getpid()}, Exception: {e}")

@huey.signal(SIGNAL_COMPLETE)
def increment_counter_when_completed(signal, task, exc=None):
    """
    Signal handler for when an analyze_worker task is completed.

    This function is called when an analyze_worker task completes its execution successfully. 
    It attempts to end the analyze track, remove the task result from Redis, and delete the 
    process ID from the tracker.

    Args:
        signal: The signal that triggered this handler.
        task: The task object that was completed.
        exc (Exception, optional): The exception that occurred during task execution, if any.
    """
    from service.task_management_service import TaskManagementService, SessionExpired
    
    try:
        logger.info(f"Analyze worker SIGNAL_COMPLETE begin, task: {task.name}, task.kwargs: {task.kwargs} pid: {os.getpid()}")
        if task.name == "analyze_worker":
            logger.debug(f"increment_counter_when_completed: {task.kwargs}, pid: {os.getpid()}")
            scan_policy_id = task.kwargs["task_uuid"]
            session_key = task.kwargs["session_key"]
            backlog_hash = task.kwargs["backlog_hash"]
            TaskManagementService(scan_policy_id, session_key).end_analyze_track(backlog_hash)
            pid_tracker = AnalyzeWorkerPidTracker()
            pid_tracker.delete(str(os.getpid()))
    except SessionExpired as e:
        logger.info(f"Analyze worker SIGNAL_COMPLETE end, task: {task.name}, pid: {os.getpid()}, session expired!")
    except KeyError as e:   
        logger.info(f"Analyze worker SIGNAL_COMPLETE end, task: {task.name}, pid: {os.getpid()}, KeyError: {e}")
    except Exception as e:
        logger.info(f"Analyze worker SIGNAL_COMPLETE end, task: {task.name}, pid: {os.getpid()}, Exception: {e}")


@huey.signal(SIGNAL_ERROR)
def increment_counter_when_failed(signal, task, exc=None):
    """
    Signal handler for when an analyze_worker task encounters an error.

    This function is called when an analyze_worker task fails during execution. 
    It attempts to end the analyze track, remove the task result from Redis, 
    and delete the process ID from the tracker.

    Args:
        signal: The signal that triggered this handler.
        task: The task object that encountered an error.
        exc (Exception, optional): The exception that caused the task to fail, if any.
    """
    from service.task_management_service import TaskManagementService, SessionExpired

    try:
        logger.info(f"Analyze worker SIGNAL_ERROR begin, task: {task.name}, task.kwargs: {task.kwargs} pid: {os.getpid()}")
        if task.name == "analyze_worker":
            logger.debug(f"increment_counter_when_failed: {task.kwargs}, pid: {os.getpid()}")
            scan_policy_id = task.kwargs["task_uuid"]
            session_key = task.kwargs["session_key"]
            backlog_hash = task.kwargs["backlog_hash"]
            TaskManagementService(scan_policy_id, session_key).end_analyze_track(backlog_hash)
            pid_tracker = AnalyzeWorkerPidTracker()
            pid_tracker.delete(str(os.getpid()))
    except SessionExpired as e:
        logger.info(f"Analyze worker SIGNAL_ERROR end, task: {task.name}, pid: {os.getpid()}, session expired!")
    except KeyError as e:
        logger.info(f"Analyze worker SIGNAL_COMPLETE end, task: {task.name}, pid: {os.getpid()}, KeyError: {e}")           
    except Exception as e:
        logger.info(f"Analyze worker SIGNAL_COMPLETE end, task: {task.name}, pid: {os.getpid()}, Exception: {e}")



@huey.signal(SIGNAL_INTERRUPTED)
def right_after_analyze_interrupted(signal, task, exc=None):
    """
    Signal handler for when an analyze_worker task is interrupted.

    This function is called when an analyze_worker task is interrupted during execution.
    It attempts to retry the task if it's still in SCANNING status, remove the task result
    from Redis, and clean up the process ID from the tracker.

    Args:
        signal: The signal that triggered this handler.
        task: The task object that was interrupted.
        exc (Exception, optional): The exception that caused the interruption, if any.
    """
    from domain_model.tracker.timeout_task_tracker import TimeoutTaskTracker
    from service.task_management_service import TaskManagementService, SessionExpired
    from util.enum_ import HueyTaskStage
    from file_analyzer.analyzer_result import EmptyResult

    try:
        logger.debug(f"right_after_analyze_interrupted: {task.kwargs}, pid: {os.getpid()}")
        timeout_tracker = TimeoutTaskTracker()

        if task.name == "analyze_worker":
            scan_policy_id = task.kwargs["task_uuid"]
            session_key = task.kwargs["session_key"]
            tms = TaskManagementService(scan_policy_id, session_key)
            if not tms.is_scanning():
                logger.info(
                    f"Task {scan_policy_id} is not in SCANNING status."
                )
                return 

            is_timeout_task, created_at = timeout_tracker.is_timeout_task(
                task.kwargs["task_uuid"],
                task.kwargs["session_key"],
                HueyTaskStage.IN_ANALYZE,
                task.kwargs["backlog_hash"],
            )
            if is_timeout_task:
                analyze_statistic = {
                    "analyze_execute_at": str(created_at),
                    "analyze_interrupt_at": str(get_utc_timestamp()),
                }

                class Result:
                    def __init__(self):
                        self.compliances = None
                        self.main_class_id = ""
                        self.sub_class_id = ""

                cache = GlobalCache(task.kwargs["scan_info"]['scan_uuid'], session_key, is_ddr=task.kwargs["scan_info"].get("is_ddr", False))
                update_cache_result(
                    cache=cache,
                    file_uuid=task.kwargs["file_uuid"],
                    session_key=task.kwargs["session_key"],
                    scan_info=task.kwargs["scan_info"],
                    result=EmptyResult(),
                    remote_info=task.kwargs["remote_info"],
                    analyze_statistic=analyze_statistic,
                    tags=None,
                )
                return

            analyze_worker(
                local_file=task.kwargs["local_file"],
                remote_info=task.kwargs["remote_info"],
                file_uuid=task.kwargs["file_uuid"],
                scan_info=task.kwargs["scan_info"],
                task_uuid=task.kwargs["task_uuid"],
                session_key=task.kwargs["session_key"],
                backlog_hash=task.kwargs["backlog_hash"],
            )
            tms.analyze_worker_tracker.stop_track_without_increment_counter(
                session_key=task.kwargs["session_key"],
                backlog_hash=task.kwargs["backlog_hash"],
            )
    except SessionExpired as e:
        logger.info(f"Task {task.name} {task.kwargs['task_uuid']} session expired!")
    except KeyError as e:
        logger.error(e)           
    except Exception as e:
        logger.error(e)
    finally:
        logger.info(f"Interrupted task {task.kwargs['task_uuid']}: PID {os.getpid()} of analyze_worker will be retried.")
        huey.storage.flush_results()
        pid_tracker = AnalyzeWorkerPidTracker()
        pid_tracker.delete(str(os.getpid()))        
        sys.exit(0)
