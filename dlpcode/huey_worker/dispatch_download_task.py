import hashlib
import json
import threading
from domain_model.huey_task_counter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from domain_model.task_queue import ChangeResultEvent, ChangeStatusEvent
from exts import logger, Session, get_logger
from huey import RedisHuey
from huey_worker.doing_download_task import (
    add_analyze_task,
    download_file,
    download_huey,
)
from huey.exceptions import CancelExecution
from huey.signals import (
    SIGNAL_COMPLETE,
    SIGNAL_ERROR,
    SIGNAL_INTERRUPTED,
)
from itertools import islice
from pathlib import Path
from service.task_management_service import TaskManagementService, SessionExpired
from service.task_queue_service import TaskQueueService
from util.config import configs, get_supported_file_type
from util.enum_ import ScanResult, TaskStatus
import linecache
import time
import os
import sys
import pickle

skip_files = get_logger("skip_files")
logger = get_logger("dispatch_download")
dispatch_huey = RedisHuey("dispatch-download-task", db=configs["huey"]["db"])
is_file_unified = configs.get("file_unified_queue", {}).get("enabled", False)
null_result = {
    "task_uuid": None,
    "group_file_path": None,
    "file_path": None,
    "params": None,
    "session_key": None,
}

completed = "COMPLETED"
completed_result = {
    "task_uuid": completed,
    "group_file_path": completed,
    "file_path": completed,
    "params": completed,
    "session_key": completed,
}

@dispatch_huey.task()
def generate_queue_group_file(task_uuid: str, params: dict, session_key: str) -> dict:
    """
    Generates a queue group for a given task.

    Args:
        task_uuid (str): The UUID of the task.
        params (dict): Additional parameters for the task.
        session_key (str): The session key for the task.

    Returns:
        dict: A dictionary containing the task UUID, file path, parameters, and session key.
    """
    try:
        logger.info(f"Dispatch download generate group begin, pid: {os.getpid()} task: {task_uuid} session: {session_key}.")
        tms = TaskManagementService(task_uuid, session_key)
        tqs = TaskQueueService()

        _, result = tqs.add_priority_event(
            ChangeStatusEvent(
                scan_policy_id=task_uuid,
                status=TaskStatus.FETCHING,
                session_key=session_key,
                wait_for_result=1,
            )
        )

        if not result:
            logger.error(f"Dispatch download generate group end, error in Wait result failed while setting FETCHING status, task: {task_uuid}, pid: {os.getpid()}.")
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Error occurred while setting FETCHING status",
                    session_key=session_key,
                )
            )
            return null_result

        connector = tms.get_connector()
        if not connector.test_connection():
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Failed to connect to storage",
                    session_key=session_key,
                )
            )
            logger.error(f"Dispatch download generate group end, error in test connection, task: {task_uuid}, pid: {os.getpid()}.")
            return null_result

        persist_path, total_count = tms.generate_task_queue_group_file()
        if persist_path is None:
            logger.error(f"Dispatch download generate group end, error in generating queue group for task: {task_uuid}, pid: {os.getpid()}.")
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Error occurred while generating download group list",
                    session_key=session_key,
                )
            )
            return null_result

        if total_count == 0:
            logger.info(f"Dispatch download generate group end, No group to scan for task: {task_uuid}, pid: {os.getpid()}.")
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.COMPLETED,
                    session_key=session_key,
                )
            )
            return completed_result

        tms.init_global_trackers(total_count)
        _, result = tqs.add_priority_event(
            ChangeStatusEvent(
                scan_policy_id=task_uuid,
                status=TaskStatus.SCANNING,
                session_key=session_key,
                wait_for_result=1,
            )
        )

        if not result:
            logger.error(f"Dispatch download generate group end, error in Wait result failed while setting SCANNING status, task: {task_uuid}, pid: {os.getpid()}.")
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Error occurred while setting SCANNING status",
                    session_key=session_key,
                )
            )
            return null_result

        return {
            "task_uuid": task_uuid,
            "group_file_path": persist_path,
            "file_path": "",
            "params": params,
            "session_key": session_key,
        }
    except SessionExpired as e:
        logger.info(f"Dispatch download generate group end, task: {task_uuid}, pid: {os.getpid()}, session: {session_key} session expired!")
        raise CancelExecution()
    except Exception as e:
        logger.error(f"Dispatch download generate group end, task: {task_uuid}, pid: {os.getpid()}, Exception: {e}")
        return null_result

def add_backlogs_to_queue(scan_policy_id: str, session_key: str) -> bool:
    import json
    from domain_model.huey_task_backlog import get_backlogs

    try:
        if is_file_unified:
            # Use unified file scheduler to schedule scan_policy files
            from service.unified_file_service import UnifiedFileScheduler, ScanPolicyFileItem

            scheduler = UnifiedFileScheduler()
            backlogs = get_backlogs(scan_policy_id, session_key)

            for backlog in backlogs:
                logger.debug(f"Dispatch download, pid: {os.getpid()}, Adding backlog to unified file scheduler: {backlog}")

                # Create scan_policy file item
                scan_policy_item = ScanPolicyFileItem(
                    task_uuid=str(backlog.scan_policy_id),
                    file_info=backlog.file_info,
                    params=json.loads(backlog.params),
                    session_key=backlog.session_key,
                    backlog_hash=backlog.backlog_hash
                )

                # Add to unified file scheduler
                success = scheduler.add_scan_policy_file(scan_policy_item)
                if not success:
                    logger.warning(f"Failed to add scan_policy file to unified file scheduler: {backlog.file_info}")
                else:
                    logger.debug(f"Successfully added scan_policy file to unified file scheduler: {backlog.file_info}")
        else:
            backlogs = get_backlogs(scan_policy_id, session_key)
            for backlog in backlogs:
                logger.debug(f"Dispatch download, pid: {os.getpid()}, Adding backlog to queue: {backlog}")
                download_file_task = download_file.s(
                    task_uuid=str(backlog.scan_policy_id),
                    file_info=backlog.file_info,
                    params=json.loads(backlog.params),
                    session_key=backlog.session_key,
                    backlog_hash=backlog.backlog_hash
                )
                pipeline = download_file_task.then(add_analyze_task, previous_task_id=download_file_task.id)
                download_huey.enqueue(pipeline)
    except Exception as e:
        logger.error(f"Dispatch download, pid: {os.getpid()}, Exception: {e}")

@dispatch_huey.task()
def generate_queue_file(task_uuid: str, group_file_path: str, file_path: str, params: dict, session_key: str) -> dict:
    """
    Generates a queue file for a given task.

    Args:
        task_uuid (str): The UUID of the task.
        params (dict): Additional parameters for the task.
        session_key (str): The session key for the task.

    Returns:
        dict: A dictionary containing the task UUID, file path, parameters, and session key.
    """
    try:
        logger.info(f"Dispatch download generate file begin, pid: {os.getpid()} task: {task_uuid} session: {session_key}.")
        tqs = TaskQueueService()

        params_to_check = {
            "task_uuid": task_uuid,
            "group_file_path": group_file_path,
            "file_path": file_path,
            "params": params,
            "session_key": session_key,
        }

        if params_to_check == completed_result:
            logger.debug(f"Dispatch download generate file end, task: {task_uuid}, pid: {os.getpid()} have completed.")
            return completed_result

        if params_to_check == null_result:
            logger.error(f"Dispatch download generate file end, error occurred while checking the params of generate queue file task: {task_uuid}, pid: {os.getpid()}, params: {params_to_check}.")
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Error occurred while checking the params of generate queue file task",
                    session_key=session_key,
                )
            )
            return null_result

        tms = TaskManagementService(task_uuid, session_key)
        tms.init_trackers(-999)

        if not tms.is_scanning():
            logger.error(f"Dispatch download generate file end, task: {task_uuid}, pid: {os.getpid()} is not in SCANNING status.")
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Error occurred while checking the status of generate queue file task",
                    session_key=session_key,
                )
            )
            return null_result

        connector = tms.get_connector()
        if not connector.test_connection():
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Failed to connect to storage",
                    session_key=session_key,
                )
            )
            logger.error(f"Dispatch download generate file end, error in test connection, task: {task_uuid}, pid: {os.getpid()}.")
            return null_result

        group_path = Path(group_file_path)
        if not group_path.exists() or not group_path.is_file():
            logger.error(f"Dispatch download generate file end, task: {task_uuid}, pid: {os.getpid()}, Group info file {group_file_path} does not exist.")
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Can not find download group list",
                    session_key=session_key,
                )
            )
            return null_result

        current_group = tms.get_current_group_progress()
        to_do_group = current_group + 1
        logger.debug(f"Dispatch download, task: {task_uuid}, pid: {os.getpid()}, current_group: {current_group}, to_do_group: {to_do_group}.")
        line = linecache.getline(group_file_path, to_do_group).strip()
        if line == "":
            logger.error(f"Dispatch download generate file end, task: {task_uuid}, pid: {os.getpid()}, error in get group info for group {to_do_group}.")
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Error occurred while retrieving download group list",
                    session_key=session_key,
                )
            )
            return null_result
        group_info = json.loads(line)
        #logger.info(f"Begin process group: {group_info}.")
        persist_path, total_count = tms.generate_task_queue_file(group_info)
        if persist_path is None:
            logger.error(f"Dispatch download generate file end, task: {task_uuid}, pid: {os.getpid()}, error in generating queue file.")
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Error occurred while retrieving file list",
                    session_key=session_key,
                )
            )
            return null_result

        if total_count == 0:
            logger.info(f"Dispatch download generate file, task: {task_uuid}, pid: {os.getpid()}, No file to scan for group {group_info}.")
            tms.current_group_increment()
            if not tms.is_scan_done():
                generate_queue_file(task_uuid=task_uuid, group_file_path=group_file_path, file_path="", params=params, session_key=session_key)
            return null_result
        else:
            logger.info(f"Dispatch download generate file, task: {task_uuid}, pid: {os.getpid()}, Group total count: {total_count}.")
            tms.init_trackers(total_count)
            dispatch_download_task(task_uuid=task_uuid, group_file_path=group_file_path, file_path=persist_path, params=params, session_key=session_key, resume_job=False)
            return {
                "task_uuid": task_uuid,
                "group_file_path": group_file_path,
                "file_path": persist_path,
                "params": params,
                "session_key": session_key,
            }
    except SessionExpired as e:
        logger.info(f"Dispatch download generate file end, task: {task_uuid}, pid: {os.getpid()}, session: {session_key}, session expired!")
        raise CancelExecution()
    except Exception as e:
        logger.error(f"Dispatch download generate file end, task: {task_uuid}, pid: {os.getpid()}, Exception: {e}")
        return null_result


@dispatch_huey.task()
def dispatch_download_task(
    task_uuid: str, group_file_path: str, file_path: str, params: dict, session_key: str, resume_job: bool = False
):
    """
    Dispatches download tasks for files in a queue.

    This function reads a file containing information about files to be downloaded,
    and dispatches download tasks for each file. It also handles task management,
    progress tracking, and error handling.

    Args:
        task_uuid (str): Unique identifier for the task.
        group_file_path (str): Path to the file containing group information to be processed.
        file_path (str): Path to the file containing file information to be processed.
        params (dict): Parameters for the task execution.
        session_key (str): Key for the current session.
        previous_task_id (str, optional): ID of the previous task in the pipeline. Defaults to "".
    """
    try:
        logger.info(f"Dispatch download dispatch begin, pid: {os.getpid()} task: {task_uuid} session: {session_key}.")
        tqs = TaskQueueService()

        params_to_check = {
            "task_uuid": task_uuid,
            "group_file_path": group_file_path,
            "file_path": file_path,
            "params": params,
            "session_key": session_key,
        }
        if params_to_check == null_result:
            logger.error(f"Dispatch download dispatch end, error occurred while checking the params of dispatch download task: {task_uuid}, pid: {os.getpid()}, params: {params_to_check}.")
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Error occurred while checking the params of dispatch download task",
                    session_key=session_key,
                )
            )
            return

        tms = TaskManagementService(task_uuid, session_key)
        if not resume_job and not tms.is_scanning():
            logger.info(f"Dispatch download dispatch end, task: {task_uuid}, pid: {os.getpid()} is not in SCANNING status")
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Error occurred while checking the status of dispatch download task",
                    session_key=session_key,
                )
            )
            return

        current_group = tms.get_current_group_progress()
        logger.debug(f"Dispatch download dispatch, task: {task_uuid}, pid: {os.getpid()}, current_group: {current_group}.")

        file_path = Path(file_path)
        if not file_path.exists() or not file_path.is_file():
            logger.error(f"Dispatch download dispatch end, task: {task_uuid}, pid: {os.getpid()}, File {file_path} does not exist.")
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Can not find file list",
                    session_key=session_key,
                )
            )
            return

        logger.info(f"Dispatch download dispatch, task: {task_uuid}, pid: {os.getpid()}, {file_path}: Dispatching files to queue.")
        total = tms.get_dispatch_total_count()
        start = tms.get_dispatch_current_progress()
        if total == -1 or start == -1:
            logger.error(f"Dispatch download dispatch end, task: {task_uuid}, pid: {os.getpid()}, error in getting total count or current progress.")
            tqs.add_priority_event(
                ChangeResultEvent(
                    scan_policy_id=task_uuid,
                    scan_result=ScanResult.FAILED,
                    fail_reason="Error occurred when retrieving total count or progress for download task",
                    session_key=session_key,
                )
            )
            return

        if resume_job:
            tms.restore_resume_backlogs()
            add_backlogs_to_queue(task_uuid, session_key)

        supported_file_types = get_supported_file_type(
            params["scan_info"]["scan_file_type"]
        )
        end = start + tms.get_dispatch_batch_size()
        while start < total:
            logger.debug(f"Dispatch download dispatch, task: {task_uuid}, pid: {os.getpid()}, start: {start} end: {end} total: {total}")

            with open(file_path, "r", encoding="utf-8") as f:
                for line in islice(f, start, end):
                    while tms.is_dispatch_needed_to_back_off():
                        tms.dispatch_backoff()

                    backlog_hash = hashlib.sha1(line.encode("utf-8")).hexdigest()
                    file_info = json.loads(line)
                    file_ext = file_info["file_name"].split(".")[-1].lower()

                    if supported_file_types["ext"] and file_ext not in supported_file_types["ext"]:
                        file_full_path = file_info.get("display_path") or f"{file_info.get('folder', '')}/{file_info.get('file_name', '')}"
                        logger.debug(f"Dispatch download dispatch, task: {task_uuid}, pid: {os.getpid()}, File {file_full_path} is not supported.")
                        skip_files.info(f"Task {task_uuid}: Dispatch tracking ended, ignore file {file_full_path}, because extension is not supported")
                        with Session() as session:
                            with session.begin():
                                record = (
                                    session.query(HueyTaskCounter)
                                    .with_for_update()
                                    .filter_by(
                                        scan_policy_id=task_uuid,
                                        session_key=session_key,
                                        namespace=tms.analyze_worker_tracker.ignored_counter_key,
                                    )
                                    .first()
                                )
                                record.value += 1
                                tms.increment_dispatch_progress()
                    else:
                        current = tms.get_dispatch_current_progress()
                        create_result = tms.create_backlog(file_info, backlog_hash, params, current)
                        if not resume_job or create_result:
                            # Use unified file scheduler to schedule scan_policy files
                            if is_file_unified:
                                from service.unified_file_service import UnifiedFileScheduler, ScanPolicyFileItem
                                scheduler = UnifiedFileScheduler()
                                scan_policy_item = ScanPolicyFileItem(
                                    task_uuid=task_uuid,
                                    file_info=file_info,
                                    params=params,
                                    session_key=session_key,
                                    backlog_hash=backlog_hash
                                )
                                success = scheduler.add_scan_policy_file(scan_policy_item)
                                if not success:
                                    logger.warning(f"Failed to add scan_policy file to unified file scheduler: {file_info}")
                                else:
                                    logger.debug(f"Successfully added scan_policy file to unified file scheduler: {file_info}")
                            else:
                                download_file_task = download_file.s(
                                    task_uuid=task_uuid,
                                    file_info=file_info,
                                    params=params,
                                    session_key=session_key,
                                    backlog_hash=backlog_hash
                                )
                                pipeline = download_file_task.then(add_analyze_task, previous_task_id=download_file_task.id)
                                download_huey.enqueue(pipeline)
                        tms.increment_dispatch_progress()

            start = tms.get_dispatch_current_progress()
            end = start + tms.get_dispatch_batch_size()

        logger.debug(f"Dispatch download dispatch, task: {task_uuid}, pid: {os.getpid()} dispatch done")
        tms.set_dispatch_done()

        while not tms.is_scan_finished():
            logger.debug(f"Dispatch download dispatch, task: {task_uuid}, pid: {os.getpid()} wait all the file finished")
            tms.dispatch_backoff_no_update_time()

        group_counter = tms._get_analyze_counter()
        tms.set_file_total(tms.get_file_total() + group_counter.get("total", 0))
        tms.set_file_counter(tms.get_file_counter() + group_counter.get("current", 0))
        tms.set_file_ignored_counter(tms.get_file_ignored_counter() + group_counter.get("ignored", 0))
        tms.current_group_increment()
        tms.init_trackers(-999)
        if not tms.is_scan_done():
            generate_queue_file(task_uuid=task_uuid, group_file_path=group_file_path, file_path="", params=params, session_key=session_key)
    except SessionExpired as e:
        logger.info(f"Dispatch download dispatch end, task: {task_uuid}, pid: {os.getpid()}, session: {session_key} session expired!")
        raise CancelExecution()
    except Exception as e:
        logger.error(f"Dispatch download dispatch end, task: {task_uuid}, pid: {os.getpid()}, Exception: {str(e)}")

@dispatch_huey.task()
def test_dispatch_download_task(job_id):
    try:
        logger.debug(f"Dispatch download, test_dispatch_download_task: begin job_id: {job_id}, my pid: {os.getpid()} thread id: {threading.get_ident()}")
        #logger.debug(f"test_dispatch_download_task: aaa: {a['b']}")
        time.sleep(300)
        logger.debug(f"Dispatch download, test_dispatch_download_task: end")
        return {"status": 1}
    #except KeyboardInterrupt as e:
    #    logger.debug(f"Dispatch download, test_dispatch_download_task: KeyboardInterrupt: {str(e)}")
    #    test_handle_interrupt()
    except Exception as e:
        logger.debug(f"Dispatch download, test_dispatch_download_task: Exception: {str(e)} ----end")
        #raise e

#def test_handle_interrupt():
#    logger.debug(f"test_analyze_worker: test_handle_interrupt")
#    sys.exit(0)

@dispatch_huey.signal(SIGNAL_INTERRUPTED)
def right_after_interrupted(signal, task, exc=None):
    """
    Signal handler for when a task is interrupted.

    This function is called when a task is interrupted during execution. It attempts to
    retrieve and process the results of the interrupted task and its previous task.

    Args:
        signal: The signal that triggered this handler.
        task: The task object that was interrupted.
        exc (Exception, optional): The exception that caused the interruption, if any.
    """

    try:
        logger.info(f"Dispatch download SIGNAL_INTERRUPTED begin, task: {task.name}, task.kwargs: {task.kwargs} add to huey enqueue again, pid: {os.getpid()}")

        if "task_uuid" in task.kwargs and "session_key" in task.kwargs:
            tms = TaskManagementService(
                    task.kwargs["task_uuid"], task.kwargs["session_key"]
                )
            if not tms.session_tracker.is_alive():
                logger.info(f"Dispatch download SIGNAL_INTERRUPTED end, task: {task.kwargs['task_uuid']}, pid: {os.getpid()}, session: {task.kwargs['session_key']}, session is not alive, no need to retry")
                return

        if task.name == "generate_queue_group_file":
            flag = False
            for i in range(5):
                logger.info(f"Dispatch download SIGNAL_INTERRUPTED, add to huey enqueue retry time: {i+1}, task name: {task.name}, pid: {os.getpid()}")
                generate_queue_group_file_task = generate_queue_group_file.s(
                    task_uuid=task.kwargs["task_uuid"],
                    params=task.kwargs["params"],
                    session_key=task.kwargs["session_key"],
                )
                pipeline = generate_queue_group_file_task.then(
                    generate_queue_file
                )
                dispatch_huey.enqueue(pipeline)

                for item in dispatch_huey.storage.enqueued_items():
                    decoded_item = pickle.loads(item)
                    if decoded_item.id == pipeline.id:
                        flag = True
                        break

                if flag == True:
                    logger.info(f"Dispatch download SIGNAL_INTERRUPTED, add to huey enqueue again success, id: {pipeline.id}, pid: {os.getpid()}")
                    break
                time.sleep(0.5)

            if flag == False:
                logger.info(f"Dispatch download SIGNAL_INTERRUPTED, add to huey enqueue again failed, pid: {os.getpid()}")
        elif task.name == "generate_queue_file":
            flag = False
            for i in range(5):
                logger.info(f"Dispatch download SIGNAL_INTERRUPTED, add to huey enqueue retry time: {i+1}, task name: {task.name}, pid: {os.getpid()}")
                res = generate_queue_file(
                        task_uuid=task.kwargs["task_uuid"],
                        group_file_path=task.kwargs["group_file_path"],
                        file_path=task.kwargs["file_path"],
                        params=task.kwargs["params"],
                        session_key=task.kwargs["session_key"],
                    )

                for item in dispatch_huey.storage.enqueued_items():
                    decoded_item = pickle.loads(item)
                    if decoded_item.id == res.id:
                        flag = True
                        break

                if flag == True:
                    logger.info(f"Dispatch download SIGNAL_INTERRUPTED, add to huey enqueue again success, id: {res.id}, pid: {os.getpid()}")
                    break
                time.sleep(0.5)

            if flag == False:
                logger.info(f"Dispatch download SIGNAL_INTERRUPTED, add to huey enqueue again failed, pid: {os.getpid()}")
        elif task.name == "dispatch_download_task":
            flag = False
            for i in range(5):
                logger.info(f"Dispatch download SIGNAL_INTERRUPTED, add to huey enqueue retry time: {i+1}, task name: {task.name}, pid: {os.getpid()}")
                res = dispatch_download_task(
                        task_uuid=task.kwargs["task_uuid"],
                        group_file_path=task.kwargs["group_file_path"],
                        file_path=task.kwargs["file_path"],
                        params=task.kwargs["params"],
                        session_key=task.kwargs["session_key"],
                        resume_job=task.kwargs["resume_job"],
                    )

                for item in dispatch_huey.storage.enqueued_items():
                    decoded_item = pickle.loads(item)
                    if decoded_item.id == res.id:
                        flag = True
                        break

                if flag == True:
                    logger.info(f"Dispatch download SIGNAL_INTERRUPTED, add to huey enqueue again success, id: {res.id}, pid: {os.getpid()}")
                    break
                time.sleep(0.5)

            if flag == False:
                logger.info(f"Dispatch download SIGNAL_INTERRUPTED, add to huey enqueue again failed, pid: {os.getpid()}")
        elif task.name == "test_dispatch_download_task":
            flag = False
            for i in range(5):
                logger.info(f"Dispatch download SIGNAL_INTERRUPTED, add to huey enqueue retry time: {i+1}, task name: {task.name}, pid: {os.getpid()}")
                res = test_dispatch_download_task(
                        job_id=task.kwargs["job_id"]
                    )
                for item in dispatch_huey.storage.enqueued_items():
                    decoded_item = pickle.loads(item)
                    if decoded_item.id == res.id:
                        flag = True
                        break

                if flag == True:
                    logger.info(f"Dispatch download SIGNAL_INTERRUPTED, add to huey enqueue again success, id: {res.id}, pid: {os.getpid()}")
                    break
                time.sleep(0.5)

            if flag == False:
                logger.info(f"Dispatch download SIGNAL_INTERRUPTED, add to huey enqueue again failed, pid: {os.getpid()}")
    except AttributeError as e:
        logger.error(f"Dispatch download SIGNAL_INTERRUPTED end, pid: {os.getpid()}, AttributeError {str(e)}")
    except KeyError as e:
        logger.error(f"Dispatch download SIGNAL_INTERRUPTED end, pid: {os.getpid()}, KeyError {str(e)}")
    except Exception as e:
        logger.error(f"Dispatch download SIGNAL_INTERRUPTED end, pid: {os.getpid()}, Exception {str(e)}")
    finally:
        sys.exit(0)

@dispatch_huey.signal(SIGNAL_COMPLETE)
def right_after_completed(signal, task, exc=None):
    """
    Signal handler for when a task is completed.

    This function is called when a task completes its execution successfully. It attempts to
    retrieve and process the results of the completed task and its previous task.

    Args:
        signal: The signal that triggered this handler.
        task: The task object that was completed.
        exc (Exception, optional): The exception that occurred during task execution, if any.
    """
    try:
        logger.info(f"Dispatch download SIGNAL_COMPLETE begin, task: {task.name}, task.kwargs: {task.kwargs}, pid: {os.getpid()}")
    except Exception as e:
        logger.error(f"Dispatch download SIGNAL_COMPLETE end, pid: {os.getpid()}, Exception {str(e)}")

@dispatch_huey.signal(SIGNAL_ERROR)
def right_after_error(signal, task, exc=None):
    """
    Signal handler for when a task encounters an error.

    This function is called when a task fails during execution. It attempts to
    retrieve and process the results of the failed task and its previous task
    to clean up Redis entries.

    Args:
        signal: The signal that triggered this handler.
        task: The task object that encountered an error.
        exc (Exception, optional): The exception that caused the task to fail, if any.
    """
    try:
        logger.info(f"Dispatch download SIGNAL_ERROR begin, task: {task.name}, task.kwargs: {task.kwargs}, pid: {os.getpid()}")
    except Exception as e:
        logger.error(f"Dispatch download SIGNAL_ERROR end, pid: {os.getpid()}, Exception {str(e)}")
