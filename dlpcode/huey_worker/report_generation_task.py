from huey import RedisHuey
from util.config import configs
from exts import logger
from reports import generate_report

report_huey = RedisHuey("report-generation-task", db=configs["huey"]["db"])

@report_huey.task()
def generate_report_worker(report_id: str) -> bool:
    """
    Huey task to generate a report given its task ID.
    """
    try:
        result = 0
        logger.info(f"generate_report_worker: got a report gen event on <PERSON>ey")
        # call the report generation api
        result = generate_report.generate_new_report(report_id)
        if result:
            logger.info(f"generate_report_worker: Report generated successfully: {result}")
        else:
            logger.info(f"generate_report_worker: Report generated failed: {result}")
        return result

    except Exception as e:
        logger.error(f"generate_report_worker: Error generating report {report_id}: {e}", exc_info=True)
        # re-raise so <PERSON><PERSON> can retry according to its config
        return False
