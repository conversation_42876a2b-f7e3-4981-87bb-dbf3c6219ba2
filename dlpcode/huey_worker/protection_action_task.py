from huey import RedisExpireHuey
from connector.aws_connector import AWSConnector
from connector.sharepoint_connector import SharePointConnector
from connector.sharepoint_token_connector import SharePointTokenConnector
from connector.google_connector import GoogleConnector
from connector.smb_connector import SMBConnector
from util.config import get_global_config
from exts import logger
from domain_model.scan_policy import get_scan_policy
from service.protection_profile_service import get_protection_profiles
from util.enum_ import StorageType, CredentialType, ProtectionConst, ProtectionQuarantineStatus, ProtectionCopyStatus, ProtectionProfileType
from domain_model.global_cache import GlobalCache
import hashlib
import csv
import os
import urllib.parse
from pathlib import Path
import shutil
from system.system_log import record_event_log, LogLevel, LogAction, LogType

configs = get_global_config()
huey_db = configs["huey"]["db"]
download_file_path = Path("/var/log/protection_action/download_files")
meta_info_file_path = "/var/log/protection_action/meta_info_files/"
notification_file_local_path = "/var/log/protection_action/notification_files/default"
target_file_name_max = configs["protection_action"]["target_file_name_max"]
notification_file_name_max = configs["protection_action"]["notification_file_name_max"]

huey = RedisExpireHuey("protection_action", expire_time=180, db=huey_db)

def hash_string_to_8_bytes(s):
    hash_value = hashlib.sha256(s.encode('utf-8')).digest()[:4]
    hex_str = hash_value.hex()
    return hex_str

def get_connector_from_protection_profile(protection_profile, pwd_encrypted=1, pwd_encrypted2=1):
    from storage.service.profiles import get_storage_profile_by_id
    try:
        profile = protection_profile['profile']
        storage_type = profile['storage_type']
        credential_type = profile['credential_type']
        storage = get_storage_profile_by_id(profile['storage_id'])
        storage['auth_info']['storage_id'] = profile['storage_id']

        connector_class_map = {
                    1: AWSConnector,
                    2: SharePointTokenConnector,
                    3: SharePointConnector,
                    4: SMBConnector,
                    6: GoogleConnector,
                }
        connector_class = connector_class_map.get(storage_type)
        if storage_type == StorageType.SHAREPOINT_OL and credential_type == CredentialType.USE_CUSTOM_CREDENTIAL:
            connector_class = SharePointConnector
        connector = connector_class(storage['auth_info'], pwd_encrypted=pwd_encrypted, pwd_encrypted2=pwd_encrypted2)
        return connector
    except Exception as e:
        logger.error(f"Error get connector from protection profile: {str(e)}")
        return None

def get_connector_from_scan_policy(storage_type, scan_init_info):
    from storage.service.profiles import get_storage_profile_by_id
    try:
        connector_obj = None
        storage = get_storage_profile_by_id(scan_init_info['storage_id'])
        auth_info = storage['auth_info']
        auth_info['storage_id'] = scan_init_info['storage_id']

        if storage_type == StorageType.AWS:
            connector_obj = AWSConnector(auth_info)
        elif ((storage_type == StorageType.SHAREPOINT_OL and auth_info.get("usecredentials", True)) or (storage_type == StorageType.SHAREPOINT_OP)):
            connector_obj = SharePointConnector(auth_info)
        elif (storage_type == StorageType.SHAREPOINT_OL and auth_info.get("usecredentials") == False):
            connector_obj = SharePointTokenConnector(auth_info)
        elif storage_type == StorageType.SMB:
            connector_obj = SMBConnector(auth_info)
        elif storage_type == StorageType.GOOGLE:
            connector_obj = GoogleConnector(auth_info)
        return connector_obj
    except Exception as e:
        logger.error(f"Error get connector from scan policy: {str(e)}")
        return None

def generate_meta_info_csv_file(task_uuid, file_name, data):
    local_meta_info_file = f"{meta_info_file_path}{task_uuid[:8]}-{file_name}"
    try:
        with open(local_meta_info_file, "w", newline="") as csvfile:
            writer = csv.writer(csvfile)
            writer.writerows(data)
        return local_meta_info_file
    except Exception as e:
        logger.error(f"Error generate meta info csv file: {str(e)}")
        return None

def check_target_path_and_retry_move(connector, folder, file_name, path, target_file_name, task_uuid, local_file):
    try:
        if connector.check_folder_exist(path, task_uuid):
            logger.info(f"Folder {path} is exist, no need to retry")
            return None

        parts = path.rsplit('/', 1)
        if len(parts) != 2:
            logger.info(f"Folder {path} is invalid, no need to retry")
            return None

        if connector.create_folder(parts[0], parts[1], task_uuid) is None:
            logger.error(f"Create {parts[1]} in target path {parts[0]} error")
            return None

        return connector.move_file(folder, file_name, path, target_file_name, task_uuid, local_file)
    except Exception as e:
        logger.error(f"Error generate meta info csv file: {str(e)}")
        return None

def check_target_path_and_retry_copy(connector, folder, file_name, path, target_file_name, task_uuid, local_file):
    try:
        if connector.check_folder_exist(path, task_uuid):
            logger.info(f"Folder {path} is exist, no need to retry")
            return None

        parts = path.rsplit('/', 1)
        if len(parts) != 2:
            logger.info(f"Folder {path} is invalid, no need to retry")
            return None

        if connector.create_folder(parts[0], parts[1], task_uuid) is None:
            logger.error(f"Create {parts[1]} in target path {parts[0]} error")
            return None

        return connector.copy_file(folder, file_name, path, target_file_name, task_uuid, local_file)
    except Exception as e:
        logger.error(f"Error generate meta info csv file: {str(e)}")
        return None

def check_target_path_and_retry_upload(connector, local_file, path, target_file_name, task_uuid):
    try:
        if connector.check_folder_exist(path, task_uuid):
            logger.info(f"Folder {path} is exist, no need to retry")
            return None

        parts = path.rsplit('/', 1)
        if len(parts) != 2:
            logger.info(f"Folder {path} is invalid, no need to retry")
            return None

        if connector.create_folder(parts[0], parts[1], task_uuid) is None:
            logger.error(f"Create {parts[1]} in target path {parts[0]} error")
            return None

        return connector.upload(local_file, path, target_file_name, task_uuid)
    except Exception as e:
        logger.error(f"Error generate meta info csv file: {str(e)}")
        return None

def error_process(file_uuid, file_name, scan_policy, info, action_type, err_message, user=None):
    logger.error(err_message)
    if action_type == "file_copy":
        info['copy_status'] = ProtectionCopyStatus.FAILED
        info['copy_failed_message'] = err_message
    elif action_type == "file_quarantine":
        info['quarantine_status'] = ProtectionQuarantineStatus.FAILED
        info['quarantine_failed_message'] = err_message
        if user:
            record_event_log(user=user, level=LogLevel.ERROR.value,
                            message=f'Quarantine file {file_name if file_name != "" else file_uuid} for scan {scan_policy.name if scan_policy else ""} failed, detail: {err_message}',
                            desc='Quarantine file failed', type=LogType.QUARANTINE_FILES.value, action=LogAction.EDIT.value,
                            extend={"file_id": file_uuid})
    elif action_type == "restore_file_quarantine":
        info['restore_quarantine_failed_message'] = err_message
        if user:
            record_event_log(user=user, level=LogLevel.ERROR.value,
                            message=f'Restore quarantined file {file_name if file_name != "" else file_uuid} for scan {scan_policy.name if scan_policy else ""} failed, detail: {err_message}',
                            desc='Restore quarantine file failed', type=LogType.QUARANTINE_FILES.value, action=LogAction.EDIT.value,
                            extend={"file_id": file_uuid})

def update_cache_remediation_info(cache, file_uuid, task_uuid, full_path, remediation_info):
    payload = {
                'id': file_uuid,
                'scan_uuid': task_uuid,
                'full_path': full_path,
                'reserve_json3': remediation_info
            }
    cache.put(payload, update_only=True)

def do_rollback_operations(rollback_operations, task_uuid):
    for operation in rollback_operations:
        logger.debug(f'Do rollback operations,  operation: {operation}')
        connector = operation.get("connector")
        if connector is None:
            continue

        if operation.get("action") == "delete":
            connector.delete_file(operation.get("folder", ""), operation.get("file_name", ""), task_uuid)
        else:
            logger.error(f'Unsupport rollback operation: {operation.get("action")}')

@huey.task()
def protection_task_file_copy(task_uuid, file_uuid, local_file = None, last_modify_time = None):
    local_file_origin = None
    remediation_info = None
    full_path = None
    file_name = ""
    cache = None
    scan_policy = None
    rollback_operations = []
    info = {
        "copy_status": ProtectionCopyStatus.INIT,
        "copy_targets": [],
        "copy_failed_message": ""
    }
    try:
        logger.info(f"file copy task_uuid: {task_uuid}, file_uuid: {file_uuid} local_file: {local_file} last_modify_time: {last_modify_time}")
        cache = GlobalCache(task_uuid)
        fr = cache.get_by_file_uuid(file_uuid=file_uuid)
        if not fr:
            err_message = f"File record not found in db {file_uuid}"
            error_process(file_uuid, file_name, scan_policy, info, "file_copy", err_message)
            return -1, info
        full_path = fr.get("full_path", "")
        file_name = fr.get("file_name", "")
        file_hash = fr.get("file_hash", "")
        file_attributes = fr.get("file_attributes", "")
        folder = full_path.rsplit('/', 1)[0]
        remediation_info = fr.get("reserve_json3")
        if remediation_info is None:
            err_message = f"Can not find remediation info in cache record"
            error_process(file_uuid, file_name, scan_policy, info, "file_copy", err_message)
            return -1, info

        remediation_info['copy_status'] = ProtectionCopyStatus.DOING
        remediation_info['copy_targets'] = []
        remediation_info['copy_failed_message'] = ""
        update_cache_remediation_info(cache, file_uuid, task_uuid, full_path, remediation_info)

        scan_policy = get_scan_policy(id=task_uuid)
        if scan_policy is None:
            err_message = f"Can not get scan policy {task_uuid}"
            error_process(file_uuid, file_name, scan_policy, remediation_info, "file_copy", err_message)
            return -1, remediation_info

        scan_connector = get_connector_from_scan_policy(scan_policy.storage_type, scan_policy.scan_init_info)
        if scan_connector is None:
            err_message = f"Can not get connector with scan policy"
            error_process(file_uuid, file_name, scan_policy, remediation_info, "file_copy", err_message)
            return -1, remediation_info

        protection_profiles = scan_policy.protection_profiles
        if protection_profiles is None:
            err_message = f"Can not find protection_profiles in scan_policy"
            error_process(file_uuid, file_name, scan_policy, remediation_info, "file_copy", err_message)
            return -1, remediation_info

        copy_uuids = protection_profiles.get('copy_profiles', [])
        for copy_uuid in copy_uuids:
            copy_profiles, _ = get_protection_profiles({'id': copy_uuid})
            if copy_profiles is None or len(copy_profiles) < 1:
                err_message = f"Can not find copy profiles {copy_uuid}"
                error_process(file_uuid, file_name, scan_policy, remediation_info, "file_copy", err_message)
                return -1, remediation_info
            copy_profile = copy_profiles[0]
            profile = copy_profile['profile']
            path = f"{profile['path'].rstrip('/')}/{ProtectionConst.SENSITIVE_FILES_FOLDER_PREFIX.value}{task_uuid.split('-')[0]}"
            credential_type = profile['credential_type']
            if credential_type == CredentialType.USE_SAME_CREDENTIAL_WITH_SCAN_POLICY:
                connector = scan_connector
            else:
                connector = get_connector_from_protection_profile(copy_profile)
            if connector is None:
                err_message = f"Can not get connector with protection profile"
                error_process(file_uuid, file_name, scan_policy, remediation_info, "file_copy", err_message)
                return -1, remediation_info
            # Check file consistency
            ret, file_info = scan_connector.get_file_info(folder, file_name)
            if ret:
                err_message = f"Can not get file info of {file_name}, give up file copy"
                error_process(file_uuid, file_name, scan_policy, remediation_info, "file_copy", err_message)
                return -1, remediation_info

            if last_modify_time and file_info.get('last_modified', '') != last_modify_time:
                err_message = f"File {file_name} has been modified, give up file copy"
                error_process(file_uuid, file_name, scan_policy, remediation_info, "file_copy", err_message)
                return -1, remediation_info
            # Copy or Upload origin file to target path
            name_part = file_name.rsplit('.', 1)[0]
            name_part = name_part if len(name_part) <= target_file_name_max else name_part[:target_file_name_max]
            extension = file_name.rsplit('.', 1)[1] if '.' in file_name else ""
            full_path_hash = hash_string_to_8_bytes(full_path)
            target_file_name = f"{name_part}_{full_path_hash}_{file_hash[:8]}_{ProtectionProfileType.COPY}.{extension}"
            if credential_type == CredentialType.USE_SAME_CREDENTIAL_WITH_SCAN_POLICY and scan_policy.storage_type != StorageType.GOOGLE:
                target_file_info = connector.copy_file(folder, file_name, path, target_file_name, task_uuid, local_file)
                if target_file_info is None:
                    target_file_info = check_target_path_and_retry_copy(connector, folder, file_name, path, target_file_name, task_uuid, local_file)
                    if target_file_info is None:
                        err_message = f"Error copying file to target path {path}"
                        error_process(file_uuid, file_name, scan_policy, remediation_info, "file_copy", err_message)
                        return -1, remediation_info
            else:
                if local_file is None:
                    _, local_file_origin, _ = scan_connector.download(
                        folder, file_name, task_uuid, download_dir = download_file_path
                    )
                    if local_file_origin is None:
                        err_message = f"Download origin file error"
                        error_process(file_uuid, file_name, scan_policy, remediation_info, "file_copy", err_message)
                        return -1, remediation_info
                    local_file = local_file_origin

                target_file_info = connector.upload(local_file, path, target_file_name, task_uuid)
                if target_file_info is None:
                    target_file_info = check_target_path_and_retry_upload(connector, local_file, path, target_file_name, task_uuid)
                    if target_file_info is None:
                        err_message = f"Error copying file to target path {path}"
                        error_process(file_uuid, file_name, scan_policy, remediation_info, "file_copy", err_message)
                        return -1, remediation_info
            # Upload meta info file
            target_meta_info_file_name = f"{name_part}_{full_path_hash}_{file_hash[:8]}_{ProtectionProfileType.COPY}_meta.csv"
            original_file = file_attributes.get('file_display_path', "N/A")
            if original_file == "N/A":
                original_file = scan_connector.adapt_full_path_format(full_path)
            data = [["Original file", "Original file hash"], [original_file, file_hash]]
            local_meta_info_file = generate_meta_info_csv_file(task_uuid, target_meta_info_file_name, data)
            if local_meta_info_file is None:
                err_message = f"Error generating meta info CSV file"
                error_process(file_uuid, file_name, scan_policy, remediation_info, "file_copy", err_message)
                rollback_operations.append({"connector": connector, "action": "delete", "folder": target_file_info.get('folder', ''), "file_name": target_file_info.get('file_name', '')})
                return -1, remediation_info

            upload_meta_file_info = connector.upload(local_meta_info_file, path, target_meta_info_file_name, task_uuid)
            if upload_meta_file_info is None:
                err_message = f"Error uploading meta file to target path {path}"
                error_process(file_uuid, file_name, scan_policy, remediation_info, "file_copy", err_message)
                os.remove(local_meta_info_file)
                rollback_operations.append({"connector": connector, "action": "delete", "folder": target_file_info.get('folder', ''), "file_name": target_file_info.get('file_name', '')})
                return -1, remediation_info

            os.remove(local_meta_info_file)
            target_file_info['protection_profile_id'] = copy_uuid
            target_file_info['meta_file_info'] = upload_meta_file_info
            remediation_info['copy_targets'].append(target_file_info)

        remediation_info['copy_status'] = ProtectionCopyStatus.SUCCESS
        return 0, remediation_info
    except Exception as e:
        err_message = f"Error in protection task file copy: {str(e)}"
        if remediation_info:
            error_process(file_uuid, file_name, scan_policy, remediation_info, "file_copy", err_message)
            return -1, remediation_info
        else:
            error_process(file_uuid, file_name, scan_policy, info, "file_copy", err_message)
            return -1, info
    finally:
        do_rollback_operations(rollback_operations, task_uuid)
        if remediation_info and full_path and cache:
            update_cache_remediation_info(cache, file_uuid, task_uuid, full_path, remediation_info)
        if local_file_origin is not None:
            os.remove(local_file_origin)

@huey.task()
def protection_task_file_quarantine(task_uuid, file_uuid, local_file = None, last_modify_time = None, user = None):
    local_file_origin = None
    remediation_info = None
    full_path = None
    file_name = ""
    scan_policy = None
    cache = None
    rollback_operations = []
    info = {
        "quarantine_status": ProtectionQuarantineStatus.INIT,
        "quarantine_targets": [],
        "quarantine_failed_message": ""
    }
    try:
        logger.info(f"file quarantine task_uuid: {task_uuid}, file_uuid: {file_uuid}  local_file: {local_file}, last_modify_time: {last_modify_time}")
        cache = GlobalCache(task_uuid)
        fr = cache.get_by_file_uuid(file_uuid=file_uuid)
        if not fr:
            err_message = f"File record not found in db {file_uuid}"
            error_process(file_uuid, file_name, scan_policy, info, "file_quarantine", err_message, user)
            return -1, info
        full_path = fr.get("full_path", "")
        file_name = fr.get("file_name", "")
        file_hash = fr.get("file_hash", "")
        file_attributes = fr.get("file_attributes", "")
        folder = full_path.rsplit('/', 1)[0]
        remediation_info = fr.get("reserve_json3")
        if remediation_info is None:
            err_message = f"Can not find remediation info"
            error_process(file_uuid, file_name, scan_policy, info, "file_quarantine", err_message, user)
            return -1, info

        remediation_info['quarantine_status'] = ProtectionQuarantineStatus.DOING
        remediation_info['quarantine_targets'] = []
        remediation_info['quarantine_failed_message'] = ""
        update_cache_remediation_info(cache, file_uuid, task_uuid, full_path, remediation_info)

        scan_policy = get_scan_policy(id=task_uuid)
        if scan_policy is None:
            err_message = f"Can not get scan policy {task_uuid}"
            error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
            return -1, remediation_info

        scan_connector = get_connector_from_scan_policy(scan_policy.storage_type, scan_policy.scan_init_info)
        if scan_connector is None:
            err_message = f"Can not get connector with scan policy"
            error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
            return -1, remediation_info

        protection_profiles = scan_policy.protection_profiles
        if protection_profiles is None:
            err_message = f"Can not find protection_profiles in scan_policy"
            error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
            return -1, remediation_info

        quarantine_uuids = protection_profiles.get('quarantine_profiles', [])
        for quarantine_uuid in quarantine_uuids:
            quarantine_profiles, _ = get_protection_profiles({'id': quarantine_uuid})
            if quarantine_profiles is None or len(quarantine_profiles) < 1:
                err_message = f"Can not find quarantine profiles {quarantine_uuid}"
                error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
                return -1, remediation_info
            quarantine_profile = quarantine_profiles[0]
            profile = quarantine_profile['profile']
            path = f"{profile['path'].rstrip('/')}/{ProtectionConst.SENSITIVE_FILES_FOLDER_PREFIX.value}{task_uuid.split('-')[0]}"
            credential_type = profile['credential_type']
            if credential_type == CredentialType.USE_SAME_CREDENTIAL_WITH_SCAN_POLICY:
                connector = scan_connector
            else:
                connector = get_connector_from_protection_profile(quarantine_profile)
            if connector is None:
                err_message = f"Can not get connector with protection profile"
                error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
                return -1, remediation_info

            name_part = file_name.rsplit('.', 1)[0]
            name_part = name_part if len(name_part) <= target_file_name_max else name_part[:target_file_name_max]
            extension = file_name.rsplit('.', 1)[1] if '.' in file_name else ""
            full_path_hash = hash_string_to_8_bytes(full_path)
            # Upload notification file
            file_name_truncated = file_name if len(file_name) <= notification_file_name_max else file_name[:notification_file_name_max]
            notification_file_name = f"{ProtectionConst.QUARANTINE_NOTIFY_FILE_PREFIX.value}{task_uuid.split('-')[0]}-{file_name_truncated}.txt"
            if type(scan_connector) is SharePointTokenConnector:
                file_display_path = file_attributes.get("file_display_path")
                if file_display_path is None:
                    err_message = f"Error in get file_display_path"
                    error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
                    return -1, remediation_info
                notification_file_folder = file_display_path.rsplit('/', 1)[0]
            else:
                notification_file_folder = folder
            upload_nofity_file_info = scan_connector.upload(notification_file_local_path, notification_file_folder, notification_file_name, task_uuid)
            if upload_nofity_file_info is None:
                err_message = f"Error uploading notify file (One possible cause is that the folder may not have write permissions)"
                error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
                return -1, remediation_info
            # Upload meta info file
            target_meta_info_file_name = f"{name_part}_{full_path_hash}_{file_hash[:8]}_{ProtectionProfileType.QUARANTINE}_meta.csv"
            original_file = file_attributes.get('file_display_path', "N/A")
            if original_file == "N/A":
                original_file = scan_connector.adapt_full_path_format(full_path)
            data = [
                ["Original file", "Original file hash", "Original file notification file"],
                [original_file, file_hash, upload_nofity_file_info.get('file_display_path', upload_nofity_file_info.get('full_path'))]
            ]
            local_meta_info_file = generate_meta_info_csv_file(task_uuid, target_meta_info_file_name, data)
            if local_meta_info_file is None:
                err_message = f"Error generating meta info CSV file"
                error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
                rollback_operations.append({"connector": scan_connector, "action": "delete", "folder": upload_nofity_file_info.get('folder', ''), "file_name": upload_nofity_file_info.get('file_name', '')})
                return -1, remediation_info

            upload_meta_file_info = connector.upload(local_meta_info_file, path, target_meta_info_file_name, task_uuid)
            if upload_meta_file_info is None:
                upload_meta_file_info = check_target_path_and_retry_upload(connector, local_meta_info_file, path, target_meta_info_file_name, task_uuid)
                if upload_meta_file_info is None:
                    err_message = f"Error uploading meta file to target path {path}"
                    error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
                    os.remove(local_meta_info_file)
                    rollback_operations.append({"connector": scan_connector, "action": "delete", "folder": upload_nofity_file_info.get('folder', ''), "file_name": upload_nofity_file_info.get('file_name', '')})
                    return -1, remediation_info
            os.remove(local_meta_info_file)
            # Check file consistency
            ret, file_info = scan_connector.get_file_info(folder, file_name)
            if ret:
                err_message = f"Can not get file info of {file_name}, give up file quarantine"
                error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
                rollback_operations.append({"connector": scan_connector, "action": "delete", "folder": upload_nofity_file_info.get('folder', ''), "file_name": upload_nofity_file_info.get('file_name', '')})
                rollback_operations.append({"connector": connector, "action": "delete", "folder": upload_meta_file_info.get('folder', ''), "file_name": upload_meta_file_info.get('file_name', '')})
                return -1, remediation_info

            if last_modify_time and file_info.get('last_modified', '') != last_modify_time:
                err_message = f"File {file_name} has been modified, give up file quarantine"
                error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
                rollback_operations.append({"connector": scan_connector, "action": "delete", "folder": upload_nofity_file_info.get('folder', ''), "file_name": upload_nofity_file_info.get('file_name', '')})
                rollback_operations.append({"connector": connector, "action": "delete", "folder": upload_meta_file_info.get('folder', ''), "file_name": upload_meta_file_info.get('file_name', '')})
                return -1, remediation_info
            # Move or Upload origin file to target path
            target_file_name = f"{name_part}_{full_path_hash}_{file_hash[:8]}_{ProtectionProfileType.QUARANTINE}.{extension}"
            if credential_type == CredentialType.USE_SAME_CREDENTIAL_WITH_SCAN_POLICY and scan_policy.storage_type != StorageType.GOOGLE:
                target_file_info = connector.move_file(folder, file_name, path, target_file_name, task_uuid, local_file)
                if target_file_info is None:
                    target_file_info = check_target_path_and_retry_move(connector, folder, file_name, path, target_file_name, task_uuid, local_file)
                    if target_file_info is None:
                        err_message = f"Error moving file to target path {path}"
                        error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
                        rollback_operations.append({"connector": scan_connector, "action": "delete", "folder": upload_nofity_file_info.get('folder', ''), "file_name": upload_nofity_file_info.get('file_name', '')})
                        rollback_operations.append({"connector": connector, "action": "delete", "folder": upload_meta_file_info.get('folder', ''), "file_name": upload_meta_file_info.get('file_name', '')})
                        return -1, remediation_info
            else:
                if local_file is None:
                    _, local_file_origin, _ = scan_connector.download(
                        folder, file_name, task_uuid, download_dir = download_file_path
                    )
                    if local_file_origin is None:
                        err_message = f"Download origin file error"
                        error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
                        rollback_operations.append({"connector": scan_connector, "action": "delete", "folder": upload_nofity_file_info.get('folder', ''), "file_name": upload_nofity_file_info.get('file_name', '')})
                        rollback_operations.append({"connector": connector, "action": "delete", "folder": upload_meta_file_info.get('folder', ''), "file_name": upload_meta_file_info.get('file_name', '')})
                        return -1, remediation_info
                    local_file = local_file_origin

                target_file_info = connector.upload(local_file, path, target_file_name, task_uuid)
                if target_file_info is None:
                    target_file_info = check_target_path_and_retry_upload(connector, local_file, path, target_file_name, task_uuid)
                    if target_file_info is None:
                        err_message = f"Error uploading file to target path {path}"
                        error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
                        rollback_operations.append({"connector": scan_connector, "action": "delete", "folder": upload_nofity_file_info.get('folder', ''), "file_name": upload_nofity_file_info.get('file_name', '')})
                        rollback_operations.append({"connector": connector, "action": "delete", "folder": upload_meta_file_info.get('folder', ''), "file_name": upload_meta_file_info.get('file_name', '')})
                        return -1, remediation_info

                if scan_connector.delete_file(folder, file_name, task_uuid) == False:
                    err_message = f"Delete origin file {file_name} error"
                    error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
                    rollback_operations.append({"connector": scan_connector, "action": "delete", "folder": upload_nofity_file_info.get('folder', ''), "file_name": upload_nofity_file_info.get('file_name', '')})
                    rollback_operations.append({"connector": connector, "action": "delete", "folder": upload_meta_file_info.get('folder', ''), "file_name": upload_meta_file_info.get('file_name', '')})
                    rollback_operations.append({"connector": connector, "action": "delete", "folder": target_file_info.get('folder', ''), "file_name": target_file_info.get('file_name', '')})
                    return -1, remediation_info

            target_file_info['protection_profile_id'] = quarantine_uuid
            target_file_info['meta_file_info'] = upload_meta_file_info
            target_file_info['nofity_file_info'] = upload_nofity_file_info
            remediation_info['quarantine_targets'].append(target_file_info)

        remediation_info['quarantine_status'] = ProtectionQuarantineStatus.SUCCESS
        if user:
            record_event_log(user=user, level=LogLevel.INFO.value, message=f'Quarantine file {file_name} for scan {scan_policy.name}',
                     desc='Quarantine file', type=LogType.QUARANTINE_FILES.value, action=LogAction.EDIT.value,
                     extend={"file_id": file_uuid, "target_path": remediation_info['quarantine_targets']})
        return 0, remediation_info
    except Exception as e:
        err_message = f"Error in protection task file quarantine: {str(e)}"
        if remediation_info:
            error_process(file_uuid, file_name, scan_policy, remediation_info, "file_quarantine", err_message, user)
            return -1, remediation_info
        else:
            error_process(file_uuid, file_name, scan_policy, info, "file_quarantine", err_message, user)
            return -1, info
    finally:
        do_rollback_operations(rollback_operations, task_uuid)
        if remediation_info and full_path and cache:
            update_cache_remediation_info(cache, file_uuid, task_uuid, full_path, remediation_info)
        if local_file_origin is not None:
            os.remove(local_file_origin)

@huey.task()
def protection_task_restore_file_quarantine(task_uuid, file_uuid, user):
    local_file = None
    remediation_info = None
    full_path = None
    file_name = ""
    scan_policy = None
    cache = None
    info = {
        "quarantine_status": ProtectionQuarantineStatus.SUCCESS,
        "quarantine_targets": [],
        "restore_quarantine_failed_message": ""
    }
    try:
        logger.info(f"restore file quarantine task_uuid: {task_uuid}, file_uuid: {file_uuid}")
        cache = GlobalCache(task_uuid)
        fr = cache.get_by_file_uuid(file_uuid=file_uuid)
        if not fr:
            err_message = f"File record not found in db {file_uuid}"
            error_process(file_uuid, file_name, scan_policy, info, "restore_file_quarantine", err_message, user)
            return -1, info
        full_path = fr.get("full_path", "")
        file_name = fr.get("file_name", "")
        folder = full_path.rsplit('/', 1)[0]
        remediation_info = fr.get("reserve_json3")
        if remediation_info is None:
            err_message = f"Can not find remediation info"
            error_process(file_uuid, file_name, scan_policy, info, "restore_file_quarantine", err_message, user)
            return -1, info

        remediation_info['resotre_quarantine_failed_message'] = ""
        scan_policy = get_scan_policy(id=task_uuid)
        if scan_policy is None:
            err_message = f"Can not get scan policy {task_uuid}"
            error_process(file_uuid, file_name, scan_policy, remediation_info, "restore_file_quarantine", err_message, user)
            return -1, remediation_info

        scan_connector = get_connector_from_scan_policy(scan_policy.storage_type, scan_policy.scan_init_info)
        if scan_connector is None:
            err_message = f"Can not get connector with scan policy"
            error_process(file_uuid, file_name, scan_policy, remediation_info, "restore_file_quarantine", err_message, user)
            return -1, remediation_info

        if scan_connector.check_file_exist(full_path) is True:
            err_message = f"A file with name {file_name} is exist in origin storage, give up restore quarantined file"
            error_process(file_uuid, file_name, scan_policy, remediation_info, "restore_file_quarantine", err_message, user)
            return -1, remediation_info

        quarantine_targets = remediation_info.get('quarantine_targets', [])
        for quarantine_target in quarantine_targets:
            quarantine_profiles, _ = get_protection_profiles({'id': quarantine_target.get('protection_profile_id', '')})
            if quarantine_profiles is None or len(quarantine_profiles) < 1:
                err_message = f"Can not find protection profiles {quarantine_target.get('protection_profile_id', '')}"
                error_process(file_uuid, file_name, scan_policy, remediation_info, "restore_file_quarantine", err_message, user)
                return -1, remediation_info
            quarantine_profile = quarantine_profiles[0]
            profile = quarantine_profile['profile']
            credential_type = profile['credential_type']
            if credential_type == CredentialType.USE_SAME_CREDENTIAL_WITH_SCAN_POLICY:
                connector = scan_connector
            else:
                connector = get_connector_from_protection_profile(quarantine_profile)
            if connector is None:
                err_message = f"Can not get connector with protection profile"
                error_process(file_uuid, file_name, scan_policy, remediation_info, "restore_file_quarantine", err_message, user)
                return -1, remediation_info

            if type(scan_connector) is SharePointTokenConnector:
                file_attributes = fr.get("file_attributes", {})
                file_display_path = file_attributes.get("file_display_path")
                if file_display_path is None:
                    err_message = f"Error in get file_display_path"
                    error_process(file_uuid, file_name, scan_policy, remediation_info, "restore_file_quarantine", err_message, user)
                    return -1, remediation_info
                upload_folder = file_display_path.rsplit('/', 1)[0]
            else:
                upload_folder = folder
            # Move or Upload origin file to origin path
            if credential_type == CredentialType.USE_SAME_CREDENTIAL_WITH_SCAN_POLICY and scan_policy.storage_type != StorageType.GOOGLE:
                origin_file_info = scan_connector.move_file(quarantine_target.get('folder', ''),  quarantine_target.get('file_name', ''), upload_folder, file_name, task_uuid, local_file)
                if origin_file_info is None:
                    err_message = f"Error in move file {file_name} to origin path"
                    error_process(file_uuid, file_name, scan_policy, remediation_info, "restore_file_quarantine", err_message, user)
                    return -1, remediation_info
            else:
                _, local_file, _ = connector.download(
                    quarantine_target.get('folder', ''),  quarantine_target.get('file_name', ''), task_uuid, download_dir = download_file_path
                )
                if local_file is None:
                    err_message = f"Download file {quarantine_target.get('file_name', '')} from profile target path error"
                    error_process(file_uuid, file_name, scan_policy, remediation_info, "restore_file_quarantine", err_message, user)
                    return -1, remediation_info

                origin_file_info = scan_connector.upload(local_file, upload_folder, file_name, task_uuid)
                if origin_file_info is None:
                    err_message = f"Error in upload file {file_name} to origin path"
                    error_process(file_uuid, file_name, scan_policy, remediation_info, "restore_file_quarantine", err_message, user)
                    return -1, remediation_info

                if connector.delete_file(quarantine_target.get('folder', ''), quarantine_target.get('file_name', ''), task_uuid) == False:
                    logger.error(f"Restore file quarantine: delete target file {quarantine_target.get('folder', '')}/{quarantine_target.get('file_name', '')} error")
            # Delete notification file            
            nofity_file_info = quarantine_target.get('nofity_file_info')
            if nofity_file_info:
                notification_file_folder = nofity_file_info.get('folder', '')
                notification_file_name = nofity_file_info.get('file_name', '')
            else:
                file_name_truncated = file_name if len(file_name) <= notification_file_name_max else file_name[:notification_file_name_max]
                notification_file_name = f"{ProtectionConst.QUARANTINE_NOTIFY_FILE_PREFIX.value}{task_uuid.split('-')[0]}-{file_name_truncated}.txt"
                notification_file_folder = folder
            if scan_connector.delete_file(notification_file_folder, notification_file_name, task_uuid) == False:
                logger.error(f"Restore file quarantine: delete notification file {folder}/{notification_file_name} error")
            
            # Delete meta info file
            meta_file_info = quarantine_target.get('meta_file_info', {})
            if connector.delete_file(meta_file_info.get('folder', ''), meta_file_info.get('file_name', ''), task_uuid) == False:
                logger.error(f"Restore file quarantine: delete target meta info file {meta_file_info.get('folder', '')}/{meta_file_info.get('file_name', '')} error")

        remediation_info['quarantine_targets'] = []
        remediation_info['quarantine_status'] = ProtectionQuarantineStatus.INIT
        record_event_log(user=user, level=LogLevel.INFO.value, message=f'Restore quarantine file {file_name} for scan {scan_policy.name}',
                     desc='Restore quarantine file', type=LogType.QUARANTINE_FILES.value, action=LogAction.EDIT.value,
                     extend={"file_id": file_uuid})
        return 0, remediation_info
    except Exception as e:
        err_message = f"Error in protection task restore file quarantine: {str(e)}"
        if remediation_info:
            error_process(file_uuid, file_name, scan_policy, remediation_info, "restore_file_quarantine", err_message, user)
            return -1, remediation_info
        else:
            error_process(file_uuid, file_name, scan_policy, info, "restore_file_quarantine", err_message, user)
            return -1, info
    finally:
        if remediation_info and full_path and cache:
            update_cache_remediation_info(cache, file_uuid, task_uuid, full_path, remediation_info)
        if local_file is not None:
            os.remove(local_file)

def generate_incidents(file_uuid, file_info, scan_info, remote_info, remediation_info, matched_result):
    from service.incidents_data_scanning import record_ds_incident

    copy_info = {}
    quarantine_info = {}
    tmp = [(copy_info, 'copy_status', 'copy_targets', 'copy_failed_message', ProtectionCopyStatus),
           (quarantine_info, 'quarantine_status', 'quarantine_targets', 'quarantine_failed_message', ProtectionQuarantineStatus)]
    for info_dict, status, targets, fail_msg, stat_enum in tmp:
        status_val = remediation_info[status]
        if status_val is not stat_enum.INIT:
            info_dict[status] = status_val
            info_dict[targets] = [x.get('full_path') for x in remediation_info[targets]]
            if status_val is stat_enum.FAILED:
                info_dict[fail_msg] = remediation_info[fail_msg]

    matched_policies = matched_result['policies']
    for mpolicy in matched_policies:
        # mrule = {
        #             "rinfo": {
        #             "policy_id": rule.get_attribute("policy_id"),
        #             "policy_name": rule.get_attribute("policy_name"),
        #             "rule_id": rule.get_attribute("id"),
        #             "rule_name": rule.get_attribute("name"),
        #         },
        #         "rtags": {"ml": [], "custom":[], "sensitivity":[], "compliance": []},
        # }
        curr_remed_info = {}
        if 'file_copy' in mpolicy['action']:
            curr_remed_info = copy_info
        elif 'file_quarantine' in mpolicy['action']:
            curr_remed_info = quarantine_info

        record_ds_incident(
            file_uuid=file_uuid, 
            file_info=file_info, 
            remediation_info=curr_remed_info, 
            scan_info=scan_info, 
            remote_info=remote_info, 
            mpolicy=mpolicy, 
            edm_match_info=matched_result['edm_match_info'], 
            idm_match_info=matched_result['idm_match_info'], 
            logger=logger
        )

def copy_local_file_to_protection_folder(task_uuid, local_file):
    try:
        target_folder = download_file_path / task_uuid
        target_folder.mkdir(parents=True, exist_ok=True)
        local_file_path = Path(local_file)
        shutil.copy(local_file_path, target_folder)
        target_folder_file = str(target_folder / local_file_path.name)
        return target_folder_file
    except Exception as e:
        logger.error(f"Failed to do remediation actions: {e}")
        return None


@huey.task()
def do_discover_actions(task_uuid, file_uuid, local_file, file_info, scan_info, remote_info, matched_result):
    logger.debug(f"do_discover_actions: {task_uuid} {file_uuid} {local_file} {matched_result}")

    scan_policy = get_scan_policy(id=task_uuid)
    if scan_policy is None:
        logger.error(f"Scan policy is not exist or have been deleted, no need to continue.")
        return None

    remediation_info = {
        "copy_status": ProtectionCopyStatus.INIT,
        "copy_targets": [],
        "copy_failed_message": "",
        "quarantine_status": ProtectionQuarantineStatus.INIT,
        "quarantine_targets": [],
        "quarantine_failed_message": "",
        "restore_quarantine_failed_message": ""
    }

    try:
        remediation_actions = matched_result['remediation_actions']
        if "file_copy" in remediation_actions:
            task = protection_task_file_copy(task_uuid, file_uuid, local_file=local_file, last_modify_time=remote_info.get('last_modified'))
            _, file_copy_result = task.get(blocking=True, timeout=900)
            if file_copy_result:
                remediation_info['copy_status'] = file_copy_result['copy_status']
                remediation_info['copy_targets'] = file_copy_result['copy_targets']
                remediation_info['copy_failed_message'] = file_copy_result['copy_failed_message']

        if "file_quarantine" in remediation_actions and "file_copy" not in remediation_actions:
            task = protection_task_file_quarantine(task_uuid, file_uuid, local_file=local_file, last_modify_time=remote_info.get('last_modified'))
            _, file_quarantine_result = task.get(blocking=True, timeout=900)
            if file_quarantine_result:
                remediation_info['quarantine_status'] = file_quarantine_result['quarantine_status']
                remediation_info['quarantine_targets'] = file_quarantine_result['quarantine_targets']
                remediation_info['quarantine_failed_message'] = file_quarantine_result['quarantine_failed_message']

        logger.debug(f"remediation_info: {remediation_info}")
        return None
    except Exception as e:
        logger.error(f"Failed to do remediation actions: {e}")
        return None
    finally:
        if scan_policy:
            generate_incidents(file_uuid, file_info, scan_info, remote_info, remediation_info, matched_result)

        if local_file is not None:
            os.remove(local_file)
