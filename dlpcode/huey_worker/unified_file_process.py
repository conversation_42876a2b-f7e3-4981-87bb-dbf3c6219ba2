import os
import json
import time
import hashlib
from huey import <PERSON><PERSON><PERSON><PERSON><PERSON>, cron
from util.common_log import get_logger
from util.config import configs
from service.unified_file_service import UnifiedFileScheduler
from service.task_management_service import TaskManagementService
from storage.model.activity import update_activity_status
from storage.util.enum import ActivityStatus
from ddr.model.task import get_ddr_task
from exts import SessionExpired

unified_file_huey = RedisHuey("unified_file_task", db=configs["huey"]["db"])
logger = get_logger("unified_file_task")
is_file_unified = configs.get("file_unified_queue", {}).get("enabled", False)


@unified_file_huey.periodic_task(cron(minute='*/1'))
def process_unified_file_queue():
    """
    From the unified file scheduler, get the next file to process and dispatch it to the
    appropriate task queue.
    """
    try:
        scheduler = UnifiedFileScheduler()

        # Each cycle process 10 files at most
        max_files_per_cycle = configs.get("file_unified_queue", {}).get("max_files_per_cycle", 10)
        processed_count = 0

        for _ in range(max_files_per_cycle):
            # Get next file to process
            file_item = scheduler.get_next_file_to_process()
            if not file_item:
                break

            # Schedule processing based on source type
            success = False
            source_type = file_item.get("source_type")

            if source_type == "scan_policy":
                success = process_scan_policy_file(file_item)
            elif source_type == "ddr":
                success = process_ddr_file(file_item)
            else:
                logger.error(f"Unknown source type: {source_type}")
                scheduler.decrement_processing_counter(source_type)
                continue

            if success:
                processed_count += 1
                logger.debug(f"Successfully processed {source_type} file")
            else:
                logger.error(f"Failed to process {source_type} file")
                scheduler.decrement_processing_counter(source_type)

        if processed_count > 0:
            logger.info(f"Processed {processed_count} files in this cycle")

        # Clean up expired items every 5 minutes
        current_minute = int(time.time() / 60)
        if current_minute % 5 == 0:
            expired_count = scheduler.clear_expired_items(max_age_hours=24)
            if expired_count > 0:
                logger.info(f"Cleared {expired_count} expired items")

    except Exception as e:
        logger.error(f"Error processing unified file queue: {e}")


def process_scan_policy_file(file_item: dict) -> bool:
    """
    Dispatch scan_policy file to download and analyze task
    Args:
        file_item: scan_policy file item
    Returns:
        bool: True if the file is successfully dispatched, False otherwise
    """
    try:
        from huey_worker.doing_download_task import add_analyze_task, download_file, download_huey

        scan_policy_id = file_item["task_uuid"]
        file_info = file_item["file_info"]
        params = file_item["params"]
        session_key = file_item["session_key"]
        backlog_hash = file_item["backlog_hash"]

        # Check if session is still valid
        tms = TaskManagementService(scan_policy_id, session_key)
        if not tms.session_tracker.is_alive():
            logger.warning(f"Scan policy session {session_key} is not alive, skipping file")
            return False

        # Check if task is still in scanning state
        if not tms.is_scanning():
            logger.warning(f"Scan policy task {scan_policy_id} is not in scanning state, skipping file")
            return False

        # Create download task
        download_file_task = download_file.s(
            task_uuid=scan_policy_id,
            file_info=file_info,
            params=params,
            session_key=session_key,
            backlog_hash=backlog_hash
        )
        pipeline = download_file_task.then(add_analyze_task, previous_task_id=download_file_task.id)
        result = download_huey.enqueue(pipeline)

        if result:
            logger.debug(f"Successfully enqueued scan_policy file processing pipeline for task {scan_policy_id}")
            return True
        else:
            logger.error(f"Failed to enqueue scan_policy file processing pipeline for task {scan_policy_id}")
            return False

    except Exception as e:
        logger.error(f"Error processing scan_policy file: {e}")
        return False


def process_ddr_file(file_item: dict) -> bool:
    """
    Dispatch DDR file to download and analyze task
    Args:
        file_item: DDR file item
    Returns:
        bool: True if the file is successfully dispatched, False otherwise
    """
    try:
        from huey_worker.doing_download_task import download_file
        from huey_worker.analyze_worker import add_analyze_task
        from huey_worker.dispatch_download_task import download_huey

        ddr_uuid = file_item["task_uuid"]
        file_info = file_item["file_info"]
        download_params = file_item["params"]
        session_key = file_item["session_key"]
        backlog_hash = file_item["backlog_hash"]

        # Validate DDR task
        ddr_task = get_ddr_task(id=ddr_uuid)
        if not ddr_task or not ddr_task.enabled:
            logger.error(f"DDR task {ddr_uuid} not found or disabled")
            return False

        # Validate session
        tms = TaskManagementService(ddr_uuid, session_key, is_ddr=True)
        if not tms.session_tracker.is_alive():
            logger.warning(f"DDR session {session_key} is not alive, skipping file")
            return False

        # Create download task
        download_file_task = download_file.s(
            task_uuid=ddr_uuid,
            file_info=file_info,
            params=download_params,
            session_key=session_key,
            backlog_hash=backlog_hash
        )
        pipeline = download_file_task.then(add_analyze_task, previous_task_id=download_file_task.id)
        result = download_huey.enqueue(pipeline)
        if result:
            logger.debug(f"Successfully enqueued DDR file processing pipeline for task {ddr_uuid}")
            return True
        else:
            logger.error(f"Failed to enqueue DDR file processing pipeline for task {ddr_uuid}")
            return False

    except Exception as e:
        logger.error(f"Error processing DDR file: {e}")
        return False


@unified_file_huey.task()
def handle_ddr_analyze_complete(ddr_task_id: str, activity_id: str, analyze_result: dict):
    """
    Deal with DDR analysis completion event
    Args:
        ddr_task_id: DDR task uuid
        activity_id: DDR activity uuid
        analyze_result: analysis result
    """
    try:
        logger.info(f"Handling DDR analyze completion for activity {activity_id}")

        # Decrement DDR processing counter
        scheduler = UnifiedFileScheduler()
        scheduler.decrement_processing_counter("ddr")

        # Update DDR activity status
        if analyze_result and not analyze_result.get("error", False):
            # Analysis succeeded
            update_activity_status(activity_id, ActivityStatus.COMPLETED.value)
            logger.info(f"DDR activity {activity_id} analysis completed successfully")
        else:
            # Analysis failed
            update_activity_status(activity_id, ActivityStatus.FAILED.value)
            logger.error(f"DDR activity {activity_id} analysis failed")

        # Here can add DDR-specific result handling logic, such as:
        # Trigger DDR policy matching, generate alerts, etc.

    except Exception as e:
        logger.error(f"Error handling DDR analyze completion for activity {activity_id}: {e}")


@unified_file_huey.task()
def handle_scan_policy_analyze_complete(task_uuid: str, file_info: dict, analyze_result: dict):
    """
    Deal with scan_policy analysis completion event
    Args:
        task_uuid: scan task uuid
        file_info: file info
        analyze_result: analysis result
    """
    try:
        logger.debug(f"Handling scan_policy analyze completion for task {task_uuid}")

        # Decrement scan_policy processing counter
        scheduler = UnifiedFileScheduler()
        scheduler.decrement_processing_counter("scan_policy")

        # Here can add scan_policy-specific result handling logic

    except Exception as e:
        logger.error(f"Error handling scan_policy analyze completion for task {task_uuid}: {e}")


@unified_file_huey.periodic_task(cron(minute='*/10'))  # Every 10 minutes
def log_unified_queue_statistics():
    """Log unified queue statistics"""
    try:
        scheduler = UnifiedFileScheduler()
        status = scheduler.get_queue_status()

        logger.info(f"Unified Queue Statistics: "
                   f"Total Queue: {status.get('total_queue_size', 0)}, "
                   f"DDR Queue: {status.get('ddr_queue_size', 0)}, "
                   f"Scan Policy Queue: {status.get('scan_policy_queue_size', 0)}, "
                   f"DDR Processing: {status.get('ddr_processing_count', 0)}, "
                   f"Scan Policy Processing: {status.get('scan_policy_processing_count', 0)}")

    except Exception as e:
        logger.error(f"Error logging unified queue statistics: {e}")


# Huey signal handlers
@unified_file_huey.signal()
def unified_task_complete_handler(signal, task, exc=None):
    """Unified task complete handler"""
    if exc:
        logger.error(f"Unified processor task {task.name} failed with exception: {exc}")
    else:
        logger.debug(f"Unified processor task {task.name} completed successfully")


@unified_file_huey.signal()
def unified_task_error_handler(signal, task, exc=None):
    """Unified task error handler"""
    logger.error(f"Unified processor task {task.name} error: {exc}")

    # If the task is a file processing task, decrement the counter
    if task.name in ["process_scan_policy_file", "process_ddr_file"]:
        try:
            scheduler = UnifiedFileScheduler()
            logger.error(f"File processing task failed, manual counter adjustment may be needed")
        except:
            pass


def get_unified_queue_status():
    """Get unified queue status(for external use)"""
    try:
        scheduler = UnifiedFileScheduler()
        return scheduler.get_queue_status()
    except Exception as e:
        logger.error(f"Error getting unified queue status: {e}")
        return {}


def force_process_files(max_files: int = 5):
    """Force process files(for testing)"""
    try:
        scheduler = UnifiedFileScheduler()
        processed_count = 0

        for _ in range(max_files):
            file_item = scheduler.get_next_file_to_process()
            if not file_item:
                break

            source_type = file_item.get("source_type")
            success = False

            if source_type == "scan_policy":
                success = process_scan_policy_file(file_item)
            elif source_type == "ddr":
                success = process_ddr_file(file_item)

            if success:
                processed_count += 1
            else:
                scheduler.decrement_processing_counter(source_type)

        logger.info(f"Force processed {processed_count} files")
        return processed_count

    except Exception as e:
        logger.error(f"Error force processing files: {e}")
        return 0
