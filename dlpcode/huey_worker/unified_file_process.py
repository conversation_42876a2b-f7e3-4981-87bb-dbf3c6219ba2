import os
import time
from typing import TypedDict, Optional
from huey import RedisHuey, cron
from util.common_log import get_logger
from util.config import configs
from service.unified_file_service import UnifiedFileScheduler
from service.task_management_service import TaskManagementService
from storage.model.activity import update_activity_status
from storage.util.enum import ActivityStatus
from ddr.model.task import get_ddr_task
from exts import SessionExpired


unified_file_huey = RedisHuey("unified_file_task", db=configs["huey"]["db"])
logger = get_logger("unified_file_task")
queue_config = configs.get("file_unified_queue", {})

class AnalyzeResult(TypedDict):
    error: bool
    sensitive_data: bool
    file_metadata: dict

def _enqueue_task(task_type: str, file_item: dict) -> bool:
    """Submit donwload task to huey"""
    from huey_worker.doing_download_task import download_file, add_analyze_task, download_huey

    download_file_task = download_file.s(
        task_uuid=file_item["task_uuid"],
        file_info=file_item["file_info"],
        params=file_item["params"],
        session_key=file_item["session_key"],
        backlog_hash=file_item["backlog_hash"]
    )
    pipeline = download_file_task.then(
        add_analyze_task, 
        previous_task_id=download_file_task.id
    )
    return download_huey.enqueue(pipeline)

@unified_file_huey.periodic_task(cron(minute='*/1'))
def process_unified_file_queue():
    """unified_file_queue task"""
    scheduler = UnifiedFileScheduler()
    for _ in range(queue_config.get("max_files_per_cycle", 10)):
        file_item = scheduler.get_next_file_to_process()
        if not file_item:
            break

        try:
            source_type = file_item["source_type"]
            if source_type not in ("scan_policy", "ddr"):
                logger.error(f"Unknown source type: {source_type}")
                continue

            # 验证任务状态
            if not _validate_task(file_item["task_uuid"], file_item["session_key"], is_ddr=(source_type == "ddr")):
                continue

            # 提交任务（下载无优先级，分析阶段区分优先级）
            if _enqueue_task(source_type, file_item):
                logger.debug(f"Enqueued {source_type} file: {file_item['file_info']['file']}")
            else:
                scheduler.decrement_processing_counter(source_type)
        except Exception as e:
            logger.error(f"Failed to process {file_item}: {e}")

# ---------------------------- 辅助函数 ----------------------------
def _validate_task(task_uuid: str, session_key: str, is_ddr: bool = False) -> bool:
    """Validate task status"""
    try:
        tms = TaskManagementService(task_uuid, session_key, is_ddr=is_ddr)
        return tms.session_tracker.is_alive() and (tms.is_scanning() if not is_ddr else True)
    except Exception as e:
        logger.error(f"Task validation failed: {e}")
        return False

# ---------------------------- 结果处理 ----------------------------
@unified_file_huey.task(retries=2)
def handle_ddr_analyze_complete(ddr_task_id: str, activity_id: str, result: AnalyzeResult):
    """DDR Result Processing"""
    try:
        UnifiedFileScheduler().decrement_processing_counter("ddr")
        status = ActivityStatus.COMPLETED if not result["error"] else ActivityStatus.FAILED
        update_activity_status(activity_id, status.value)
        if result["sensitive_data"]:
            # _trigger_alert(activity_id)
            pass
    except Exception as e:
        logger.error(f"Failed to handle DDR result: {e}")
        raise

@unified_file_huey.task()
def handle_scan_policy_analyze_complete(task_uuid: str, file_info: dict, result: AnalyzeResult):
    """Scan Policy Result Processing"""
    UnifiedFileScheduler().decrement_processing_counter("scan_policy")

# ---------------------------- 维护任务 ----------------------------
@unified_file_huey.periodic_task(cron(minute='*/30'))
def cleanup_expired_files():
    """Clean up expired files"""
    expired_count = UnifiedFileScheduler().clear_expired_items(queue_config.get("max_age_hours", 24))
    if expired_count > 0:
        logger.info(f"Cleared {expired_count} expired files")

# ---------------------------- External API ----------------------------
def get_queue_status() -> dict:
    """Get the status of the queue"""
    return UnifiedFileScheduler().get_queue_status()