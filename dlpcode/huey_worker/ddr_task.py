from typing import List, Dict, Any, Optional
from huey import PriorityRedisHuey, crontab
from ddr.service.ddr_service import DDRService
from ddr.model.task import get_ddr_tasks_dict, update_last_pull_time
from datetime import datetime, timedelta, timezones
from util.common_log import get_logger

logger = get_logger("ddr_task")

huey_db = 0
ddr_huey = PriorityRedisHuey("ddr_worker", db=huey_db)


@ddr_huey.task(priority=50)
def process_ddr_events(task: dict, 
                       start_time: datetime, end_time: datetime,
                       ddr_policy_ids: Optional[List[str]] = None,
                       target_operations: Optional[List[str]] = None,
                       scan_trigger_events: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Task for processing DDR events.
    
    Args:
        task: DDRTask dict
        ddr_policy_ids: List of DDR policy IDs
        target_operations: List of target operation types
        scan_trigger_events: List of scan trigger event types
    Returns:
        Result dictionary
    """
    try:
        logger.info(f"Starting DDR event processing for task: {task['name']}")

        ddr_service = DDRService(task)
        # Process platform events
        result = ddr_service.process_events(
            start_time=start_time,
            end_time=end_time,
            target_operations=target_operations,
            scan_trigger_events=scan_trigger_events
        )

        logger.info(f"DDR event processing completed for task {task['name']}: {result}")
        return result

    except Exception as e:
        logger.error(f"Error in DDR event processing task: {e}")
        return {"error": str(e), "processed": 0, "scans_triggered": 0}


# Universal schedule: check all active DDR tasks and run if interval reached
@ddr_huey.periodic_task(crontab(minute='*/2'))
def scheduled_ddr_dispatcher():
    """
    Universal DDR task scheduler: checks all active DDR tasks and runs those whose interval has elapsed.
    """
    now = datetime.now(timezone.utc)
    try:
        tasks = get_ddr_tasks_dict(enabled=True)
    except Exception as e:
        logger.error(f"Failed to fetch active DDR tasks: {e}")
        return

    for task in tasks:
        try:
            last = now - timedelta(seconds=125)
            process_ddr_events.schedule(
                args=(task, last, now),
                kwargs={
                    'scan_trigger_events': task.get('trigger_events', [])
                },
                delay=1
            )

        except Exception as e:
            logger.error(f"Error processing DDR task {task['name']}: {e}")
