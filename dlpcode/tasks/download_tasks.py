"""
Download task interfaces for writing to queues
"""
from celery_worker.celery_app import app
from typing import Dict, Any


def submit_dispatch_download_task(task_uuid: str, group_file_path: str, 
                                 file_path: str, params: dict, 
                                 session_key: str, priority: str = 'NORMAL') -> str:
    """
    Submit dispatch download task to queue
    
    Args:
        task_uuid: Task UUID
        group_file_path: Group file path
        file_path: File path
        params: Task parameters
        session_key: Session key
        priority: Task priority
        
    Returns:
        Task ID
    """
    from celery_worker.celery_app import get_priority_level
    from celery_worker.download_worker import dispatch_download_task
    
    priority_level = get_priority_level(priority)
    
    result = dispatch_download_task.apply_async(
        args=(task_uuid, group_file_path, file_path, params, session_key),
        priority=priority_level,
        queue='download'
    )
    
    return result.id


def submit_download_file_task(task_uuid: str, file_info: dict, params: dict,
                             session_key: str, backlog_hash: str,
                             priority: str = 'NORMAL') -> str:
    """
    Submit file download task to queue
    
    Args:
        task_uuid: Task UUID
        file_info: File information
        params: Task parameters
        session_key: Session key
        backlog_hash: Backlog hash
        priority: Task priority
        
    Returns:
        Task ID
    """
    from celery_worker.celery_app import get_priority_level
    from celery_worker.download_worker import download_file_task
    
    priority_level = get_priority_level(priority)
    
    result = download_file_task.apply_async(
        args=(task_uuid, file_info, params, session_key, backlog_hash),
        priority=priority_level,
        queue='download'
    )
    
    return result.id


def submit_ddr_download_task(task_uuid: str, file_info: dict, params: dict,
                            session_key: str, backlog_hash: str) -> str:
    """
    Submit DDR file download task with high priority
    
    Args:
        task_uuid: Task UUID
        file_info: File information
        params: Task parameters
        session_key: Session key
        backlog_hash: Backlog hash
        
    Returns:
        Task ID
    """
    from celery_worker.download_worker import download_file_task
    
    result = download_file_task.apply_async(
        args=(task_uuid, file_info, params, session_key, backlog_hash),
        priority=8,  # DDR priority
        queue='download'
    )
    
    return result.id


def create_download_analyze_chain(task_uuid: str, file_info: dict, params: dict,
                                 session_key: str, backlog_hash: str,
                                 priority: str = 'DDR') -> str:
    """
    Create a chain of download -> analyze tasks for DDR processing
    
    Args:
        task_uuid: Task UUID
        file_info: File information
        params: Task parameters
        session_key: Session key
        backlog_hash: Backlog hash
        priority: Task priority
        
    Returns:
        Chain task ID
    """
    from celery import chain
    from celery_worker.celery_app import get_priority_level
    from celery_worker.download_worker import download_file_task
    from celery_worker.analyze_worker import analyze_worker_task
    
    priority_level = get_priority_level(priority)
    
    # Create download task
    download_task = download_file_task.s(task_uuid, file_info, params, session_key, backlog_hash)
    
    # Create analyze task (will receive download result)
    analyze_task = analyze_worker_task.s(file_info, task_uuid, session_key, backlog_hash)
    
    # Create chain
    pipeline = chain(download_task, analyze_task)
    
    result = pipeline.apply_async(
        priority=priority_level,
        queue='download'
    )
    
    return result.id


def get_download_queue_status() -> Dict[str, Any]:
    """
    Get download queue status
    
    Returns:
        Queue status information
    """
    inspect = app.control.inspect()
    
    # Get active tasks
    active_tasks = inspect.active()
    reserved_tasks = inspect.reserved()
    scheduled_tasks = inspect.scheduled()
    
    download_active = 0
    download_reserved = 0
    download_scheduled = 0
    
    if active_tasks:
        for worker, tasks in active_tasks.items():
            download_active += len([t for t in tasks if t.get('delivery_info', {}).get('routing_key') == 'download'])
    
    if reserved_tasks:
        for worker, tasks in reserved_tasks.items():
            download_reserved += len([t for t in tasks if t.get('delivery_info', {}).get('routing_key') == 'download'])
    
    if scheduled_tasks:
        for worker, tasks in scheduled_tasks.items():
            download_scheduled += len([t for t in tasks if t.get('delivery_info', {}).get('routing_key') == 'download'])
    
    return {
        'active': download_active,
        'reserved': download_reserved,
        'scheduled': download_scheduled,
        'total_pending': download_reserved + download_scheduled
    }
