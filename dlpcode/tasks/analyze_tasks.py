"""
Analyze task interfaces for writing to queues
"""
from celery_worker.celery_app import app
from typing import Dict, Any


def submit_analyze_task(local_file: str, remote_info: dict, file_uuid: str, 
                       scan_info: dict, task_uuid: str, session_key: str, 
                       backlog_hash: str, priority: str = 'NORMAL') -> str:
    """
    Submit file analysis task to queue
    
    Args:
        local_file: Path to local file
        remote_info: Remote file information
        file_uuid: File UUID
        scan_info: Scan configuration
        task_uuid: Task UUID
        session_key: Session key
        backlog_hash: Backlog hash
        priority: Task priority ('LOW', 'NORMAL', 'HIGH', 'DDR', 'CRITICAL', 'EMERGENCY')
        
    Returns:
        Task ID
    """
    from celery_worker.celery_app import get_priority_level
    
    priority_level = get_priority_level(priority)
    
    # Import the actual task
    from celery_worker.analyze_worker import analyze_worker_task
    
    result = analyze_worker_task.apply_async(
        args=(local_file, remote_info, file_uuid, scan_info, task_uuid, session_key, backlog_hash),
        priority=priority_level,
        queue='analyze'
    )
    
    return result.id


def submit_priority_analyze_task(local_file: str, remote_info: dict, file_uuid: str,
                                scan_info: dict, task_uuid: str, session_key: str,
                                backlog_hash: str) -> str:
    """
    Submit high priority file analysis task to queue
    
    Args:
        local_file: Path to local file
        remote_info: Remote file information
        file_uuid: File UUID
        scan_info: Scan configuration
        task_uuid: Task UUID
        session_key: Session key
        backlog_hash: Backlog hash
        
    Returns:
        Task ID
    """
    from celery_worker.analyze_worker import priority_analyze_worker_task
    
    result = priority_analyze_worker_task.apply_async(
        args=(local_file, remote_info, file_uuid, scan_info, task_uuid, session_key, backlog_hash),
        priority=9,  # CRITICAL priority
        queue='analyze'
    )
    
    return result.id


def get_analyze_queue_status() -> Dict[str, Any]:
    """
    Get analyze queue status
    
    Returns:
        Queue status information
    """
    inspect = app.control.inspect()
    
    # Get active tasks
    active_tasks = inspect.active()
    reserved_tasks = inspect.reserved()
    scheduled_tasks = inspect.scheduled()
    
    analyze_active = 0
    analyze_reserved = 0
    analyze_scheduled = 0
    
    if active_tasks:
        for worker, tasks in active_tasks.items():
            analyze_active += len([t for t in tasks if t.get('delivery_info', {}).get('routing_key') == 'analyze'])
    
    if reserved_tasks:
        for worker, tasks in reserved_tasks.items():
            analyze_reserved += len([t for t in tasks if t.get('delivery_info', {}).get('routing_key') == 'analyze'])
    
    if scheduled_tasks:
        for worker, tasks in scheduled_tasks.items():
            analyze_scheduled += len([t for t in tasks if t.get('delivery_info', {}).get('routing_key') == 'analyze'])
    
    return {
        'active': analyze_active,
        'reserved': analyze_reserved,
        'scheduled': analyze_scheduled,
        'total_pending': analyze_reserved + analyze_scheduled
    }
