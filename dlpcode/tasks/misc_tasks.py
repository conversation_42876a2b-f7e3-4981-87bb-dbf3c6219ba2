"""
Miscellaneous task interfaces for writing to queues
"""
from celery_worker.celery_app import app
from typing import Dict, Any


def submit_tag_update_task(remote_info: dict, tags: list, priority: str = 'NORMAL') -> str:
    """
    Submit tag update task to queue
    
    Args:
        remote_info: Remote file information
        tags: Tags to update
        priority: Task priority
        
    Returns:
        Task ID
    """
    from celery_worker.celery_app import get_priority_level
    from celery_worker.misc_worker import update_remote_tag_task
    
    priority_level = get_priority_level(priority)
    
    result = update_remote_tag_task.apply_async(
        args=(remote_info, tags),
        priority=priority_level,
        queue='default'
    )
    
    return result.id


def submit_report_generation_task(report_id: str, priority: str = 'NORMAL') -> str:
    """
    Submit report generation task to queue
    
    Args:
        report_id: Report ID
        priority: Task priority
        
    Returns:
        Task ID
    """
    from celery_worker.celery_app import get_priority_level
    from celery_worker.report_worker import generate_report_task
    
    priority_level = get_priority_level(priority)
    
    result = generate_report_task.apply_async(
        args=(report_id,),
        priority=priority_level,
        queue='report'
    )
    
    return result.id


def submit_protection_action_task(task_uuid: str, file_uuid: str, local_file_copy: str,
                                 file_info: dict, scan_info: dict, remote_info: dict,
                                 matched_result: dict, priority: str = 'HIGH') -> str:
    """
    Submit protection action task to queue
    
    Args:
        task_uuid: Task UUID
        file_uuid: File UUID
        local_file_copy: Local file copy path
        file_info: File information
        scan_info: Scan information
        remote_info: Remote information
        matched_result: Matched result
        priority: Task priority
        
    Returns:
        Task ID
    """
    from celery_worker.celery_app import get_priority_level
    from celery_worker.protection_worker import protection_action_task
    
    priority_level = get_priority_level(priority)
    
    result = protection_action_task.apply_async(
        args=(task_uuid, file_uuid, local_file_copy, file_info, scan_info, remote_info, matched_result),
        priority=priority_level,
        queue='protection'
    )
    
    return result.id


def submit_fetch_storage_task(storage_config: dict, priority: str = 'NORMAL') -> str:
    """
    Submit fetch storage task to queue
    
    Args:
        storage_config: Storage configuration
        priority: Task priority
        
    Returns:
        Task ID
    """
    from celery_worker.celery_app import get_priority_level
    from celery_worker.storage_worker import fetch_storage_task
    
    priority_level = get_priority_level(priority)
    
    result = fetch_storage_task.apply_async(
        args=(storage_config,),
        priority=priority_level,
        queue='default'
    )
    
    return result.id


def submit_unified_file_process_task(priority: str = 'NORMAL') -> str:
    """
    Submit unified file processing task to queue
    
    Args:
        priority: Task priority
        
    Returns:
        Task ID
    """
    from celery_worker.celery_app import get_priority_level
    from celery_worker.unified_worker import process_unified_files_task
    
    priority_level = get_priority_level(priority)
    
    result = process_unified_files_task.apply_async(
        priority=priority_level,
        queue='default'
    )
    
    return result.id


def get_all_queue_status() -> Dict[str, Any]:
    """
    Get status of all queues
    
    Returns:
        All queue status information
    """
    inspect = app.control.inspect()
    
    # Get active tasks
    active_tasks = inspect.active()
    reserved_tasks = inspect.reserved()
    scheduled_tasks = inspect.scheduled()
    
    queue_stats = {
        'analyze': {'active': 0, 'reserved': 0, 'scheduled': 0},
        'ddr': {'active': 0, 'reserved': 0, 'scheduled': 0},
        'download': {'active': 0, 'reserved': 0, 'scheduled': 0},
        'report': {'active': 0, 'reserved': 0, 'scheduled': 0},
        'protection': {'active': 0, 'reserved': 0, 'scheduled': 0},
        'default': {'active': 0, 'reserved': 0, 'scheduled': 0}
    }
    
    # Count active tasks
    if active_tasks:
        for worker, tasks in active_tasks.items():
            for task in tasks:
                queue = task.get('delivery_info', {}).get('routing_key', 'default')
                if queue in queue_stats:
                    queue_stats[queue]['active'] += 1
    
    # Count reserved tasks
    if reserved_tasks:
        for worker, tasks in reserved_tasks.items():
            for task in tasks:
                queue = task.get('delivery_info', {}).get('routing_key', 'default')
                if queue in queue_stats:
                    queue_stats[queue]['reserved'] += 1
    
    # Count scheduled tasks
    if scheduled_tasks:
        for worker, tasks in scheduled_tasks.items():
            for task in tasks:
                queue = task.get('delivery_info', {}).get('routing_key', 'default')
                if queue in queue_stats:
                    queue_stats[queue]['scheduled'] += 1
    
    # Calculate totals
    for queue in queue_stats:
        queue_stats[queue]['total_pending'] = queue_stats[queue]['reserved'] + queue_stats[queue]['scheduled']
    
    return queue_stats
