"""
DDR task interfaces for writing to queues
"""
from celery_worker.celery_app import app
from typing import List, Dict, Any, Optional
from datetime import datetime


def submit_ddr_events_task(task: dict, start_time: datetime, end_time: datetime,
                          ddr_policy_ids: Optional[List[str]] = None,
                          target_operations: Optional[List[str]] = None,
                          scan_trigger_events: Optional[List[str]] = None,
                          priority: str = 'DDR') -> str:
    """
    Submit DDR events processing task to queue
    
    Args:
        task: DDR task dictionary
        start_time: Start time for event processing
        end_time: End time for event processing
        ddr_policy_ids: List of DDR policy IDs
        target_operations: List of target operation types
        scan_trigger_events: List of scan trigger event types
        priority: Task priority ('DDR', 'EMERGENCY')
        
    Returns:
        Task ID
    """
    from celery_worker.celery_app import get_priority_level
    from celery_worker.ddr_worker import process_ddr_events_task
    
    priority_level = get_priority_level(priority)
    
    result = process_ddr_events_task.apply_async(
        args=(task, start_time, end_time),
        kwargs={
            'ddr_policy_ids': ddr_policy_ids,
            'target_operations': target_operations,
            'scan_trigger_events': scan_trigger_events
        },
        priority=priority_level,
        queue='ddr'
    )
    
    return result.id


def submit_emergency_ddr_task(task: dict, start_time: datetime, end_time: datetime,
                             ddr_policy_ids: Optional[List[str]] = None,
                             target_operations: Optional[List[str]] = None,
                             scan_trigger_events: Optional[List[str]] = None) -> str:
    """
    Submit emergency DDR task with highest priority
    
    Args:
        task: DDR task dictionary
        start_time: Start time for event processing
        end_time: End time for event processing
        ddr_policy_ids: List of DDR policy IDs
        target_operations: List of target operation types
        scan_trigger_events: List of scan trigger event types
        
    Returns:
        Task ID
    """
    from celery_worker.ddr_worker import emergency_ddr_events_task
    
    result = emergency_ddr_events_task.apply_async(
        args=(task, start_time, end_time),
        kwargs={
            'ddr_policy_ids': ddr_policy_ids,
            'target_operations': target_operations,
            'scan_trigger_events': scan_trigger_events
        },
        priority=10,  # EMERGENCY priority
        queue='ddr'
    )
    
    return result.id


def schedule_ddr_dispatcher() -> str:
    """
    Schedule DDR dispatcher task
    
    Returns:
        Task ID
    """
    from celery_worker.ddr_worker import scheduled_ddr_dispatcher_task
    
    result = scheduled_ddr_dispatcher_task.apply_async(
        priority=8,  # DDR priority
        queue='ddr'
    )
    
    return result.id


def get_ddr_queue_status() -> Dict[str, Any]:
    """
    Get DDR queue status
    
    Returns:
        Queue status information
    """
    inspect = app.control.inspect()
    
    # Get active tasks
    active_tasks = inspect.active()
    reserved_tasks = inspect.reserved()
    scheduled_tasks = inspect.scheduled()
    
    ddr_active = 0
    ddr_reserved = 0
    ddr_scheduled = 0
    
    if active_tasks:
        for worker, tasks in active_tasks.items():
            ddr_active += len([t for t in tasks if t.get('delivery_info', {}).get('routing_key') == 'ddr'])
    
    if reserved_tasks:
        for worker, tasks in reserved_tasks.items():
            ddr_reserved += len([t for t in tasks if t.get('delivery_info', {}).get('routing_key') == 'ddr'])
    
    if scheduled_tasks:
        for worker, tasks in scheduled_tasks.items():
            ddr_scheduled += len([t for t in tasks if t.get('delivery_info', {}).get('routing_key') == 'ddr'])
    
    return {
        'active': ddr_active,
        'reserved': ddr_reserved,
        'scheduled': ddr_scheduled,
        'total_pending': ddr_reserved + ddr_scheduled
    }


def revoke_ddr_tasks_by_session(scan_policy_id: str, session_key: str) -> int:
    """
    Revoke DDR tasks for a specific session
    
    Args:
        scan_policy_id: Scan policy ID
        session_key: Session key
        
    Returns:
        Number of tasks revoked
    """
    from service.celery_queue_management_service import CeleryQueueManagementService
    
    service = CeleryQueueManagementService()
    return service.revoke_tasks_by_session(scan_policy_id, session_key)
