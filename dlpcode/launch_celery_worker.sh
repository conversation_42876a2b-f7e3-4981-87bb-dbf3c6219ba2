#!/bin/bash

# Celery worker startup script for DLP system
# This script replaces the huey worker startup script

# Check if log directory exists, if not, create it
if [ ! -d "log" ]; then
  mkdir log
fi

# Set environment variables
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Function to start a celery worker
start_worker() {
    local worker_name=$1
    local queue_name=$2
    local concurrency=${3:-4}
    local log_level=${4:-info}
    
    echo "Starting Celery worker: $worker_name for queue: $queue_name"
    
    nohup celery -A celery_app worker \
        --hostname="$worker_name@%h" \
        --queues="$queue_name" \
        --concurrency="$concurrency" \
        --loglevel="$log_level" \
        --logfile="log/${worker_name}.log" \
        --pidfile="log/${worker_name}.pid" \
        --detach \
        --pool=prefork \
        --without-gossip \
        --without-mingle \
        --without-heartbeat > /dev/null 2>&1 &
    
    echo "Started $worker_name worker (PID file: log/${worker_name}.pid)"
}

# Function to start celery beat scheduler
start_beat() {
    echo "Starting Celery beat scheduler"
    
    nohup celery -A celery_app beat \
        --loglevel=info \
        --logfile=log/celery_beat.log \
        --pidfile=log/celery_beat.pid \
        --detach > /dev/null 2>&1 &
    
    echo "Started Celery beat scheduler (PID file: log/celery_beat.pid)"
}

# Start workers for different queues with appropriate concurrency
echo "Starting Celery workers..."

# Analyze worker - high concurrency for file analysis
start_worker "analyze_worker" "analyze" 8 "info"

# DDR worker - medium concurrency with high priority
start_worker "ddr_worker" "ddr" 4 "info"

# Download workers - medium concurrency
start_worker "download_worker" "download" 6 "info"

# Default worker - for miscellaneous tasks
start_worker "default_worker" "default" 2 "info"

# Report worker - low concurrency for report generation
start_worker "report_worker" "report" 2 "info"

# Protection worker - low concurrency for protection actions
start_worker "protection_worker" "protection" 2 "info"

# Start beat scheduler for periodic tasks
start_beat

echo "All Celery workers and beat scheduler started successfully!"
echo "Check log files in the 'log' directory for worker output."
echo "Use 'stop_celery_worker.sh' to stop all workers."

# Wait a moment to let workers start
sleep 2

# Show worker status
echo ""
echo "Checking worker status..."
celery -A celery_app inspect active_queues 2>/dev/null || echo "Workers are starting up..."
