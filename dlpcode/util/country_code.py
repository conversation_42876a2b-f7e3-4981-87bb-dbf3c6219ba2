import pycountry

OTHER_AMERICAS = {
    "MX", "BR", "AR", "CL", "CO", "PE", "VE", "UY", "EC", "BO", "PY", "GY", "SR", "GF",
    "CR", "PA", "GT", "CU", "DO", "HN", "NI", "SV", "BS", "BB", "BZ", "JM", "TT"
}

EUROPE = {
    "GB", "DE", "FR", "IT", "ES", "NL", "BE", "SE", "CH", "NO", "DK", "FI", "PL", "PT", "GR",
    "IE", "AT", "CZ", "HU", "RO", "BG", "SK", "HR", "SI", "EE", "LV", "LT", "LU", "IS", "LI", "MT", "MC", "SM"
}

ASIA = {
    "CN", "JP", "KR", "SG", "IN", "TH", "VN", "MY", "PH", "ID", "HK", "TW", "AE", "SA", "IL", "PK", "BD", "LK"
}

AWS_REGION_TO_COUNTRY = {
    "us-east-1": "US",
    "us-west-1": "US",
    "us-west-2": "US",
    "ca-central-1": "CA",
    "eu-west-1": "IE",
    "eu-central-1": "DE",
    "eu-west-3": "FR",
    "eu-north-1": "SE",
    "eu-south-1": "IT",
    "ap-northeast-1": "JP",
    "ap-northeast-2": "KR",
    "ap-southeast-1": "SG",
    "ap-southeast-2": "AU",
    "ap-south-1": "IN",
    "sa-east-1": "BR",
    "me-south-1": "BH",
    "af-south-1": "ZA",
    "cn-north-1": "CN",
    "cn-northwest-1": "CN",
    "us-gov-west-1": "US",
    "us-gov-east-1": "US",
}

def normalize_country_code(code: str) -> str:
    if not code:
        return None
    code = code.strip().upper()
    try:
        if len(code) == 2:
            country = pycountry.countries.get(alpha_2=code)
        elif len(code) == 3:
            country = pycountry.countries.get(alpha_3=code)
        else:
            return None
        return country.alpha_2 if country else None
    except Exception:
        return None

def get_region_from_input(input_value: str) -> str:
    if not input_value:
        return "Other Regions"

    input_value = input_value.strip().lower()
    
    if input_value in AWS_REGION_TO_COUNTRY:
        alpha2 = AWS_REGION_TO_COUNTRY[input_value]
    else:
        alpha2 = normalize_country_code(input_value)

    if not alpha2:
        return "Other Regions"

    if alpha2 == "US":
        return "United States"
    elif alpha2 == "CA":
        return "Canada"
    elif alpha2 in OTHER_AMERICAS:
        return "Other Americas"
    elif alpha2 in EUROPE:
        return "Europe"
    elif alpha2 in ASIA:
        return "Asia"
    else:
        return "Other Regions"
    
if __name__ == "__main__":
    print(get_region_from_input("CHE"))