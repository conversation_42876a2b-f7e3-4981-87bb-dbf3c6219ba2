#!/usr/bin/env python3
"""
验证Celery架构的脚本
检查tasks和celery_worker目录的文件结构和导入
"""

import os
import sys
from pathlib import Path

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✓ {description}: {file_path}")
        return True
    else:
        print(f"✗ {description}: {file_path} (不存在)")
        return False

def check_directory_structure():
    """检查目录结构"""
    print("=== 检查目录结构 ===")
    
    base_dir = Path(".")
    
    # 检查主要目录
    tasks_dir = base_dir / "tasks"
    celery_worker_dir = base_dir / "celery_worker"
    
    dirs_ok = True
    dirs_ok &= check_file_exists(tasks_dir, "Tasks目录")
    dirs_ok &= check_file_exists(celery_worker_dir, "Celery Worker目录")
    
    # 检查tasks目录文件
    print("\n--- Tasks接口文件 ---")
    task_files = [
        "tasks/__init__.py",
        "tasks/analyze_tasks.py",
        "tasks/ddr_tasks.py", 
        "tasks/download_tasks.py",
        "tasks/misc_tasks.py"
    ]
    
    for file_path in task_files:
        dirs_ok &= check_file_exists(file_path, f"任务接口")
    
    # 检查celery_worker目录文件
    print("\n--- Worker处理器文件 ---")
    worker_files = [
        "celery_worker/__init__.py",
        "celery_worker/celery_app.py",
        "celery_worker/analyze_worker.py",
        "celery_worker/ddr_worker.py",
        "celery_worker/download_worker.py", 
        "celery_worker/misc_worker.py"
    ]
    
    for file_path in worker_files:
        dirs_ok &= check_file_exists(file_path, f"Worker处理器")
    
    return dirs_ok

def check_task_interfaces():
    """检查任务接口函数"""
    print("\n=== 检查任务接口函数 ===")
    
    interfaces_ok = True
    
    try:
        # 检查analyze_tasks接口
        print("--- 分析任务接口 ---")
        with open("tasks/analyze_tasks.py", "r") as f:
            content = f.read()
            if "submit_analyze_task" in content:
                print("✓ submit_analyze_task 接口存在")
            else:
                print("✗ submit_analyze_task 接口缺失")
                interfaces_ok = False
                
            if "submit_priority_analyze_task" in content:
                print("✓ submit_priority_analyze_task 接口存在")
            else:
                print("✗ submit_priority_analyze_task 接口缺失")
                interfaces_ok = False
        
        # 检查ddr_tasks接口
        print("\n--- DDR任务接口 ---")
        with open("tasks/ddr_tasks.py", "r") as f:
            content = f.read()
            if "submit_ddr_events_task" in content:
                print("✓ submit_ddr_events_task 接口存在")
            else:
                print("✗ submit_ddr_events_task 接口缺失")
                interfaces_ok = False
                
            if "submit_emergency_ddr_task" in content:
                print("✓ submit_emergency_ddr_task 接口存在")
            else:
                print("✗ submit_emergency_ddr_task 接口缺失")
                interfaces_ok = False
        
        # 检查download_tasks接口
        print("\n--- 下载任务接口 ---")
        with open("tasks/download_tasks.py", "r") as f:
            content = f.read()
            if "submit_download_file_task" in content:
                print("✓ submit_download_file_task 接口存在")
            else:
                print("✗ submit_download_file_task 接口缺失")
                interfaces_ok = False
                
    except Exception as e:
        print(f"✗ 检查任务接口时出错: {e}")
        interfaces_ok = False
    
    return interfaces_ok

def check_worker_tasks():
    """检查Worker任务函数"""
    print("\n=== 检查Worker任务函数 ===")
    
    workers_ok = True
    
    try:
        # 检查analyze_worker
        print("--- 分析Worker ---")
        with open("celery_worker/analyze_worker.py", "r") as f:
            content = f.read()
            if "analyze_worker_task" in content:
                print("✓ analyze_worker_task 任务存在")
            else:
                print("✗ analyze_worker_task 任务缺失")
                workers_ok = False
                
            if "priority_analyze_worker_task" in content:
                print("✓ priority_analyze_worker_task 任务存在")
            else:
                print("✗ priority_analyze_worker_task 任务缺失")
                workers_ok = False
        
        # 检查ddr_worker
        print("\n--- DDR Worker ---")
        with open("celery_worker/ddr_worker.py", "r") as f:
            content = f.read()
            if "process_ddr_events_task" in content:
                print("✓ process_ddr_events_task 任务存在")
            else:
                print("✗ process_ddr_events_task 任务缺失")
                workers_ok = False
                
            if "emergency_ddr_events_task" in content:
                print("✓ emergency_ddr_events_task 任务存在")
            else:
                print("✗ emergency_ddr_events_task 任务缺失")
                workers_ok = False
        
        # 检查download_worker
        print("\n--- 下载Worker ---")
        with open("celery_worker/download_worker.py", "r") as f:
            content = f.read()
            if "download_file_task" in content:
                print("✓ download_file_task 任务存在")
            else:
                print("✗ download_file_task 任务缺失")
                workers_ok = False
                
    except Exception as e:
        print(f"✗ 检查Worker任务时出错: {e}")
        workers_ok = False
    
    return workers_ok

def check_priority_configuration():
    """检查优先级配置"""
    print("\n=== 检查优先级配置 ===")
    
    priority_ok = True
    
    try:
        with open("celery_worker/celery_app.py", "r") as f:
            content = f.read()
            
            if "PRIORITY_LEVELS" in content:
                print("✓ PRIORITY_LEVELS 配置存在")
                
                if "'DDR': 8" in content:
                    print("✓ DDR优先级设置为8")
                else:
                    print("✗ DDR优先级配置缺失")
                    priority_ok = False
                    
                if "'EMERGENCY': 10" in content:
                    print("✓ EMERGENCY优先级设置为10")
                else:
                    print("✗ EMERGENCY优先级配置缺失")
                    priority_ok = False
            else:
                print("✗ PRIORITY_LEVELS 配置缺失")
                priority_ok = False
                
    except Exception as e:
        print(f"✗ 检查优先级配置时出错: {e}")
        priority_ok = False
    
    return priority_ok

def main():
    """主函数"""
    print("Celery架构验证")
    print("=" * 50)
    
    # 检查各个方面
    structure_ok = check_directory_structure()
    interfaces_ok = check_task_interfaces()
    workers_ok = check_worker_tasks()
    priority_ok = check_priority_configuration()
    
    # 总结
    print("\n" + "=" * 50)
    print("验证结果总结:")
    print(f"目录结构: {'✓ 通过' if structure_ok else '✗ 失败'}")
    print(f"任务接口: {'✓ 通过' if interfaces_ok else '✗ 失败'}")
    print(f"Worker任务: {'✓ 通过' if workers_ok else '✗ 失败'}")
    print(f"优先级配置: {'✓ 通过' if priority_ok else '✗ 失败'}")
    
    all_ok = structure_ok and interfaces_ok and workers_ok and priority_ok
    
    if all_ok:
        print("\n🎉 架构验证通过！Celery架构配置正确。")
        return 0
    else:
        print("\n❌ 架构验证失败，请检查上述问题。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
