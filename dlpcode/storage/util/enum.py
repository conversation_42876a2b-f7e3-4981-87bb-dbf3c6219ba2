from enum import Enum, IntEnum
from typing import Optional, Dict, Any, List
from datetime import datetime
from util.enum_ import StorageType
from util.common_log import get_logger

logger = get_logger("fetch_storage_log")


class ActivityStatus(IntEnum):
    NEW = 0
    PENDING = 1
    PROCESSING = 2
    COMPLETED = 3
    FAILED = 4
    SKIPPED = 5

class EventType(Enum):
    """Event type enumeration for DDR system"""
    # Basic file operations
    FILE_CREATED = "file_created"           # File created
    FILE_MODIFIED = "file_modified"         # File modified
    FILE_DELETED = "file_deleted"           # File deleted
    FILE_RENAMED = "file_renamed"           # File renamed
    FILE_ACCESSED = "file_accessed"         # File accessed
    FILE_DOWNLOADED = "file_downloaded"     # File downloaded
    FILE_UPLOADED = "file_uploaded"         # File uploaded
    FILE_MOVED = "file_moved"               # File moved
    FILE_COPIED = "file_copied"             # File copied
    FILE_RESTORED = "file_restored"         # File restored
    FILE_RECYCLED = "file_recycled"         # File recycled
    
    # Bulk operations
    BULK_DELETE = "bulk_delete"             # Bulk delete
    BULK_UPLOAD = "bulk_upload"             # Bulk upload

    # Sharing operations
    FILE_SHARED = "file_shared"             # File shared
    FILE_UNSHARED = "file_unshared"         # File unshared
    SHARE_PERMISSION_CHANGED = "share_permission_changed"  # Share permission changed
    SHARE_LINK_CREATED = "share_link_created"  # Share link created
    SHARE_LINK_MODIFIED = "share_link_modified"  # Share link modified
    SHARE_LINK_DELETED = "share_link_deleted"  # Share link deleted
    SHARE_LINK_USED = "share_link_used"     # Share link used
    SHARE_INVITATION_SENT = "share_invitation_sent"  # Share invitation sent
    SHARE_INVITATION_ACCEPTED = "share_invitation_accepted"  # Share invitation accepted
    SHARE_INVITATION_DECLINED = "share_invitation_declined"  # Share invitation declined
    
    # Permission operations
    FILE_PERMISSION_CHANGED = "file_permission_changed"  # File permission changed
    FILE_ACCESS_DENIED = "file_access_denied"  # File access denied
    
    # Unknown event
    UNKNOWN = "unknown"                     # Unknown event


class PlatformEventMapper:
    """
    Platform event mapper - provides event type mapping rules for different platforms
    """
    # List of event types that require scanning for each platform
    SCAN_REQUIRED_EVENTS = {
        StorageType.AWS: [
            "GetObject",    # download/access
            "HeadObject",   # access
            "CopyObject",   # copy
            "PutObjectAcl" # share (permission change)
        ],
        StorageType.SHAREPOINT_OL: [
            "FileDownloaded", 
            "FileAccessed", 
            "FileShared", 
            "FileCopied", 
            "FileMoved"
        ],
        StorageType.GOOGLE: [
            "download",   # download
            "view",       # access
            "share",      # share
            "copy",       # copy
            "move"        # move
        ]
    }

    @staticmethod
    def is_scan_required(event_type: str, platform: StorageType) -> bool:
        """
        Determine whether the event type requires scanning for the specified platform
        """
        scan_events = PlatformEventMapper.SCAN_REQUIRED_EVENTS.get(platform, [])
        return event_type in scan_events
    
    @staticmethod
    def get_aws_event_mappings() -> Dict[str, str]:
        """Get AWS CloudTrail file event mappings"""
        return {
            # Basic file operations
            "PutObject": EventType.FILE_UPLOADED.value,
            "GetObject": EventType.FILE_ACCESSED.value,
            "DeleteObject": EventType.FILE_DELETED.value,
            "CopyObject": EventType.FILE_COPIED.value,
            "HeadObject": EventType.FILE_ACCESSED.value,
            "DeleteObjects": EventType.BULK_DELETE.value,
            
            # Permission related
            "PutObjectAcl": EventType.FILE_PERMISSION_CHANGED.value,
            "GetObjectAcl": EventType.FILE_ACCESSED.value,
            "AccessDenied": EventType.FILE_ACCESS_DENIED.value,
        }
    
    @staticmethod
    def get_sharepoint_event_mappings() -> Dict[str, str]:
        """Get SharePoint file event mappings"""
        return {
            # Basic file operations
            "FileUploaded": EventType.FILE_UPLOADED.value,
            "FileModified": EventType.FILE_MODIFIED.value,
            "FileDeleted": EventType.FILE_DELETED.value,
            "FileRenamed": EventType.FILE_RENAMED.value,
            "FileAccessed": EventType.FILE_ACCESSED.value,
            "FileDownloaded": EventType.FILE_DOWNLOADED.value,
            "FileMoved": EventType.FILE_MOVED.value,
            "FileCopied": EventType.FILE_COPIED.value,
            "FileRestored": EventType.FILE_RESTORED.value,
            "FileRecycled": EventType.FILE_RECYCLED.value,

            # Sharing operations
            "FileShared": EventType.FILE_SHARED.value,
            "FileUnshared": EventType.FILE_UNSHARED.value,
            "SharePermissionChanged": EventType.SHARE_PERMISSION_CHANGED.value,
            "ShareLinkCreated": EventType.SHARE_LINK_CREATED.value,
            "ShareLinkModified": EventType.SHARE_LINK_MODIFIED.value,
            "ShareLinkDeleted": EventType.SHARE_LINK_DELETED.value,
            "ShareInvitationSent": EventType.SHARE_INVITATION_SENT.value,
            "ShareInvitationAccepted": EventType.SHARE_INVITATION_ACCEPTED.value,
            "ShareInvitationDeclined": EventType.SHARE_INVITATION_DECLINED.value,
            "SharingSet": EventType.FILE_SHARED.value,
            "CompanyLinkCreated": EventType.SHARE_LINK_CREATED.value,
            "CompanyLinkRemoved": EventType.SHARE_LINK_DELETED.value,
            "AnonymousLinkCreated": EventType.SHARE_LINK_CREATED.value,
            "AnonymousLinkRemoved": EventType.SHARE_LINK_DELETED.value,
            "SecureLinkCreated": EventType.SHARE_LINK_CREATED.value,
            "SecureLinkDeleted": EventType.SHARE_LINK_DELETED.value,
            "AddedToSecureLink": EventType.SHARE_PERMISSION_CHANGED.value,
            "RemovedFromSecureLink": EventType.SHARE_PERMISSION_CHANGED.value,
            "SecureLinkUsed": EventType.SHARE_LINK_USED.value,
            "SharingLinkUsed": EventType.SHARE_LINK_USED.value,
            "CompanyLinkUsed": EventType.SHARE_LINK_USED.value,
            "AnonymousLinkUsed": EventType.SHARE_LINK_USED.value,

            # Permission related
            "PermissionChanged": EventType.FILE_PERMISSION_CHANGED.value,
            "AccessDenied": EventType.FILE_ACCESS_DENIED.value,
        }

    @staticmethod
    def get_google_event_mappings() -> Dict[str, str]:
        """Get Google Drive file event mappings"""
        return {
            "create": EventType.FILE_CREATED.value,
            "upload": EventType.FILE_UPLOADED.value,
            "edit": EventType.FILE_MODIFIED.value,
            "update": EventType.FILE_MODIFIED.value,
            "delete": EventType.FILE_DELETED.value,
            "move": EventType.FILE_MOVED.value,
            "copy": EventType.FILE_COPIED.value,
            "rename": EventType.FILE_RENAMED.value,
            "view": EventType.FILE_ACCESSED.value,
            "download": EventType.FILE_DOWNLOADED.value,
            "share": EventType.FILE_SHARED.value,
            "unshare": EventType.FILE_UNSHARED.value,
            "permissionChange": EventType.FILE_PERMISSION_CHANGED.value,
            "accessDenied": EventType.FILE_ACCESS_DENIED.value,
        }

    @staticmethod
    def get_event_mappings(platform: StorageType) -> Dict[str, str]:
        mapping_funcs = {
            StorageType.AWS: PlatformEventMapper.get_aws_event_mappings,
            StorageType.SHAREPOINT_OL: PlatformEventMapper.get_sharepoint_event_mappings,
            StorageType.GOOGLE: PlatformEventMapper.get_google_event_mappings
        }

        func = mapping_funcs.get(platform)
        if func:
            return func()
        else:
            logger.warning(f"Unsupported platform: {platform}")
            return {}

    @staticmethod
    def get_supported_operations(platform: StorageType) -> List[str]:
        """
        Get all supported operations for a specific platform
        Args:
            platform (StorageType): Platform enum (e.g. SHAREPOINT_OL, GOOGLE, AWS)
        Returns:
            List of supported operation names for the platform
        """
        event_mappings = PlatformEventMapper.get_event_mappings(platform)
        return list(event_mappings.keys())
