from exts import Base, Session, get_logger
import uuid
from sqlalchemy import Column, String, Integer, DateTime, or_, func, select
from sqlalchemy.dialects.postgresql import JSONB, UUID
from datetime import timezone
from util.enum_ import SyncVersion

fetch_storage_log = get_logger("fetch_storage_log")


class StorageSharedDrives(Base):
    __tablename__ = 'storage_shared_drives'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sid = Column(String, nullable=False)
    name = Column(String)
    organizer_email = Column(String)
    identifier = Column(String,nullable=True)
    info = Column(JSONB,nullable=True)
    ext_info = Column(JSONB,nullable=True)
    version = Column(Integer)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    def to_dict(self):
        return {
            'id': str(self.id),
            'sid': str(self.sid),
            'name': self.name,
            'organizer_email': self.organizer_email,
            'identifier': self.identifier,
            'info': self.info,
            'ext_info': self.ext_info,
            'version': self.version,
            'created_at': self.created_at.replace(tzinfo=timezone.utc).timestamp() if self.created_at else None,
            'updated_at': self.updated_at.replace(tzinfo=timezone.utc).timestamp() if self.updated_at else None
        }


def add_storage_shared_drives(storage_id, shared_drive_list, version=SyncVersion.UPDATE):
    try:
        session = Session()
        for drive in shared_drive_list:
            session.add(StorageSharedDrives(sid = storage_id, name=drive.get('name'),
                                organizer_email=drive.get('organizer_email'), identifier=drive.get('id'),
                                version=version))
        session.commit()
    except Exception as e:
        fetch_storage_log.info(f"Error add_storage_shared_drives, {e}")
        session.rollback()
    finally:
        session.close()

def get_storage_shared_drives(conditions: dict) -> tuple[list, int]:
    filters = []
    for k, v in conditions.items():
        col = getattr(StorageSharedDrives, k, None)
        if col is None:
            continue

        if k in {'sid', 'id', 'identifier'}:
            filters.append(col == v)
        elif k in {'name', 'organizer_email'}:
            filters.append(col.ilike(f"%{v}%"))

    filters.append(StorageSharedDrives.version == SyncVersion.INIT)
    count_stmt = select(func.count()).select_from(StorageSharedDrives).where(*filters)
    stmt = select(StorageSharedDrives).where(*filters)

    sort_field = conditions.get('sort_field', None)
    sort_method = conditions.get('sort_method', None)
    if sort_field:
        sort_col = getattr(StorageSharedDrives, sort_field, None)
        if sort_col:
            stmt = stmt.order_by(sort_col.desc() if (sort_method == 'desc') else sort_col.asc())

    page = conditions.get('page', None)
    per_page = conditions.get('per_page', 10)
    if page:
        offset = (page - 1) * per_page
        stmt = stmt.offset(offset).limit(per_page)

    with Session() as session:
        total_count = session.execute(count_stmt).scalar()
        result = session.execute(stmt).scalars().all()
        result = [r.to_dict() for r in result]

    return result, total_count