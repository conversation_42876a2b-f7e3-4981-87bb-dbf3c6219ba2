from exts import Base, Session, get_logger
import uuid
from sqlalchemy import Column, String, Integer, DateTime, func, select
from sqlalchemy.dialects.postgresql import JSONB, UUID
from util.enum_ import SyncVersion
from datetime import timezone

logger = get_logger("fetch_storage_log")

class StorageSite(Base):
    __tablename__ = 'storage_sites'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sid = Column(String, nullable=False)
    site_id = Column(String, unique=True, nullable=False)
    site_url = Column(String)
    version = Column(Integer)
    location = Column(String)
    info = Column(JSONB,nullable=True)
    ext_info = Column(JSONB,nullable=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    def to_dict(self):
        return {
            'id': str(self.id),
            'sid': str(self.sid),
            'site_id': self.site_id,
            'site_url': self.site_url,
            'info': self.info,
            'ext_info': self.ext_info,
            'version': self.version,
            'created_at': self.created_at.replace(tzinfo=timezone.utc).timestamp() if self.created_at else None,
            'updated_at': self.updated_at.replace(tzinfo=timezone.utc).timestamp() if self.updated_at else None
        }


def add_storage_sites(storage_id, sites, version=SyncVersion.UPDATE):
    try:
        session = Session()
        for site in sites:
            session.add(StorageSite(sid = storage_id, site_id=site.get('id'), site_url=site.get('webUrl'),
                                    version=version, location=site.get('location', 'UNKNOWN')))
        session.commit()
    except Exception as e:
        logger.info(f"Error add_storage_sites, {e}")
        session.rollback()
    finally:
        session.close()


def get_storage_sites(conditions: dict) -> tuple[list, int]:
    filters = []
    for k, v in conditions.items():
        col = getattr(StorageSite, k, None)
        if col is None:
            continue

        if k in {'sid', 'site_url'}:
            filters.append(col == v)
        elif k == "site_id":
            value = f"%{v}%"
            filters.append(col.ilike(value))

    filters.append(StorageSite.version == SyncVersion.INIT)
    count_stmt = select(func.count()).select_from(StorageSite).where(*filters)
    stmt = select(StorageSite).where(*filters)

    sort_field = conditions.get('sort_field', None)
    sort_method = conditions.get('sort_method', None)
    if sort_field:
        sort_col = getattr(StorageSite, sort_field, None)
        if sort_col:
            stmt = stmt.order_by(sort_col.desc() if (sort_method == 'desc') else sort_col.asc())

    page = conditions.get('page', None)
    per_page = conditions.get('per_page', 10)
    if page:
        offset = (page - 1) * per_page
        stmt = stmt.offset(offset).limit(per_page)

    with Session() as session:
        total_count = session.execute(count_stmt).scalar()
        result = session.execute(stmt).scalars().all()
        result = [r.to_dict() for r in result]

    return result, total_count