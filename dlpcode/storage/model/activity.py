import uuid
import traceback
from exts import Session, get_logger, Base
from sqlalchemy import Column, String, SmallInteger, DateTime, Boolean, and_
from sqlalchemy.dialects.postgresql import UUID, JSONB, insert
from sqlalchemy.sql import func, exists
from datetime import datetime, timezone
from psycopg2.errors import UniqueViolation
from sqlalchemy.orm.attributes import flag_modified
from typing import List

logger = get_logger("fetch_storage_log")


class StorageActivity(Base):
    __tablename__ = "storage_activity"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    storage_id = Column(UUID(as_uuid=True), nullable=True)
    storage_type = Column(SmallInteger, nullable=False)
    event_time = Column(DateTime(timezone=True), nullable=False)
    event_id = Column(String, nullable=True)
    event_type = Column(String, nullable=True)
    scan_policy_fields = Column(JSONB)
    ddr_fields = Column(JSONB)
    raw_data = Column(JSONB, nullable=False)
    reserve_json = Column(JSONB)
    status = Column(SmallInteger, nullable=False, default=0)  # 0: new, 1: pending, 2: processing, 3: completed, 4: failed, 5: skipped
    scan_triggered = Column(Boolean, default=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    def Get_id(self):
        return self.id
    
    def to_dict(self):
        return {
            'id': str(self.id),
            'storage_id': str(self.storage_id) if self.storage_id else None,
            'storage_type': self.storage_type,
            'event_time': self.event_time.isoformat() if self.event_time else None,
            'event_id': self.event_id,
            'event_type': self.event_type,
            'scan_policy_fields': self.scan_policy_fields,
            'ddr_fields': self.ddr_fields,
            'raw_data': self.raw_data,
            'reserve_json': self.reserve_json,
            'status': self.status,
            'scan_triggered': self.scan_triggered,
            'created_at': self.created_at.replace(tzinfo=timezone.utc).timestamp() if self.created_at.tzinfo is None else self.created_at.timestamp(),
            'updated_at': self.updated_at.replace(tzinfo=timezone.utc).timestamp() if self.updated_at.tzinfo is None else self.updated_at.timestamp(),
        }

def get_fetched_activity(id: str, logger=logger) -> StorageActivity:
    try:
        with Session() as session:
            return session.query(StorageActivity).filter(StorageActivity.id == id).first()
    except Exception as e:
        logger.error(e)
        return None

def get_fetched_activities(**kwargs) -> list:
    try:
        filters = []
        for key, value in kwargs.items():
            filters.append(getattr(StorageActivity, key) == value)

        with Session() as session:
            records = session.query(StorageActivity).filter(and_(*filters)).all()
            return records
    except Exception as e:
        logger.error(e)
        return []

def delete_fetched_activity(ids: list, logger=logger) -> bool:
    try:
        delete_names = []
        with Session() as session:
            policies = session.query(StorageActivity).filter(StorageActivity.id.in_(ids)).all()
            for policy in policies:
                delete_names.append(policy.name)
                session.delete(policy)
                session.commit()
            return delete_names
    except Exception as e:
        logger.error(e)
        return []

def create_fetched_activity(data: dict, logger=logger) -> StorageActivity:
    try:
        with Session() as session:
            if session.query(exists().where(StorageActivity.event_id == data.get('event_id') and 
                                            StorageActivity.event_type == data.get('event_type'))).scalar():
                raise UniqueViolation
            policy = StorageActivity(**data)
            policy.created_at = func.now()
            policy.updated_at = func.now()
            session.add(policy)
            session.commit()
            session.refresh(policy)
            return policy
    except UniqueViolation:
        raise
    except Exception as e:
        logger.error(e)
        return None

def update_fetched_activity(id, data, logger=logger):
    try:
        session = Session()
        activity = session.query(StorageActivity).filter(StorageActivity.id == id).first()
        if not activity:
            logger.info(f"activity {id} not found")
            return None
        
        valid_columns = {column.name for column in StorageActivity.__table__.columns}
        for key, value in data.items():
            if key in valid_columns and value is not None:
                setattr(activity, key, value)
                flag_modified(activity, key)
                logger.info(f"update {key} to {value}")

        activity.updated_at = func.now()
        session.commit()
        return activity.to_dict()
    except Exception as e:
        session.rollback()
        logger.error(e)
    finally:
        session.close()
    

def add_fetched_activities(datas: List[dict], logger=logger):  
    try:
        with Session() as session:
            stmt = insert(StorageActivity).values([
                {**data, "created_at": func.now(), "updated_at": func.now()}
                for data in datas
            ]).on_conflict_do_nothing(
                index_elements=["event_id", "event_type"]
            )

            session.execute(stmt)
            session.commit()
    except Exception as e:
        logger.error(e)
        return False
    return True

condition_fields = {
    "id":   None,  # custom match method
    "storage_id": None,
    "storage_type": {'method': "equal", "type": "int"},
    "event_id": {'method': "ilike", "type": "str"}
}

def get_activities(conditions, logger=logger):
    try:
        session = Session()
        params = []
        # generate the query filters
        for key, value in conditions.items():
            if key not in condition_fields:
                continue

            if value is None:
                continue

            val_dict = condition_fields[key]
            if key == "id" or key == "storage_id":
                if value:
                    if isinstance(value, list):
                        params.append(StorageActivity.id.in_(value))
                    else:
                        params.append(StorageActivity.id == value)
            elif val_dict:
                column = getattr(StorageActivity, key)
                if val_dict["method"] == "like":
                        value = f"%{value}%"
                        params.append(column.like(value))
                elif val_dict["method"] == "ilike":
                        value = f"%{value}%"
                        params.append(column.ilike(value))
                elif val_dict["method"] == "equal":
                    params.append(column == value)
                elif val_dict["method"] == "iequal":
                    if val_dict["type"] == "str":
                        params.append(column.ilike(value))
                else:
                    logger.error(f"invalid match method {val_dict['method']}")
            else:
                logger.error(f"wrong query condition defined {key}")

        results = session.query(StorageActivity).filter(*params).all()
        if results:
            return [result.to_dict() for result in results], results.count()
        else:
            return [], 0
    except:
        logger.exception(traceback.format_exc())
        return [], 0
    finally:
        session.close()

def update_activities(datas: List[dict], logger=logger):
    try:
        session = Session()
        for data in datas:
            session.query(StorageActivity).filter(StorageActivity.id == data["id"]).update(data)
        session.commit()
    except:
        logger.exception(traceback.format_exc())
    finally:
        session.close()

def update_activity_status(activity_id: str, status: int) -> bool:
    """
    Update Storage activity status
    Args:
        activity_id: activity id
        status: new status
    Returns:
        bool: Success status
    """
    try:
        session = Session()
        activity = session.query(StorageActivity).filter(StorageActivity.id == activity_id).first()
        if activity:
            activity.status = status
            activity.updated_at = func.now()
            session.commit()
            return True
        return False
    except Exception as e:
        logger.error(f"Error updating Storage activity status: {e}")
        session.rollback()
        return False
    finally:
        session.close()