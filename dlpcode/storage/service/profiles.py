import traceback
from exts import Session, get_logger
from sqlalchemy import or_, exists
from sqlalchemy.sql import func, exists
from natsort import natsorted, ns
from sqlalchemy.dialects.postgresql import UUID
from typing import Optional
from storage.model.profiles import StorageProfile

logger = get_logger("fetch_storage_log")


condition_fields = {
    "id":   None,  # custom match method
    "name": {'method': "ilike", "type": "str"},
    "type": {'method': "equal", "type": "int"}
}

def get_storage_summary():
    with Session() as session:
        results = session.query(
            StorageProfile.id,
            StorageProfile.name,
            StorageProfile.type,
            StorageProfile.notes
        ).all()
        fields = ["id", "name", "type", "notes"]
        return [dict(zip(fields, row)) for row in results]

def get_storage_profiles(conditions, logger=logger):
    try:
        session = Session()
        params = []
        # generate the query filters
        for key, value in conditions.items():
            if key not in condition_fields:
                continue

            if value is None:
                continue

            val_dict = condition_fields[key]
            if key == "id":
                if value:
                    if isinstance(value, list):
                        params.append(StorageProfile.id.in_(value))
                    else:
                        params.append(StorageProfile.id == value)
            elif val_dict:
                column = getattr(StorageProfile, key)
                if val_dict["method"] == "like":
                        value = f"%{value}%"
                        params.append(column.like(value))
                elif val_dict["method"] == "ilike":
                        value = f"%{value}%"
                        params.append(column.ilike(value))
                elif val_dict["method"] == "equal":
                    params.append(column == value)
                elif val_dict["method"] == "iequal":
                    if val_dict["type"] == "str":
                        params.append(column.ilike(value))
                else:
                    logger.error(f"invalid match method {val_dict['method']}")
            else:
                logger.error(f"wrong query condition defined {key}")

        sort_field = conditions.get('sort_field') if conditions.get('sort_field') is not None  else 'updated_at'
        sort_method = conditions.get('sort_method') if conditions.get('sort_method') is not None  else 'desc'

        # Validate if the sort_field is in the allowed list
        allow_sort_fields = ['id', 'name', 'created_at', 'updated_at']
        if sort_field not in allow_sort_fields:
            # Use the default sort field, if the sort_field not in the allow sort field list.
            logger.error(f"Sort field '{sort_field}' not in the allow list, so uses the default sort field 'updated_at'")
            sort_field = 'updated_at'

        column = getattr(StorageProfile, sort_field)
        if sort_method == 'desc':
            results = session.query(StorageProfile).filter(*params).order_by(column.desc())
        else:
            results = session.query(StorageProfile).filter(*params).order_by(column.asc())

        total = results.count()
        page = conditions.get('page')
        per_page = conditions.get('per_page', 10)
        # if no page setting, return all matched items
        if page is not None:
            page = int(page)
            per_page = int(per_page)
            slice_from = per_page * (page - 1)
            slice_to = per_page * page
            results = results.slice(slice_from, slice_to)

        profiles = results.all()
        if sort_field == "name":
            if sort_method == "asc":
                profiles = natsorted(profiles, key=lambda x: x.name, alg=ns.IGNORECASE)
            else:
                profiles = natsorted(profiles, key=lambda x: x.name, alg=ns.IGNORECASE, reverse=True)
        if profiles:
            return [profile.to_dict() for profile in profiles], total
        else:
            return [], total
    except:
        logger.exception(traceback.format_exc())
        return [], 0
    finally:
        session.close()

def check_ref(ids, logger=logger):
    from domain_model.scan_policy import ScanPolicy
    from service.protection_profile_service import ProtectionProfile
    try:
        with Session() as session:
            if session.query(
                exists().where(
                    or_(*[ScanPolicy.scan_init_info.contains({'storage_id': i}) for i in ids])
                )
            ).scalar():
                return -1

            if session.query(
                exists().where(
                    or_(*[ProtectionProfile.profile.contains({'storage_id': i}) for i in ids])
                )
            ).scalar():
                return -2
    except Exception as e:
        logger.exception(f"Database query failed: {e}")

    return 0

def delete_storage_profiles(ids, logger=logger):
    from storage.model.sites import StorageSite
    from storage.model.identity import StorageIdentity
    from storage.model.activity import StorageActivity
    
    deleted_profile = []
    failed_profile = []

    with Session() as session:
        for id in ids:
            try:
                record = session.query(StorageProfile).filter(StorageProfile.id == id).one()
                record_name = record.name
                session.delete(record)
                session.query(StorageSite).filter_by(sid=id).delete()
                session.query(StorageIdentity).filter_by(sid=id).delete()
                session.query(StorageActivity).filter_by(storage_id=id).delete()
                session.commit()
                deleted_profile.append(record_name)
            except Exception as e:
                session.rollback()
                logger.error(e)
                failed_profile.append(id)

        return deleted_profile, failed_profile

def encrypt_password(**payload):
    from util.random_password.service import RandomPasswordService
    from util.enum_ import StorageType
    try:
        storage_type = payload.get('type')
        auth_info = payload.get('auth_info')
        logger.info(f"storage_type {storage_type}, auth_info {auth_info}")
        if storage_type == StorageType.AWS:
            key_id = auth_info.get('key_id')
            payload['auth_info']['key_id'] = RandomPasswordService.encrypt_scan_pwd(key_id)

            pwd = auth_info.get('access_key')
            payload['auth_info']['access_key'] = RandomPasswordService.encrypt_scan_pwd(pwd)
        else:
            if storage_type == StorageType.SMB:
                if not auth_info.get('anonymous', False):
                    pwd = auth_info.get('password')
                    payload['auth_info']['password'] = RandomPasswordService.encrypt_scan_pwd(pwd)
            elif (storage_type == StorageType.SHAREPOINT_OP) \
                or (storage_type== StorageType.SHAREPOINT_OL and auth_info.get("usecredentials", True)):
                    pwd = auth_info.get('password')
                    payload['auth_info']['password'] = RandomPasswordService.encrypt_scan_pwd(pwd)
            elif storage_type == StorageType.SHAREPOINT_OL and "clientsecret" in auth_info and 'clientid' in auth_info:
                clientid = auth_info.get('clientid')
                payload['auth_info']['clientid'] = RandomPasswordService.encrypt_scan_pwd(clientid)

                clientsecret = auth_info.get('clientsecret')
                payload['auth_info']['clientsecret'] = RandomPasswordService.encrypt_scan_pwd(clientsecret)
            elif (storage_type == StorageType.GOOGLE):
                service_account_info = auth_info.get('service_account_info')
                payload['auth_info']['service_account_info'] = RandomPasswordService.encrypt_scan_pwd(service_account_info)
            else:
                clientsecret = auth_info.get('clientsecret')
                payload['auth_info']['clientsecret'] = RandomPasswordService.encrypt_scan_pwd(clientsecret)
    except:
        logger.exception(traceback.format_exc())

def create_storage_profile(payload: dict, pwd_encrypted, pwd_encrypted2, logger=logger) -> Optional[UUID]:
    from psycopg2.errors import UniqueViolation
    try:
        if pwd_encrypted == 0 or pwd_encrypted2 == 0:
            encrypt_password(**payload, logger=logger)

        with Session() as session:
            if session.query(exists().where(StorageProfile.name == payload.get('name'))).scalar():
                raise UniqueViolation

            storage_profile = StorageProfile(**payload)
            session.add(storage_profile)
            session.commit()
            return storage_profile.id
    except UniqueViolation:
        raise
    except Exception as e:
        logger.error(e)
        return None

def update_storage_profile(profile_id: str, pwd_encrypted, pwd_encrypted2, **kwargs: dict) -> bool:
    try:
        with Session() as session:
            record = session.query(StorageProfile).filter(StorageProfile.id == profile_id).first()
            if not record:
                logger.info(f"Storage profile({profile_id}) not found.")
                return False

            if pwd_encrypted == 0 or pwd_encrypted2 == 0:
                encrypt_password(**kwargs, logger=logger)

            valid_columns = {column.name for column in StorageProfile.__table__.columns}
            for key, value in kwargs.items():
                if key in valid_columns and key != 'id' and key != 'name' and key != 'type':
                    setattr(record, key, value)

            record.updated_at = func.now()
            session.commit()
    except Exception as e:
        logger.error(e)
        return False
    return True

def get_storage_profile_by_id(profile_id, logger=logger):
    with Session() as session:
        try:
            result = session.query(StorageProfile).filter_by(id=profile_id).first()
            return result.to_dict() if result else None
        except Exception:
            logger.exception(traceback.format_exc())
            return None