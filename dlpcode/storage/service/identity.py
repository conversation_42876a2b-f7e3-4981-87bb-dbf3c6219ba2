from exts import Session, get_logger
from sqlalchemy import or_, func, select, cast, exists
from sqlalchemy.dialects.postgresql import JSONB
from util.enum_ import SyncVersion, IdentityType, UserType
from domain_model.file_info import FileInfoView
from storage.model.sites import StorageSite
from storage.model.identity import StorageIdentity
from storage.model.shared_drives import StorageSharedDrives

logger = get_logger("fetch_storage_log")


def cleanup_and_switch_version(storage_id, task):
    table_map = {
            "sites": StorageSite,
            "users": StorageIdentity,
            "groups": StorageIdentity,
            "shared_drives": StorageSharedDrives
    }
    table = table_map.get(task)

    try:
        with Session() as session:
            base_filter = {"sid": storage_id, "version": SyncVersion.INIT}
            update_filter = {"sid": storage_id}

            if task == "users":
                base_filter["identity_type"] = IdentityType.PEOPLE
                update_filter["identity_type"] = IdentityType.PEOPLE
            elif task == "groups":
                base_filter["identity_type"] = IdentityType.GROUP
                update_filter["identity_type"] = IdentityType.GROUP

            session.query(table).filter_by(**base_filter).delete()
            session.query(table).filter_by(**update_filter).update({table.version: SyncVersion.INIT})
            session.commit()
        logger.info(f"cleanup_and_switch_version {task} finish")
    except Exception as e:
        logger.error(f"Error cleanup_and_switch_version: {e}")
        raise

def get_sensitive_file_stats(storage_id, identity) -> tuple[int, int, int]:
    """ Get sensitive file stats of storage identity. (owned, shared, access) """
    reserve_json1 = FileInfoView.reserve_json1
    metadata = FileInfoView.file_metadata

    owned_query = select(func.count()).where(
        reserve_json1['sensitive_data'].as_boolean() == True,
        metadata['owners'].contains([identity]),
        FileInfoView.storage_id == storage_id
        )

    share = {'with_public_shareable_link', 'with_internal_shareable_link', 'with_external_shareable_link',
            'with_internal_collaborators', 'with_external_collaborators'}
    shared_query = select(func.count()).where(
        reserve_json1['sensitive_data'].as_boolean() == True,
        metadata['owners'].contains([identity]),
        FileInfoView.storage_id == storage_id,
        or_(*[reserve_json1['shared_data'][s].as_boolean() == True for s in share])
        )

    links = func.jsonb_array_elements(metadata['share_link']).alias('links')
    link_collaborators = cast(links.table_valued(), JSONB)['collaborators']
    access_query = select(func.count()).where(
        reserve_json1['sensitive_data'].as_boolean() == True,
        FileInfoView.storage_id == storage_id,
        or_(metadata['collaborators'].has_key(identity),
            reserve_json1['shared_data']['with_public_shareable_link'].as_boolean() == True,
            exists(select(1).select_from(links).where(link_collaborators.contains([identity]))))
        )

    with Session() as session:
        owned_cnt = session.execute(owned_query).scalar()
        shared_cnt = session.execute(shared_query).scalar()
        access_cnt = session.execute(access_query).scalar()
        return owned_cnt, shared_cnt, access_cnt

def get_owned_sensitive_file_stats(storage_id, identity):
    """ Get ml category and share stats of sensitive files owned by identity. """
    category_stats = {}
    share_stats = {'public': 0, 'external': 0, 'internal': 0}

    stmt = (select(FileInfoView.main_class_id, FileInfoView.sub_class_id, FileInfoView.reserve_json1['shared_data'])
            .where(FileInfoView.reserve_json1['sensitive_data'].as_boolean() == True,
                   FileInfoView.file_metadata['owners'].contains([identity]),
                   FileInfoView.storage_id == storage_id))
    with Session() as session:
        result = session.execute(stmt).all()
        for main_id, sub_id, shared in result:
            id = sub_id if sub_id else main_id
            category_stats[id] = category_stats.get(id, 0) + 1;
            if shared is None:
                continue
            if shared['with_public_shareable_link']:
                share_stats['public'] += 1
            if shared['with_internal_shareable_link'] or shared['with_internal_collaborators']:
                share_stats['internal'] += 1
            if shared['with_external_shareable_link'] or shared['with_external_collaborators']:
                share_stats['external'] += 1
    return category_stats, share_stats

def get_sensitive_file_owners(storage_id: str = None) -> list[str]:
    """ Get all identities that own sensitive files """
    filters = [FileInfoView.reserve_json1['sensitive_data'].as_boolean() == True]
    if storage_id:
        filters.append(FileInfoView.storage_id == storage_id)
    query = (select(func.jsonb_array_elements_text(cast(FileInfoView.file_metadata['owners'], JSONB)))
            .distinct()
            .where(*filters))
    with Session() as session:
        return session.execute(query).scalars().all()

def get_sensitive_file_accessors(storage_id: str = None):
    """ Get identities with access to sensitive files 
    Returns: 
        all_identities: bool
        collaborators: list(str)
    """
    collaborators = set()
    reserve_json1 = FileInfoView.reserve_json1
    filters = [reserve_json1['sensitive_data'].as_boolean() == True]
    if storage_id:
        filters.append(FileInfoView.storage_id == storage_id)
    
    # public link, all identities have access
    query_pub_link = (select(exists()
                      .where(*filters, 
                             reserve_json1['shared_data']['with_public_shareable_link'].as_boolean() == True)))
    # link collaborators
    query_links = (select(FileInfoView.file_metadata['share_link'])
                   .where(*filters, 
                          or_(reserve_json1['shared_data']['with_internal_shareable_link'].as_boolean() == True,
                              reserve_json1['shared_data']['with_external_shareable_link'].as_boolean() == True)))
    # collaborators
    query_coll = (select(func.jsonb_object_keys(cast(FileInfoView.file_metadata['collaborators'], JSONB)))
             .distinct()
             .where(*filters))
    
    with Session() as session:
        if session.execute(query_pub_link).scalar():
            return True, None
        result = session.execute(query_links).scalars().all()
        for r in result:
            for link in r:
                collaborators.update(link.get('collaborators', []))
        result = session.execute(query_coll).scalars().all()
        collaborators.update(result)
        return False, collaborators

def get_storage_identity(conditions: dict) -> tuple[list, int]:
    filters = []
    for k, v in conditions.items():
        if k == 'sensitive_file':
            sens = get_sensitive_file_owners(conditions['sid'] if 'sid' in conditions else None)
            if v:
                filters.append(StorageIdentity.identifier.in_(sens))
            else:
                filters.append(StorageIdentity.identifier.not_in(sens))
            continue
        elif k == 'sensitive_file_access':
            all_identities, coll = get_sensitive_file_accessors(conditions['sid'] if 'sid' in conditions else None)
            if v:
                if not all_identities:
                    filters.append(StorageIdentity.identifier.in_(coll))
            else:
                if all_identities:
                    return [], 0
                filters.append(StorageIdentity.identifier.not_in(coll))
            continue

        col = getattr(StorageIdentity, k, None)
        if col is None:
            continue

        if k in {'sid', 'type', 'identifier', 'identity_type'}:
            filters.append(col == v)
        elif k in {'name', 'email'}:
            filters.append(col.ilike(f"%{v}%"))

    filters.append(StorageIdentity.version == SyncVersion.INIT)
    count_stmt = select(func.count()).select_from(StorageIdentity).where(*filters)
    stmt = select(StorageIdentity).where(*filters)

    sort_field = conditions.get('sort_field', None)
    sort_method = conditions.get('sort_method', None)
    if sort_field:
        sort_col = getattr(StorageIdentity, sort_field, None)
        if sort_col:
            if sort_method == 'asc':
                stmt = stmt.order_by(sort_col.asc(), StorageIdentity.identifier.asc())
            else:
                stmt = stmt.order_by(sort_col.desc(), StorageIdentity.identifier.asc())

    page = conditions.get('page', None)
    per_page = conditions.get('per_page', 10)
    if page:
        offset = (page - 1) * per_page
        stmt = stmt.offset(offset).limit(per_page)

    with Session() as session:
        total_count = session.execute(count_stmt).scalar()
        result = session.execute(stmt).scalars().all()
        result = [r.to_dict() for r in result]

    return result, total_count

def get_storage_identity_summary(storage_id):
    filters = [StorageIdentity.version == SyncVersion.INIT]
    if storage_id:
        filters.append(StorageIdentity.sid == storage_id)
    with Session() as session:
        query_result = session.execute(select(StorageIdentity).where(*filters)).scalars().all()
        result = []
        for r in query_result:
            result.append({
                'sid': r.sid,
                'name': r.name,
                'identifier': r.identifier,
                'type': r.type,
                'identity_type': r.identity_type
            })
        return result

def get_storage_identity_by_identifier(storage_id, identifiers: list[str]) -> list:
    if not identifiers:
        return []
    with Session() as session:
        result = (session.execute(select(StorageIdentity)
                                  .where(StorageIdentity.sid == storage_id,
                                         StorageIdentity.identifier.in_(identifiers),
                                         StorageIdentity.version == SyncVersion.INIT))
                                  .scalars().all())
        return [r.to_dict() for r in result]

def get_storage_identity_details(storage_id, identifier: str) -> dict | None:   
    identity = get_storage_identity_by_identifier(storage_id, [identifier])
    if not identity:
        return None

    details = identity[0]
    category_stats, share_stats = get_owned_sensitive_file_stats(storage_id, identifier)
    details['sensitive_file_stats'] = {
        'category': category_stats,
        'shared': share_stats
    }
    return details


def add_storage_identity(storage_id, user_list, identity_type, version=SyncVersion.UPDATE):
    try:
        session = Session()
        for user in user_list:
            internal = UserType.MEMBER if user.get("userType") == "Member" else UserType.GUEST
            session.add(StorageIdentity(sid = storage_id, name=user.get('displayName'),
                                email=user.get('mail'), type=internal, identifier=user.get('id'),
                                identity_type=identity_type,version=version))
        session.commit()
    except Exception as e:
        logger.error(f"Error add_storage_identity, {e}")
        session.rollback()
    finally:
        session.close()
